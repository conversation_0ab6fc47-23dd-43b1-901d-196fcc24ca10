import type { Config } from "tailwindcss";

const config: Config = {
    darkMode: ["class"],
    content: [
    "./src/(pages)/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}"
  ],
  theme: {
  	container: {
  		padding: '1rem',
  		screens: {
  			'2xl': '1328px'
  		}
  	},
  	extend: {
  		fontFamily: {
  			quicksand: ["Quicksand", "sans-serif"]
  		},
  		boxShadow: {
  			custom: '0px 4px 4px 0px #00000040',
  			'shadow-1': '0px 1px 3px 0px #0000001A',
  			'shadow-2': '0px 1px 2px -1px #0000001A',
  			'combined': '0px 1px 3px 0px #0000001A, 0px 1px 2px -1px #0000001A'
  		},
  		backgroundImage: {
  			'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
  			'gradient-conic': 'conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))'
  		},
  		colors: {
  			main: '#0077d4',
  			background: 'hsl(var(--background))',
  			foreground: 'hsl(var(--foreground))',
  			card: {
  				DEFAULT: 'hsl(var(--card))',
  				foreground: 'hsl(var(--card-foreground))'
  			},
  			popover: {
  				DEFAULT: 'hsl(var(--popover))',
  				foreground: 'hsl(var(--popover-foreground))'
  			},
			primary: "#2e2e8b",
      		secondary: "#4b5563",
  			// primary: {
  			// 	DEFAULT: 'hsl(var(--primary))',
  			// 	foreground: 'hsl(var(--primary-foreground))'
  			// },
  			// secondary: {
  			// 	DEFAULT: 'hsl(var(--secondary))',
  			// 	foreground: 'hsl(var(--secondary-foreground))'
  			// },
  			muted: {
  				DEFAULT: 'hsl(var(--muted))',
  				foreground: 'hsl(var(--muted-foreground))'
  			},
  			accent: {
  				DEFAULT: 'hsl(var(--accent))',
  				foreground: 'hsl(var(--accent-foreground))'
  			},
  			destructive: {
  				DEFAULT: 'hsl(var(--destructive))',
  				foreground: 'hsl(var(--destructive-foreground))'
  			},
  			border: 'hsl(var(--border))',
  			input: 'hsl(var(--input))',
  			ring: 'hsl(var(--ring))',
  			chart: {
  				'1': 'hsl(var(--chart-1))',
  				'2': 'hsl(var(--chart-2))',
  				'3': 'hsl(var(--chart-3))',
  				'4': 'hsl(var(--chart-4))',
  				'5': 'hsl(var(--chart-5))'
  			}
  		},
  		keyframes: {
  			fadeIn: {
  				'0%': {
  					opacity: '0'
  				},
  				'100%': {
  					opacity: '1'
  				}
  			},
  			widthIn: {
  				'0%': {
  					width: '0'
  				},
  				'100%': {
  					width: '100px'
  				}
  			},
  			translate: {
  				'0%': {
  					transform: 'translateX(40%)'
  				},
  				'100%': {
  					transform: 'translateX(0)'
  				}
  			},
  			spin: {
  				'from': {
  					transform: 'rotate(0deg)'
  				},
  				'to': {
  					transform: 'rotate(360deg)'
  				}
  			}
  		},
  		animation: {
  			fadeIn: 'fadeIn 0.4s ease-in-out',
  			translate: 'translate 0.4s ease-in-out',
  			widthIn: 'widthIn 0.2s ease-in-out',
  			spin: 'spin 20s infinite linear'
  		},
  		borderRadius: {
  			lg: 'var(--radius)',
  			md: 'calc(var(--radius) - 2px)',
  			sm: 'calc(var(--radius) - 4px)'
  		}
  	}
  },
  plugins: [require("tailwindcss-animate")]
};

export default config;
