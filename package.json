{"name": "unesco", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "build:staging": "env-cmd -f .env.staging pnpm build", "build:prod": "next build", "start": "next start", "lint": "next lint --fix", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,css,md}\"", "type-check": "tsc --noEmit -p tsconfig.json --composite false", "prepare": "husky install"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@radix-ui/react-avatar": "^1.1.6", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.11", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-tabs": "^1.1.8", "@react-pdf-viewer/core": "^3.12.0", "@react-pdf-viewer/default-layout": "^3.12.0", "@stomp/stompjs": "^7.1.1", "@tailwindcss/typography": "^0.5.16", "@tinymce/tinymce-react": "^6.1.0", "@tiptap/extension-link": "^2.11.7", "@tiptap/extension-text-align": "^2.11.7", "@tiptap/react": "^2.11.7", "@tiptap/starter-kit": "^2.11.7", "axios": "^1.8.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cookies-next": "^5.1.0", "date-fns": "^4.1.0", "dompurify": "^3.2.5", "embla-carousel-react": "^8.6.0", "env-cmd": "^10.1.0", "lucide-react": "^0.445.0", "next": "^15.3.0", "pdfjs-dist": "3.11.174", "react": "^18.3.1", "react-doc-viewer": "^0.1.14", "react-dom": "^18.3.1", "react-hook-form": "^7.55.0", "react-icons": "^5.5.0", "react-pdf": "^9.2.1", "react-quill": "^2.0.0", "react-quill-new": "^3.4.6", "react-toastify": "^11.0.5", "sockjs-client": "^1.6.1", "sonner": "^2.0.3", "swiper": "^11.2.6", "swr": "^2.3.3", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "yet-another-react-lightbox": "^3.23.0", "yup": "^1.6.1", "zustand": "^4.5.6"}, "devDependencies": {"@types/dompurify": "^3.2.0", "@types/node": "^20.17.30", "@types/react": "^18.3.20", "@types/react-dom": "^18.3.6", "@types/sockjs-client": "^1.5.4", "autoprefixer": "^10.4.21", "husky": "^8.0.0", "lint-staged": "^15.5.1", "postcss": "^8.5.3", "prettier": "^3.5.3", "prettier-plugin-css-order": "^2.1.2", "prettier-plugin-tailwindcss": "^0.5.14", "tailwindcss": "^3.4.17", "typescript": "^5.8.3"}, "engines": {"node": ">=20.x", "pnpm": ">=8.15.5"}, "packageManager": "pnpm@9.15.4+sha512.b2dc20e2fc72b3e18848459b37359a32064663e5627a51e4c74b2c29dd8e8e0491483c3abb40789cfd578bf362fb6ba8261b05f0387d76792ed6e23ea3b1b6a0", "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,css,md}": ["prettier --write"]}}