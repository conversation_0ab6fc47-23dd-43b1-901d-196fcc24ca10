<!DOCTYPE html>
<html lang="vi">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title><PERSON><PERSON><PERSON></title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: { primary: "#2e2e8b", secondary: "#4b5563" },
            borderRadius: {
              none: "0px",
              sm: "4px",
              DEFAULT: "8px",
              md: "12px",
              lg: "16px",
              xl: "20px",
              "2xl": "24px",
              "3xl": "32px",
              full: "9999px",
              button: "8px",
            },
          },
        },
      };
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap"
      rel="stylesheet"
    />
    <link
      href="https://cdn.jsdelivr.net/npm/remixicon@4.5.0/fonts/remixicon.css"
      rel="stylesheet"
    />
    <style>
      :where([class^="ri-"])::before { content: "\f3c2"; }
      body {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
      }
      input[type="number"]::-webkit-inner-spin-button,
      input[type="number"]::-webkit-outer-spin-button {
      -webkit-appearance: none;
      margin: 0;
      }
    </style>
  </head>
  <body class="bg-gray-50">
    <!-- Header Navigation -->
    <header class="bg-primary text-white">
      <div
        class="container mx-auto px-4 py-3 flex items-center justify-between"
      >
        <div class="flex items-center space-x-8">
          <a href="#" class="flex items-center text-xl font-['Pacifico']">
            <div class="w-8 h-8 flex items-center justify-center mr-2">
              <i class="ri-global-line ri-lg"></i>
            </div>
            <span>logo</span>
          </a>
          <nav class="hidden md:flex space-x-6">
            <a href="#" class="hover:text-gray-200 text-sm font-medium"
              >Giới thiệu</a
            >
            <a href="#" class="hover:text-gray-200 text-sm font-medium"
              >Tin tức</a
            >
            <a href="#" class="hover:text-gray-200 text-sm font-medium"
              >Thông kê</a
            >
            <a href="#" class="hover:text-gray-200 text-sm font-medium"
              >Diễn đàn</a
            >
            <a href="#" class="hover:text-gray-200 text-sm font-medium"
              >Tài nguyên</a
            >
            <a href="#" class="hover:text-gray-200 text-sm font-medium"
              >Xếp hạng</a
            >
            <a href="#" class="hover:text-gray-200 text-sm font-medium"
              >Khảo sát</a
            >
          </nav>
        </div>
        <div>
          <a href="#" class="text-sm font-medium hover:text-gray-200"
            >Đăng nhập</a
          >
        </div>
      </div>
    </header>
    <!-- Breadcrumb -->
    <div class="border-b">
      <div class="container mx-auto px-4 py-2">
        <div class="flex items-center justify-between">
          <div class="flex items-center text-sm text-gray-500">
            <a href="#" class="hover:text-primary">Trang chủ</a>
            <span class="mx-2">/</span>
            <div class="relative">
              <button
                id="dropdownButton"
                class="flex items-center hover:text-primary"
              >
                Mầm non
                <div class="w-4 h-4 flex items-center justify-center ml-1">
                  <i class="ri-arrow-down-s-line"></i>
                </div>
              </button>
              <div
                id="dropdownMenu"
                class="hidden absolute top-full left-0 mt-1 w-48 bg-white rounded shadow-lg border border-gray-100 py-1 z-50"
              >
                <a
                  href="#"
                  class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                  >Chương trình học</a
                >
                <a
                  href="#"
                  class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                  >Tài liệu</a
                >
                <a
                  href="#"
                  class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                  >Hoạt động</a
                >
                <a
                  href="#"
                  class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                  >Giáo viên</a
                >
                <a
                  href="#"
                  class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                  >Phụ huynh</a
                >
                <a
                  href="#"
                  class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                  >Thời khóa biểu</a
                >
              </div>
            </div>
            <span class="mx-2">/</span>
            <span class="text-gray-700">Diễn đàn</span>
          </div>
          <div class="flex items-center text-sm text-gray-500">
            <div class="w-4 h-4 flex items-center justify-center mr-1">
              <i class="ri-time-line"></i>
            </div>
            <span>Bài viết mới nhất 24/4/2025</span>
          </div>
        </div>
      </div>
    </div>
    <!-- Main Content -->
    <div class="container mx-auto px-4 py-6">
      <div class="flex flex-col md:flex-row gap-6">
        <!-- Left Column - Main Content -->
        <div class="w-full md:w-2/3">
          <!-- Help Box -->
          <div class="bg-gray-100 rounded-md p-6 mb-6 flex items-center">
            <div class="mr-6">
              <img
                src="https://readdy.ai/api/search-image?query=A%20person%20in%20business%20attire%20walking%20with%20a%20briefcase%2C%20minimalist%20style%2C%20light%20blue%20background%20with%20abstract%20geometric%20shapes%2C%20professional%20looking%2C%20clean%20design&width=150&height=150&seq=1&orientation=squarish"
                alt="Hỗ trợ"
                class="w-24 h-24 object-cover rounded"
              />
            </div>
            <div>
              <h2 class="text-lg font-semibold mb-2">
                Bạn chưa tìm thấy giải pháp?
              </h2>
              <p class="text-gray-600">
                Hãy tạo bài viết và cộng đồng sẽ giúp đỡ bạn
              </p>
            </div>
          </div>
          <!-- Forum Categories -->
          <div class="bg-white rounded-md shadow-sm mb-6 overflow-hidden">
            <!-- Forum Header -->
            <div
              class="grid grid-cols-12 bg-gray-50 text-sm text-gray-500 py-3 px-4 border-b"
            >
              <div class="col-span-6">Chủ đề</div>
              <div class="col-span-2 text-center">Bài viết</div>
              <div class="col-span-2 text-center">Hồi mới</div>
              <div class="col-span-2 text-right"></div>
            </div>
            <!-- Forum Category 1 -->
            <div class="border-b">
              <div class="grid grid-cols-12 py-4 px-4 items-center">
                <div class="col-span-6 flex">
                  <div
                    class="w-10 h-10 flex items-center justify-center bg-gray-100 rounded-full mr-3"
                  >
                    <i class="ri-computer-line ri-lg text-primary"></i>
                  </div>
                  <div>
                    <a
                      href="https://readdy.ai/home/<USER>/c64c6f22-be71-4c45-84df-97af283fb187"
                      data-readdy="true"
                      class="font-medium text-primary hover:underline"
                      >Diễn đàn công nghệ</a
                    >
                    <p class="text-sm text-gray-500 mt-1">
                      Chia nhớt công nghệ mới
                    </p>
                  </div>
                </div>
                <div class="col-span-2 text-center">
                  <span class="text-gray-700">0</span>
                </div>
                <div class="col-span-2 text-center">
                  <span class="text-gray-700">2</span>
                </div>
                <div class="col-span-2 text-right text-sm text-gray-500">
                  <div>7 giờ trước</div>
                  <div class="text-primary">Tien Hoang</div>
                </div>
              </div>
            </div>
            <!-- Forum Category 2 -->
            <div class="border-b">
              <div class="grid grid-cols-12 py-4 px-4 items-center">
                <div class="col-span-6 flex">
                  <div
                    class="w-10 h-10 flex items-center justify-center bg-gray-100 rounded-full mr-3"
                  >
                    <i class="ri-book-open-line ri-lg text-primary"></i>
                  </div>
                  <div>
                    <a href="#" class="font-medium text-primary hover:underline"
                      >Diễn đàn học tập</a
                    >
                    <p class="text-sm text-gray-500 mt-1">Học ngày càng giỏi</p>
                  </div>
                </div>
                <div class="col-span-2 text-center">
                  <span class="text-gray-700">1</span>
                </div>
                <div class="col-span-2 text-center">
                  <span class="text-gray-700">2</span>
                </div>
                <div class="col-span-2 text-right text-sm text-gray-500">
                  <div>2 ngày trước</div>
                  <div class="text-primary">admin</div>
                </div>
              </div>
            </div>
            <!-- Forum Category 3 -->
            <div class="border-b">
              <div class="grid grid-cols-12 py-4 px-4 items-center">
                <div class="col-span-6 flex">
                  <div
                    class="w-10 h-10 flex items-center justify-center bg-gray-100 rounded-full mr-3"
                  >
                    <i class="ri-discuss-line ri-lg text-primary"></i>
                  </div>
                  <div>
                    <a href="#" class="font-medium text-primary hover:underline"
                      >Diễn đàn đối mới</a
                    >
                    <p class="text-sm text-gray-500 mt-1">
                      Diễn đàn đổi mới đất nước
                    </p>
                  </div>
                </div>
                <div class="col-span-2 text-center">
                  <span class="text-gray-700">0</span>
                </div>
                <div class="col-span-2 text-center">
                  <span class="text-gray-700">0</span>
                </div>
                <div class="col-span-2 text-right text-sm text-gray-500">
                  <div>2 ngày trước</div>
                  <div class="text-primary">admin</div>
                </div>
              </div>
            </div>
            <!-- Forum Category 4 -->
            <div>
              <div class="grid grid-cols-12 py-4 px-4 items-center">
                <div class="col-span-6 flex">
                  <div
                    class="w-10 h-10 flex items-center justify-center bg-gray-100 rounded-full mr-3"
                  >
                    <i class="ri-gamepad-line ri-lg text-primary"></i>
                  </div>
                  <div>
                    <a href="#" class="font-medium text-primary hover:underline"
                      >Diễn đàn trò chơi</a
                    >
                    <p class="text-sm text-gray-500 mt-1">
                      Diễn đàn trò chơi giải trí
                    </p>
                  </div>
                </div>
                <div class="col-span-2 text-center">
                  <span class="text-gray-700">4</span>
                </div>
                <div class="col-span-2 text-center">
                  <span class="text-gray-700">34</span>
                </div>
                <div class="col-span-2 text-right text-sm text-gray-500">
                  <div>2 giờ trước</div>
                  <div class="text-primary">Hayden Nguyen</div>
                </div>
              </div>
            </div>
          </div>
          <!-- Pagination -->
          <div class="flex items-center justify-between">
            <div class="text-sm text-gray-500">
              Hiển thị 4 trong tổng số 4 kết quả
            </div>
            <div class="flex items-center space-x-1">
              <a
                href="#"
                class="w-8 h-8 flex items-center justify-center rounded border text-gray-500 hover:bg-gray-50"
              >
                <i class="ri-arrow-left-s-line"></i>
              </a>
              <a
                href="#"
                class="w-8 h-8 flex items-center justify-center rounded border text-gray-500 hover:bg-gray-50"
              >
                <i class="ri-arrow-left-line"></i>
              </a>
              <span class="text-sm text-gray-500 px-2">Trang 1 / 1</span>
              <a
                href="#"
                class="w-8 h-8 flex items-center justify-center rounded border text-gray-500 hover:bg-gray-50"
              >
                <i class="ri-arrow-right-line"></i>
              </a>
              <a
                href="#"
                class="w-8 h-8 flex items-center justify-center rounded border text-gray-500 hover:bg-gray-50"
              >
                <i class="ri-arrow-right-s-line"></i>
              </a>
            </div>
          </div>
        </div>
        <!-- Right Column - Sidebar -->
        <div class="w-full md:w-1/3">
          <!-- Top Posts -->
          <div class="bg-white rounded-md shadow-sm mb-6 overflow-hidden">
            <div class="px-4 py-3 border-b">
              <h3 class="font-medium">Top 10 bài viết nổi bật</h3>
            </div>
            <div class="divide-y">
              <div class="px-4 py-3 flex items-center justify-between">
                <div class="flex items-center">
                  <div
                    class="w-8 h-8 flex items-center justify-center bg-purple-100 rounded-full mr-3 text-purple-600"
                  >
                    <i class="ri-building-4-line"></i>
                  </div>
                  <a href="#" class="text-sm hover:text-primary"
                    >Tạo Dựng Môi Trường Học Tập...</a
                  >
                </div>
                <span class="text-xs text-gray-500">9823</span>
              </div>
              <div class="px-4 py-3 flex items-center justify-between">
                <div class="flex items-center">
                  <div
                    class="w-8 h-8 flex items-center justify-center bg-blue-100 rounded-full mr-3 text-blue-600"
                  >
                    <i class="ri-code-box-line"></i>
                  </div>
                  <a href="#" class="text-sm hover:text-primary"
                    >Ứng Dụng Công Nghệ Trong...</a
                  >
                </div>
                <span class="text-xs text-gray-500">54</span>
              </div>
              <div class="px-4 py-3 flex items-center justify-between">
                <div class="flex items-center">
                  <div
                    class="w-8 h-8 flex items-center justify-center bg-green-100 rounded-full mr-3 text-green-600"
                  >
                    <i class="ri-plant-line"></i>
                  </div>
                  <a href="#" class="text-sm hover:text-primary"
                    >Xây Dựng Hệ Sinh Thái...</a
                  >
                </div>
                <span class="text-xs text-gray-500">3200</span>
              </div>
              <div class="px-4 py-3 flex items-center justify-between">
                <div class="flex items-center">
                  <div
                    class="w-8 h-8 flex items-center justify-center bg-yellow-100 rounded-full mr-3 text-yellow-600"
                  >
                    <i class="ri-lightbulb-line"></i>
                  </div>
                  <a href="#" class="text-sm hover:text-primary"
                    >Phát Triển Kỹ Năng Mềm...</a
                  >
                </div>
                <span class="text-xs text-gray-500">1642</span>
              </div>
              <div class="px-4 py-3 flex items-center justify-between">
                <div class="flex items-center">
                  <div
                    class="w-8 h-8 flex items-center justify-center bg-red-100 rounded-full mr-3 text-red-600"
                  >
                    <i class="ri-book-line"></i>
                  </div>
                  <a href="#" class="text-sm hover:text-primary"
                    >Lớm Thế Nào Để...</a
                  >
                </div>
                <span class="text-xs text-gray-500">1400</span>
              </div>
              <div class="px-4 py-3 flex items-center justify-between">
                <div class="flex items-center">
                  <div
                    class="w-8 h-8 flex items-center justify-center bg-indigo-100 rounded-full mr-3 text-indigo-600"
                  >
                    <i class="ri-graduation-cap-line"></i>
                  </div>
                  <a href="#" class="text-sm hover:text-primary"
                    >Cải Tiến Quy Trình Giáo...</a
                  >
                </div>
                <span class="text-xs text-gray-500">1199</span>
              </div>
              <div class="px-4 py-3 flex items-center justify-between">
                <div class="flex items-center">
                  <div
                    class="w-8 h-8 flex items-center justify-center bg-pink-100 rounded-full mr-3 text-pink-600"
                  >
                    <i class="ri-heart-line"></i>
                  </div>
                  <a href="#" class="text-sm hover:text-primary"
                    >Khám Phá Các Mô...</a
                  >
                </div>
                <span class="text-xs text-gray-500">1000</span>
              </div>
              <div class="px-4 py-3 flex items-center justify-between">
                <div class="flex items-center">
                  <div
                    class="w-8 h-8 flex items-center justify-center bg-orange-100 rounded-full mr-3 text-orange-600"
                  >
                    <i class="ri-flag-line"></i>
                  </div>
                  <a href="#" class="text-sm hover:text-primary"
                    >Chiến Lược Tài Chính và...</a
                  >
                </div>
                <span class="text-xs text-gray-500">867</span>
              </div>
              <div class="px-4 py-3 flex items-center justify-between">
                <div class="flex items-center">
                  <div
                    class="w-8 h-8 flex items-center justify-center bg-blue-100 rounded-full mr-3 text-blue-600"
                  >
                    <i class="ri-graduation-cap-line"></i>
                  </div>
                  <a href="#" class="text-sm hover:text-primary"
                    >Cải Tiến Quy Trình Giáo...</a
                  >
                </div>
                <span class="text-xs text-gray-500">400</span>
              </div>
              <div class="px-4 py-3 flex items-center justify-between">
                <div class="flex items-center">
                  <div
                    class="w-8 h-8 flex items-center justify-center bg-cyan-100 rounded-full mr-3 text-cyan-600"
                  >
                    <i class="ri-global-line"></i>
                  </div>
                  <a href="#" class="text-sm hover:text-primary"
                    >Hợp Tác Quốc Tế Trong...</a
                  >
                </div>
                <span class="text-xs text-gray-500">342</span>
              </div>
            </div>
          </div>
          <!-- Popular Tags -->
          <div class="bg-white rounded-md shadow-sm overflow-hidden">
            <div class="px-4 py-3 border-b">
              <h3 class="font-medium">Từ khóa tìm kiếm nhiều nhất</h3>
            </div>
            <div class="p-4">
              <div class="flex flex-wrap gap-2">
                <a
                  href="#"
                  class="px-3 py-1 bg-blue-50 text-blue-600 rounded-full text-sm hover:bg-blue-100"
                  >Học Tập</a
                >
                <a
                  href="#"
                  class="px-3 py-1 bg-blue-50 text-blue-600 rounded-full text-sm hover:bg-blue-100"
                  >Công Dạy</a
                >
                <a
                  href="#"
                  class="px-3 py-1 bg-blue-50 text-blue-600 rounded-full text-sm hover:bg-blue-100"
                  >Phương Pháp</a
                >
                <a
                  href="#"
                  class="px-3 py-1 bg-blue-50 text-blue-600 rounded-full text-sm hover:bg-blue-100"
                  >Chủ Động</a
                >
                <a
                  href="#"
                  class="px-3 py-1 bg-blue-50 text-blue-600 rounded-full text-sm hover:bg-blue-100"
                  >Công Nghệ</a
                >
                <a
                  href="#"
                  class="px-3 py-1 bg-blue-50 text-blue-600 rounded-full text-sm hover:bg-blue-100"
                  >Giáo Dục</a
                >
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Footer -->
    <footer class="bg-white border-t mt-8">
      <div class="container mx-auto px-4 py-8">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
          <!-- Contact Info -->
          <div>
            <h3 class="text-lg font-medium mb-4">Liên hệ</h3>
            <div class="space-y-2 text-gray-600 text-sm">
              <p>
                Trụ sở chính: Số 98 Phố Dương Quảng Hàm, Phường Quan Hoa, Quận
                Cầu Giấy, TP. Hà Nội
              </p>
              <p>(+84) 24.3833.0708</p>
              <p>(+84) 24.3833.5426</p>
              <p><EMAIL></p>
            </div>
          </div>
          <!-- Social Links -->
          <div>
            <h3 class="text-lg font-medium mb-4">Mạng xã hội</h3>
            <div class="flex space-x-4">
              <a
                href="#"
                class="text-gray-600 hover:text-primary flex items-center"
              >
                <div class="w-5 h-5 flex items-center justify-center mr-2">
                  <i class="ri-facebook-fill"></i>
                </div>
                Facebook
              </a>
              <a
                href="#"
                class="text-gray-600 hover:text-primary flex items-center"
              >
                <div class="w-5 h-5 flex items-center justify-center mr-2">
                  <i class="ri-youtube-fill"></i>
                </div>
                YouTube
              </a>
              <a
                href="#"
                class="text-gray-600 hover:text-primary flex items-center"
              >
                <div class="w-5 h-5 flex items-center justify-center mr-2">
                  <i class="ri-tiktok-fill"></i>
                </div>
                TikTok
              </a>
            </div>
          </div>
        </div>
        <!-- Copyright -->
        <div class="border-t mt-8 pt-4 text-sm text-gray-500">
          <p>Copyright ©2025 Đại học Thủ đô Hà Nội</p>
        </div>
      </div>
    </footer>
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        const dropdownButton = document.getElementById("dropdownButton");
        const dropdownMenu = document.getElementById("dropdownMenu");

        if (dropdownButton && dropdownMenu) {
          dropdownButton.addEventListener("click", function (e) {
            e.stopPropagation();
            dropdownMenu.classList.toggle("hidden");
          });

          document.addEventListener("click", function (e) {
            if (!dropdownButton.contains(e.target)) {
              dropdownMenu.classList.add("hidden");
            }
          });
        }
      });
    </script>
  </body>
</html>
