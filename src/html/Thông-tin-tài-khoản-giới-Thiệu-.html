<!DOCTYPE html>
<html lang="vi">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title><PERSON>i<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON></title>
<script src="https://cdn.tailwindcss.com/3.4.16"></script>
<script>tailwind.config={theme:{extend:{colors:{primary:'#2E228B',secondary:'#818cf8'},borderRadius:{'none':'0px','sm':'4px',DEFAULT:'8px','md':'12px','lg':'16px','xl':'20px','2xl':'24px','3xl':'32px','full':'9999px','button':'8px'}}}}</script>
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css">
<style>
:where([class^="ri-"])::before { content: "\f3c2"; }
body {
font-family: 'Roboto', sans-serif;
}
.breadcrumb-item:not(:last-child)::after {
content: "/";
margin: 0 0.5rem;
color: #6b7280;
}
.tab-active {
border-bottom: 2px solid #2E228B;
color: #2E228B;
font-weight: 500;
}
.tab-inactive {
color: #6b7280;
}
.tab-inactive:hover {
color: #2E228B;
}
</style>
</head>
<body class="bg-gray-50">
<!-- Header -->
<header class="bg-primary text-white">
<div class="container mx-auto px-4 py-3 flex items-center justify-between">
<div class="flex items-center space-x-10">
<a href="#" class="text-2xl font-['Pacifico'] text-white">logo</a>
<nav class="hidden md:flex space-x-6">
<a href="#" class="hover:text-white/80">Trang chủ</a>
<a href="#" class="hover:text-white/80">Bài viết</a>
<a href="#" class="hover:text-white/80">Tác giả</a>
<a href="#" class="hover:text-white/80">Chủ đề</a>
</nav>
</div>
<div class="flex items-center space-x-4">
<div class="relative">
<div class="w-10 h-10 flex items-center justify-center text-white cursor-pointer">
<i class="ri-notification-3-line ri-lg"></i>
</div>
<span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">1</span>
</div>
<div class="w-10 h-10 rounded-full overflow-hidden">
<img src="https://readdy.ai/api/search-image?query=professional%252520asian%252520man%252520in%252520business%252520suit%25252C%252520portrait%252520photo%25252C%252520professional%252520headshot%25252C%252520high%252520quality%25252C%252520realistic&width=100&height=100&seq=1&orientation=squarish" alt="User Avatar" class="w-full h-full object-cover">
</div>
</div>
</div>
</header>
<!-- Breadcrumb -->
<div class="container mx-auto px-4 py-3 flex items-center justify-between">
<div class="flex items-center text-sm text-gray-500">
<a href="#" class="breadcrumb-item hover:text-primary">Trang chủ</a>
<a href="#" class="breadcrumb-item hover:text-primary">Mầm non</a>
<a href="#" class="breadcrumb-item hover:text-primary">Trang cá nhân</a>
<span class="breadcrumb-item text-gray-700">Giới thiệu</span>
</div>
<div class="text-sm text-gray-500 flex items-center">
<i class="ri-time-line mr-1"></i>
<span>Cập nhật lần cuối: 28/04/2025</span>
</div>
</div>
<!-- Profile Section -->
<div class="container mx-auto px-4 py-6">
<div class="flex flex-col md:flex-row items-center md:items-start gap-6 pb-6 border-b">
<div class="relative group">
<div class="w-32 h-32 rounded-full overflow-hidden border-4 border-white shadow-lg">
<img id="avatar-preview" src="https://readdy.ai/api/search-image?query=professional%252520asian%252520man%252520in%252520business%252520suit%25252C%252520portrait%252520photo%25252C%252520professional%252520headshot%25252C%252520high%252520quality%25252C%252520realistic&width=200&height=200&seq=2&orientation=squarish" alt="Nguyễn Văn Minh" class="w-full h-full object-cover">
</div>
<label for="avatar-upload" class="absolute inset-0 flex items-center justify-center bg-black/50 text-white opacity-0 group-hover:opacity-100 cursor-pointer rounded-full transition-opacity">
<i class="ri-camera-line text-2xl"></i>
<input type="file" id="avatar-upload" class="hidden" accept="image/*">
</label>
</div>
<div class="flex-1 text-center md:text-left">
<h1 class="text-2xl font-bold text-gray-800">Nguyễn Văn Minh</h1>
<p class="text-primary font-medium">Chuyên gia Trí tuệ nhân tạo & Khoa học dữ liệu</p>
<p class="mt-2 text-gray-600">
Tiến sĩ Khoa học máy tính với hơn 15 năm kinh nghiệm nghiên cứu và phát triển trong lĩnh vực AI, Machine Learning và xử lý ngôn ngữ tự nhiên. Hiện đang là Giáo sư tại Đại học Bách Khoa Hà Nội.
</p>
</div>
</div>
<!-- Tabs -->
<div class="border-b mt-4">
<nav class="flex space-x-8">
<a href="https://readdy.ai/home/<USER>/d0219845-abfa-486f-b32c-f236acce0f33" data-readdy="true" class="tab-inactive px-1 py-4">Tổng quan</a>
<a href="https://readdy.ai/home/<USER>/d0219845-abfa-486f-b32c-f236acce0f33" data-readdy="true" class="tab-inactive px-1 py-4">Bài viết</a>
<a href="#" class="tab-active px-1 py-4">Giới thiệu</a>
</nav>
</div>
<!-- Main Content -->
<div class="mt-6">
<!-- Bio and Contact Content -->
<div id="bio-content" class="bg-white rounded shadow p-6 space-y-8">
<div class="flex justify-between items-center mb-6">
<h2 class="text-xl font-bold text-gray-800">Thông tin cá nhân</h2>
<button id="edit-bio-btn" class="flex items-center gap-2 text-primary hover:text-primary/80 transition-colors !rounded-button whitespace-nowrap">
<i class="ri-edit-line"></i>
<span>Chỉnh sửa</span>
</button>
</div>
<!-- View Mode -->
<div id="bio-view-mode">
<div class="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-4">
<div>
<h3 class="text-sm font-medium text-gray-500 mb-1">Họ</h3>
<p class="text-gray-800">Nguyễn Văn</p>
</div>
<div>
<h3 class="text-sm font-medium text-gray-500 mb-1">Tên</h3>
<p class="text-gray-800">Minh</p>
</div>
<div>
<h3 class="text-sm font-medium text-gray-500 mb-1">Ngày sinh</h3>
<p class="text-gray-800">15/08/1980</p>
</div>
<div>
<h3 class="text-sm font-medium text-gray-500 mb-1">Giới tính</h3>
<p class="text-gray-800">Nam</p>
</div>
<div>
<h3 class="text-sm font-medium text-gray-500 mb-1">Quê quán</h3>
<p class="text-gray-800">Hà Nội, Việt Nam</p>
</div>
<div>
<h3 class="text-sm font-medium text-gray-500 mb-1">Tổ chức</h3>
<p class="text-gray-800">Đại học Bách Khoa Hà Nội</p>
</div>
<div>
<h3 class="text-sm font-medium text-gray-500 mb-1">Chức vụ hiện tại</h3>
<p class="text-gray-800">Giáo sư, Khoa Công nghệ thông tin</p>
</div>
</div>
<div class="mt-6">
<h3 class="text-sm font-medium text-gray-500 mb-1">Giới thiệu bản thân</h3>
<p class="text-gray-800">
Tôi là Tiến sĩ Khoa học máy tính với hơn 15 năm kinh nghiệm nghiên cứu và giảng dạy trong lĩnh vực Trí tuệ nhân tạo và Khoa học dữ liệu. Tốt nghiệp Tiến sĩ tại Đại học Stanford (Hoa Kỳ) năm 2010, tôi đã trở về Việt Nam và hiện đang là Giáo sư tại Đại học Bách Khoa Hà Nội. Nghiên cứu của tôi tập trung vào các mô hình học sâu, xử lý ngôn ngữ tự nhiên và ứng dụng AI trong các lĩnh vực thực tế tại Việt Nam.
</p>
</div>
</div>
<!-- Edit Mode -->
<div id="bio-edit-mode" class="hidden">
<form id="bio-form" class="space-y-6">
<div class="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-4">
<div>
<label for="lastname" class="block text-sm font-medium text-gray-700 mb-1">Họ</label>
<input type="text" id="lastname" name="lastname" value="Nguyễn Văn" class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary">
</div>
<div>
<label for="firstname" class="block text-sm font-medium text-gray-700 mb-1">Tên</label>
<input type="text" id="firstname" name="firstname" value="Minh" class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary">
</div>
<div>
<label for="phone" class="block text-sm font-medium text-gray-700 mb-1">Số điện thoại</label>
<input type="tel" id="phone" name="phone" value="+84 912 345 678" class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary">
</div>
<div class="md:col-span-2">
<label for="address" class="block text-sm font-medium text-gray-700 mb-1">Địa chỉ</label>
<input type="text" id="address" name="address" value="Phòng 504, Tòa nhà B1, Đại học Bách Khoa Hà Nội, Số 1 Đại Cồ Việt, Hai Bà Trưng, Hà Nội" class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary">
</div>
<div>
<label for="birthdate" class="block text-sm font-medium text-gray-700 mb-1">Ngày sinh</label>
<input type="date" id="birthdate" name="birthdate" value="1980-08-15" class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary">
</div>
<div>
<label for="hometown" class="block text-sm font-medium text-gray-700 mb-1">Quê quán</label>
<input type="text" id="hometown" name="hometown" value="Hà Nội, Việt Nam" class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary">
</div>
<div>
<label for="gender" class="block text-sm font-medium text-gray-700 mb-1">Giới tính</label>
<select id="gender" name="gender" class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary">
<option value="male" selected>Nam</option>
<option value="female">Nữ</option>
<option value="other">Khác</option>
</select>
</div>
<div>
<label for="organization" class="block text-sm font-medium text-gray-700 mb-1">Tổ chức</label>
<input type="text" id="organization" name="organization" value="Đại học Bách Khoa Hà Nội" class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary">
</div>
<div>
<label for="position" class="block text-sm font-medium text-gray-700 mb-1">Chức vụ hiện tại</label>
<input type="text" id="position" name="position" value="Giáo sư, Khoa Công nghệ thông tin" class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary">
</div>
</div>
<div>
<label for="bio" class="block text-sm font-medium text-gray-700 mb-1">Giới thiệu bản thân</label>
<textarea id="bio" name="bio" rows="4" class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary">Tôi là Tiến sĩ Khoa học máy tính với hơn 15 năm kinh nghiệm nghiên cứu và giảng dạy trong lĩnh vực Trí tuệ nhân tạo và Khoa học dữ liệu. Tốt nghiệp Tiến sĩ tại Đại học Stanford (Hoa Kỳ) năm 2010, tôi đã trở về Việt Nam và hiện đang là Giáo sư tại Đại học Bách Khoa Hà Nội. Nghiên cứu của tôi tập trung vào các mô hình học sâu, xử lý ngôn ngữ tự nhiên và ứng dụng AI trong các lĩnh vực thực tế tại Việt Nam.</textarea>
</div>
<div>
<label for="hobbies" class="block text-sm font-medium text-gray-700 mb-1">Sở thích</label>
<textarea id="hobbies" name="hobbies" rows="2" class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary">Đọc sách khoa học, chơi cờ vua, đi bộ đường dài, chụp ảnh phong cảnh và khám phá ẩm thực địa phương.</textarea>
</div>
<div>
<label for="career-goals" class="block text-sm font-medium text-gray-700 mb-1">Mục tiêu nghề nghiệp</label>
<textarea id="career-goals" name="career-goals" rows="3" class="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary">Phát triển các giải pháp AI tiên tiến phù hợp với bối cảnh Việt Nam, đào tạo thế hệ kế tiếp các nhà nghiên cứu AI và xây dựng cộng đồng AI mạnh mẽ tại Việt Nam. Mục tiêu dài hạn là thiết lập một trung tâm nghiên cứu AI xuất sắc tại Việt Nam, kết nối với mạng lưới nghiên cứu toàn cầu.</textarea>
</div>
<div class="flex justify-end gap-3">
<button type="button" id="cancel-bio-btn" class="px-4 py-2 border border-gray-300 rounded text-gray-700 hover:bg-gray-50 transition-colors !rounded-button whitespace-nowrap">Hủy</button>
<button type="submit" class="px-4 py-2 bg-primary text-white rounded hover:bg-primary/90 transition-colors !rounded-button whitespace-nowrap">Lưu thay đổi</button>
</div>
</form>
</div>
<!-- Contact Information Section -->
<div class="pt-8 border-t">
<div class="mb-6">
<h2 class="text-xl font-bold text-gray-800">Thông tin liên hệ</h2>
</div>
<div id="contact-view-mode">
<div class="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-6">
<div class="flex items-start gap-3">
<div class="w-10 h-10 flex items-center justify-center bg-primary/10 text-primary rounded-full">
<i class="ri-mail-line"></i>
</div>
<div>
<h3 class="text-sm font-medium text-gray-500 mb-1">Email</h3>
<p class="text-gray-800"><EMAIL></p>
</div>
</div>
<div class="flex items-start gap-3">
<div class="w-10 h-10 flex items-center justify-center bg-primary/10 text-primary rounded-full">
<i class="ri-phone-line"></i>
</div>
<div>
<h3 class="text-sm font-medium text-gray-500 mb-1">Số điện thoại</h3>
<p class="text-gray-800">+84 912 345 678</p>
</div>
</div>
<div class="flex items-start gap-3 md:col-span-2">
<div class="w-10 h-10 flex items-center justify-center bg-primary/10 text-primary rounded-full">
<i class="ri-map-pin-line"></i>
</div>
<div>
<h3 class="text-sm font-medium text-gray-500 mb-1">Địa chỉ</h3>
<p class="text-gray-800">Phòng 504, Tòa nhà B1, Đại học Bách Khoa Hà Nội, Số 1 Đại Cồ Việt, Hai Bà Trưng, Hà Nội</p>
</div>
</div>
</div>
</div>
</div>
</div>
<script>
document.addEventListener('DOMContentLoaded', function() {
// Avatar upload functionality
const avatarUpload = document.getElementById('avatar-upload');
const avatarPreview = document.getElementById('avatar-preview');
avatarUpload.addEventListener('change', function(e) {
const file = e.target.files[0];
if (file) {
const reader = new FileReader();
reader.onload = function(e) {
avatarPreview.src = e.target.result;
};
reader.readAsDataURL(file);
}
});
// Bio tab functionality
const editBioBtn = document.getElementById('edit-bio-btn');
const cancelBioBtn = document.getElementById('cancel-bio-btn');
const bioViewMode = document.getElementById('bio-view-mode');
const bioEditMode = document.getElementById('bio-edit-mode');
const bioForm = document.getElementById('bio-form');
editBioBtn.addEventListener('click', function() {
bioViewMode.classList.add('hidden');
bioEditMode.classList.remove('hidden');
});
cancelBioBtn.addEventListener('click', function() {
bioViewMode.classList.remove('hidden');
bioEditMode.classList.add('hidden');
});
bioForm.addEventListener('submit', function(e) {
e.preventDefault();
bioViewMode.classList.remove('hidden');
bioEditMode.classList.add('hidden');
// Here you would normally save the data
});
});

</script>
</body>
</html>
