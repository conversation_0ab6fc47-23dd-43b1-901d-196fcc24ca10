<!DOCTYPE html>
<html lang="vi">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>Tạo bài viết mới - <PERSON><PERSON><PERSON> đàn công nghệ</title>
<script src="https://cdn.tailwindcss.com/3.4.16"></script>
<script>tailwind.config={theme:{extend:{colors:{primary:'#2e2e8b',secondary:'#4b5563'},borderRadius:{'none':'0px','sm':'4px',DEFAULT:'8px','md':'12px','lg':'16px','xl':'20px','2xl':'24px','3xl':'32px','full':'9999px','button':'8px'}}}}</script>
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css">
<style>
:where([class^="ri-"])::before { content: "\f3c2"; }
body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
.editor-toolbar {
  border-bottom: 1px solid #e5e7eb;
  padding: 0.5rem;
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}
.editor-toolbar button {
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  color: #4b5563;
  background: transparent;
  border: none;
}
.editor-toolbar button:hover {
  background-color: #f3f4f6;
}
.editor-content {
  min-height: 300px;
  padding: 1rem;
  outline: none;
}
.tag-input {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  align-items: center;
}
.tag {
  display: flex;
  align-items: center;
  background-color: #e5e7eb;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.875rem;
}
.tag-close {
  margin-left: 0.25rem;
  width: 1rem;
  height: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
</style>
</head>
<body class="bg-gray-50">
<!-- Header Navigation -->
<header class="bg-primary text-white">
  <div class="container mx-auto px-4 py-3 flex items-center justify-between">
    <div class="flex items-center space-x-8">
      <a href="#" class="flex items-center text-xl font-['Pacifico']">
        <div class="w-8 h-8 flex items-center justify-center mr-2">
          <i class="ri-global-line ri-lg"></i>
        </div>
        <span>logo</span>
      </a>
      <nav class="hidden md:flex space-x-6">
        <a href="#" class="hover:text-gray-200 text-sm font-medium">Giới thiệu</a>
        <a href="#" class="hover:text-gray-200 text-sm font-medium">Tin tức</a>
        <a href="#" class="hover:text-gray-200 text-sm font-medium">Thông kê</a>
        <a href="https://readdy.ai/home/<USER>/c64c6f22-be71-4c45-84df-97af283fb187" data-readdy="true" class="hover:text-gray-200 text-sm font-medium">Diễn đàn</a>
        <a href="#" class="hover:text-gray-200 text-sm font-medium">Tài nguyên</a>
        <a href="#" class="hover:text-gray-200 text-sm font-medium">Xếp hạng</a>
        <a href="#" class="hover:text-gray-200 text-sm font-medium">Khảo sát</a>
      </nav>
    </div>
    <div>
      <a href="#" class="text-sm font-medium hover:text-gray-200">Đăng nhập</a>
    </div>
  </div>
</header>

<!-- Breadcrumb -->
<div class="border-b">
  <div class="container mx-auto px-4 py-2">
    <div class="flex items-center text-sm text-gray-500">
      <a href="https://readdy.ai/home/<USER>/c64c6f22-be71-4c45-84df-97af283fb187" data-readdy="true" class="hover:text-primary flex items-center">
        <div class="w-4 h-4 flex items-center justify-center mr-1">
          <i class="ri-arrow-left-line"></i>
        </div>
        Quay lại
      </a>
      <span class="mx-2">/</span>
      <a href="#" class="hover:text-primary">Trang chủ</a>
      <span class="mx-2">/</span>
      <a href="https://readdy.ai/home/<USER>/c64c6f22-be71-4c45-84df-97af283fb187" data-readdy="true" class="hover:text-primary">Diễn đàn</a>
      <span class="mx-2">/</span>
      <span class="text-gray-700">Tạo bài viết mới</span>
    </div>
  </div>
</div>

<!-- Main Content -->
<div class="container mx-auto px-4 py-6">
  <div class="max-w-4xl mx-auto">
    <h1 class="text-2xl font-bold mb-6">Tạo bài viết mới</h1>
    
    <!-- Guidelines Panel -->
    <div class="bg-blue-50 border border-blue-200 rounded-md p-4 mb-6">
      <div class="flex items-start">
        <div class="w-5 h-5 flex items-center justify-center mt-0.5 text-blue-500 mr-2">
          <i class="ri-information-line"></i>
        </div>
        <div>
          <h3 class="font-medium text-blue-800 mb-1">Hướng dẫn đăng bài</h3>
          <ul class="text-sm text-blue-700 space-y-1 list-disc list-inside ml-1">
            <li>Tiêu đề bài viết nên ngắn gọn, rõ ràng và mô tả chính xác nội dung.</li>
            <li>Chọn chủ đề phù hợp để bài viết của bạn dễ dàng được tìm thấy.</li>
            <li>Nội dung bài viết cần đầy đủ, rõ ràng và tôn trọng quy tắc cộng đồng.</li>
            <li>Sử dụng thẻ (tags) để phân loại nội dung và tăng khả năng tìm kiếm.</li>
            <li>Kiểm tra chính tả và định dạng trước khi đăng bài.</li>
          </ul>
          <a href="#" class="text-sm text-primary font-medium mt-2 inline-block hover:underline">Xem đầy đủ quy định diễn đàn</a>
        </div>
      </div>
    </div>
    
    <!-- Post Form -->
    <form id="post-form" class="space-y-6">
      <!-- Title -->
      <div>
        <label for="post-title" class="block text-sm font-medium text-gray-700 mb-1">Tiêu đề bài viết <span class="text-red-500">*</span></label>
        <input 
          type="text" 
          id="post-title" 
          class="w-full px-4 py-2 border border-gray-300 rounded focus:ring-2 focus:ring-primary focus:border-transparent text-gray-900"
          placeholder="Nhập tiêu đề bài viết (tối đa 100 ký tự)"
          maxlength="100"
          required
        >
      </div>
      
      <!-- Category -->
      <div>
        <label for="post-category" class="block text-sm font-medium text-gray-700 mb-1">Chủ đề <span class="text-red-500">*</span></label>
        <div class="relative">
          <select 
            id="post-category" 
            class="w-full appearance-none bg-white border border-gray-300 rounded px-4 py-2 pr-8 focus:ring-2 focus:ring-primary focus:border-transparent text-gray-900"
            required
          >
            <option value="" disabled selected>Chọn chủ đề</option>
            <option value="ai">Trí tuệ nhân tạo</option>
            <option value="web">Phát triển web</option>
            <option value="mobile">Ứng dụng di động</option>
            <option value="cloud">Điện toán đám mây</option>
            <option value="security">Bảo mật</option>
            <option value="devops">DevOps</option>
            <option value="blockchain">Blockchain</option>
            <option value="iot">Internet of Things (IoT)</option>
            <option value="data">Khoa học dữ liệu</option>
            <option value="other">Khác</option>
          </select>
          <div class="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 flex items-center justify-center pointer-events-none">
            <i class="ri-arrow-down-s-line text-gray-500"></i>
          </div>
        </div>
      </div>
      
      <!-- Editor -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-1">Nội dung bài viết <span class="text-red-500">*</span></label>
        <div class="border border-gray-300 rounded overflow-hidden bg-white">
          <div class="editor-toolbar">
            <button type="button" data-command="bold" title="In đậm">
              <i class="ri-bold"></i>
            </button>
            <button type="button" data-command="italic" title="In nghiêng">
              <i class="ri-italic"></i>
            </button>
            <button type="button" data-command="underline" title="Gạch chân">
              <i class="ri-underline"></i>
            </button>
            <button type="button" data-command="strikeThrough" title="Gạch ngang">
              <i class="ri-strikethrough"></i>
            </button>
            <button type="button" data-command="insertOrderedList" title="Danh sách có thứ tự">
              <i class="ri-list-ordered"></i>
            </button>
            <button type="button" data-command="insertUnorderedList" title="Danh sách không thứ tự">
              <i class="ri-list-unordered"></i>
            </button>
            <button type="button" data-command="createLink" title="Chèn liên kết">
              <i class="ri-link"></i>
            </button>
            <button type="button" data-command="insertImage" title="Chèn hình ảnh">
              <i class="ri-image-line"></i>
            </button>
            <button type="button" data-command="formatBlock" data-value="h2" title="Tiêu đề lớn">
              <i class="ri-heading"></i>
            </button>
            <button type="button" data-command="formatBlock" data-value="h3" title="Tiêu đề nhỏ">
              <i class="ri-text"></i>
            </button>
            <button type="button" data-command="insertHTML" data-value="<code></code>" title="Chèn mã">
              <i class="ri-code-line"></i>
            </button>
            <button type="button" data-command="insertHTML" data-value="<blockquote></blockquote>" title="Trích dẫn">
              <i class="ri-double-quotes-l"></i>
            </button>
          </div>
          <div id="editor" class="editor-content" contenteditable="true"></div>
          <input type="hidden" id="editor-content" name="content" required>
        </div>
      </div>
      
      <!-- Attachments -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-1">Đính kèm tệp/hình ảnh</label>
        <div class="border border-dashed border-gray-300 rounded-md p-6 flex flex-col items-center justify-center bg-white">
          <div class="w-12 h-12 flex items-center justify-center mb-3 text-gray-400">
            <i class="ri-upload-cloud-line ri-2x"></i>
          </div>
          <p class="text-sm text-gray-600 mb-2">Kéo và thả tệp vào đây hoặc</p>
          <label for="file-upload" class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-button cursor-pointer text-sm whitespace-nowrap">
            Chọn tệp
          </label>
          <input id="file-upload" type="file" multiple class="hidden" accept="image/*,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.zip,.rar">
          <p class="text-xs text-gray-500 mt-2">Định dạng hỗ trợ: JPG, PNG, GIF, PDF, DOC, XLS, PPT, ZIP (tối đa 10MB)</p>
        </div>
        
        <!-- Preview Attachments -->
        <div id="attachment-preview" class="mt-3 grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3 hidden">
          <!-- Attachment items will be added here -->
        </div>
      </div>
      
      <!-- Tags -->
      <div>
        <label for="post-tags" class="block text-sm font-medium text-gray-700 mb-1">Thẻ (tags)</label>
        <div class="tag-input border border-gray-300 rounded bg-white px-3 py-2 focus-within:ring-2 focus-within:ring-primary focus-within:border-transparent">
          <div id="tags-container" class="flex flex-wrap gap-2 mb-2"></div>
          <input 
            type="text" 
            id="tag-input" 
            class="outline-none text-sm border-none p-0 w-full"
            placeholder="Nhập thẻ và nhấn Enter (tối đa 5 thẻ)"
          >
        </div>
        <div class="mt-2">
          <p class="text-xs text-gray-500 mb-1">Thẻ phổ biến:</p>
          <div class="flex flex-wrap gap-2">
            <button type="button" class="tag-suggestion text-xs bg-gray-100 hover:bg-gray-200 text-gray-700 px-2 py-1 rounded-full">javascript</button>
            <button type="button" class="tag-suggestion text-xs bg-gray-100 hover:bg-gray-200 text-gray-700 px-2 py-1 rounded-full">python</button>
            <button type="button" class="tag-suggestion text-xs bg-gray-100 hover:bg-gray-200 text-gray-700 px-2 py-1 rounded-full">react</button>
            <button type="button" class="tag-suggestion text-xs bg-gray-100 hover:bg-gray-200 text-gray-700 px-2 py-1 rounded-full">machine-learning</button>
            <button type="button" class="tag-suggestion text-xs bg-gray-100 hover:bg-gray-200 text-gray-700 px-2 py-1 rounded-full">docker</button>
            <button type="button" class="tag-suggestion text-xs bg-gray-100 hover:bg-gray-200 text-gray-700 px-2 py-1 rounded-full">kubernetes</button>
            <button type="button" class="tag-suggestion text-xs bg-gray-100 hover:bg-gray-200 text-gray-700 px-2 py-1 rounded-full">aws</button>
          </div>
        </div>
      </div>
      
      <!-- Auto-save Status -->
      <div class="flex items-center text-sm text-gray-500">
        <div class="w-4 h-4 flex items-center justify-center mr-1">
          <i class="ri-time-line"></i>
        </div>
        <span id="autosave-status">Tự động lưu: Chưa có thay đổi</span>
      </div>
      
      <!-- Action Buttons -->
      <div class="flex flex-wrap gap-3 pt-4 border-t">
        <button type="submit" id="publish-button" class="bg-primary hover:bg-primary/90 text-white px-5 py-2 rounded-button flex items-center whitespace-nowrap">
          <div class="w-5 h-5 flex items-center justify-center mr-2">
            <i class="ri-send-plane-line"></i>
          </div>
          Đăng bài
        </button>
        <button type="button" id="save-draft-button" class="bg-white hover:bg-gray-50 text-gray-700 border border-gray-300 px-5 py-2 rounded-button flex items-center whitespace-nowrap">
          <div class="w-5 h-5 flex items-center justify-center mr-2">
            <i class="ri-draft-line"></i>
          </div>
          Lưu nháp
        </button>
        <a href="https://readdy.ai/home/<USER>/c64c6f22-be71-4c45-84df-97af283fb187" data-readdy="true" class="text-gray-500 hover:text-gray-700 px-5 py-2 flex items-center whitespace-nowrap">
          <div class="w-5 h-5 flex items-center justify-center mr-2">
            <i class="ri-close-line"></i>
          </div>
          Hủy
        </a>
      </div>
    </form>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Editor functionality
  const editor = document.getElementById('editor');
  const editorContent = document.getElementById('editor-content');
  const editorButtons = document.querySelectorAll('.editor-toolbar button');
  
  editorButtons.forEach(button => {
    button.addEventListener('click', function() {
      const command = this.dataset.command;
      const value = this.dataset.value || null;
      
      if (command === 'createLink') {
        const url = prompt('Nhập URL liên kết:', 'https://');
        if (url) document.execCommand(command, false, url);
      } else if (command === 'insertImage') {
        const url = prompt('Nhập URL hình ảnh:', 'https://');
        if (url) document.execCommand(command, false, url);
      } else {
        document.execCommand(command, false, value);
      }
      
      editor.focus();
      updateEditorContent();
    });
  });
  
  editor.addEventListener('input', updateEditorContent);
  
  function updateEditorContent() {
    editorContent.value = editor.innerHTML;
    updateAutosaveStatus('Đã lưu tự động lúc ' + new Date().toLocaleTimeString());
  }
  
  // Tags functionality
  const tagInput = document.getElementById('tag-input');
  const tagsContainer = document.getElementById('tags-container');
  const tagSuggestions = document.querySelectorAll('.tag-suggestion');
  let tags = [];
  
  tagInput.addEventListener('keydown', function(e) {
    if (e.key === 'Enter' && this.value.trim() !== '') {
      e.preventDefault();
      addTag(this.value.trim());
      this.value = '';
    }
  });
  
  tagSuggestions.forEach(suggestion => {
    suggestion.addEventListener('click', function() {
      addTag(this.textContent);
    });
  });
  
  function addTag(tagText) {
    if (tags.includes(tagText) || tags.length >= 5) return;
    
    tags.push(tagText);
    
    const tagElement = document.createElement('div');
    tagElement.className = 'tag';
    tagElement.innerHTML = `
      <span>${tagText}</span>
      <span class="tag-close" data-tag="${tagText}">
        <i class="ri-close-line"></i>
      </span>
    `;
    
    tagsContainer.appendChild(tagElement);
    
    tagElement.querySelector('.tag-close').addEventListener('click', function() {
      const tagToRemove = this.dataset.tag;
      tags = tags.filter(tag => tag !== tagToRemove);
      this.parentElement.remove();
    });
    
    updateAutosaveStatus('Đã lưu tự động lúc ' + new Date().toLocaleTimeString());
  }
  
  // File upload and preview
  const fileUpload = document.getElementById('file-upload');
  const attachmentPreview = document.getElementById('attachment-preview');
  const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.ms-powerpoint', 'application/vnd.openxmlformats-officedocument.presentationml.presentation', 'application/zip', 'application/x-rar-compressed'];
  
  fileUpload.addEventListener('change', function() {
    if (this.files.length > 0) {
      attachmentPreview.classList.remove('hidden');
      
      for (let i = 0; i < this.files.length; i++) {
        const file = this.files[i];
        
        if (!allowedTypes.includes(file.type)) {
          alert(`Định dạng tệp "${file.name}" không được hỗ trợ.`);
          continue;
        }
        
        if (file.size > 10 * 1024 * 1024) {
          alert(`Tệp "${file.name}" vượt quá kích thước tối đa (10MB).`);
          continue;
        }
        
        const attachmentItem = document.createElement('div');
        attachmentItem.className = 'relative bg-gray-100 rounded p-2 flex items-center';
        
        if (file.type.startsWith('image/')) {
          const reader = new FileReader();
          reader.onload = function(e) {
            attachmentItem.innerHTML = `
              <img src="${e.target.result}" class="w-16 h-16 object-cover object-top rounded mr-2">
              <div class="flex-grow overflow-hidden">
                <p class="text-sm font-medium text-gray-700 truncate">${file.name}</p>
                <p class="text-xs text-gray-500">${formatFileSize(file.size)}</p>
              </div>
              <button type="button" class="remove-attachment absolute top-1 right-1 w-5 h-5 bg-white rounded-full flex items-center justify-center text-gray-500 hover:text-red-500">
                <i class="ri-close-line"></i>
              </button>
            `;
          };
          reader.readAsDataURL(file);
        } else {
          let iconClass = 'ri-file-line';
          if (file.type.includes('pdf')) iconClass = 'ri-file-pdf-line';
          else if (file.type.includes('word')) iconClass = 'ri-file-word-line';
          else if (file.type.includes('excel')) iconClass = 'ri-file-excel-line';
          else if (file.type.includes('powerpoint')) iconClass = 'ri-file-ppt-line';
          else if (file.type.includes('zip') || file.type.includes('rar')) iconClass = 'ri-file-zip-line';
          
          attachmentItem.innerHTML = `
            <div class="w-16 h-16 flex items-center justify-center bg-gray-200 rounded mr-2">
              <i class="${iconClass} ri-2x text-gray-500"></i>
            </div>
            <div class="flex-grow overflow-hidden">
              <p class="text-sm font-medium text-gray-700 truncate">${file.name}</p>
              <p class="text-xs text-gray-500">${formatFileSize(file.size)}</p>
            </div>
            <button type="button" class="remove-attachment absolute top-1 right-1 w-5 h-5 bg-white rounded-full flex items-center justify-center text-gray-500 hover:text-red-500">
              <i class="ri-close-line"></i>
            </button>
          `;
        }
        
        attachmentPreview.appendChild(attachmentItem);
        
        attachmentItem.querySelector('.remove-attachment').addEventListener('click', function() {
          attachmentItem.remove();
          if (attachmentPreview.children.length === 0) {
            attachmentPreview.classList.add('hidden');
          }
        });
      }
      
      updateAutosaveStatus('Đã lưu tự động lúc ' + new Date().toLocaleTimeString());
    }
  });
  
  function formatFileSize(bytes) {
    if (bytes < 1024) return bytes + ' B';
    else if (bytes < 1048576) return (bytes / 1024).toFixed(1) + ' KB';
    else return (bytes / 1048576).toFixed(1) + ' MB';
  }
  
  // Form submission
  const postForm = document.getElementById('post-form');
  const publishButton = document.getElementById('publish-button');
  const saveDraftButton = document.getElementById('save-draft-button');
  
  postForm.addEventListener('submit', function(e) {
    e.preventDefault();
    
    // Validate form
    const title = document.getElementById('post-title').value;
    const category = document.getElementById('post-category').value;
    const content = editorContent.value;
    
    if (!title || !category || !content) {
      alert('Vui lòng điền đầy đủ thông tin bắt buộc (tiêu đề, chủ đề và nội dung).');
      return;
    }
    
    // Show loading state
    publishButton.disabled = true;
    publishButton.innerHTML = '<div class="w-5 h-5 flex items-center justify-center mr-2"><i class="ri-loader-4-line animate-spin"></i></div> Đang đăng...';
    
    // Simulate API call
    setTimeout(() => {
      alert('Bài viết đã được đăng thành công!');
      window.location.href = 'https://readdy.ai/home/<USER>/c64c6f22-be71-4c45-84df-97af283fb187';
    }, 1500);
  });
  
  saveDraftButton.addEventListener('click', function() {
    // Show loading state
    saveDraftButton.disabled = true;
    saveDraftButton.innerHTML = '<div class="w-5 h-5 flex items-center justify-center mr-2"><i class="ri-loader-4-line animate-spin"></i></div> Đang lưu...';
    
    // Simulate API call
    setTimeout(() => {
      saveDraftButton.disabled = false;
      saveDraftButton.innerHTML = '<div class="w-5 h-5 flex items-center justify-center mr-2"><i class="ri-draft-line"></i></div> Lưu nháp';
      updateAutosaveStatus('Đã lưu nháp lúc ' + new Date().toLocaleTimeString());
    }, 1000);
  });
  
  // Autosave status
  const autosaveStatus = document.getElementById('autosave-status');
  
  function updateAutosaveStatus(message) {
    autosaveStatus.textContent = message;
  }
  
  // Warn before leaving page with unsaved changes
  window.addEventListener('beforeunload', function(e) {
    if (document.getElementById('post-title').value || editorContent.value || tags.length > 0) {
      e.preventDefault();
      e.returnValue = '';
    }
  });
  
  // Initialize editor with placeholder
  editor.innerHTML = '<p>Viết nội dung bài viết của bạn tại đây...</p>';
  editor.addEventListener('focus', function() {
    if (editor.innerHTML === '<p>Viết nội dung bài viết của bạn tại đây...</p>') {
      editor.innerHTML = '';
    }
  });
  
  editor.addEventListener('blur', function() {
    if (editor.innerHTML === '' || editor.innerHTML === '<p></p>') {
      editor.innerHTML = '<p>Viết nội dung bài viết của bạn tại đây...</p>';
    }
  });
});
</script>
</body>
</html>
