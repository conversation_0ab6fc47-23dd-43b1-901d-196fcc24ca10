<!DOCTYPE html>
<html lang="vi">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title><PERSON><PERSON><PERSON> đàn công ngh<PERSON></title>
<script src="https://cdn.tailwindcss.com/3.4.16"></script>
<script>tailwind.config={theme:{extend:{colors:{primary:'#2e2e8b',secondary:'#4b5563'},borderRadius:{'none':'0px','sm':'4px',DEFAULT:'8px','md':'12px','lg':'16px','xl':'20px','2xl':'24px','3xl':'32px','full':'9999px','button':'8px'}}}}</script>
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">
<link href="https://cdn.jsdelivr.net/npm/remixicon@4.5.0/fonts/remixicon.css" rel="stylesheet">
<style>
:where([class^="ri-"])::before { content: "\f3c2"; }
body {
font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
-webkit-appearance: none;
margin: 0;
}
</style>
</head>
<body class="bg-gray-50">
<!-- Header Navigation -->
<header class="bg-primary text-white">
<div class="container mx-auto px-4 py-3 flex items-center justify-between">
<div class="flex items-center space-x-8">
<a href="#" class="flex items-center text-xl font-['Pacifico']">
<div class="w-8 h-8 flex items-center justify-center mr-2">
<i class="ri-global-line ri-lg"></i>
</div>
<span>logo</span>
</a>
<nav class="hidden md:flex space-x-6">
<a href="#" class="hover:text-gray-200 text-sm font-medium">Giới thiệu</a>
<a href="#" class="hover:text-gray-200 text-sm font-medium">Tin tức</a>
<a href="#" class="hover:text-gray-200 text-sm font-medium">Thông kê</a>
<a href="#" class="hover:text-gray-200 text-sm font-medium">Diễn đàn</a>
<a href="#" class="hover:text-gray-200 text-sm font-medium">Tài nguyên</a>
<a href="#" class="hover:text-gray-200 text-sm font-medium">Xếp hạng</a>
<a href="#" class="hover:text-gray-200 text-sm font-medium">Khảo sát</a>
</nav>
</div>
<div>
<a href="#" class="text-sm font-medium hover:text-gray-200">Đăng nhập</a>
</div>
</div>
</header>
<!-- Breadcrumb -->
<div class="border-b">
<div class="container mx-auto px-4 py-2">
<div class="flex items-center justify-between">
<div class="flex items-center text-sm text-gray-500">
<a href="https://readdy.ai/home/<USER>/824939a7-8e0e-41fd-8bc8-2daf351786d2" data-readdy="true" class="hover:text-primary flex items-center">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-arrow-left-line"></i>
</div>
Quay lại
</a>
<span class="mx-2">/</span>
<a href="#" class="hover:text-primary">Trang chủ</a>
<span class="mx-2">/</span>
<a href="https://readdy.ai/home/<USER>/824939a7-8e0e-41fd-8bc8-2daf351786d2" data-readdy="true" class="hover:text-primary">Diễn đàn</a>
<span class="mx-2">/</span>
<span class="text-gray-700">Diễn đàn công nghệ</span>
</div>
<div class="flex items-center text-sm text-gray-500">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-time-line"></i>
</div>
<span>Bài viết mới nhất 24/4/2025</span>
</div>
</div>
</div>
</div>
<!-- Forum Header Banner -->
<div class="container mx-auto px-4 py-6">
<div class="bg-white rounded-md shadow-sm p-6 mb-6">
<div class="flex flex-col md:flex-row items-center justify-between">
<div class="flex items-center mb-4 md:mb-0">
<div class="w-16 h-16 flex items-center justify-center bg-blue-100 rounded-full mr-4 text-primary">
<i class="ri-computer-line ri-2x"></i>
</div>
<div>
<h1 class="text-2xl font-bold">Diễn đàn công nghệ</h1>
<p class="text-gray-600 mt-1">Nơi chia sẻ và thảo luận về các công nghệ mới, xu hướng công nghệ và giải pháp kỹ thuật</p>
</div>
</div>
<div class="flex flex-col items-end">
<div class="flex space-x-4 mb-3">
<div class="text-center">
<div class="text-xl font-semibold text-gray-800">142</div>
<div class="text-sm text-gray-500">Bài viết</div>
</div>
<div class="text-center">
<div class="text-xl font-semibold text-gray-800">1,245</div>
<div class="text-sm text-gray-500">Thành viên</div>
</div>
</div>
<a href="https://readdy.ai/home/<USER>/772c5e6e-cad3-41ee-97b6-329cbee33ea0" data-readdy="true" class="bg-primary text-white px-4 py-2 rounded-button flex items-center whitespace-nowrap">
<div class="w-5 h-5 flex items-center justify-center mr-2">
<i class="ri-add-line"></i>
</div>
Tạo bài viết mới
</a>
</div>
</div>
</div>
<!-- Filter and Search -->
<div class="bg-white rounded-md shadow-sm p-4 mb-6">
<div class="flex flex-col md:flex-row items-center justify-between gap-4">
<div class="flex flex-wrap gap-4 w-full md:w-auto">
<div class="relative w-full md:w-48">
<select class="w-full appearance-none bg-gray-50 border border-gray-200 rounded px-3 py-2 pr-8 text-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
<option value="">Tất cả chủ đề con</option>
<option value="ai">Trí tuệ nhân tạo</option>
<option value="web">Phát triển web</option>
<option value="mobile">Ứng dụng di động</option>
<option value="cloud">Điện toán đám mây</option>
<option value="security">Bảo mật</option>
</select>
<div class="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 flex items-center justify-center pointer-events-none">
<i class="ri-arrow-down-s-line text-gray-500"></i>
</div>
</div>
<div class="relative w-full md:w-48">
<select class="w-full appearance-none bg-gray-50 border border-gray-200 rounded px-3 py-2 pr-8 text-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
<option value="newest">Mới nhất</option>
<option value="popular">Nhiều bình luận nhất</option>
<option value="views">Nhiều lượt xem nhất</option>
</select>
<div class="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 flex items-center justify-center pointer-events-none">
<i class="ri-arrow-down-s-line text-gray-500"></i>
</div>
</div>
</div>
<div class="relative w-full md:w-64">
<input type="text" placeholder="Tìm kiếm bài viết" class="w-full bg-gray-50 border border-gray-200 rounded px-3 py-2 pl-9 text-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
<div class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 flex items-center justify-center">
<i class="ri-search-line text-gray-500"></i>
</div>
</div>
</div>
</div>
<!-- Posts List -->
<div class="space-y-4 mb-6">
<!-- Post 1 -->
<div class="bg-white rounded-md shadow-sm p-4 hover:shadow-md transition-shadow">
<div class="flex items-start gap-4">
<div class="flex-shrink-0">
<div class="w-12 h-12 rounded-full overflow-hidden">
<img src="https://readdy.ai/api/search-image?query=professional%2520male%2520avatar%2520with%2520short%2520dark%2520hair%2520and%2520glasses%252C%2520simple%2520clean%2520background%252C%2520minimalist%2520style%252C%2520professional%2520portrait&width=100&height=100&seq=1&orientation=squarish" alt="Avatar" class="w-full h-full object-cover object-top">
</div>
</div>
<div class="flex-grow">
<div class="flex flex-wrap items-center gap-2 mb-1">
<span class="bg-blue-100 text-blue-700 px-2 py-0.5 rounded-full text-xs">Trí tuệ nhân tạo</span>
<span class="bg-green-100 text-green-700 px-2 py-0.5 rounded-full text-xs">Hot</span>
</div>
<h3 class="text-lg font-medium mb-1">
<a href="https://readdy.ai/home/<USER>/ea3d21a5-a74e-4e47-9db6-6bb597b00d1b" data-readdy="true" class="text-gray-900 hover:text-primary">Tổng quan về các mô hình ngôn ngữ lớn (LLM) và ứng dụng trong doanh nghiệp</a>
</h3>
<p class="text-gray-600 text-sm mb-3">Bài viết này phân tích các mô hình ngôn ngữ lớn như GPT-4, Claude và Llama 2, cùng với các ứng dụng thực tế trong doanh nghiệp. Chúng ta sẽ tìm hiểu về cách triển khai, chi phí và lợi ích...</p>
<div class="flex flex-wrap items-center justify-between">
<div class="flex items-center text-sm text-gray-500">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-user-line"></i>
</div>
<span class="mr-4">Nguyễn Văn Minh</span>
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-time-line"></i>
</div>
<span>2 giờ trước</span>
</div>
<div class="flex items-center space-x-4 text-sm text-gray-500">
<div class="flex items-center">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-eye-line"></i>
</div>
<span>1,245</span>
</div>
<div class="flex items-center">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-message-2-line"></i>
</div>
<span>32</span>
</div>
<div class="flex items-center">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-heart-line"></i>
</div>
<span>78</span>
</div>
</div>
</div>
</div>
</div>
</div>
<!-- Post 2 -->
<div class="bg-white rounded-md shadow-sm p-4 hover:shadow-md transition-shadow">
<div class="flex items-start gap-4">
<div class="flex-shrink-0">
<div class="w-12 h-12 rounded-full overflow-hidden">
<img src="https://readdy.ai/api/search-image?query=professional%2520female%2520avatar%2520with%2520long%2520brown%2520hair%252C%2520simple%2520clean%2520background%252C%2520minimalist%2520style%252C%2520professional%2520portrait&width=100&height=100&seq=2&orientation=squarish" alt="Avatar" class="w-full h-full object-cover object-top">
</div>
</div>
<div class="flex-grow">
<div class="flex flex-wrap items-center gap-2 mb-1">
<span class="bg-purple-100 text-purple-700 px-2 py-0.5 rounded-full text-xs">Phát triển web</span>
</div>
<h3 class="text-lg font-medium mb-1">
<a href="#" class="text-gray-900 hover:text-primary">So sánh các framework JavaScript hiện đại: React, Vue, Angular và Svelte</a>
</h3>
<p class="text-gray-600 text-sm mb-3">Phân tích chi tiết về ưu nhược điểm của các framework JavaScript phổ biến nhất hiện nay. Bài viết cung cấp các ví dụ mã nguồn, so sánh hiệu suất và giúp bạn lựa chọn công cụ phù hợp...</p>
<div class="flex flex-wrap items-center justify-between">
<div class="flex items-center text-sm text-gray-500">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-user-line"></i>
</div>
<span class="mr-4">Trần Thị Hương</span>
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-time-line"></i>
</div>
<span>5 giờ trước</span>
</div>
<div class="flex items-center space-x-4 text-sm text-gray-500">
<div class="flex items-center">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-eye-line"></i>
</div>
<span>876</span>
</div>
<div class="flex items-center">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-message-2-line"></i>
</div>
<span>24</span>
</div>
<div class="flex items-center">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-heart-line"></i>
</div>
<span>45</span>
</div>
</div>
</div>
</div>
</div>
</div>
<!-- Post 3 -->
<div class="bg-white rounded-md shadow-sm p-4 hover:shadow-md transition-shadow">
<div class="flex items-start gap-4">
<div class="flex-shrink-0">
<div class="w-12 h-12 rounded-full overflow-hidden">
<img src="https://readdy.ai/api/search-image?query=professional%2520male%2520avatar%2520with%2520beard%2520and%2520casual%2520attire%252C%2520simple%2520clean%2520background%252C%2520minimalist%2520style%252C%2520professional%2520portrait&width=100&height=100&seq=3&orientation=squarish" alt="Avatar" class="w-full h-full object-cover object-top">
</div>
</div>
<div class="flex-grow">
<div class="flex flex-wrap items-center gap-2 mb-1">
<span class="bg-orange-100 text-orange-700 px-2 py-0.5 rounded-full text-xs">Điện toán đám mây</span>
</div>
<h3 class="text-lg font-medium mb-1">
<a href="#" class="text-gray-900 hover:text-primary">Kiến trúc Microservices với Kubernetes: Từ lý thuyết đến thực hành</a>
</h3>
<p class="text-gray-600 text-sm mb-3">Hướng dẫn chi tiết về việc xây dựng, triển khai và vận hành hệ thống microservices trên Kubernetes. Bài viết bao gồm các mẫu thiết kế, chiến lược scale và cách xử lý các thách thức phổ biến...</p>
<div class="flex flex-wrap items-center justify-between">
<div class="flex items-center text-sm text-gray-500">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-user-line"></i>
</div>
<span class="mr-4">Lê Đức Thắng</span>
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-time-line"></i>
</div>
<span>1 ngày trước</span>
</div>
<div class="flex items-center space-x-4 text-sm text-gray-500">
<div class="flex items-center">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-eye-line"></i>
</div>
<span>1,032</span>
</div>
<div class="flex items-center">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-message-2-line"></i>
</div>
<span>56</span>
</div>
<div class="flex items-center">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-heart-line"></i>
</div>
<span>92</span>
</div>
</div>
</div>
</div>
</div>
</div>
<!-- Post 4 -->
<div class="bg-white rounded-md shadow-sm p-4 hover:shadow-md transition-shadow">
<div class="flex items-start gap-4">
<div class="flex-shrink-0">
<div class="w-12 h-12 rounded-full overflow-hidden">
<img src="https://readdy.ai/api/search-image?query=professional%2520female%2520avatar%2520with%2520short%2520black%2520hair%252C%2520simple%2520clean%2520background%252C%2520minimalist%2520style%252C%2520professional%2520portrait&width=100&height=100&seq=4&orientation=squarish" alt="Avatar" class="w-full h-full object-cover object-top">
</div>
</div>
<div class="flex-grow">
<div class="flex flex-wrap items-center gap-2 mb-1">
<span class="bg-red-100 text-red-700 px-2 py-0.5 rounded-full text-xs">Bảo mật</span>
</div>
<h3 class="text-lg font-medium mb-1">
<a href="#" class="text-gray-900 hover:text-primary">Các phương pháp phòng chống tấn công ransomware hiệu quả cho doanh nghiệp</a>
</h3>
<p class="text-gray-600 text-sm mb-3">Bài viết phân tích các chiến lược bảo mật để bảo vệ doanh nghiệp khỏi các cuộc tấn công ransomware ngày càng phức tạp. Từ sao lưu dữ liệu, đào tạo nhân viên đến triển khai giải pháp bảo mật...</p>
<div class="flex flex-wrap items-center justify-between">
<div class="flex items-center text-sm text-gray-500">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-user-line"></i>
</div>
<span class="mr-4">Phạm Thanh Hà</span>
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-time-line"></i>
</div>
<span>2 ngày trước</span>
</div>
<div class="flex items-center space-x-4 text-sm text-gray-500">
<div class="flex items-center">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-eye-line"></i>
</div>
<span>945</span>
</div>
<div class="flex items-center">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-message-2-line"></i>
</div>
<span>38</span>
</div>
<div class="flex items-center">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-heart-line"></i>
</div>
<span>67</span>
</div>
</div>
</div>
</div>
</div>
</div>
<!-- Post 5 -->
<div class="bg-white rounded-md shadow-sm p-4 hover:shadow-md transition-shadow">
<div class="flex items-start gap-4">
<div class="flex-shrink-0">
<div class="w-12 h-12 rounded-full overflow-hidden">
<img src="https://readdy.ai/api/search-image?query=professional%2520male%2520avatar%2520with%2520glasses%2520and%2520formal%2520attire%252C%2520simple%2520clean%2520background%252C%2520minimalist%2520style%252C%2520professional%2520portrait&width=100&height=100&seq=5&orientation=squarish" alt="Avatar" class="w-full h-full object-cover object-top">
</div>
</div>
<div class="flex-grow">
<div class="flex flex-wrap items-center gap-2 mb-1">
<span class="bg-green-100 text-green-700 px-2 py-0.5 rounded-full text-xs">Ứng dụng di động</span>
</div>
<h3 class="text-lg font-medium mb-1">
<a href="#" class="text-gray-900 hover:text-primary">Flutter vs React Native: Đâu là lựa chọn tốt nhất cho phát triển ứng dụng đa nền tảng năm 2025?</a>
</h3>
<p class="text-gray-600 text-sm mb-3">So sánh toàn diện giữa hai framework phát triển ứng dụng di động đa nền tảng phổ biến nhất. Phân tích hiệu suất, trải nghiệm phát triển, cộng đồng hỗ trợ và các trường hợp sử dụng phù hợp...</p>
<div class="flex flex-wrap items-center justify-between">
<div class="flex items-center text-sm text-gray-500">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-user-line"></i>
</div>
<span class="mr-4">Hoàng Minh Tuấn</span>
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-time-line"></i>
</div>
<span>3 ngày trước</span>
</div>
<div class="flex items-center space-x-4 text-sm text-gray-500">
<div class="flex items-center">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-eye-line"></i>
</div>
<span>1,378</span>
</div>
<div class="flex items-center">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-message-2-line"></i>
</div>
<span>72</span>
</div>
<div class="flex items-center">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-heart-line"></i>
</div>
<span>124</span>
</div>
</div>
</div>
</div>
</div>
</div>
<!-- Post 6 -->
<div class="bg-white rounded-md shadow-sm p-4 hover:shadow-md transition-shadow">
<div class="flex items-start gap-4">
<div class="flex-shrink-0">
<div class="w-12 h-12 rounded-full overflow-hidden">
<img src="https://readdy.ai/api/search-image?query=professional%2520female%2520avatar%2520with%2520glasses%2520and%2520business%2520casual%2520attire%252C%2520simple%2520clean%2520background%252C%2520minimalist%2520style%252C%2520professional%2520portrait&width=100&height=100&seq=6&orientation=squarish" alt="Avatar" class="w-full h-full object-cover object-top">
</div>
</div>
<div class="flex-grow">
<div class="flex flex-wrap items-center gap-2 mb-1">
<span class="bg-indigo-100 text-indigo-700 px-2 py-0.5 rounded-full text-xs">DevOps</span>
</div>
<h3 class="text-lg font-medium mb-1">
<a href="#" class="text-gray-900 hover:text-primary">CI/CD Pipeline hiện đại: Tự động hóa quy trình phát triển phần mềm từ A-Z</a>
</h3>
<p class="text-gray-600 text-sm mb-3">Hướng dẫn toàn diện về xây dựng và tối ưu hóa quy trình CI/CD cho dự án phần mềm. Bài viết bao gồm các công cụ, thực tiễn tốt nhất và chiến lược triển khai cho các đội phát triển...</p>
<div class="flex flex-wrap items-center justify-between">
<div class="flex items-center text-sm text-gray-500">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-user-line"></i>
</div>
<span class="mr-4">Vũ Thị Mai Anh</span>
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-time-line"></i>
</div>
<span>4 ngày trước</span>
</div>
<div class="flex items-center space-x-4 text-sm text-gray-500">
<div class="flex items-center">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-eye-line"></i>
</div>
<span>823</span>
</div>
<div class="flex items-center">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-message-2-line"></i>
</div>
<span>29</span>
</div>
<div class="flex items-center">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-heart-line"></i>
</div>
<span>51</span>
</div>
</div>
</div>
</div>
</div>
</div>
<!-- Post 7 -->
<div class="bg-white rounded-md shadow-sm p-4 hover:shadow-md transition-shadow">
<div class="flex items-start gap-4">
<div class="flex-shrink-0">
<div class="w-12 h-12 rounded-full overflow-hidden">
<img src="https://readdy.ai/api/search-image?query=professional%2520male%2520avatar%2520with%2520casual%2520business%2520attire%252C%2520simple%2520clean%2520background%252C%2520minimalist%2520style%252C%2520professional%2520portrait&width=100&height=100&seq=7&orientation=squarish" alt="Avatar" class="w-full h-full object-cover object-top">
</div>
</div>
<div class="flex-grow">
<div class="flex flex-wrap items-center gap-2 mb-1">
<span class="bg-yellow-100 text-yellow-700 px-2 py-0.5 rounded-full text-xs">Blockchain</span>
</div>
<h3 class="text-lg font-medium mb-1">
<a href="#" class="text-gray-900 hover:text-primary">Web3 và tương lai của Internet: Cơ hội và thách thức</a>
</h3>
<p class="text-gray-600 text-sm mb-3">Phân tích chuyên sâu về công nghệ Web3, blockchain và các ứng dụng phi tập trung. Bài viết thảo luận về tiềm năng, hạn chế và cách các doanh nghiệp có thể tận dụng những công nghệ này...</p>
<div class="flex flex-wrap items-center justify-between">
<div class="flex items-center text-sm text-gray-500">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-user-line"></i>
</div>
<span class="mr-4">Đỗ Quang Huy</span>
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-time-line"></i>
</div>
<span>5 ngày trước</span>
</div>
<div class="flex items-center space-x-4 text-sm text-gray-500">
<div class="flex items-center">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-eye-line"></i>
</div>
<span>1,156</span>
</div>
<div class="flex items-center">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-message-2-line"></i>
</div>
<span>47</span>
</div>
<div class="flex items-center">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-heart-line"></i>
</div>
<span>83</span>
</div>
</div>
</div>
</div>
</div>
</div>
<!-- Post 8 -->
<div class="bg-white rounded-md shadow-sm p-4 hover:shadow-md transition-shadow">
<div class="flex items-start gap-4">
<div class="flex-shrink-0">
<div class="w-12 h-12 rounded-full overflow-hidden">
<img src="https://readdy.ai/api/search-image?query=professional%2520male%2520avatar%2520with%2520neat%2520haircut%2520and%2520business%2520attire%252C%2520simple%2520clean%2520background%252C%2520minimalist%2520style%252C%2520professional%2520portrait&width=100&height=100&seq=8&orientation=squarish" alt="Avatar" class="w-full h-full object-cover object-top">
</div>
</div>
<div class="flex-grow">
<div class="flex flex-wrap items-center gap-2 mb-1">
<span class="bg-blue-100 text-blue-700 px-2 py-0.5 rounded-full text-xs">Trí tuệ nhân tạo</span>
</div>
<h3 class="text-lg font-medium mb-1">
<a href="#" class="text-gray-900 hover:text-primary">Computer Vision trong thực tế: Ứng dụng và triển khai</a>
</h3>
<p class="text-gray-600 text-sm mb-3">Khám phá các ứng dụng thực tế của Computer Vision trong các ngành công nghiệp khác nhau. Bài viết cung cấp các ví dụ cụ thể, mô hình và thư viện phổ biến, cùng với các bước triển khai...</p>
<div class="flex flex-wrap items-center justify-between">
<div class="flex items-center text-sm text-gray-500">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-user-line"></i>
</div>
<span class="mr-4">Nguyễn Quốc Bảo</span>
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-time-line"></i>
</div>
<span>6 ngày trước</span>
</div>
<div class="flex items-center space-x-4 text-sm text-gray-500">
<div class="flex items-center">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-eye-line"></i>
</div>
<span>987</span>
</div>
<div class="flex items-center">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-message-2-line"></i>
</div>
<span>41</span>
</div>
<div class="flex items-center">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-heart-line"></i>
</div>
<span>76</span>
</div>
</div>
</div>
</div>
</div>
</div>
<!-- Post 9 -->
<div class="bg-white rounded-md shadow-sm p-4 hover:shadow-md transition-shadow">
<div class="flex items-start gap-4">
<div class="flex-shrink-0">
<div class="w-12 h-12 rounded-full overflow-hidden">
<img src="https://readdy.ai/api/search-image?query=professional%2520female%2520avatar%2520with%2520business%2520attire%2520and%2520confident%2520expression%252C%2520simple%2520clean%2520background%252C%2520minimalist%2520style%252C%2520professional%2520portrait&width=100&height=100&seq=9&orientation=squarish" alt="Avatar" class="w-full h-full object-cover object-top">
</div>
</div>
<div class="flex-grow">
<div class="flex flex-wrap items-center gap-2 mb-1">
<span class="bg-purple-100 text-purple-700 px-2 py-0.5 rounded-full text-xs">Phát triển web</span>
</div>
<h3 class="text-lg font-medium mb-1">
<a href="#" class="text-gray-900 hover:text-primary">Tối ưu hóa hiệu suất website: Chiến lược và công cụ</a>
</h3>
<p class="text-gray-600 text-sm mb-3">Hướng dẫn toàn diện về cách cải thiện tốc độ tải trang và trải nghiệm người dùng. Bài viết bao gồm các kỹ thuật tối ưu hóa hình ảnh, lazy loading, caching và các phương pháp đo lường hiệu suất...</p>
<div class="flex flex-wrap items-center justify-between">
<div class="flex items-center text-sm text-gray-500">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-user-line"></i>
</div>
<span class="mr-4">Trần Thị Lan Anh</span>
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-time-line"></i>
</div>
<span>1 tuần trước</span>
</div>
<div class="flex items-center space-x-4 text-sm text-gray-500">
<div class="flex items-center">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-eye-line"></i>
</div>
<span>1,421</span>
</div>
<div class="flex items-center">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-message-2-line"></i>
</div>
<span>68</span>
</div>
<div class="flex items-center">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-heart-line"></i>
</div>
<span>112</span>
</div>
</div>
</div>
</div>
</div>
</div>
<!-- Post 10 -->
<div class="bg-white rounded-md shadow-sm p-4 hover:shadow-md transition-shadow">
<div class="flex items-start gap-4">
<div class="flex-shrink-0">
<div class="w-12 h-12 rounded-full overflow-hidden">
<img src="https://readdy.ai/api/search-image?query=professional%2520male%2520avatar%2520with%2520neat%2520appearance%2520and%2520friendly%2520smile%252C%2520simple%2520clean%2520background%252C%2520minimalist%2520style%252C%2520professional%2520portrait&width=100&height=100&seq=10&orientation=squarish" alt="Avatar" class="w-full h-full object-cover object-top">
</div>
</div>
<div class="flex-grow">
<div class="flex flex-wrap items-center gap-2 mb-1">
<span class="bg-green-100 text-green-700 px-2 py-0.5 rounded-full text-xs">IoT</span>
</div>
<h3 class="text-lg font-medium mb-1">
<a href="#" class="text-gray-900 hover:text-primary">Internet of Things (IoT) và Smart Home: Xu hướng và giải pháp</a>
</h3>
<p class="text-gray-600 text-sm mb-3">Khám phá các xu hướng mới nhất trong lĩnh vực IoT và nhà thông minh. Bài viết thảo luận về các thiết bị, giao thức kết nối, vấn đề bảo mật và cách xây dựng hệ thống nhà thông minh hiệu quả...</p>
<div class="flex flex-wrap items-center justify-between">
<div class="flex items-center text-sm text-gray-500">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-user-line"></i>
</div>
<span class="mr-4">Phan Văn Đức</span>
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-time-line"></i>
</div>
<span>1 tuần trước</span>
</div>
<div class="flex items-center space-x-4 text-sm text-gray-500">
<div class="flex items-center">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-eye-line"></i>
</div>
<span>1,089</span>
</div>
<div class="flex items-center">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-message-2-line"></i>
</div>
<span>53</span>
</div>
<div class="flex items-center">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-heart-line"></i>
</div>
<span>94</span>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
<!-- Pagination -->
<div class="flex items-center justify-between">
<div class="text-sm text-gray-500">Hiển thị 1-10 trong tổng số 142 bài viết</div>
<div class="flex items-center space-x-1">
<a href="#" class="w-8 h-8 flex items-center justify-center rounded border text-gray-500 hover:bg-gray-50">
<i class="ri-arrow-left-s-line"></i>
</a>
<a href="#" class="w-8 h-8 flex items-center justify-center rounded border bg-primary text-white">1</a>
<a href="#" class="w-8 h-8 flex items-center justify-center rounded border text-gray-700 hover:bg-gray-50">2</a>
<a href="#" class="w-8 h-8 flex items-center justify-center rounded border text-gray-700 hover:bg-gray-50">3</a>
<span class="text-gray-500">...</span>
<a href="#" class="w-8 h-8 flex items-center justify-center rounded border text-gray-700 hover:bg-gray-50">14</a>
<a href="#" class="w-8 h-8 flex items-center justify-center rounded border text-gray-500 hover:bg-gray-50">
<i class="ri-arrow-right-s-line"></i>
</a>
</div>
</div>
<!-- Fixed Create Post Button (Mobile) -->
<div class="md:hidden fixed bottom-6 right-6">
<a href="https://readdy.ai/home/<USER>/772c5e6e-cad3-41ee-97b6-329cbee33ea0" data-readdy="true" class="bg-primary text-white p-3 rounded-full shadow-lg flex items-center justify-center whitespace-nowrap">
<div class="w-6 h-6 flex items-center justify-center">
<i class="ri-add-line ri-lg"></i>
</div>
</a>
</div>
</div>
<script>
document.addEventListener('DOMContentLoaded', function() {
// Dropdown functionality for filters
const selects = document.querySelectorAll('select');
selects.forEach(select => {
select.addEventListener('change', function() {
console.log('Filter changed:', this.value);
});
});
// Search functionality
const searchInput = document.querySelector('input[placeholder="Tìm kiếm bài viết"]');
if (searchInput) {
searchInput.addEventListener('keyup', function(e) {
if (e.key === 'Enter') {
console.log('Search for:', this.value);
}
});
}
});
</script>
</body>
</html>
