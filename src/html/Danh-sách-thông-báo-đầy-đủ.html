<!DOCTYPE html>
<html lang="vi">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>Tất cả thông b<PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON></title>
<script src="https://cdn.tailwindcss.com/3.4.16"></script>
<script>tailwind.config={theme:{extend:{colors:{primary:'#2E228B',secondary:'#818cf8'},borderRadius:{'none':'0px','sm':'4px',DEFAULT:'8px','md':'12px','lg':'16px','xl':'20px','2xl':'24px','3xl':'32px','full':'9999px','button':'8px'}}}}</script>
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css">
<style>
:where([class^="ri-"])::before { content: "\f3c2"; }
body {
font-family: 'Roboto', sans-serif;
}
#notificationDropdown::-webkit-scrollbar {
width: 6px;
}
#notificationDropdown::-webkit-scrollbar-track {
background: transparent;
}
#notificationDropdown::-webkit-scrollbar-thumb {
background-color: rgba(156, 163, 175, 0.5);
border-radius: 3px;
}
#notificationDropdown::-webkit-scrollbar-thumb:hover {
background-color: rgba(156, 163, 175, 0.7);
}
.notification-item.unread {
border-left: 3px solid #2E228B;
}
.custom-checkbox {
display: none;
}
.custom-checkbox + label {
display: inline-block;
width: 18px;
height: 18px;
border: 2px solid #d1d5db;
border-radius: 4px;
cursor: pointer;
position: relative;
}
.custom-checkbox:checked + label {
background-color: #2E228B;
border-color: #2E228B;
}
.custom-checkbox:checked + label:after {
content: '';
position: absolute;
left: 5px;
top: 2px;
width: 5px;
height: 10px;
border: solid white;
border-width: 0 2px 2px 0;
transform: rotate(45deg);
}
.dropdown-menu {
display: none;
position: absolute;
right: 0;
top: 100%;
background-color: white;
border-radius: 8px;
box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
z-index: 10;
min-width: 160px;
}
.dropdown {
position: relative;
}
.dropdown.active .dropdown-menu {
display: block;
}
</style>
</head>
<body class="bg-gray-50">
<!-- Header -->
<header class="bg-primary text-white">
<div class="container mx-auto px-4 py-3 flex items-center justify-between">
<div class="flex items-center space-x-10">
<a href="#" class="text-2xl font-['Pacifico'] text-white">logo</a>
<nav class="hidden md:flex space-x-6">
<a href="#" class="hover:text-white/80">Trang chủ</a>
<a href="#" class="hover:text-white/80">Bài viết</a>
<a href="#" class="hover:text-white/80">Tác giả</a>
<a href="#" class="hover:text-white/80">Chủ đề</a>
</nav>
</div>
<div class="flex items-center space-x-4">
<div class="relative" id="notificationDropdownContainer">
<button id="notificationButton" class="w-10 h-10 flex items-center justify-center text-white cursor-pointer">
<i class="ri-notification-3-line ri-lg"></i>
</button>
<span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center" id="notificationBadge">3</span>
<div id="notificationDropdown" class="hidden absolute right-0 top-full mt-2 w-96 bg-white rounded-lg shadow-lg z-50 max-h-[480px] overflow-y-auto">
<div class="p-4 border-b">
<div class="flex items-center justify-between mb-2">
<h3 class="font-medium text-gray-900">Thông báo</h3>
<button id="markAllReadDropdown" class="text-sm text-primary hover:text-primary/80">Đánh dấu tất cả đã đọc</button>
</div>
</div>
<div class="divide-y" id="notificationDropdownList">
<div class="p-4 hover:bg-gray-50 transition-colors cursor-pointer unread">
<div class="flex items-start gap-3">
<div class="w-8 h-8 flex items-center justify-center bg-blue-100 rounded-full">
<i class="ri-message-2-line text-blue-500"></i>
</div>
<div class="flex-1 min-w-0">
<p class="text-sm text-gray-900 font-medium mb-1">Nguyễn Thị Hương đã bình luận về bài viết của bạn</p>
<p class="text-xs text-gray-500 mb-1">28/04/2025, 10:25</p>
<p class="text-sm text-gray-600 truncate">"Bài viết rất hay và sâu sắc về chủ đề AI..."</p>
</div>
<button class="mark-as-read-btn w-8 h-8 flex items-center justify-center text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full">
<i class="ri-check-line"></i>
</button>
</div>
</div>
<div class="p-4 hover:bg-gray-50 transition-colors cursor-pointer unread">
<div class="flex items-start gap-3">
<div class="w-8 h-8 flex items-center justify-center bg-red-100 rounded-full">
<i class="ri-heart-line text-red-500"></i>
</div>
<div class="flex-1 min-w-0">
<p class="text-sm text-gray-900 font-medium mb-1">Trần Văn Đức và 5 người khác đã thích bài viết của bạn</p>
<p class="text-xs text-gray-500 mb-1">28/04/2025, 09:15</p>
<p class="text-sm text-gray-600 truncate">"Tương lai của mô hình ngôn ngữ lớn..."</p>
</div>
<button class="mark-as-read-btn w-8 h-8 flex items-center justify-center text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full">
<i class="ri-check-line"></i>
</button>
</div>
</div>
<div class="p-4 hover:bg-gray-50 transition-colors cursor-pointer unread">
<div class="flex items-start gap-3">
<div class="w-8 h-8 flex items-center justify-center bg-green-100 rounded-full">
<i class="ri-user-follow-line text-green-500"></i>
</div>
<div class="flex-1 min-w-0">
<p class="text-sm text-gray-900 font-medium mb-1">Lê Minh Tuấn đã bắt đầu theo dõi bạn</p>
<p class="text-xs text-gray-500 mb-1">28/04/2025, 08:30</p>
<p class="text-sm text-gray-600 truncate">Lê Minh Tuấn, Giảng viên Đại học Khoa học...</p>
</div>
<button class="mark-as-read-btn w-8 h-8 flex items-center justify-center text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full">
<i class="ri-check-line"></i>
</button>
</div>
</div>
<div class="p-4 hover:bg-gray-50 transition-colors cursor-pointer">
<div class="flex items-start gap-3">
<div class="w-8 h-8 flex items-center justify-center bg-purple-100 rounded-full">
<i class="ri-share-forward-line text-purple-500"></i>
</div>
<div class="flex-1 min-w-0">
<p class="text-sm text-gray-900 font-medium mb-1">Phạm Thanh Hà đã chia sẻ bài viết của bạn</p>
<p class="text-xs text-gray-500 mb-1">27/04/2025, 16:45</p>
<p class="text-sm text-gray-600 truncate">"Kỹ thuật tối ưu hóa mô hình học máy..."</p>
</div>
<button class="mark-as-read-btn w-8 h-8 flex items-center justify-center text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full">
<i class="ri-check-line"></i>
</button>
</div>
</div>
<div class="p-4 hover:bg-gray-50 transition-colors cursor-pointer">
<div class="flex items-start gap-3">
<div class="w-8 h-8 flex items-center justify-center bg-yellow-100 rounded-full">
<i class="ri-award-line text-yellow-500"></i>
</div>
<div class="flex-1 min-w-0">
<p class="text-sm text-gray-900 font-medium mb-1">Bài viết của bạn đã được chọn làm bài viết nổi bật</p>
<p class="text-xs text-gray-500 mb-1">27/04/2025, 14:20</p>
<p class="text-sm text-gray-600 truncate">"Tương lai của mô hình ngôn ngữ lớn..."</p>
</div>
<button class="mark-as-read-btn w-8 h-8 flex items-center justify-center text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full">
<i class="ri-check-line"></i>
</button>
</div>
</div>
</div>
<div class="p-3 bg-gray-50 rounded-b-lg">
<a href="#" class="block w-full py-2 px-3 text-sm text-center text-white bg-primary hover:bg-primary/90 rounded !rounded-button">Xem tất cả thông báo</a>
</div>
</div>
</div>
<div class="w-10 h-10 rounded-full overflow-hidden">
<img src="https://readdy.ai/api/search-image?query=professional%2520asian%2520man%2520in%2520business%2520suit%252C%2520portrait%2520photo%252C%2520professional%2520headshot%252C%2520high%2520quality%252C%2520realistic&width=100&height=100&seq=1&orientation=squarish" alt="User Avatar" class="w-full h-full object-cover">
</div>
</div>
</div>
</header>
<!-- Main Content -->
<main class="container mx-auto px-4 py-6">
<div class="flex items-center justify-between mb-6">
<h1 class="text-2xl font-bold text-gray-800">Thông báo của bạn</h1>
<a href="https://readdy.ai/home/<USER>/09bf0679-2726-4a74-a098-0c3912645aa1" data-readdy="true" class="flex items-center text-primary hover:text-primary/80">
<i class="ri-arrow-left-line mr-1"></i>
Quay lại
</a>
</div>
<!-- Filter and Search Bar -->
<div class="bg-white rounded shadow p-4 mb-6">
<div class="flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
<div class="flex items-center space-x-4">
<div class="inline-flex rounded-md shadow-sm" role="group">
<button type="button" class="px-4 py-2 text-sm font-medium text-white bg-primary rounded-l-md focus:outline-none !rounded-button" id="allNotifications">
Tất cả
</button>
<button type="button" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-200 hover:bg-gray-100 focus:outline-none !rounded-button" id="unreadNotifications">
Chưa đọc
</button>
<button type="button" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-200 hover:bg-gray-100 focus:outline-none !rounded-button rounded-r-md" id="readNotifications">
Đã đọc
</button>
</div>
<button id="markAllAsRead" class="px-4 py-2 text-sm font-medium text-primary hover:bg-gray-50 focus:outline-none !rounded-button flex items-center whitespace-nowrap">
<i class="ri-check-double-line mr-1"></i>
Đánh dấu tất cả đã đọc
</button>
</div>
<div class="flex items-center space-x-4 w-full md:w-auto">
<div class="relative w-full md:w-64">
<div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
<i class="ri-search-line text-gray-400"></i>
</div>
<input type="text" id="searchNotifications" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded focus:ring-primary focus:border-primary block w-full pl-10 p-2.5" placeholder="Tìm kiếm thông báo...">
</div>
<div class="text-sm text-gray-500 whitespace-nowrap">
<span id="notificationCount">15</span> thông báo
</div>
</div>
</div>
</div>
<!-- Notifications List -->
<div class="space-y-4 mb-6">
<!-- Notification 1 -->
<div class="notification-item unread bg-white rounded shadow p-4 hover:bg-gray-50 transition-colors">
<div class="flex items-start">
<div class="w-10 h-10 flex items-center justify-center bg-blue-100 rounded-full mr-3">
<i class="ri-message-2-line text-blue-500"></i>
</div>
<div class="flex-1">
<div class="flex flex-col md:flex-row md:items-center justify-between mb-1">
<p class="text-gray-800 font-medium">Nguyễn Thị Hương đã bình luận về bài viết của bạn</p>
<span class="text-xs text-gray-500">28/04/2025, 10:25</span>
</div>
<p class="text-gray-600 text-sm mb-2">"Bài viết rất hay và sâu sắc về chủ đề AI. Tôi đặc biệt thích phần phân tích về các mô hình ngôn ngữ lớn và ứng dụng thực tế trong doanh nghiệp Việt Nam."</p>
<div class="flex items-center justify-between">
<a href="#" class="text-primary text-sm hover:underline">Xem bài viết</a>
<div class="dropdown relative">
<button class="w-8 h-8 flex items-center justify-center text-gray-500 hover:bg-gray-100 rounded-full dropdown-toggle">
<i class="ri-more-2-fill"></i>
</button>
<div class="dropdown-menu">
<a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 mark-as-read">Đánh dấu đã đọc</a>
<a href="#" class="block px-4 py-2 text-sm text-red-600 hover:bg-gray-100 delete-notification">Xóa thông báo</a>
</div>
</div>
</div>
</div>
</div>
</div>
<!-- Notification 2 -->
<div class="notification-item unread bg-white rounded shadow p-4 hover:bg-gray-50 transition-colors">
<div class="flex items-start">
<div class="w-10 h-10 flex items-center justify-center bg-red-100 rounded-full mr-3">
<i class="ri-heart-line text-red-500"></i>
</div>
<div class="flex-1">
<div class="flex flex-col md:flex-row md:items-center justify-between mb-1">
<p class="text-gray-800 font-medium">Trần Văn Đức và 5 người khác đã thích bài viết của bạn</p>
<span class="text-xs text-gray-500">28/04/2025, 09:15</span>
</div>
<p class="text-gray-600 text-sm mb-2">"Tương lai của mô hình ngôn ngữ lớn (LLM) và ứng dụng trong doanh nghiệp Việt Nam"</p>
<div class="flex items-center justify-between">
<a href="#" class="text-primary text-sm hover:underline">Xem bài viết</a>
<div class="dropdown relative">
<button class="w-8 h-8 flex items-center justify-center text-gray-500 hover:bg-gray-100 rounded-full dropdown-toggle">
<i class="ri-more-2-fill"></i>
</button>
<div class="dropdown-menu">
<a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 mark-as-read">Đánh dấu đã đọc</a>
<a href="#" class="block px-4 py-2 text-sm text-red-600 hover:bg-gray-100 delete-notification">Xóa thông báo</a>
</div>
</div>
</div>
</div>
</div>
</div>
<!-- Notification 3 -->
<div class="notification-item unread bg-white rounded shadow p-4 hover:bg-gray-50 transition-colors">
<div class="flex items-start">
<div class="w-10 h-10 flex items-center justify-center bg-green-100 rounded-full mr-3">
<i class="ri-user-follow-line text-green-500"></i>
</div>
<div class="flex-1">
<div class="flex flex-col md:flex-row md:items-center justify-between mb-1">
<p class="text-gray-800 font-medium">Lê Minh Tuấn đã bắt đầu theo dõi bạn</p>
<span class="text-xs text-gray-500">28/04/2025, 08:30</span>
</div>
<p class="text-gray-600 text-sm mb-2">Lê Minh Tuấn, Giảng viên Đại học Khoa học Tự nhiên TP.HCM, chuyên ngành Deep Learning và Computer Vision.</p>
<div class="flex items-center justify-between">
<a href="#" class="text-primary text-sm hover:underline">Xem hồ sơ</a>
<div class="dropdown relative">
<button class="w-8 h-8 flex items-center justify-center text-gray-500 hover:bg-gray-100 rounded-full dropdown-toggle">
<i class="ri-more-2-fill"></i>
</button>
<div class="dropdown-menu">
<a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 mark-as-read">Đánh dấu đã đọc</a>
<a href="#" class="block px-4 py-2 text-sm text-red-600 hover:bg-gray-100 delete-notification">Xóa thông báo</a>
</div>
</div>
</div>
</div>
</div>
</div>
<!-- Notification 4 -->
<div class="notification-item bg-white rounded shadow p-4 hover:bg-gray-50 transition-colors">
<div class="flex items-start">
<div class="w-10 h-10 flex items-center justify-center bg-purple-100 rounded-full mr-3">
<i class="ri-share-forward-line text-purple-500"></i>
</div>
<div class="flex-1">
<div class="flex flex-col md:flex-row md:items-center justify-between mb-1">
<p class="text-gray-800 font-medium">Phạm Thanh Hà đã chia sẻ bài viết của bạn</p>
<span class="text-xs text-gray-500">27/04/2025, 16:45</span>
</div>
<p class="text-gray-600 text-sm mb-2">"Kỹ thuật tối ưu hóa mô hình học máy cho dữ liệu không cân bằng"</p>
<div class="flex items-center justify-between">
<a href="#" class="text-primary text-sm hover:underline">Xem bài viết</a>
<div class="dropdown relative">
<button class="w-8 h-8 flex items-center justify-center text-gray-500 hover:bg-gray-100 rounded-full dropdown-toggle">
<i class="ri-more-2-fill"></i>
</button>
<div class="dropdown-menu">
<a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 mark-as-unread">Đánh dấu chưa đọc</a>
<a href="#" class="block px-4 py-2 text-sm text-red-600 hover:bg-gray-100 delete-notification">Xóa thông báo</a>
</div>
</div>
</div>
</div>
</div>
</div>
<!-- Notification 5 -->
<div class="notification-item bg-white rounded shadow p-4 hover:bg-gray-50 transition-colors">
<div class="flex items-start">
<div class="w-10 h-10 flex items-center justify-center bg-yellow-100 rounded-full mr-3">
<i class="ri-award-line text-yellow-500"></i>
</div>
<div class="flex-1">
<div class="flex flex-col md:flex-row md:items-center justify-between mb-1">
<p class="text-gray-800 font-medium">Bài viết của bạn đã được chọn làm bài viết nổi bật</p>
<span class="text-xs text-gray-500">27/04/2025, 14:20</span>
</div>
<p class="text-gray-600 text-sm mb-2">"Tương lai của mô hình ngôn ngữ lớn (LLM) và ứng dụng trong doanh nghiệp Việt Nam" đã được biên tập viên chọn làm bài viết nổi bật trong tuần.</p>
<div class="flex items-center justify-between">
<a href="#" class="text-primary text-sm hover:underline">Xem bài viết</a>
<div class="dropdown relative">
<button class="w-8 h-8 flex items-center justify-center text-gray-500 hover:bg-gray-100 rounded-full dropdown-toggle">
<i class="ri-more-2-fill"></i>
</button>
<div class="dropdown-menu">
<a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 mark-as-unread">Đánh dấu chưa đọc</a>
<a href="#" class="block px-4 py-2 text-sm text-red-600 hover:bg-gray-100 delete-notification">Xóa thông báo</a>
</div>
</div>
</div>
</div>
</div>
</div>
<!-- Notification 6 -->
<div class="notification-item bg-white rounded shadow p-4 hover:bg-gray-50 transition-colors">
<div class="flex items-start">
<div class="w-10 h-10 flex items-center justify-center bg-blue-100 rounded-full mr-3">
<i class="ri-message-2-line text-blue-500"></i>
</div>
<div class="flex-1">
<div class="flex flex-col md:flex-row md:items-center justify-between mb-1">
<p class="text-gray-800 font-medium">Vũ Hoàng Nam đã bình luận về bài viết của bạn</p>
<span class="text-xs text-gray-500">26/04/2025, 11:35</span>
</div>
<p class="text-gray-600 text-sm mb-2">"Tôi đã áp dụng phương pháp của anh vào dự án của công ty và thấy hiệu quả rõ rệt. Cảm ơn anh đã chia sẻ kiến thức chuyên sâu này!"</p>
<div class="flex items-center justify-between">
<a href="#" class="text-primary text-sm hover:underline">Xem bài viết</a>
<div class="dropdown relative">
<button class="w-8 h-8 flex items-center justify-center text-gray-500 hover:bg-gray-100 rounded-full dropdown-toggle">
<i class="ri-more-2-fill"></i>
</button>
<div class="dropdown-menu">
<a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 mark-as-unread">Đánh dấu chưa đọc</a>
<a href="#" class="block px-4 py-2 text-sm text-red-600 hover:bg-gray-100 delete-notification">Xóa thông báo</a>
</div>
</div>
</div>
</div>
</div>
</div>
<!-- Notification 7 -->
<div class="notification-item bg-white rounded shadow p-4 hover:bg-gray-50 transition-colors">
<div class="flex items-start">
<div class="w-10 h-10 flex items-center justify-center bg-red-100 rounded-full mr-3">
<i class="ri-heart-line text-red-500"></i>
</div>
<div class="flex-1">
<div class="flex flex-col md:flex-row md:items-center justify-between mb-1">
<p class="text-gray-800 font-medium">Nguyễn Minh Châu và 12 người khác đã thích bài viết của bạn</p>
<span class="text-xs text-gray-500">25/04/2025, 20:15</span>
</div>
<p class="text-gray-600 text-sm mb-2">"Kỹ thuật tối ưu hóa mô hình học máy cho dữ liệu không cân bằng"</p>
<div class="flex items-center justify-between">
<a href="#" class="text-primary text-sm hover:underline">Xem bài viết</a>
<div class="dropdown relative">
<button class="w-8 h-8 flex items-center justify-center text-gray-500 hover:bg-gray-100 rounded-full dropdown-toggle">
<i class="ri-more-2-fill"></i>
</button>
<div class="dropdown-menu">
<a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 mark-as-unread">Đánh dấu chưa đọc</a>
<a href="#" class="block px-4 py-2 text-sm text-red-600 hover:bg-gray-100 delete-notification">Xóa thông báo</a>
</div>
</div>
</div>
</div>
</div>
</div>
<!-- Notification 8 -->
<div class="notification-item bg-white rounded shadow p-4 hover:bg-gray-50 transition-colors">
<div class="flex items-start">
<div class="w-10 h-10 flex items-center justify-center bg-green-100 rounded-full mr-3">
<i class="ri-calendar-event-line text-green-500"></i>
</div>
<div class="flex-1">
<div class="flex flex-col md:flex-row md:items-center justify-between mb-1">
<p class="text-gray-800 font-medium">Lời mời tham gia hội thảo "AI Summit Vietnam 2025"</p>
<span class="text-xs text-gray-500">24/04/2025, 15:40</span>
</div>
<p class="text-gray-600 text-sm mb-2">Ban tổ chức hội thảo "AI Summit Vietnam 2025" trân trọng mời bạn tham gia làm diễn giả với chủ đề "Tương lai của NLP trong doanh nghiệp Việt Nam". Thời gian: 15/05/2025, địa điểm: GEM Center, TP.HCM.</p>
<div class="flex items-center justify-between">
<a href="#" class="text-primary text-sm hover:underline">Xem chi tiết</a>
<div class="dropdown relative">
<button class="w-8 h-8 flex items-center justify-center text-gray-500 hover:bg-gray-100 rounded-full dropdown-toggle">
<i class="ri-more-2-fill"></i>
</button>
<div class="dropdown-menu">
<a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 mark-as-unread">Đánh dấu chưa đọc</a>
<a href="#" class="block px-4 py-2 text-sm text-red-600 hover:bg-gray-100 delete-notification">Xóa thông báo</a>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
<!-- Pagination -->
<div class="bg-white rounded shadow p-4">
<div class="flex flex-col md:flex-row items-center justify-between gap-4">
<div class="flex items-center space-x-2">
<span class="text-sm text-gray-700">Hiển thị</span>
<select class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded focus:ring-primary focus:border-primary pr-8 p-2">
<option value="8">8</option>
<option value="16">16</option>
<option value="24">24</option>
<option value="32">32</option>
</select>
<span class="text-sm text-gray-700">thông báo mỗi trang</span>
</div>
<div class="flex items-center">
<span class="text-sm text-gray-700 mr-4">
Trang <span class="font-medium">1</span> / <span class="font-medium">2</span>
</span>
<div class="inline-flex space-x-2">
<button class="px-3 py-1 text-sm text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed !rounded-button" disabled>
<i class="ri-arrow-left-s-line"></i>
Trước
</button>
<button class="px-3 py-1 text-sm text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-100 !rounded-button whitespace-nowrap">
Sau
<i class="ri-arrow-right-s-line"></i>
</button>
</div>
</div>
</div>
</div>
</main>
<script>
document.addEventListener('DOMContentLoaded', function() {
// Filter buttons
const allBtn = document.getElementById('allNotifications');
const unreadBtn = document.getElementById('unreadNotifications');
const readBtn = document.getElementById('readNotifications');
const notificationItems = document.querySelectorAll('.notification-item');
allBtn.addEventListener('click', function() {
setActiveFilter(allBtn);
notificationItems.forEach(item => {
item.style.display = 'block';
});
});
unreadBtn.addEventListener('click', function() {
setActiveFilter(unreadBtn);
notificationItems.forEach(item => {
if (item.classList.contains('unread')) {
item.style.display = 'block';
} else {
item.style.display = 'none';
}
});
});
readBtn.addEventListener('click', function() {
setActiveFilter(readBtn);
notificationItems.forEach(item => {
if (!item.classList.contains('unread')) {
item.style.display = 'block';
} else {
item.style.display = 'none';
}
});
});
function setActiveFilter(activeBtn) {
[allBtn, unreadBtn, readBtn].forEach(btn => {
if (btn === activeBtn) {
btn.classList.remove('text-gray-700', 'bg-white', 'border', 'border-gray-200', 'hover:bg-gray-100');
btn.classList.add('text-white', 'bg-primary');
} else {
btn.classList.remove('text-white', 'bg-primary');
btn.classList.add('text-gray-700', 'bg-white', 'border', 'border-gray-200', 'hover:bg-gray-100');
}
});
}
// Mark all as read
const markAllAsReadBtn = document.getElementById('markAllAsRead');
markAllAsReadBtn.addEventListener('click', function() {
notificationItems.forEach(item => {
item.classList.remove('unread');
});
document.getElementById('notificationCount').textContent = '15';
// Show success toast
showToast('Tất cả thông báo đã được đánh dấu là đã đọc');
});
// Search functionality
const searchInput = document.getElementById('searchNotifications');
searchInput.addEventListener('input', function() {
const searchTerm = this.value.toLowerCase();
notificationItems.forEach(item => {
const content = item.textContent.toLowerCase();
if (content.includes(searchTerm)) {
item.style.display = 'block';
} else {
item.style.display = 'none';
}
});
});
// Dropdown toggles
const dropdownToggles = document.querySelectorAll('.dropdown-toggle');
dropdownToggles.forEach(toggle => {
toggle.addEventListener('click', function(e) {
e.stopPropagation();
const dropdown = this.closest('.dropdown');
// Close all other dropdowns
document.querySelectorAll('.dropdown.active').forEach(d => {
if (d !== dropdown) {
d.classList.remove('active');
}
});
// Toggle current dropdown
dropdown.classList.toggle('active');
});
});
// Close dropdowns when clicking outside
document.addEventListener('click', function(event) {
document.querySelectorAll('.dropdown.active').forEach(dropdown => {
dropdown.classList.remove('active');
});

const notificationDropdown = document.getElementById('notificationDropdown');
const notificationButton = document.getElementById('notificationButton');
const notificationContainer = document.getElementById('notificationDropdownContainer');

if (!notificationContainer.contains(event.target)) {
notificationDropdown.classList.add('hidden');
}
});

// Notification dropdown toggle
document.getElementById('notificationButton').addEventListener('click', function(e) {
e.preventDefault();
e.stopPropagation();
const dropdown = document.getElementById('notificationDropdown');
dropdown.classList.toggle('hidden');
});

// Mark notification as read in dropdown
document.querySelectorAll('.mark-as-read-btn').forEach(btn => {
btn.addEventListener('click', function(e) {
e.preventDefault();
e.stopPropagation();
const notificationItem = this.closest('.unread');
if (notificationItem) {
notificationItem.classList.remove('unread');
const currentBadgeCount = parseInt(document.getElementById('notificationBadge').textContent);
if (currentBadgeCount > 0) {
document.getElementById('notificationBadge').textContent = currentBadgeCount - 1;
}
showToast('Thông báo đã được đánh dấu là đã đọc');
}
});
});

// Mark all as read in dropdown
document.getElementById('markAllReadDropdown').addEventListener('click', function(e) {
e.preventDefault();
e.stopPropagation();
document.querySelectorAll('#notificationDropdownList .unread').forEach(item => {
item.classList.remove('unread');
});
document.getElementById('notificationBadge').textContent = '0';
showToast('Tất cả thông báo đã được đánh dấu là đã đọc');
});
// Mark as read/unread actions
document.querySelectorAll('.mark-as-read').forEach(btn => {
btn.addEventListener('click', function(e) {
e.preventDefault();
const notificationItem = this.closest('.notification-item');
notificationItem.classList.remove('unread');
showToast('Thông báo đã được đánh dấu là đã đọc');
});
});
document.querySelectorAll('.mark-as-unread').forEach(btn => {
btn.addEventListener('click', function(e) {
e.preventDefault();
const notificationItem = this.closest('.notification-item');
notificationItem.classList.add('unread');
showToast('Thông báo đã được đánh dấu là chưa đọc');
});
});
// Delete notification
document.querySelectorAll('.delete-notification').forEach(btn => {
btn.addEventListener('click', function(e) {
e.preventDefault();
const notificationItem = this.closest('.notification-item');
// Add fade-out animation
notificationItem.style.transition = 'opacity 0.3s ease';
notificationItem.style.opacity = '0';
setTimeout(() => {
notificationItem.remove();
// Update count
const currentCount = parseInt(document.getElementById('notificationCount').textContent);
document.getElementById('notificationCount').textContent = currentCount - 1;
showToast('Thông báo đã được xóa');
}, 300);
});
});
// Toast notification function
function showToast(message) {
// Check if toast container exists
let toastContainer = document.getElementById('toast-container');
if (!toastContainer) {
toastContainer = document.createElement('div');
toastContainer.id = 'toast-container';
toastContainer.className = 'fixed bottom-4 right-4 z-50';
document.body.appendChild(toastContainer);
}
// Create toast
const toast = document.createElement('div');
toast.className = 'bg-primary text-white px-4 py-3 rounded shadow-lg mb-2 flex items-center';
toast.innerHTML = `
<i class="ri-check-line mr-2"></i>
<span>${message}</span>
`;
// Add to container
toastContainer.appendChild(toast);
// Remove after 3 seconds
setTimeout(() => {
toast.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
toast.style.opacity = '0';
toast.style.transform = 'translateX(20px)';
setTimeout(() => {
toast.remove();
}, 300);
}, 3000);
}
});
</script>
</body>
</html>
