<!DOCTYPE html>
<html lang="vi">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>B<PERSON>i viế<PERSON> - <PERSON><PERSON><PERSON><PERSON></title>
<script src="https://cdn.tailwindcss.com/3.4.16"></script>
<script>tailwind.config={theme:{extend:{colors:{primary:'#2E228B',secondary:'#818cf8'},borderRadius:{'none':'0px','sm':'4px',DEFAULT:'8px','md':'12px','lg':'16px','xl':'20px','2xl':'24px','3xl':'32px','full':'9999px','button':'8px'}}}}</script>
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css">
<style>
:where([class^="ri-"])::before { content: "\f3c2"; }
body {
font-family: 'Roboto', sans-serif;
}
.breadcrumb-item:not(:last-child)::after {
content: "/";
margin: 0 0.5rem;
color: #6b7280;
}
.filter-tag.active {
background-color: #2E228B;
color: white;
}
</style>
</head>
<body class="bg-gray-50">
<!-- Header -->
<header class="bg-primary text-white">
<div class="container mx-auto px-4 py-3 flex items-center justify-between">
<div class="flex items-center space-x-10">
<a href="#" class="text-2xl font-['Pacifico'] text-white">logo</a>
<nav class="hidden md:flex space-x-6">
<a href="#" class="hover:text-white/80">Trang chủ</a>
<a href="#" class="hover:text-white/80">Bài viết</a>
<a href="#" class="hover:text-white/80">Tác giả</a>
<a href="#" class="hover:text-white/80">Chủ đề</a>
</nav>
</div>
<div class="flex items-center space-x-4">
<div class="relative">
<div class="w-10 h-10 flex items-center justify-center text-white cursor-pointer">
<i class="ri-notification-3-line ri-lg"></i>
</div>
<span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">1</span>
</div>
<div class="w-10 h-10 rounded-full overflow-hidden">
<img src="https://readdy.ai/api/search-image?query=professional%2520asian%2520man%2520in%2520business%2520suit%252C%2520portrait%2520photo%252C%2520professional%2520headshot%252C%2520high%2520quality%252C%2520realistic&width=100&height=100&seq=1&orientation=squarish" alt="User Avatar" class="w-full h-full object-cover">
</div>
</div>
</div>
</header>
<!-- Breadcrumb -->
<div class="container mx-auto px-4 py-3 flex items-center justify-between">
<div class="flex items-center text-sm text-gray-500">
<a href="#" class="breadcrumb-item hover:text-primary">Trang chủ</a>
<a href="#" class="breadcrumb-item hover:text-primary">Mầm non</a>
<a href="#" class="breadcrumb-item hover:text-primary">Trang cá nhân</a>
<span class="breadcrumb-item text-gray-700">Bài viết</span>
</div>
<div class="text-sm text-gray-500 flex items-center">
<i class="ri-time-line mr-1"></i>
<span>Bài viết mới nhất 28/04/2025</span>
</div>
</div>
<!-- Profile Section -->
<div class="container mx-auto px-4 py-6">
<div class="flex flex-col md:flex-row items-center md:items-start gap-6 pb-6 border-b">
<div class="w-32 h-32 rounded-full overflow-hidden border-4 border-white shadow-lg">
<img src="https://readdy.ai/api/search-image?query=professional%2520asian%2520man%2520in%2520business%2520suit%252C%2520portrait%2520photo%252C%2520professional%2520headshot%252C%2520high%2520quality%252C%2520realistic&width=200&height=200&seq=2&orientation=squarish" alt="Nguyễn Văn Minh" class="w-full h-full object-cover">
</div>
<div class="flex-1 text-center md:text-left">
<h1 class="text-2xl font-bold text-gray-800">Nguyễn Văn Minh</h1>
<p class="text-primary font-medium">Chuyên gia Trí tuệ nhân tạo & Khoa học dữ liệu</p>
<p class="mt-2 text-gray-600">
Tiến sĩ Khoa học máy tính với hơn 15 năm kinh nghiệm nghiên cứu và phát triển trong lĩnh vực AI, Machine Learning và xử lý ngôn ngữ tự nhiên. Hiện đang là Giáo sư tại Đại học Bách Khoa Hà Nội.
</p>
</div>
</div>
<!-- Tabs -->
<div class="border-b mt-4">
<nav class="flex space-x-8">
<a href="https://readdy.ai/home/<USER>/09bf0679-2726-4a74-a098-0c3912645aa1" data-readdy="true" class="text-gray-500 hover:text-primary px-1 py-4">Tổng quan</a>
<a href="#" class="border-b-2 border-primary text-primary px-1 py-4 font-medium">Bài viết</a>
<a href="https://readdy.ai/home/<USER>/11d55c3c-e2ae-422d-b45f-f5d3aaffdc76" data-readdy="true" class="text-gray-500 hover:text-primary px-1 py-4">Giới thiệu</a>
</nav>
</div>
<!-- Filter and Search -->
<div class="mt-6 bg-white rounded shadow p-4">
<div class="flex justify-end">
<div class="flex items-center gap-3">
<div class="relative">
<select class="appearance-none bg-gray-50 border border-gray-300 text-gray-700 py-2 px-4 pr-8 rounded leading-tight focus:outline-none focus:bg-white focus:border-primary transition-colors duration-200">
<option>Mới nhất</option>
<option>Cũ nhất</option>
<option>Xem nhiều nhất</option>
<option>Thích nhiều nhất</option>
</select>
<div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
<i class="ri-arrow-down-s-line"></i>
</div>
</div>
<div class="relative flex-1 min-w-[200px]">
<input type="text" placeholder="Tìm kiếm bài viết" class="w-full bg-gray-50 border border-gray-300 text-gray-700 py-2 px-4 pl-10 rounded leading-tight focus:outline-none focus:bg-white focus:border-primary transition-colors duration-200">
<div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-500">
<i class="ri-search-line"></i>
</div>
</div>
</div>
</div>
</div>
<!-- Posts List -->
<div class="mt-6">
<div class="flex items-center justify-between mb-4">
<h2 class="text-xl font-bold text-gray-800">Tất cả bài viết (48)</h2>
</div>
<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
<!-- Post 1 -->
<div class="bg-white rounded shadow overflow-hidden transition-transform duration-300 hover:-translate-y-1 hover:shadow-md">
<div class="p-6">
<div class="flex items-center mb-2">
<span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded mr-2">Trí tuệ nhân tạo</span>
<span class="text-gray-500 text-sm">28/04/2025</span>
</div>
<h3 class="text-xl font-bold mb-2">Tương lai của mô hình ngôn ngữ lớn (LLM) và ứng dụng trong doanh nghiệp Việt Nam</h3>
<p class="text-gray-600 mb-4 line-clamp-2">Phân tích chi tiết về tiềm năng và thách thức khi triển khai các mô hình ngôn ngữ lớn trong bối cảnh doanh nghiệp tại Việt Nam, cùng các giải pháp khả thi.</p>
<div class="flex items-center text-sm text-gray-500">
<div class="flex items-center mr-4">
<i class="ri-eye-line mr-1"></i>
<span>4,289</span>
</div>
<div class="flex items-center mr-4">
<i class="ri-thumb-up-line mr-1"></i>
<span>342</span>
</div>
<div class="flex items-center">
<i class="ri-message-2-line mr-1"></i>
<span>87</span>
</div>
</div>
</div>
</div>
<!-- Post 2 -->
<div class="bg-white rounded shadow overflow-hidden transition-transform duration-300 hover:-translate-y-1 hover:shadow-md">
<div class="p-6">
<div class="flex items-center mb-2">
<span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded mr-2">Machine Learning</span>
<span class="text-gray-500 text-sm">15/04/2025</span>
</div>
<h3 class="text-xl font-bold mb-2">Kỹ thuật tối ưu hóa mô hình học máy cho dữ liệu không cân bằng</h3>
<p class="text-gray-600 mb-4 line-clamp-2">Nghiên cứu về các phương pháp hiệu quả để xử lý dữ liệu không cân bằng trong các bài toán phân loại, từ kỹ thuật lấy mẫu đến điều chỉnh thuật toán.</p>
<div class="flex items-center text-sm text-gray-500">
<div class="flex items-center mr-4">
<i class="ri-eye-line mr-1"></i>
<span>3,156</span>
</div>
<div class="flex items-center mr-4">
<i class="ri-thumb-up-line mr-1"></i>
<span>287</span>
</div>
<div class="flex items-center">
<i class="ri-message-2-line mr-1"></i>
<span>62</span>
</div>
</div>
</div>
</div>
<!-- Post 3 -->
<div class="bg-white rounded shadow overflow-hidden transition-transform duration-300 hover:-translate-y-1 hover:shadow-md">
<div class="p-6">
<div class="flex items-center mb-2">
<span class="bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded mr-2">NLP</span>
<span class="text-gray-500 text-sm">10/04/2025</span>
</div>
<h3 class="text-xl font-bold mb-2">Phương pháp xử lý ngôn ngữ tự nhiên cho tiếng Việt: Thách thức và giải pháp</h3>
<p class="text-gray-600 mb-4 line-clamp-2">Nghiên cứu về các kỹ thuật và công cụ hiện đại trong xử lý ngôn ngữ tự nhiên cho tiếng Việt, từ tokenization đến mô hình embedding đặc thù.</p>
<div class="flex items-center text-sm text-gray-500">
<div class="flex items-center mr-4">
<i class="ri-eye-line mr-1"></i>
<span>2,845</span>
</div>
<div class="flex items-center mr-4">
<i class="ri-thumb-up-line mr-1"></i>
<span>214</span>
</div>
<div class="flex items-center">
<i class="ri-message-2-line mr-1"></i>
<span>48</span>
</div>
</div>
</div>
</div>
<!-- Post 4 -->
<div class="bg-white rounded shadow overflow-hidden transition-transform duration-300 hover:-translate-y-1 hover:shadow-md">
<div class="p-6">
<div class="flex items-center mb-2">
<span class="bg-red-100 text-red-800 text-xs px-2 py-1 rounded mr-2">Deep Learning</span>
<span class="text-gray-500 text-sm">05/04/2025</span>
</div>
<h3 class="text-xl font-bold mb-2">Kiến trúc mạng nơ-ron sâu cho nhận dạng đối tượng trong ảnh y tế</h3>
<p class="text-gray-600 mb-4 line-clamp-2">Nghiên cứu về các kiến trúc mạng nơ-ron sâu hiệu quả cho việc phát hiện và phân loại các bất thường trong ảnh y tế, với trường hợp nghiên cứu cụ thể tại Việt Nam.</p>
<div class="flex items-center text-sm text-gray-500">
<div class="flex items-center mr-4">
<i class="ri-eye-line mr-1"></i>
<span>2,367</span>
</div>
<div class="flex items-center mr-4">
<i class="ri-thumb-up-line mr-1"></i>
<span>198</span>
</div>
<div class="flex items-center">
<i class="ri-message-2-line mr-1"></i>
<span>42</span>
</div>
</div>
</div>
</div>
<!-- Post 5 -->
<div class="bg-white rounded shadow overflow-hidden transition-transform duration-300 hover:-translate-y-1 hover:shadow-md">
<div class="p-6">
<div class="flex items-center mb-2">
<span class="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded mr-2">Computer Vision</span>
<span class="text-gray-500 text-sm">28/03/2025</span>
</div>
<h3 class="text-xl font-bold mb-2">Ứng dụng thị giác máy tính trong hệ thống giao thông thông minh tại Hà Nội</h3>
<p class="text-gray-600 mb-4 line-clamp-2">Phân tích và đánh giá các giải pháp thị giác máy tính trong việc giám sát và điều khiển giao thông tại Hà Nội, từ phát hiện vi phạm đến tối ưu hóa luồng giao thông.</p>
<div class="flex items-center text-sm text-gray-500">
<div class="flex items-center mr-4">
<i class="ri-eye-line mr-1"></i>
<span>2,134</span>
</div>
<div class="flex items-center mr-4">
<i class="ri-thumb-up-line mr-1"></i>
<span>176</span>
</div>
<div class="flex items-center">
<i class="ri-message-2-line mr-1"></i>
<span>38</span>
</div>
</div>
</div>
</div>
<!-- Post 6 -->
<div class="bg-white rounded shadow overflow-hidden transition-transform duration-300 hover:-translate-y-1 hover:shadow-md">
<div class="p-6">
<div class="flex items-center mb-2">
<span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded mr-2">Transformer</span>
<span class="text-gray-500 text-sm">20/03/2025</span>
</div>
<h3 class="text-xl font-bold mb-2">Phân tích cơ chế Attention trong kiến trúc Transformer và ứng dụng</h3>
<p class="text-gray-600 mb-4 line-clamp-2">Nghiên cứu chuyên sâu về cơ chế Attention trong kiến trúc Transformer, phân tích các biến thể và ứng dụng trong các mô hình ngôn ngữ hiện đại.</p>
<div class="flex items-center text-sm text-gray-500">
<div class="flex items-center mr-4">
<i class="ri-eye-line mr-1"></i>
<span>1,987</span>
</div>
<div class="flex items-center mr-4">
<i class="ri-thumb-up-line mr-1"></i>
<span>165</span>
</div>
<div class="flex items-center">
<i class="ri-message-2-line mr-1"></i>
<span>35</span>
</div>
</div>
</div>
</div>
<!-- Post 7 -->
<div class="bg-white rounded shadow overflow-hidden transition-transform duration-300 hover:-translate-y-1 hover:shadow-md">
<div class="p-6">
<div class="flex items-center mb-2">
<span class="bg-pink-100 text-pink-800 text-xs px-2 py-1 rounded mr-2">BERT</span>
<span class="text-gray-500 text-sm">15/03/2025</span>
</div>
<h3 class="text-xl font-bold mb-2">Fine-tuning BERT cho các tác vụ phân loại văn bản tiếng Việt</h3>
<p class="text-gray-600 mb-4 line-clamp-2">Hướng dẫn chi tiết về quy trình fine-tuning mô hình BERT cho các tác vụ phân loại văn bản tiếng Việt, từ chuẩn bị dữ liệu đến đánh giá hiệu suất.</p>
<div class="flex items-center text-sm text-gray-500">
<div class="flex items-center mr-4">
<i class="ri-eye-line mr-1"></i>
<span>1,876</span>
</div>
<div class="flex items-center mr-4">
<i class="ri-thumb-up-line mr-1"></i>
<span>154</span>
</div>
<div class="flex items-center">
<i class="ri-message-2-line mr-1"></i>
<span>32</span>
</div>
</div>
</div>
</div>
<!-- Post 8 -->
<div class="bg-white rounded shadow overflow-hidden transition-transform duration-300 hover:-translate-y-1 hover:shadow-md">
<div class="p-6">
<div class="flex items-center mb-2">
<span class="bg-indigo-100 text-indigo-800 text-xs px-2 py-1 rounded mr-2">Neural Networks</span>
<span class="text-gray-500 text-sm">10/03/2025</span>
</div>
<h3 class="text-xl font-bold mb-2">Tối ưu hóa hiệu suất mạng nơ-ron với kỹ thuật regularization hiện đại</h3>
<p class="text-gray-600 mb-4 line-clamp-2">Nghiên cứu về các kỹ thuật regularization hiện đại để cải thiện khả năng tổng quát hóa của mạng nơ-ron, bao gồm Dropout, BatchNorm và các biến thể mới nhất.</p>
<div class="flex items-center text-sm text-gray-500">
<div class="flex items-center mr-4">
<i class="ri-eye-line mr-1"></i>
<span>1,765</span>
</div>
<div class="flex items-center mr-4">
<i class="ri-thumb-up-line mr-1"></i>
<span>143</span>
</div>
<div class="flex items-center">
<i class="ri-message-2-line mr-1"></i>
<span>29</span>
</div>
</div>
</div>
</div>
</div>
<!-- Pagination -->
<div class="mt-8 flex justify-center">
<nav class="flex items-center space-x-2">
<a href="#" class="px-3 py-2 rounded border border-gray-300 text-gray-500 hover:bg-gray-50 hover:text-primary transition-colors duration-200 !rounded-button whitespace-nowrap">
<i class="ri-arrow-left-s-line"></i>
</a>
<a href="#" class="px-3 py-2 rounded border border-primary bg-primary text-white transition-colors duration-200 !rounded-button whitespace-nowrap">1</a>
<a href="#" class="px-3 py-2 rounded border border-gray-300 text-gray-700 hover:bg-gray-50 hover:text-primary transition-colors duration-200 !rounded-button whitespace-nowrap">2</a>
<a href="#" class="px-3 py-2 rounded border border-gray-300 text-gray-700 hover:bg-gray-50 hover:text-primary transition-colors duration-200 !rounded-button whitespace-nowrap">3</a>
<span class="px-3 py-2 text-gray-500">...</span>
<a href="#" class="px-3 py-2 rounded border border-gray-300 text-gray-700 hover:bg-gray-50 hover:text-primary transition-colors duration-200 !rounded-button whitespace-nowrap">6</a>
<a href="#" class="px-3 py-2 rounded border border-gray-300 text-gray-500 hover:bg-gray-50 hover:text-primary transition-colors duration-200 !rounded-button whitespace-nowrap">
<i class="ri-arrow-right-s-line"></i>
</a>
</nav>
</div>
</div>
</div>
<script>
</script>
</body>
</html>
