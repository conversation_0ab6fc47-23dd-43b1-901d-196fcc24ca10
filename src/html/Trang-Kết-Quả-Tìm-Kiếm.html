<!DOCTYPE html>
<html lang="vi">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title><PERSON><PERSON><PERSON> qu<PERSON> tìm k<PERSON>ế<PERSON></title>
<script src="https://cdn.tailwindcss.com/3.4.16"></script>
<script>tailwind.config={theme:{extend:{colors:{primary:'#2e2e8b',secondary:'#4b5563'},borderRadius:{'none':'0px','sm':'4px',DEFAULT:'8px','md':'12px','lg':'16px','xl':'20px','2xl':'24px','3xl':'32px','full':'9999px','button':'8px'}}}}</script>
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css" rel="stylesheet">
<style>
:where([class^="ri-"])::before { content: "\f3c2"; }
body {
font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
-webkit-appearance: none;
margin: 0;
}
.custom-checkbox {
position: relative;
padding-left: 28px;
cursor: pointer;
user-select: none;
}
.custom-checkbox input {
position: absolute;
opacity: 0;
cursor: pointer;
height: 0;
width: 0;
}
.checkmark {
position: absolute;
top: 0;
left: 0;
height: 18px;
width: 18px;
background-color: #fff;
border: 1px solid #d1d5db;
border-radius: 4px;
}
.custom-checkbox:hover input ~ .checkmark {
background-color: #f3f4f6;
}
.custom-checkbox input:checked ~ .checkmark {
background-color: #2e2e8b;
border-color: #2e2e8b;
}
.checkmark:after {
content: "";
position: absolute;
display: none;
}
.custom-checkbox input:checked ~ .checkmark:after {
display: block;
}
.custom-checkbox .checkmark:after {
left: 6px;
top: 2px;
width: 5px;
height: 10px;
border: solid white;
border-width: 0 2px 2px 0;
transform: rotate(45deg);
}
</style>
</head>
<body class="bg-gray-50">
<!-- Header Navigation -->
<header class="bg-primary text-white">
<div class="container mx-auto px-4 py-3 flex items-center justify-between">
<div class="flex items-center space-x-8">
<a href="#" class="flex items-center text-xl font-['Pacifico']">
<div class="w-8 h-8 flex items-center justify-center mr-2">
<i class="ri-global-line ri-lg"></i>
</div>
<span>logo</span>
</a>
<nav class="hidden md:flex space-x-6">
<div class="relative group">
<button class="hover:text-gray-200 text-sm font-medium flex items-center">
Giới thiệu
<div class="w-4 h-4 flex items-center justify-center ml-1">
<i class="ri-arrow-down-s-line"></i>
</div>
</button>
<div class="absolute top-full left-0 mt-1 w-48 bg-white rounded shadow-lg border border-gray-100 py-1 hidden group-hover:block z-50">
<a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">Tổng quan</a>
<a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">Lịch sử phát triển</a>
<a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">Sứ mệnh & Tầm nhìn</a>
<a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">Đội ngũ</a>
</div>
</div>
<div class="relative group">
<button class="hover:text-gray-200 text-sm font-medium flex items-center">
Tin tức
<div class="w-4 h-4 flex items-center justify-center ml-1">
<i class="ri-arrow-down-s-line"></i>
</div>
</button>
<div class="absolute top-full left-0 mt-1 w-48 bg-white rounded shadow-lg border border-gray-100 py-1 hidden group-hover:block z-50">
<a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">Tin hoạt động</a>
<a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">Tin giáo dục</a>
<a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">Thông báo</a>
<a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">Sự kiện</a>
</div>
</div>
<div class="relative group">
<button class="hover:text-gray-200 text-sm font-medium flex items-center">
Thống kê
<div class="w-4 h-4 flex items-center justify-center ml-1">
<i class="ri-arrow-down-s-line"></i>
</div>
</button>
<div class="absolute top-full left-0 mt-1 w-48 bg-white rounded shadow-lg border border-gray-100 py-1 hidden group-hover:block z-50">
<a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">Báo cáo tổng hợp</a>
<a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">Số liệu thống kê</a>
<a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">Biểu đồ phân tích</a>
</div>
</div>
<a href="#" id="forumLink" class="text-gray-200 text-sm font-medium">Diễn đàn</a>
<div class="relative group">
<button class="hover:text-gray-200 text-sm font-medium flex items-center">
Tài nguyên
<div class="w-4 h-4 flex items-center justify-center ml-1">
<i class="ri-arrow-down-s-line"></i>
</div>
</button>
<div class="absolute top-full left-0 mt-1 w-48 bg-white rounded shadow-lg border border-gray-100 py-1 hidden group-hover:block z-50">
<a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">Tài liệu học tập</a>
<a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">Giáo trình</a>
<a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">Bài giảng</a>
<a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">Đề thi mẫu</a>
</div>
</div>
<div class="relative group">
<button class="hover:text-gray-200 text-sm font-medium flex items-center">
Xếp hạng
<div class="w-4 h-4 flex items-center justify-center ml-1">
<i class="ri-arrow-down-s-line"></i>
</div>
</button>
<div class="absolute top-full left-0 mt-1 w-48 bg-white rounded shadow-lg border border-gray-100 py-1 hidden group-hover:block z-50">
<a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">Bảng xếp hạng</a>
<a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">Thành tích</a>
<a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">Giải thưởng</a>
</div>
</div>
<div class="relative group">
<button class="hover:text-gray-200 text-sm font-medium flex items-center">
Khảo sát
<div class="w-4 h-4 flex items-center justify-center ml-1">
<i class="ri-arrow-down-s-line"></i>
</div>
</button>
<div class="absolute top-full left-0 mt-1 w-48 bg-white rounded shadow-lg border border-gray-100 py-1 hidden group-hover:block z-50">
<a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">Khảo sát đang diễn ra</a>
<a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">Kết quả khảo sát</a>
<a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">Tạo khảo sát mới</a>
</div>
</div>
</nav>
</div>
<div>
<a href="#" class="text-sm font-medium hover:text-gray-200">Đăng nhập</a>
</div>
</div>
</header>
<!-- Breadcrumb -->
<div class="border-b">
<div class="container mx-auto px-4 py-2">
<div class="flex items-center text-sm text-gray-500">
<a href="#" class="hover:text-primary">Trang chủ</a>
<span class="mx-2">/</span>
<a href="#" class="hover:text-primary">Diễn đàn</a>
<span class="mx-2">/</span>
<span class="text-gray-700">Kết quả tìm kiếm: Học Tập</span>
</div>
</div>
</div>
<!-- Main Content -->
<div class="container mx-auto px-4 py-6">
<!-- Search Bar -->
<div class="bg-white rounded-lg shadow-sm p-4 mb-6">
<div class="relative">
<input type="text" value="Học Tập" class="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
<div class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 flex items-center justify-center text-gray-400">
<i class="ri-search-line"></i>
</div>
<button class="absolute right-3 top-1/2 transform -translate-y-1/2 bg-primary text-white px-4 py-1 rounded-button text-sm whitespace-nowrap">Tìm kiếm</button>
</div>
</div>
<!-- Filter Categories -->
<div class="bg-white rounded-lg shadow-sm p-4 mb-6 overflow-x-auto">
<div class="flex items-center space-x-3 min-w-max">
<span class="text-sm font-medium text-gray-500">Diễn đàn:</span>
<div class="flex items-center space-x-2">
<button class="px-3 py-1.5 bg-primary text-white text-sm rounded-full whitespace-nowrap">Tất cả</button>
<button class="px-3 py-1.5 bg-gray-100 text-gray-700 text-sm rounded-full hover:bg-gray-200 whitespace-nowrap">Diễn đàn công nghệ</button>
<button class="px-3 py-1.5 bg-gray-100 text-gray-700 text-sm rounded-full hover:bg-gray-200 whitespace-nowrap">Diễn đàn học tập</button>
<button class="px-3 py-1.5 bg-gray-100 text-gray-700 text-sm rounded-full hover:bg-gray-200 whitespace-nowrap">Diễn đàn đổi mới</button>
<button class="px-3 py-1.5 bg-gray-100 text-gray-700 text-sm rounded-full hover:bg-gray-200 whitespace-nowrap">Diễn đàn trò chơi</button>
</div>
</div>
</div>
<div class="flex flex-col lg:flex-row gap-6">
<!-- Left Sidebar - Filters -->
<div class="w-full lg:w-1/4">
<div class="bg-white rounded-lg shadow-sm p-4 mb-6">
<h3 class="font-medium mb-4">Bộ lọc tìm kiếm</h3>
<!-- Filter by Topic -->
<div class="mb-6">
<h4 class="text-sm font-medium text-gray-700 mb-3">Chủ đề</h4>
<div class="space-y-2">
<label class="custom-checkbox flex items-center text-sm text-gray-600">
<input type="checkbox" checked>
<span class="checkmark"></span>
Phương pháp học tập
</label>
<label class="custom-checkbox flex items-center text-sm text-gray-600">
<input type="checkbox">
<span class="checkmark"></span>
Kỹ năng mềm
</label>
<label class="custom-checkbox flex items-center text-sm text-gray-600">
<input type="checkbox">
<span class="checkmark"></span>
Công nghệ giáo dục
</label>
<label class="custom-checkbox flex items-center text-sm text-gray-600">
<input type="checkbox">
<span class="checkmark"></span>
Tài liệu học tập
</label>
<label class="custom-checkbox flex items-center text-sm text-gray-600">
<input type="checkbox">
<span class="checkmark"></span>
Chia sẻ kinh nghiệm
</label>
</div>
</div>
<!-- Filter by Time -->
<div class="mb-6">
<h4 class="text-sm font-medium text-gray-700 mb-3">Thời gian đăng</h4>
<div class="space-y-2">
<label class="custom-checkbox flex items-center text-sm text-gray-600">
<input type="checkbox">
<span class="checkmark"></span>
Hôm nay
</label>
<label class="custom-checkbox flex items-center text-sm text-gray-600">
<input type="checkbox" checked>
<span class="checkmark"></span>
Tuần này
</label>
<label class="custom-checkbox flex items-center text-sm text-gray-600">
<input type="checkbox">
<span class="checkmark"></span>
Tháng này
</label>
<label class="custom-checkbox flex items-center text-sm text-gray-600">
<input type="checkbox">
<span class="checkmark"></span>
3 tháng gần đây
</label>
<label class="custom-checkbox flex items-center text-sm text-gray-600">
<input type="checkbox">
<span class="checkmark"></span>
Năm nay
</label>
</div>
</div>
<!-- Filter by Views/Comments -->
<div class="mb-6">
<h4 class="text-sm font-medium text-gray-700 mb-3">Lượt xem/bình luận</h4>
<div class="space-y-2">
<label class="custom-checkbox flex items-center text-sm text-gray-600">
<input type="checkbox">
<span class="checkmark"></span>
Trên 1000 lượt xem
</label>
<label class="custom-checkbox flex items-center text-sm text-gray-600">
<input type="checkbox" checked>
<span class="checkmark"></span>
Trên 100 bình luận
</label>
<label class="custom-checkbox flex items-center text-sm text-gray-600">
<input type="checkbox">
<span class="checkmark"></span>
Bài viết nổi bật
</label>
</div>
</div>
<div class="pt-4 border-t flex justify-between">
<button class="text-sm text-gray-500 hover:text-gray-700">Xóa bộ lọc</button>
<button class="text-sm text-primary hover:text-primary/80">Áp dụng</button>
</div>
</div>
</div>
<!-- Right Content - Search Results -->
<div class="w-full lg:w-3/4">
<!-- View Toggle and Sort -->
<div class="bg-white rounded-lg shadow-sm p-4 mb-6 flex justify-between items-center">
<div class="text-sm text-gray-500">
Hiển thị <span class="font-medium text-gray-700">24</span> kết quả cho <span class="font-medium text-gray-700">"Học Tập"</span>
</div>
<div class="flex items-center space-x-4">
<div class="flex items-center space-x-2">
<span class="text-sm text-gray-500">Sắp xếp:</span>
<div class="relative">
<button class="text-sm text-gray-700 flex items-center border rounded px-2 py-1">
Mới nhất
<div class="w-4 h-4 flex items-center justify-center ml-1">
<i class="ri-arrow-down-s-line"></i>
</div>
</button>
</div>
</div>
</div>
</div>
<!-- Grid View Results -->
<div id="gridView" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
<!-- Result Item 1 -->
<div class="bg-white rounded-lg shadow-sm overflow-hidden">
<div class="p-4">
<div class="flex items-center text-xs text-gray-500 mb-2">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-folder-line"></i>
</div>
<span>Diễn đàn học tập</span>
</div>
<h3 class="font-medium mb-2 line-clamp-2">
<a href="#" class="hover:text-primary">Phương pháp học tập hiệu quả cho sinh viên năm nhất</a>
</h3>
<p class="text-sm text-gray-600 mb-3 line-clamp-3">
Bài viết chia sẻ các phương pháp học tập hiệu quả giúp sinh viên năm nhất thích nghi nhanh chóng với môi trường đại học, quản lý thời gian và áp dụng các kỹ thuật ghi nhớ hiệu quả.
</p>
<div class="flex items-center justify-between text-xs text-gray-500">
<div class="flex items-center">
<div class="w-6 h-6 rounded-full bg-gray-200 flex items-center justify-center mr-1">
<i class="ri-user-line"></i>
</div>
<span>Nguyễn Minh Tuấn</span>
</div>
<div>15 giờ trước</div>
</div>
</div>
<div class="bg-gray-50 px-4 py-2 flex justify-between items-center">
<div class="flex items-center space-x-3 text-xs text-gray-500">
<div class="flex items-center">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-eye-line"></i>
</div>
<span>1.2k</span>
</div>
<div class="flex items-center">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-message-2-line"></i>
</div>
<span>45</span>
</div>
</div>
<a href="#" class="text-primary text-xs hover:underline">Xem chi tiết</a>
</div>
</div>
<!-- Result Item 2 -->
<div class="bg-white rounded-lg shadow-sm overflow-hidden">
<div class="p-4">
<div class="flex items-center text-xs text-gray-500 mb-2">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-folder-line"></i>
</div>
<span>Diễn đàn học tập</span>
</div>
<h3 class="font-medium mb-2 line-clamp-2">
<a href="#" class="hover:text-primary">Kỹ năng quản lý thời gian trong học tập</a>
</h3>
<p class="text-sm text-gray-600 mb-3 line-clamp-3">
Bài viết hướng dẫn các kỹ thuật quản lý thời gian hiệu quả, giúp học sinh và sinh viên cân bằng giữa học tập, hoạt động ngoại khóa và thời gian nghỉ ngơi để đạt kết quả tốt nhất.
</p>
<div class="flex items-center justify-between text-xs text-gray-500">
<div class="flex items-center">
<div class="w-6 h-6 rounded-full bg-gray-200 flex items-center justify-center mr-1">
<i class="ri-user-line"></i>
</div>
<span>Trần Thanh Hương</span>
</div>
<div>3 giờ trước</div>
</div>
</div>
<div class="bg-gray-50 px-4 py-2 flex justify-between items-center">
<div class="flex items-center space-x-3 text-xs text-gray-500">
<div class="flex items-center">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-eye-line"></i>
</div>
<span>845</span>
</div>
<div class="flex items-center">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-message-2-line"></i>
</div>
<span>23</span>
</div>
</div>
<a href="#" class="text-primary text-xs hover:underline">Xem chi tiết</a>
</div>
</div>
<!-- Result Item 3 -->
<div class="bg-white rounded-lg shadow-sm overflow-hidden">
<div class="p-4">
<div class="flex items-center text-xs text-gray-500 mb-2">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-folder-line"></i>
</div>
<span>Diễn đàn học tập</span>
</div>
<h3 class="font-medium mb-2 line-clamp-2">
<a href="#" class="hover:text-primary">Làm thế nào để duy trì động lực học tập</a>
</h3>
<p class="text-sm text-gray-600 mb-3 line-clamp-3">
Bài viết chia sẻ các phương pháp duy trì động lực học tập trong thời gian dài, vượt qua những khó khăn và áp lực, tạo thói quen học tập tích cực và hiệu quả.
</p>
<div class="flex items-center justify-between text-xs text-gray-500">
<div class="flex items-center">
<div class="w-6 h-6 rounded-full bg-gray-200 flex items-center justify-center mr-1">
<i class="ri-user-line"></i>
</div>
<span>Lê Văn Nam</span>
</div>
<div>5 giờ trước</div>
</div>
</div>
<div class="bg-gray-50 px-4 py-2 flex justify-between items-center">
<div class="flex items-center space-x-3 text-xs text-gray-500">
<div class="flex items-center">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-eye-line"></i>
</div>
<span>1.5k</span>
</div>
<div class="flex items-center">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-message-2-line"></i>
</div>
<span>34</span>
</div>
</div>
<a href="#" class="text-primary text-xs hover:underline">Xem chi tiết</a>
</div>
</div>
<!-- Result Item 4 -->
<div class="bg-white rounded-lg shadow-sm overflow-hidden">
<div class="p-4">
<div class="flex items-center text-xs text-gray-500 mb-2">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-folder-line"></i>
</div>
<span>Diễn đàn học tập</span>
</div>
<h3 class="font-medium mb-2 line-clamp-2">
<a href="#" class="hover:text-primary">Phương pháp ghi chú hiệu quả trong giờ học</a>
</h3>
<p class="text-sm text-gray-600 mb-3 line-clamp-3">
Bài viết hướng dẫn các kỹ thuật ghi chú hiệu quả như phương pháp Cornell, Mind Mapping, và các công cụ số hóa giúp sinh viên tối ưu hóa việc ghi chép và ôn tập.
</p>
<div class="flex items-center justify-between text-xs text-gray-500">
<div class="flex items-center">
<div class="w-6 h-6 rounded-full bg-gray-200 flex items-center justify-center mr-1">
<i class="ri-user-line"></i>
</div>
<span>Phạm Thu Hà</span>
</div>
<div>1 ngày trước</div>
</div>
</div>
<div class="bg-gray-50 px-4 py-2 flex justify-between items-center">
<div class="flex items-center space-x-3 text-xs text-gray-500">
<div class="flex items-center">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-eye-line"></i>
</div>
<span>2.3k</span>
</div>
<div class="flex items-center">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-message-2-line"></i>
</div>
<span>56</span>
</div>
</div>
<a href="#" class="text-primary text-xs hover:underline">Xem chi tiết</a>
</div>
</div>
<!-- Result Item 5 -->
<div class="bg-white rounded-lg shadow-sm overflow-hidden">
<div class="p-4">
<div class="flex items-center text-xs text-gray-500 mb-2">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-folder-line"></i>
</div>
<span>Diễn đàn công nghệ</span>
</div>
<h3 class="font-medium mb-2 line-clamp-2">
<a href="#" class="hover:text-primary">Ứng dụng công nghệ AI trong học tập hiện đại</a>
</h3>
<p class="text-sm text-gray-600 mb-3 line-clamp-3">
Bài viết giới thiệu các ứng dụng trí tuệ nhân tạo hỗ trợ học tập như trợ lý ảo, phần mềm tạo tóm tắt, công cụ phân tích dữ liệu và cách áp dụng hiệu quả.
</p>
<div class="flex items-center justify-between text-xs text-gray-500">
<div class="flex items-center">
<div class="w-6 h-6 rounded-full bg-gray-200 flex items-center justify-center mr-1">
<i class="ri-user-line"></i>
</div>
<span>Đặng Minh Quân</span>
</div>
<div>2 ngày trước</div>
</div>
</div>
<div class="bg-gray-50 px-4 py-2 flex justify-between items-center">
<div class="flex items-center space-x-3 text-xs text-gray-500">
<div class="flex items-center">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-eye-line"></i>
</div>
<span>1.8k</span>
</div>
<div class="flex items-center">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-message-2-line"></i>
</div>
<span>42</span>
</div>
</div>
<a href="#" class="text-primary text-xs hover:underline">Xem chi tiết</a>
</div>
</div>
<!-- Result Item 6 -->
<div class="bg-white rounded-lg shadow-sm overflow-hidden">
<div class="p-4">
<div class="flex items-center text-xs text-gray-500 mb-2">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-folder-line"></i>
</div>
<span>Diễn đàn học tập</span>
</div>
<h3 class="font-medium mb-2 line-clamp-2">
<a href="#" class="hover:text-primary">Học nhóm hiệu quả: Phương pháp và lợi ích</a>
</h3>
<p class="text-sm text-gray-600 mb-3 line-clamp-3">
Bài viết phân tích lợi ích của việc học nhóm, cách tổ chức nhóm học tập hiệu quả, phân công nhiệm vụ và giải quyết các vấn đề thường gặp khi học nhóm.
</p>
<div class="flex items-center justify-between text-xs text-gray-500">
<div class="flex items-center">
<div class="w-6 h-6 rounded-full bg-gray-200 flex items-center justify-center mr-1">
<i class="ri-user-line"></i>
</div>
<span>Hoàng Thị Mai</span>
</div>
<div>3 ngày trước</div>
</div>
</div>
<div class="bg-gray-50 px-4 py-2 flex justify-between items-center">
<div class="flex items-center space-x-3 text-xs text-gray-500">
<div class="flex items-center">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-eye-line"></i>
</div>
<span>975</span>
</div>
<div class="flex items-center">
<div class="w-4 h-4 flex items-center justify-center mr-1">
<i class="ri-message-2-line"></i>
</div>
<span>28</span>
</div>
</div>
<a href="#" class="text-primary text-xs hover:underline">Xem chi tiết</a>
</div>
</div>
</div>
<!-- Pagination -->
<div class="mt-6 flex items-center justify-between">
<div class="text-sm text-gray-500">Hiển thị 1-6 trong tổng số 24 kết quả</div>
<div class="flex items-center space-x-1">
<a href="#" class="w-8 h-8 flex items-center justify-center rounded border text-gray-500 hover:bg-gray-50">
<i class="ri-arrow-left-s-line"></i>
</a>
<a href="#" class="w-8 h-8 flex items-center justify-center rounded border bg-primary text-white">1</a>
<a href="#" class="w-8 h-8 flex items-center justify-center rounded border text-gray-500 hover:bg-gray-50">2</a>
<a href="#" class="w-8 h-8 flex items-center justify-center rounded border text-gray-500 hover:bg-gray-50">3</a>
<a href="#" class="w-8 h-8 flex items-center justify-center rounded border text-gray-500 hover:bg-gray-50">4</a>
<a href="#" class="w-8 h-8 flex items-center justify-center rounded border text-gray-500 hover:bg-gray-50">
<i class="ri-arrow-right-s-line"></i>
</a>
</div>
</div>
</div>
</div>
</div>
<!-- Footer -->
<footer class="bg-white border-t mt-8">
<div class="container mx-auto px-4 py-8">
<div class="grid grid-cols-1 md:grid-cols-2 gap-8">
<!-- Contact Info -->
<div>
<h3 class="text-lg font-medium mb-4">Liên hệ</h3>
<div class="space-y-2 text-gray-600 text-sm">
<p>Trụ sở chính: Số 98 Phố Dương Quảng Hàm, Phường Quan Hoa, Quận Cầu Giấy, TP. Hà Nội</p>
<p>(+84) 24.3833.0708</p>
<p>(+84) 24.3833.5426</p>
<p><EMAIL></p>
</div>
</div>
<!-- Social Links -->
<div>
<h3 class="text-lg font-medium mb-4">Mạng xã hội</h3>
<div class="flex space-x-4">
<a href="#" class="text-gray-600 hover:text-primary flex items-center">
<div class="w-5 h-5 flex items-center justify-center mr-2">
<i class="ri-facebook-fill"></i>
</div>
Facebook
</a>
<a href="#" class="text-gray-600 hover:text-primary flex items-center">
<div class="w-5 h-5 flex items-center justify-center mr-2">
<i class="ri-youtube-fill"></i>
</div>
YouTube
</a>
<a href="#" class="text-gray-600 hover:text-primary flex items-center">
<div class="w-5 h-5 flex items-center justify-center mr-2">
<i class="ri-tiktok-fill"></i>
</div>
TikTok
</a>
</div>
</div>
</div>
<!-- Copyright -->
<div class="border-t mt-8 pt-4 text-sm text-gray-500">
<p>Copyright ©2025 Đại học Thủ đô Hà Nội</p>
</div>
</div>
</footer>
<script>
document.addEventListener('DOMContentLoaded', function() {
// Checkbox functionality
const checkboxes = document.querySelectorAll('.custom-checkbox input[type="checkbox"]');
checkboxes.forEach(checkbox => {
checkbox.addEventListener('change', function() {
// Here you would typically filter results based on the selected checkboxes
// For demo purposes, we're just logging the change
console.log('Filter changed:', this.parentElement.textContent.trim(), 'is', this.checked);
});
});
});
</script>
</body>
</html>