<!DOCTYPE html>
<html lang="vi">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>Trang cá nhân - <PERSON><PERSON><PERSON><PERSON></title>
<script src="https://cdn.tailwindcss.com/3.4.16"></script>
<script>tailwind.config={theme:{extend:{colors:{primary:'#2E228B',secondary:'#818cf8'},borderRadius:{'none':'0px','sm':'4px',DEFAULT:'8px','md':'12px','lg':'16px','xl':'20px','2xl':'24px','3xl':'32px','full':'9999px','button':'8px'}}}}</script>
<script>
document.addEventListener('DOMContentLoaded', function() {
const notificationButton = document.getElementById('notificationButton');
const notificationDropdown = document.getElementById('notificationDropdown');
const notificationBadge = document.getElementById('notificationBadge');
const markAllRead = document.getElementById('markAllRead');
let isDropdownOpen = false;
notificationButton.addEventListener('click', function(e) {
e.stopPropagation();
isDropdownOpen = !isDropdownOpen;
notificationDropdown.classList.toggle('hidden');
});
document.addEventListener('click', function(e) {
if (!notificationDropdown.contains(e.target) && isDropdownOpen) {
notificationDropdown.classList.add('hidden');
isDropdownOpen = false;
}
});
markAllRead.addEventListener('click', function(e) {
e.stopPropagation();
const unreadDots = document.querySelectorAll('.notification-item .bg-blue-500');
unreadDots.forEach(dot => dot.remove());
notificationBadge.textContent = '0';
notificationBadge.classList.add('hidden');
});
document.querySelectorAll('.notification-item').forEach(item => {
item.addEventListener('click', function() {
const unreadDot = this.querySelector('.bg-blue-500');
if (unreadDot) {
unreadDot.remove();
const currentCount = parseInt(notificationBadge.textContent);
notificationBadge.textContent = currentCount - 1;
if (currentCount - 1 <= 0) {
notificationBadge.classList.add('hidden');
}
}
});
});
});
</script>
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css">
<style>
:where([class^="ri-"])::before { content: "\f3c2"; }
body {
font-family: 'Roboto', sans-serif;
}
.breadcrumb-item:not(:last-child)::after {
content: "/";
margin: 0 0.5rem;
color: #6b7280;
}
</style>
</head>
<body class="bg-gray-50">
<!-- Header -->
<header class="bg-primary text-white">
<div class="container mx-auto px-4 py-3 flex items-center justify-between">
<div class="flex items-center space-x-10">
<a href="#" class="text-2xl font-['Pacifico'] text-white">logo</a>
<nav class="hidden md:flex space-x-6">
<a href="#" class="hover:text-white/80">Trang chủ</a>
<a href="#" class="hover:text-white/80">Bài viết</a>
<a href="#" class="hover:text-white/80">Tác giả</a>
<a href="#" class="hover:text-white/80">Chủ đề</a>
</nav>
</div>
<div class="flex items-center space-x-4">
<div class="relative">
<div id="notificationButton" class="w-10 h-10 flex items-center justify-center text-white cursor-pointer">
<i class="ri-notification-3-line ri-lg"></i>
</div>
<span id="notificationBadge" class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">3</span>
<div id="notificationDropdown" class="hidden absolute right-0 top-12 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
<div class="p-4 border-b border-gray-100">
<div class="flex items-center justify-between">
<h3 class="text-lg font-semibold text-gray-800">Thông báo</h3>
<button id="markAllRead" class="text-sm text-primary hover:underline">Đánh dấu đã đọc</button>
</div>
</div>
<div class="max-h-96 overflow-y-auto">
<div class="notification-item p-4 hover:bg-gray-50 border-b border-gray-100 cursor-pointer">
<div class="flex items-start space-x-3">
<div class="w-8 h-8 flex items-center justify-center bg-blue-100 rounded-full">
<i class="ri-message-2-line text-blue-500"></i>
</div>
<div class="flex-1">
<p class="text-sm text-gray-800">Nguyễn Thị Hương đã bình luận về bài viết của bạn</p>
<p class="text-xs text-gray-500 mt-1">2 phút trước</p>
</div>
<div class="w-2 h-2 bg-blue-500 rounded-full"></div>
</div>
</div>
<div class="notification-item p-4 hover:bg-gray-50 border-b border-gray-100 cursor-pointer">
<div class="flex items-start space-x-3">
<div class="w-8 h-8 flex items-center justify-center bg-red-100 rounded-full">
<i class="ri-heart-line text-red-500"></i>
</div>
<div class="flex-1">
<p class="text-sm text-gray-800">Trần Văn Đức và 5 người khác đã thích bài viết của bạn</p>
<p class="text-xs text-gray-500 mt-1">15 phút trước</p>
</div>
<div class="w-2 h-2 bg-blue-500 rounded-full"></div>
</div>
</div>
<div class="notification-item p-4 hover:bg-gray-50 border-b border-gray-100 cursor-pointer">
<div class="flex items-start space-x-3">
<div class="w-8 h-8 flex items-center justify-center bg-green-100 rounded-full">
<i class="ri-user-follow-line text-green-500"></i>
</div>
<div class="flex-1">
<p class="text-sm text-gray-800">Lê Minh Tuấn đã bắt đầu theo dõi bạn</p>
<p class="text-xs text-gray-500 mt-1">1 giờ trước</p>
</div>
<div class="w-2 h-2 bg-blue-500 rounded-full"></div>
</div>
</div>
</div>
<div class="p-4 border-t border-gray-100">
<a href="https://readdy.ai/home/<USER>/f903a75d-748a-4f2d-bf03-8a6b2e310da9" data-readdy="true" class="block text-center text-primary text-sm hover:underline">Xem tất cả thông báo</a>
</div>
</div>
</div>
<div class="w-10 h-10 rounded-full overflow-hidden">
<img src="https://readdy.ai/api/search-image?query=professional%20asian%20man%20in%20business%20suit%2C%20portrait%20photo%2C%20professional%20headshot%2C%20high%20quality%2C%20realistic&width=100&height=100&seq=1&orientation=squarish" alt="User Avatar" class="w-full h-full object-cover">
</div>
</div>
</div>
</header>
<!-- Breadcrumb -->
<div class="container mx-auto px-4 py-3 flex items-center justify-between">
<div class="flex items-center text-sm text-gray-500">
<a href="#" class="breadcrumb-item hover:text-primary">Trang chủ</a>
<a href="#" class="breadcrumb-item hover:text-primary">Mầm non</a>
<span class="breadcrumb-item text-gray-700">Trang cá nhân</span>
</div>
<div class="text-sm text-gray-500 flex items-center">
<i class="ri-time-line mr-1"></i>
<span>Bài viết mới nhất 24/4/2025</span>
</div>
</div>
<!-- Profile Section -->
<div class="container mx-auto px-4 py-6">
<div class="flex flex-col md:flex-row items-center md:items-start gap-6 pb-6 border-b">
<div class="w-32 h-32 rounded-full overflow-hidden border-4 border-white shadow-lg">
<img src="https://readdy.ai/api/search-image?query=professional%20asian%20man%20in%20business%20suit%2C%20portrait%20photo%2C%20professional%20headshot%2C%20high%20quality%2C%20realistic&width=200&height=200&seq=2&orientation=squarish" alt="Nguyễn Văn Minh" class="w-full h-full object-cover">
</div>
<div class="flex-1 text-center md:text-left">
<h1 class="text-2xl font-bold text-gray-800">Nguyễn Văn Minh</h1>
<p class="text-primary font-medium">Chuyên gia Trí tuệ nhân tạo & Khoa học dữ liệu</p>
<p class="mt-2 text-gray-600">
Tiến sĩ Khoa học máy tính với hơn 15 năm kinh nghiệm nghiên cứu và phát triển trong lĩnh vực AI, Machine Learning và xử lý ngôn ngữ tự nhiên. Hiện đang là Giáo sư tại Đại học Bách Khoa Hà Nội.
</p>
</div>
</div>
<!-- Tabs -->
<div class="border-b mt-4">
<nav class="flex space-x-8">
<a href="#" class="border-b-2 border-primary text-primary px-1 py-4 font-medium">Tổng quan</a>
<a href="https://readdy.ai/home/<USER>/d0219845-abfa-486f-b32c-f236acce0f33" data-readdy="true" class="text-gray-500 hover:text-primary px-1 py-4">Bài viết</a>
<a href="https://readdy.ai/home/<USER>/11d55c3c-e2ae-422d-b45f-f5d3aaffdc76" data-readdy="true" class="text-gray-500 hover:text-primary px-1 py-4">Giới thiệu</a>
</nav>
</div>
<!-- Stats and Content -->
<div class="mt-8 grid grid-cols-1 lg:grid-cols-3 gap-8">
<!-- Left Column - Stats and Posts -->
<div class="lg:col-span-2 space-y-8">
<!-- Stats -->
<div class="grid grid-cols-3 gap-4">
<div class="bg-white rounded shadow p-4 flex flex-col items-center">
<div class="w-10 h-10 flex items-center justify-center text-primary mb-2">
<i class="ri-file-list-line ri-xl"></i>
</div>
<span class="text-gray-600 text-sm">Bài viết</span>
<span class="text-2xl font-bold">48</span>
</div>
<div class="bg-white rounded shadow p-4 flex flex-col items-center">
<div class="w-10 h-10 flex items-center justify-center text-green-500 mb-2">
<i class="ri-thumb-up-line ri-xl"></i>
</div>
<span class="text-gray-600 text-sm">Lượt thích</span>
<span class="text-2xl font-bold">2,547</span>
</div>
<div class="bg-white rounded shadow p-4 flex flex-col items-center">
<div class="w-10 h-10 flex items-center justify-center text-blue-500 mb-2">
<i class="ri-message-2-line ri-xl"></i>
</div>
<span class="text-gray-600 text-sm">Bình luận</span>
<span class="text-2xl font-bold">1,283</span>
</div>
</div>
<!-- Posts -->
<div>
<div class="flex items-center justify-between mb-4">
<h2 class="text-xl font-bold text-gray-800">Bài viết nổi bật</h2>
<a href="#" class="text-primary flex items-center text-sm">
Xem tất cả
<i class="ri-arrow-right-s-line ml-1"></i>
</a>
</div>
<!-- Post 1 -->
<div class="bg-white rounded shadow mb-6 overflow-hidden">
<div class="flex flex-col md:flex-row">
<div class="p-6 flex-1">
<div class="flex items-center mb-2">
<span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded mr-2">Trí tuệ nhân tạo</span>
<span class="text-gray-500 text-sm">28/04/2025</span>
</div>
<h3 class="text-xl font-bold mb-2">Tương lai của mô hình ngôn ngữ lớn (LLM) và ứng dụng trong doanh nghiệp Việt Nam</h3>
<p class="text-gray-600 mb-4">Phân tích chi tiết về tiềm năng và thách thức khi triển khai các mô hình ngôn ngữ lớn trong bối cảnh doanh nghiệp tại Việt Nam, cùng các giải pháp khả thi.</p>
<div class="flex items-center text-sm text-gray-500">
<div class="flex items-center mr-4">
<i class="ri-eye-line mr-1"></i>
<span>4,289</span>
</div>
<div class="flex items-center mr-4">
<i class="ri-thumb-up-line mr-1"></i>
<span>342</span>
</div>
<div class="flex items-center">
<i class="ri-message-2-line mr-1"></i>
<span>87</span>
</div>
</div>
</div>
</div>
</div>
<!-- Post 2 -->
<div class="bg-white rounded shadow mb-6 overflow-hidden">
<div class="flex flex-col md:flex-row">
<div class="p-6 flex-1">
<div class="flex items-center mb-2">
<span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded mr-2">Machine Learning</span>
<span class="text-gray-500 text-sm">15/04/2025</span>
</div>
<h3 class="text-xl font-bold mb-2">Kỹ thuật tối ưu hóa mô hình học máy cho dữ liệu không cân bằng</h3>
<p class="text-gray-600 mb-4">Nghiên cứu về các phương pháp hiệu quả để xử lý dữ liệu không cân bằng trong các bài toán phân loại, từ kỹ thuật lấy mẫu đến điều chỉnh thuật toán.</p>
<div class="flex items-center text-sm text-gray-500">
<div class="flex items-center mr-4">
<i class="ri-eye-line mr-1"></i>
<span>3,156</span>
</div>
<div class="flex items-center mr-4">
<i class="ri-thumb-up-line mr-1"></i>
<span>287</span>
</div>
<div class="flex items-center">
<i class="ri-message-2-line mr-1"></i>
<span>62</span>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
<!-- Right Column - Categories and Recent Activities -->
<div class="space-y-8">
<!-- Categories -->
<div class="bg-white rounded shadow p-6">
<h3 class="text-lg font-bold mb-4">Chuyên môn</h3>
<div class="flex flex-wrap gap-2">
<a href="#" class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">Trí tuệ nhân tạo</a>
<a href="#" class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm">Machine Learning</a>
<a href="#" class="bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm">NLP</a>
<a href="#" class="bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm">Deep Learning</a>
<a href="#" class="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm">Computer Vision</a>
<a href="#" class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">Transformer</a>
<a href="#" class="bg-pink-100 text-pink-800 px-3 py-1 rounded-full text-sm">BERT</a>
<a href="#" class="bg-indigo-100 text-indigo-800 px-3 py-1 rounded-full text-sm">Neural Networks</a>
</div>
</div>
<!-- Recent Activities -->
<div class="bg-white rounded shadow p-6">
<h3 class="text-lg font-bold mb-4">Hoạt động gần đây</h3>
<div class="space-y-4">
<div class="border-l-2 border-primary pl-4 py-1">
<div class="flex items-start">
<div class="w-8 h-8 flex items-center justify-center bg-blue-100 text-primary rounded mr-3">
<i class="ri-file-list-line"></i>
</div>
<div class="flex-1">
<p class="text-sm">Đã đăng bài viết mới: <a href="#" class="text-primary hover:underline">Tương lai của mô hình ngôn ngữ lớn (LLM) và ứng dụng trong doanh nghiệp Việt Nam</a></p>
<span class="text-xs text-gray-500">28/04/2025</span>
</div>
</div>
</div>
<div class="border-l-2 border-blue-500 pl-4 py-1">
<div class="flex items-start">
<div class="w-8 h-8 flex items-center justify-center bg-blue-100 text-blue-500 rounded mr-3">
<i class="ri-message-2-line"></i>
</div>
<div class="flex-1">
<p class="text-sm">Đã bình luận về bài viết: <a href="#" class="text-primary hover:underline">Ứng dụng của GPT-4 trong giáo dục</a></p>
<span class="text-xs text-gray-500">25/04/2025</span>
</div>
</div>
</div>
<div class="border-l-2 border-green-500 pl-4 py-1">
<div class="flex items-start">
<div class="w-8 h-8 flex items-center justify-center bg-green-100 text-green-500 rounded mr-3">
<i class="ri-thumb-up-line"></i>
</div>
<div class="flex-1">
<p class="text-sm">Đã thích bài viết: <a href="#" class="text-primary hover:underline">Phương pháp học sâu cho nhận dạng khuôn mặt</a></p>
<span class="text-xs text-gray-500">23/04/2025</span>
</div>
</div>
</div>
<div class="border-l-2 border-yellow-500 pl-4 py-1">
<div class="flex items-start">
<div class="w-8 h-8 flex items-center justify-center bg-yellow-100 text-yellow-500 rounded mr-3">
<i class="ri-file-list-line"></i>
</div>
<div class="flex-1">
<p class="text-sm">Đã đăng bài viết mới: <a href="#" class="text-primary hover:underline">Kỹ thuật tối ưu hóa mô hình học máy cho dữ liệu không cân bằng</a></p>
<span class="text-xs text-gray-500">15/04/2025</span>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
</body>
</html>
