import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { CookieName } from '@/constants/cookie'

export function middleware(request: NextRequest) {
  if (request.nextUrl.pathname.startsWith('/social-network')) {
    const token = request.cookies.get(CookieName.TOKEN)
    
    if (!token) {
      return NextResponse.redirect(new URL('/login', request.url))
    }
  }

  return NextResponse.next()
}

export const config = {
  matcher: '/social-network/:path*'
} 