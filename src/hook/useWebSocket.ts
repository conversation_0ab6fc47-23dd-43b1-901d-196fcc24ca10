import { useEffect, useRef, useCallback } from 'react';
import { getCookie } from 'cookies-next';
import { Client } from '@stomp/stompjs';
import SockJS from 'sockjs-client';
import { CookieName } from '@/constants/cookie';

interface WebSocketMessage {
  type: string;
  data: any;
}

const WS_URL = process.env.NEXT_PUBLIC_WS_URL || 'https://ecosys-be.onrender.com/ws';

export const useWebSocket = (onMessage: (message: WebSocketMessage) => void) => {
  const clientRef = useRef<Client | null>(null);
  const reconnectAttempts = useRef(0);
  const MAX_RECONNECT_ATTEMPTS = 5;

  const connect = useCallback(() => {
    const userCookie = getCookie(CookieName.USER);
    const tokenCookie = getCookie(CookieName.TOKEN);

    const token = tokenCookie?.toString();
    if (!userCookie || !token) {
      console.warn('No user or token found for WebSocket connection');
      return;
    }

    try {
      const user = typeof userCookie === 'string' ? JSON.parse(userCookie) : userCookie;

      if (!user?.forumInfo?.username) {
        console.warn('Invalid user data structure:', user);
        return;
      }

      const username = user.forumInfo.username;
      const wsEndpoint = `${WS_URL}?username=${username}`;

      const client = new Client({
        webSocketFactory: () => new SockJS(wsEndpoint),
        reconnectDelay: 5000,
        onConnect: () => {
          console.log('STOMP WebSocket Connected');

          // Ví dụ: đăng ký nhận tin nhắn từ topic riêng
          client.subscribe(`/user/queue/messages`, (message) => {
            try {
              const body: WebSocketMessage = JSON.parse(message.body);
              console.log('Received message:', body);
              onMessage(body);
            } catch (error) {
              console.error('Error parsing STOMP message', error);
            }
          });
        },
        onStompError: (frame) => {
          console.error('Broker reported error:', frame.headers['message']);
          console.error('Additional details:', frame.body);
        },
      });

      client.activate();
      clientRef.current = client;
      reconnectAttempts.current = 0;

    } catch (error) {
      console.error('Error setting up STOMP client:', error);
    }
  }, [onMessage]);

  useEffect(() => {
    // connect();
    // return () => {
    //   clientRef.current?.deactivate();
    // };
  }, []);

  const sendMessage = useCallback((destination: string, message: any) => {
    if (clientRef.current?.connected) {
      try {
        clientRef.current.publish({
          destination,
          body: JSON.stringify(message),
        });
      } catch (error) {
        console.error('Error sending STOMP message:', error);
      }
    } else {
      console.warn('STOMP client is not connected. Message not sent.');
    }
  }, []);

  return { sendMessage };
};
