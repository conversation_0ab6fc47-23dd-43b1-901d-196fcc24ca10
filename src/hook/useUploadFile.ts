import api from "@/lib/Axios";
import { useState } from "react";
import { toast } from "react-toastify";

type FileType = "ranking" | "forums" | "social-network";

const useUploadFile = () => {
  const [loading, setLoading] = useState(false);
  const [progress, setProgress] = useState(0);

  // Upload file cho phần forum
  const uploadForumFile = async (file: File) => {
    try {
      setLoading(true);
      setProgress(0);

      const formData = new FormData();
      formData.append("file", file);

      const response = await api.postFileRequest(
        "/file/forums",
        formData as unknown as Record<string, unknown>,
        {
          headers: {
            "Content-Type": "multipart/form-data"
          }
        }
      );

      return response.data;
    } catch (error) {
      throw error;
    } finally {
      setLoading(false);
      setProgress(0);
    }
  };

  // Upload file cho phần social network
  const uploadSocialNetworkFile = async (file: File) => {
    try {
      setLoading(true);
      setProgress(0);

      const formData = new FormData();
      formData.append("file", file);

      const response = await api.postFileRequest(
        "/file/social-network",
        formData as unknown as Record<string, unknown>,
        {
          headers: {
            "Content-Type": "multipart/form-data"
          }
        }
      );

      return response.data;
    } catch (error) {
      throw error;
    } finally {
      setLoading(false);
      setProgress(0);
    }
  };

  // Xem file
  const viewFile = async (fileId: string | unknown, type: FileType = "ranking") => {
    if (!fileId) return;
    try {
      const data: Record<FileType, string> = {
        ranking: "/file/ranking/view",
        forums: "/file/forums/view",
        "social-network": "/file/social-network/view"
      };

      // const response = await api.getFileRequest(
      //   data[type],
      //   { fileId },
      //   {
      //     responseType: "blob"
      //   }
      // );

      // return new Promise((resolve, reject) => {
      //   const reader = new FileReader();
      //   reader.onloadend = () => {
      //     const base64data = reader.result;
      //     resolve(base64data);
      //   };
      //   reader.onerror = reject;
      //   reader.readAsDataURL(response.data);
      // });
      return process.env.NEXT_PUBLIC_FILE_SERVER_URL + "/uploads/" + fileId;
    } catch (error) {
      throw error;
    }
  };

  // Tải file
  const downloadFile = async (fileId: string, type: FileType = "ranking") => {
    try {
      const response = await api.getFileRequest("/uploads/" + fileId, {
        responseType: "blob"
      });

      // Tạo URL và link tải
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", `file-${fileId}`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);

      toast.success("Tải file thành công");
    } catch (error) {
      toast.error("Không thể tải file");
      throw error;
    }
  };

  return {
    loading,
    progress,
    viewFile,
    downloadFile,
    uploadForumFile,
    uploadSocialNetworkFile
  };
};

export default useUploadFile;
