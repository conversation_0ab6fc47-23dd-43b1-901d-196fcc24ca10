import axios, {
    AxiosResponse,
    InternalAxiosRequestConfig,
    RawAxiosRequestHeaders,
    type AxiosInstance,
    type AxiosRequestConfig
} from "axios";
import {getCookie, deleteCookie} from "cookies-next";
import {CookieName} from "@/constants/cookie";

const axiosDefaultHeaderOption = {
    "cache-control": "public, s-maxage=10, stale-while-revalidate=59"
};

// Tạo các instance axios với cấu hình mặc định
const createAxiosInstance = (baseURL: string): AxiosInstance => {
    const instance = axios.create({
        baseURL,
        headers: axiosDefaultHeaderOption
    });

    // Thêm interceptor request
    instance.interceptors.request.use((config: InternalAxiosRequestConfig<unknown>) => {
        const token = getCookie(CookieName.TOKEN);
        if (token) {
            config.headers = config.headers ?? {};
            config.headers["Authorization"] = `Bearer ${token}`;
        }
        return config;
    });

    return instance;
};

const axiosInstance = createAxiosInstance(process.env.NEXT_PUBLIC_SERVER_URL as string);
const axiosFileInstance = createAxiosInstance(process.env.NEXT_PUBLIC_FILE_SERVER_URL as string);

// Xử lý response cho main instance
axiosInstance.interceptors.response.use(
    (response: AxiosResponse) => response,
    (error) => {
        if (error?.response?.data?.statusCode === 401 && window.location.pathname !== '/') {
            deleteCookie(CookieName.TOKEN);
            window.location.href = "/login";
        }
        return Promise.reject(error);
    }
);

// Xử lý response cho file instance  
axiosFileInstance.interceptors.response.use(
    (response: AxiosResponse) => response,
    (error) => {
        if (error?.response?.data?.statusCode === 401 && window.location.pathname !== '/') {
            deleteCookie(CookieName.TOKEN);
            window.location.href = "/";
        }
        return Promise.reject(error);
    }
);

// Helper functions
export const getAPIHeaders = () => axiosInstance.defaults.headers.common;

export const setAPIHeaders = (inputHeaders: RawAxiosRequestHeaders) =>
    (axiosInstance.defaults.headers.common = {
        ...axiosInstance.defaults.headers.common,
        ...inputHeaders
    });

// Base request function
const makeRequest = (
    instance: AxiosInstance,
    method: string,
    endpointApiUrl: string,
    payload: Record<string, unknown> = {},
    config: AxiosRequestConfig = {}
): Promise<AxiosResponse> => {
    switch (method) {
        case 'GET':
            return instance.get(endpointApiUrl, {
                params: payload,
                ...config
            });
        case 'POST':
            return instance.post(endpointApiUrl, payload, config);
        case 'PUT':
            return instance.put(endpointApiUrl, payload, config);
        case 'PATCH':
            return instance.patch(endpointApiUrl, payload, config);
        case 'DELETE': {
            const queryString = buildQuery(payload);
            const urlWithQuery = `${endpointApiUrl}?${queryString}`;
            return instance.delete(urlWithQuery, config);
        }
        default:
            throw new Error(`Unsupported HTTP method: ${method}`);
    }
};

// Request functions
export const getRequest = (url: string, payload = {}, config = {}) => 
    makeRequest(axiosInstance, 'GET', url, payload, config);

export const getFileRequest = (url: string, payload = {}, config = {}) =>
    makeRequest(axiosFileInstance, 'GET', url, payload, config);

export const postRequest = (url: string, payload = {}, config = {}) =>
    makeRequest(axiosInstance, 'POST', url, payload, config);

export const postFileRequest = (url: string, payload = {}, config = {}) =>
    makeRequest(axiosInstance, 'POST', url, payload, config);

export const putRequest = (url: string, payload = {}, config = {}) =>
    makeRequest(axiosInstance, 'PUT', url, payload, config);

export const patchRequest = (url: string, payload = {}, config = {}) =>
    makeRequest(axiosInstance, 'PATCH', url, payload, config);

export const deleteRequest = (url: string, payload = {}, config = {}) =>
    makeRequest(axiosInstance, 'DELETE', url, payload, config);

function buildQuery(params: Record<string, unknown>): string {
    return Object.entries(params)
        .map(([key, value]) => {
            if (Array.isArray(value)) {
                return value.map(v => `${key}=${encodeURIComponent(v)}`).join("&");
            }
            return `${key}=${encodeURIComponent(value as string)}`;
        })
        .join("&");
}

const Axios = {
    getRequest,
    getFileRequest,
    postRequest,
    postFileRequest,
    putRequest,
    patchRequest,
    deleteRequest
};

export default Axios;
