export interface LoginRequest {
  [key: string]: string;
  username: string;
  password: string;
}

export interface RegisterRequest {
  [key: string]: string;
  username: string;
  password: string;
  firstName: string;
  lastName: string;
}

export interface LoginGoogleRequest {
  [key: string]: string;
  token: string;
}

export interface ForgotPasswordRequest {
  [key: string]: string;
  username: string;
}

export interface ConfirmRegistrationRequest {
  [key: string]: string;
  token: string;
}

export interface CheckResetTokenRequest {
  [key: string]: string;
  token: string;
}

export interface ResetPasswordRequest {
  [key: string]: string;
  password: string;
  token: string;
} 