import Axios from "../../lib/Axios";
import {
  LoginRequest,
  RegisterRequest,
  LoginGoogleRequest,
  ForgotPasswordRequest,
  ConfirmRegistrationRequest,
  CheckResetTokenRequest,
  ResetPasswordRequest
} from "./types";

const login = (payload: LoginRequest) =>
  Axios.postRequest('/auth/login', payload);

const register = (payload: RegisterRequest) =>
  Axios.postRequest('/auth/register', payload);

const loginGoogle = (payload: LoginGoogleRequest) =>
  Axios.postRequest('/oauth2/authorization/google', payload);

const forgotPassword = (payload: ForgotPasswordRequest) =>
  Axios.getRequest('/auth/forgot-password', payload);

const confirmRegistration = (payload: ConfirmRegistrationRequest) =>
  Axios.postRequest('/auth/confirm-registration', payload);

const checkResetToken = (payload: CheckResetTokenRequest) =>
  Axios.postRequest('/auth/forgot-password', payload);

const resetPassword = (payload: ResetPasswordRequest) =>
  Axios.postRequest('/auth/reset-password', payload);

const getForumUserInfo = () =>
  Axios.getRequest('/auth/get-info-user-forum');

const getSocialNetworkUserInfo = () =>
  Axios.getRequest('/auth/get-info-user-social-network');

const authServices = {
  login,
  register,
  loginGoogle,
  forgotPassword,
  confirmRegistration,
  checkResetToken,
  resetPassword,
  getForumUserInfo,
  getSocialNetworkUserInfo
};

export default authServices;
