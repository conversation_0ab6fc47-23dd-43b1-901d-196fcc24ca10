import Axios from "@/lib/Axios";

export interface ForumNotification {
  id: number;
  message: string;
  createdDate: string;
  isRead: boolean;
}

// L<PERSON>y danh sách thông báo
const getForumNotifications = (page = 0, size = 20, isRead?: string) =>
  Axios.getRequest('/user/notification/forums', { page, size, sort: "notificationsId,DESC" ,isRead});

// Đ<PERSON>h dấu thông báo đã đọc
const markNotificationAsRead = (id: number) =>
  Axios.putRequest(`/user/notification/forums/${id}`);

// Xóa thông báo
const deleteNotification = (id: number) =>
  Axios.deleteRequest(`/user/notification/forums/${id}`);

// lấy count thông báo chưa đọc
const getCountNotifyNotRead = () =>
    Axios.getRequest(`/user/notification/forums/get-count-not-read`);
// cập nhật tất cả thông báo thành đã đọc
const updateAllRead = () =>
    Axios.putRequest(`/user/notification/forums/update-all`);

const notificationsServices = {
  getForumNotifications,
  markNotificationAsRead,
  deleteNotification,
  getCountNotifyNotRead,
  updateAllRead
};

export default notificationsServices; 