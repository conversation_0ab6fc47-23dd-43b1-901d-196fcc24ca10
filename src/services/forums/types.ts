export interface Forum {
  forumId: number;
  name: string;
  eduEcosystemId: number;
  postCount: number;
  topicCount: number;
  fileId?: string;
  newPostCreatedAt?: string;
  newPostCreatedBy?: string;
  description?: string;
  userForumId?: number;
}

export interface ForumPagination {
  content: Forum[];
  pageable: {
    pageNumber: number;
    pageSize: number;
    offset: number;
    paged: boolean;
    unpaged: boolean;
    sort: {
      unsorted: boolean;
      sorted: boolean;
      empty: boolean;
    };
  };
  totalPages: number;
  totalElements: number;
  last: boolean;
  numberOfElements: number;
  size: number;
  number: number;
  first: boolean;
  empty: boolean;
  sort: {
    unsorted: boolean;
    sorted: boolean;
    empty: boolean;
  };
}

export interface ForumResponse {
  statusCode: number;
  responseCode: string | null;
  responseMessage: string;
  data: ForumPagination;
}

export interface Topic {
  topicId: number;
  title: string;
  content: string;
  createdDate: string;
  postCount: number;
  viewCount: number;
  id: number;
  description: string;
  author: string;
  replies: number;
  views: number;
  lastActivity: string;
  topicName?: string;
  tags: string[];
  fileId?: string;
  forumId: number;
}

export interface Post {
  fileId: any;
  postId: number;
  content: string;
  title: string;
  orgName: string | null;
  createdAt: string;
  userCreatedId: number;
  userCreatedFullName: string;
  userCreatedThumbNail: string | null;
  topicThumbNail: string | null;
  forumThumbNail: string | null;
  topicName: string;
  topicId: number;
  forumId: number;
  eduEcosystemId: number;
  commentCount: number;
  likeCount: number;
  isLike: boolean;
  viewCount: number;
  attachments?: string[];
  forumName?: string;
  files?: {
    fileId: string;
    fileOriginalName: string;
    fileStoredName: string;
    fileType: string;
    fileSize: number;
  }[];
  hashTags?: string[];
  readTime?: number;
  postCount?: number;
  backgroundColor?: string;
  textColor?: string;
  authorAvatar?: string;
  authorName?: string;
  views?: number;
  comments?: number;
}

export interface Comment {
  commentId: number;
  content: string;
  createdAt?: string;
  userCreatedId?: number;
  userCreatedFullName?: string;
  postId: number;
  parentId?: number;
  countLike?: number;
  isLike?: boolean;
  hasChild?: boolean;
  replies?: Comment[];
  orgName?: string;
}

export interface CommentResponse {
  statusCode: number;
  responseCode: string | null;
  responseMessage: string;
  data: {
    content: Comment[];
    pageable: {
      pageNumber: number;
      pageSize: number;
      sort: {
        unsorted: boolean;
        sorted: boolean;
        empty: boolean;
      };
      offset: number;
      paged: boolean;
      unpaged: boolean;
    };
    totalPages: number;
    totalElements: number;
    last: boolean;
    size: number;
    number: number;
    sort: {
      unsorted: boolean;
      sorted: boolean;
      empty: boolean;
    };
    numberOfElements: number;
    first: boolean;
    empty: boolean;
  };
}

export interface TopicResponse {
  statusCode: number;
  responseCode: string | null;
  responseMessage: string;
  data: Topic;
}

export interface TopicListResponse {
  statusCode: number;
  responseCode: string | null;
  responseMessage: string;
  data: {
    content: Topic[];
    pageable: {
      pageNumber: number;
      pageSize: number;
      sort: {
        unsorted: boolean;
        sorted: boolean;
        empty: boolean;
      };
      offset: number;
      paged: boolean;
      unpaged: boolean;
    };
    totalPages: number;
    totalElements: number;
    last: boolean;
    size: number;
    number: number;
    sort: {
      unsorted: boolean;
      sorted: boolean;
      empty: boolean;
    };
    numberOfElements: number;
    first: boolean;
    empty: boolean;
  };
}

export interface PostResponse {
  statusCode: number;
  responseCode: string | null;
  responseMessage: string;
  data: {
    content: Post[];
    pageable: {
      pageNumber: number;
      pageSize: number;
      sort: {
        unsorted: boolean;
        sorted: boolean;
        empty: boolean;
      };
      offset: number;
      paged: boolean;
      unpaged: boolean;
    };
    totalPages: number;
    totalElements: number;
    last: boolean;
    size: number;
    number: number;
    sort: {
      unsorted: boolean;
      sorted: boolean;
      empty: boolean;
    };
    numberOfElements: number;
    first: boolean;
    empty: boolean;
  };
}

export interface CreatePostRequest {
  content: string;
  title: string;
  topicId: number;
  forumId: number;
  eduEcosystemId: number;
  hashTags?: string[];
  fileIds?: string[];
}
