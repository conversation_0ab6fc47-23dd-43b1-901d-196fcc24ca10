import Axios from "../../lib/Axios";

// Interface cho tham số tìm kiếm
interface SearchParams {
  keyword?: string;
  hashtag?: string;
  page?: number;
  size?: number;
  ecosystemId?: number | null;
  forumId?: number | null;
  forumIds?: string | number | (string | number)[];
  topicIds?: string | number | (string | number)[];
  time?: string;
  viewCommentType?: string;
  sort?: string;
  isFeatured?: boolean;
}

const getForums = (page = 0, size = 10, ecosystemId?: number) =>
  Axios.getRequest("/anonymous/forums", { page, size, ecosystemId, sort: "forumId,desc" });

const getForumById = (id: number) =>
  Axios.getRequest(`/anonymous/forums/${id}`);

// Topics API
const getTopicsByForumId = (forumId: number, page = 0, size = 9999) =>
  Axios.getRequest(`/anonymous/topic-forums`, { forumId, page, size });

const getTopicById = (topicId: number) =>
  Axios.getRequest(`/anonymous/topic-forums/${topicId}`);

// Posts API
const getPostsByTopicId = (payLoad: {
  topicId: number | null;
  page: number;
  size: number;
  ecosystemId: number | null;
  forumId: number;
  sort?: string;
  textSearch?: string;
}) => Axios.getRequest(`/anonymous/posts`, { ...payLoad});

const getPostById = (postId: number) =>
  Axios.getRequest(`/anonymous/posts/${postId}`);

const likePost = (postId: number) =>
  Axios.getRequest(`/user/posts/like`, { postId });

const unlikePost = (postId: number) =>
  Axios.getRequest(`/user/posts/unlike`, { postId });

const createPost = (data: {
  content: string;
  topicId: number;
  forumId: number;
  eduEcosystemId: number;
  hashTags?: string[] | null;
  title: string;
  files?: string[] | null;
}) => Axios.postRequest(`/user/posts`, data);

const updatePost = (postId: number, data: {
  title: string;
  content: string;
  hashTags?: string[];
  files?: Array<{
    fileId: string;
    action: "ADD" | "DELETE";
  }>;
  topicId?: number;
}) => Axios.putRequest(`/user/posts/${postId}`, data);

// Comment API
const getCommentsByPostId = (postId: number, page = 0, size = 10) =>
  Axios.getRequest(`/anonymous/comment`, { postId, page, size, sort: "commentId,desc" });

const createComment = (data: {
  content: string;
  postId: number | null;
  parentId?: number;
}) => {
  const apiData = {
    postId: data.postId,
    content: data.content,
    commentParentId: data.parentId
  };

  return Axios.postRequest(`/user/comment`, apiData);
};

const updateComment = (commentId: number, content: string) =>
  Axios.putRequest(`/user/comment/${commentId}`, { content });

const deleteComment = (commentId: number) =>
  Axios.deleteRequest(`/user/comment/${commentId}`);

const likeComment = (commentId: number) =>
  Axios.getRequest(`/user/comment/like`, { commentId });

const unlikeComment = (commentId: number) =>
  Axios.getRequest(`/user/comment/unlike`, { commentId });

const getCommentReplies = (commentId: number, page = 0, size = 10) =>
  Axios.getRequest(`/anonymous/comment/parent`, { commentId, page, size, sort: "commentId,desc" });

const deleteForum = (forumId: number) =>
  Axios.deleteRequest(`/admin/forums/${forumId}`);

const deletePost = (postId: number) =>
  Axios.deleteRequest(`/admin/posts/${postId}`);

const reportPost = (postId: number, reportReason: string) =>
  Axios.postRequest(`/user/posts/report/${postId}`, { reportReason });

const searchPosts = (params: SearchParams) => {
  const searchParams = {
    ...params,
    sort: params.sort || "postId,desc",
    page: params.page || 0,
    size: params.size || 10
  };
  
  if (Array.isArray(searchParams.topicIds)) {
    searchParams.topicIds = searchParams.topicIds.join(',');
  }
  if (Array.isArray(searchParams.forumIds)) {
    searchParams.forumIds = searchParams.forumIds.join(',');
  }
  
  return Axios.getRequest(`/anonymous/posts/search`, searchParams);
};

const forumsServices = {
  getForums,
  getForumById,
  getTopicsByForumId,
  getTopicById,
  getPostsByTopicId,
  getPostById,
  likePost,
  unlikePost,
  createPost,
  updatePost,
  deletePost,
  // Comment services
  getCommentsByPostId,
  createComment,
  updateComment,
  deleteComment,
  likeComment,
  unlikeComment,
  getCommentReplies,
  // Admin services
  deleteForum,
  reportPost,
  // Search services
  searchPosts
}

export default forumsServices;
