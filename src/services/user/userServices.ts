import Axios from "../../lib/Axios";

type MockupPayload = {
  page: number;
  size?: number;
  textSearch?: string;
  sort?: string;
};



const getUserHistory = (payload?: MockupPayload) => {
  return Axios.getRequest(`/user/user-history-forum`, payload);
};

const getPostsUser = (payload?: MockupPayload) => {
  return Axios.getRequest(`/user/posts/my-post`, payload);
};

const getUserInfo = () => {
  return Axios.getRequest(`/user/user-forums`);
};

const changePassword = (payload: ChangePasswordPayload) => {
  return Axios.postRequest("/user/change-password", payload as unknown as Record<string, unknown>);
};

const isHasPassword = () => {
  return Axios.postRequest("/user/is-has-password");
};

const editUserInfo = (payload: EditUserInfoPayload) => {
  return Axios.postRequest("/user/user-forums", payload as unknown as Record<string, unknown>);
};

const getUserInfoById = (id: string) => {
  return Axios.getRequest(`/anonymous/user-forums/${id}`);
};

interface ChangePasswordPayload {
  oldPassword: string;
  newPassword: string;
}

interface EditUserInfoPayload {
  fullName: string;
  phoneNumber: string;
  gender: string;
  dateOfBirth: string;
  bio: string;
  thumbnail?: string;
  numberOfPost: number;
  firstName: string;
  lastName: string;
  education: string;
  job: string;
  address: string;
}

const userServices = {
  getUserHistory,
  getPostsUser,
  getUserInfo,
  changePassword,
  editUserInfo,
  isHasPassword,
  getUserInfoById
};

export default userServices;
