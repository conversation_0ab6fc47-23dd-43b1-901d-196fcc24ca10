import Axios from "../../lib/Axios";
import { UserSocialNetwork } from "./types/types";

// User API
const getUserSocialNetwork = () =>
  Axios.getRequest(`/user/user-social-network`);

const updateUserSocialNetwork = (data: UserSocialNetwork) =>
  Axios.putRequest(`/user/user-social-network`, { ...data });

const getUserSocialNetworkByUserName = (username: string) =>
  Axios.getRequest(`/user/user-social-network/profile-by-email/${username}`);

//User history social network
const userHistorySocialNetwork = ({
  page = 0,
  size = 5,
  sort = "DESC"
}: {
  page: number;
  size: number;
  sort: "ASC" | "DESC";
}) =>
  Axios.getRequest(`/user/user-history-social-network`, { page, size, sort });

//Post Social Network
const getListPostSocialNetwork = ({
  page = 0,
  size = 5
}: {
  page: number;
  size: number;
}) => Axios.getRequest(`/user/post-social-network`, { page, size });

const searchPostSocialNetwork = ({
  page = 0,
  size = 10,
  postSearch,
  hashtag
}: {
  page: number;
  size: number;
  postSearch: string;
  hashtag: string;
}) =>
  Axios.getRequest(`user/post-social-network/feeds`, {
    page,
    size,
    postSearch,
    hashtag
  });

const feedPostSocialNetwork = ({
  isReload
}: {
  isReload: boolean;
}) =>
  Axios.getRequest(`user/post-social-network/new-feeds`, {
    isReload
  });

const searchUserSocialNetwork = ({
  page = 0,
  size = 10,
  postSearch,
}: {
  page: number;
  size: number;
  postSearch: string;
}) =>
  Axios.getRequest(`user/user-social-network/search`, {
    page,
    size,
    textSearch:  postSearch
  });

const createPostSocialNetwork = (data: {
  content: string;
  privacyPolicy: string;
  hashTags: string[];
  files?: string[];
}) => Axios.postRequest(`/user/post-social-network`, { ...data });

const getPostSocialNetworkById = (postSocialNetworkId: number) =>
  Axios.getRequest(`/user/post-social-network/${postSocialNetworkId}`);

const updatePostSocialNetwork = (data: {
  postSocialNetworkId: number;
  content: string;
  privacyPolicy: string;
  hashTags: string[];
  files?: string[];
}) =>
  Axios.putRequest(`/user/post-social-network/${data.postSocialNetworkId}`, {
    ...data
  });

const deletePostSocialNetwork = (postSocialNetworkId: number) =>
  Axios.deleteRequest(`/user/post-social-network/${postSocialNetworkId}`);

const getListPostSocialNetworkByUserName = ({
  username,
  page = 0,
  size = 5
}: {
  username: string;
  page: number;
  size: number;
}) =>
  Axios.getRequest(`/user/post-social-network/profile/${username}`, {
    page,
    size
  });

const savePostSocialNetwork = (postSocialNetworkId: number) =>
  Axios.postRequest(`/user/post-social-network/save/${postSocialNetworkId}`);

const sharePostSocialNetwork = (data: {
  postReferId: number;
  content: string;
  privacyPolicy: string;
  hashTags: string[];
}) => Axios.postRequest(`/user/post-social-network/share`, { ...data });

const unSavePostSocialNetwork = (postSocialNetworkId: number) =>
  Axios.postRequest(`/user/post-social-network/un-save/${postSocialNetworkId}`);

const getSavedPostsSocialNetwork = ({
  page = 0,
  size = 5
}: {
  page: number;
  size: number;
}) => Axios.getRequest(`/user/post-social-network/save`, { page, size });

//User interaction social network
const likePostSocialNetwork = (postReferId: number) =>
  Axios.putRequest(`/user/user-interaction-social-network/like/${postReferId}`);

const unlikePostSocialNetwork = (postReferId: number) =>
  Axios.putRequest(
    `/user/user-interaction-social-network/unlike/${postReferId}`
  );

//Comment social network
const getListCommentPostSocialNetwork = (data: {
  page: number;
  size: number;
  sort: "ASC" | "DESC";
  postSocialNetworkId: number;
}) => Axios.getRequest(`/user/comment-social-network`, { ...data });

const getListChildCommentPostSocialNetwork = (data: {
  page: number;
  size: number;
  sort: "ASC" | "DESC";
  postSocialNetworkId: number;
  commentParentId: number;
}) => Axios.getRequest(`/user/comment-social-network/child`, { ...data });

const createCommentPostSocialNetwork = (data: {
  postSocialNetworkId: number;
  content: string;
  commentParentId?: number;
  imageId: string;
}) => Axios.postRequest(`/user/comment-social-network`, { ...data });

const updateCommentPostSocialNetwork = (
  data: {
    postSocialNetworkId: number;
    content: string;
    commentParentId: number;
    imageId: string;
  },
  commentId: number
) => Axios.putRequest(`/user/comment-social-network/${commentId}`, { ...data });

const deleteCommentPostSocialNetwork = (commentId: number) =>
  Axios.deleteRequest(`/user/comment-social-network/${commentId}`);

const childCommentPostSocialNetwork = (data: {
  page: number;
  size: number;
  sort: "ASC" | "DESC";
  postSocialNetworkId: number;
  commentParentId: number;
}) => Axios.getRequest(`/user/comment-social-network/child`, { ...data });

//Comment like social network
const likeCommentPostSocialNetwork = (
  postSocialNetworkId: number,
  commentId: number
) =>
  Axios.postRequest(
    `/user/like-comment-social-network/${postSocialNetworkId}/${commentId}`
  );

const unlikeCommentPostSocialNetwork = (
  postSocialNetworkId: number,
  commentId: number
) =>
  Axios.deleteRequest(
    `/user/like-comment-social-network/${postSocialNetworkId}/${commentId}`
  );

const getNearestComment = ({
  page = 0,
  size = 5,
  sort = "DESC"
}: {
  page: number;
  size: number;
  sort: "ASC" | "DESC";
}) =>
  Axios.getRequest(`/user/comment-social-network/nearest`, {
    page,
    size,
    sort
  });

const getNotification = ({
  page = 0,
  size = 5,
  sort = "DESC",
  isRead
}: {
  page: number;
  size: number;
  sort: "ASC" | "DESC";
  isRead?: boolean;
}) =>
  Axios.getRequest(`/user/notification/social-network`, {
    page,
    size,
    sort,
    isRead
  });

//Xem đồng thời chuyển trạng thái xem
const updatePostNotification = (notificationId: number) =>
  Axios.putRequest(`/user/notification/social-network/${notificationId}`);

//cập nhật thông báo đã đọc
const updateFriendNotification = (notificationId: number) =>
  Axios.putRequest(`/user/notification/social-network/read/${notificationId}`);

//Xóa thông báo
const deleteNotification = (notificationId: number) =>
  Axios.deleteRequest(`/user/notification/social-network/${notificationId}`);

//lấy count chưa đọc
const getNotificationCount = () =>
  Axios.getRequest(`/user/notification/social-network/get-count-not-read`);

// API báo cáo vi phạm bài đăng
const reportPostSocialNetwork = (postSocialNetworkId: number, content: string) =>
  Axios.postRequest(`/user/post-social-network/report/${postSocialNetworkId}`, { content });

//Cập nhật tất cả thành đã đọc
const updateAllNotification = () =>
  Axios.putRequest(`/user/notification/social-network/update-all`);

const socialNetworkServices = {
  //User Social Network
  getUserSocialNetwork,
  updateUserSocialNetwork,
  getUserSocialNetworkByUserName,
  //User history social network
  userHistorySocialNetwork,
  //Post Social Network
  getListPostSocialNetwork,
  createPostSocialNetwork,
  getPostSocialNetworkById,
  updatePostSocialNetwork,
  deletePostSocialNetwork,
  getListPostSocialNetworkByUserName,
  searchPostSocialNetwork,
  feedPostSocialNetwork,
  searchUserSocialNetwork,
  savePostSocialNetwork,
  sharePostSocialNetwork,
  unSavePostSocialNetwork,
  getSavedPostsSocialNetwork,
  reportPostSocialNetwork,
  //User interaction social network
  likePostSocialNetwork,
  unlikePostSocialNetwork,
  //Comment social network
  getListCommentPostSocialNetwork,
  getListChildCommentPostSocialNetwork,
  createCommentPostSocialNetwork,
  updateCommentPostSocialNetwork,
  deleteCommentPostSocialNetwork,
  childCommentPostSocialNetwork,
  //Comment like social network
  likeCommentPostSocialNetwork,
  unlikeCommentPostSocialNetwork,
  //Nearest comment
  getNearestComment,
  //Notification
  getNotification,
  updatePostNotification,
  updateFriendNotification,
  deleteNotification,
  getNotificationCount,
  updateAllNotification
};

export default socialNetworkServices;
