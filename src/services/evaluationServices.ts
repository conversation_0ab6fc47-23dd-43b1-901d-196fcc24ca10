import Axios from "@/lib/Axios";

// API functions
const getEducationLevels = () =>
  Axios.getRequest("/user/general-evaluation-forms/edu");

const getSchools = (ecosystemId: string) =>
  Axios.getRequest("/user/general-evaluation-forms/schools", {ecosystemId});

const getPeriodRankings = () =>
  Axios.getRequest("/user/general-evaluation-forms/period-rankings");

// API để lấy danh sách phiếu đánh giá chung theo trường học, cấp học, đợt xếp hạng và người dùng
const getGeneralEvaluationForms = (payload: {
  periodRankingId: number;
  schoolId: number;
}) =>
  Axios.getRequest("/user/general-evaluation-forms", payload);

// API để chấm phiếu đánh giá chung theo trường học, cấp học, đợt xếp hạng và người dùng
const submitGeneralEvaluationForms = (payload: {
  schoolId: number;
  periodRankingId: number;
  // topicId: number;
  eduEcosystemId: number;
  periodIndexDescriptions: Array<{
    id: number;
    description: string;
    topicId: number;
    criteriaId: string;
    criteriaIndexId: number;
    descriptionId: number;
    periodRankingId: number;
    schoolUnitsAssignedId: number;
    mark: number;
    maxMark: number;
    minMark: number;
  }>;
  fileRequests: Array<{
    fileId: string;
    action: "ADD" | "DELETE";
  }>;
}) =>
  Axios.postRequest("/user/general-evaluation-forms", payload);

export default {
  getEducationLevels,
  getSchools,
  getPeriodRankings,
  getGeneralEvaluationForms,
  submitGeneralEvaluationForms
};
