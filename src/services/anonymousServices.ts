import Axios from "../lib/Axios";


type MockupPayloadMyPostUserHighlight = {
  page: number;
  size?: number;
  textSearch?: string;
  sort?: string;
  userForumId? : number;
};

const getListEduEcosystem = () => Axios.getRequest("/anonymous/edu-ecosystem");

const getTrendingTags = (payload: {
  eduEcosystemId: string;
  forumId: string;
  topicId: string;
}) => Axios.getRequest("/anonymous/posts/top-hashtag", payload);

const getPopularPosts = (payload: {
  eduEcosystemId: string;
  forumId: string;
  topicId: string;
}) => Axios.getRequest("/anonymous/posts/highlight", payload);

const getUserPostHighlight = (payload?: MockupPayloadMyPostUserHighlight) => {
  return Axios.getRequest(`/anonymous/posts/user-post/highlight`, payload);
};

const getUserPost = (payload?: MockupPayloadMyPostUserHighlight) => {
  return Axios.getRequest(`/anonymous/posts/user-post`, payload);
};


const getAuthor = (id:number) => {
  return Axios.getRequest(`/anonymous/user-forums/${id}`);
};



export default {
  getListEduEcosystem,
  getTrendingTags,
  getPopularPosts,
  getMyPostUserHighlight: getUserPostHighlight,
  getUserPost,
  getAuthor
};
