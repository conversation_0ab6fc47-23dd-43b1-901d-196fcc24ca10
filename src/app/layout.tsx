import type { Metadata } from "next";
import { Inter, Quicksand } from "next/font/google";
import "@/style/globals.css";
import "@/style/css/style.css";
import 'react-toastify/dist/ReactToastify.css';
import { ToastContainer } from "react-toastify";
import { Suspense } from "react";

import { SWRProvider } from "@/context/SwrProvider";
import { AuthProvider } from "@/context/AuthContext";
import Loading from "@/components/ui/Loading";

const inter = Inter({ subsets: ["latin"] });
const quicksand = Quicksand({
  subsets: ["latin"]
});

export const metadata: Metadata = {
  title: "<PERSON><PERSON> sinh thái học tập, sáng tạo",
  description: "<PERSON><PERSON> sinh thái học tập, sáng tạo"
};

export default function RootLayout({
  children
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <link
          rel="stylesheet"
          href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css"
        />
        <link
          rel="stylesheet"
          href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css"
        />
      </head>
      <body className={`${quicksand.className}`}>
        <AuthProvider>
          <SWRProvider>
            <Suspense fallback={
              <div className="flex h-screen w-full items-center justify-center">
                <Loading color="primary" size="lg" variant="spinner" text="Đang tải..." />
              </div>
            }>
              {children}
            </Suspense>
          </SWRProvider>
          <ToastContainer
            position="top-right"
            autoClose={3000}
            hideProgressBar={false}
            newestOnTop
            closeOnClick
            rtl={false}
            pauseOnFocusLoss
            draggable
            pauseOnHover
            theme="light"
          />
        </AuthProvider>
      </body>
    </html>
  );
}
