"use client";

import { BsArrowDownShort } from "react-icons/bs";

import Image from "next/image";

export default function KinderGartenHomePage() {
  // data.js
  const articles = [
    {
      id: 2,
      category: "Đề nghị xây dựng Nghị quyết của Quốc hội về ...",
      title:
        "Bộ Giáo dục và Đào tạo đang lấy ý kiến góp ý với dự thảo hồ sơ đề nghị xây dựng Nghị quyết của Quốc hội về đổi mới Chương trình giáo ...",
      imageUrl: "/news/news5.png",
      link: "/kinder-garten/news/2"
    },
    {
      id: 1,
      category: "Đổi mới và sáng tạo hướng tới sự phát triển bền ...",
      title:
        "Ngày 22/11, <PERSON><PERSON> Gi<PERSON> dụ<PERSON> và <PERSON> (GDĐT) phối hợp với Trường Cao đẳng Sư phạm Trung ương tổ chức Hội thảo “Đổi mới và sáng tạo hướng tới ...",
      imageUrl: "/news/news6.png",
      link: "/kinder-garten/news/1"
    },
    {
      id: 3,
      category: "Giáo dục sáng tạo và thích ứng đối với trẻ trong ...",
      title:
        "Phát biểu tại hội thảo, Viện trưởng Viện Nghiên cứu giáo dục phát triển tiềm năng con người, Phó Chủ tịch Hội Giáo dục chăm sóc sức khỏe cộng đồng ...",
      imageUrl: "/news/news7.png",
      link: "/kinder-garten/news/3"
    },
    {
      id: 4,
      category: "Cô giáo mầm non tâm huyết và sáng tạo",
      title:
        "Hiểu trẻ, yêu trẻ và yêu nghề là những điều chúng tôi cảm nhận được khi trò chuyện với cô giáo Nguyễn Thị Hoa, sinh năm 1991, đang công tác ...",
      imageUrl: "/news/news8.png",
      link: "/kinder-garten/news/4"
    }
  ];
  const dataCart = [
    {
      id: 1,
      title: "Trẻ mầm non",
      description:
        "Trẻ mầm non (1-6 tuổi) là chủ thể sáng tạo, tham gia học tập trực tuyến, khám phá thế giới và phát triển ý tưởng phù hợp với năng lực cá nhân.",
      imageUrl: "/img/news/img1.svg"
    },
    {
      id: 2,
      title: "Chương trình",
      description:
        "Chương trình giáo dục linh hoạt cho trẻ mầm non, kết hợp phương pháp tích cực và công nghệ, tạo môi trường sáng tạo, tương tác và trải nghiệm theo nhu cầu và sở thích của trẻ.",
      imageUrl: "/img/news/img2.svg"
    },
    {
      id: 3,
      title: "Môi trường",
      description:
        "Môi trường sáng tạo hỗ trợ phát triển nghệ thuật, trí tuệ, thể chất, ngôn ngữ và kỹ năng xã hội, với hệ thống đa phương tiện phong phú giúp kết nối, tương tác và lưu trữ sản phẩm sáng tạo.",
      imageUrl: "/img/news/img3.svg"
    },
    {
      id: 4,
      title: "Giáo viên",
      description:
        "Giáo viên khơi gợi sự tò mò và sáng tạo của trẻ, hỗ trợ phát triển năng lực sáng tạo. Họ thiết kế kế hoạch học tập và xây dựng môi trường sáng tạo để nâng cao chất lượng chuyên môn nghề nghiệp.",
      imageUrl: "/img/news/img4.svg"
    },
    {
      id: 5,
      title: "Gia đình và cộng đồng",
      description:
        "Gia đình và cộng đồng hỗ trợ học tập của trẻ, phát triển kỹ năng xã hội và định hướng sáng tạo cho nhân cách công dân tương lai.",
      imageUrl: "/img/news/img5.svg"
    }
  ];
  const dataCart2 = [
    {
      id: 1,
      title: "Montessori",
      imageUrl: "/img/news/img6.svg"
    },
    {
      id: 2,
      title: "Reggio Emilia",
      imageUrl: "/img/news/img7.svg"
    },
    {
      id: 3,
      title: "Waldorf/ Steiner",
      imageUrl: "/img/news/img8.svg"
    },
    {
      id: 4,
      title: "HighScope",
      imageUrl: "/img/news/img9.svg"
    },
    {
      id: 5,
      title: "STEAM",
      imageUrl: "/img/news/img10.svg"
    }
  ];
  const articleList = [
    {
      id: 1,
      title: "Trường Mầm non Sakura Montessori",
      publisher:
        "KĐT Mễ Trì Hạ, Phạm Hùng, Nam Từ Liêm, Hà Nội 100000, Việt Nam • Ngày tham gia 21/01/2024",
      publisher1: null,
      year: "Ngày tham gia 21/01/2024",
      imageUrl: "/rank/mn-1.png",
      href: "https://truongthptcaugiay.edu.vn"
    },
    {
      id: 2,
      title: "Trường mầm non Vinschool",
      publisher:
        "Số 1 Hoàng Minh Giám, quận Cầu Giấy, Hà Nội • Ngày tham gia 15/02/2024",
      publisher1: null,
      year: "Ngày tham gia 15/02/2024",
      imageUrl: "/rank/mn-2.png",
      href: "https://hn-ams.edu.vn"
    },
    {
      id: 4,
      title: "Trường mầm non Song ngữ Quốc tế – Hà Nội Academy",
      publisher:
        "172 Đ. Nguyễn Hoàng Tôn, Xuân Đỉnh, Tây Hồ, Hà Nội 100000 • Ngày tham gia 11/03/2024",
      publisher1: null,
      year: "Ngày tham gia 29/01/2024",
      imageUrl: "/rank/mn-3.png",
      href: "https://thcs-doanthidiem.edu.vn/"
    },
    {
      id: 5,
      title: "Trường mầm non công nghệ chất lượng cao Steame Garten",
      publisher:
        "Tòa nhà 25T1, Khu đô thị Đông Nam Trần Duy Hưng, Phường Trung Hòa, Quận Cầu Giấy, Thành phố Hà Nội • Ngày tham gia 10/02/2024",
      publisher1: null,
      year: "Ngày tham gia 20/01/2024",
      imageUrl: "/rank/mn-4.png",
      href: "http://c2chuvanan.edu.vn/"
    },
    {
      id: 5,
      title: "Trường quốc tế Nhật bản – JIS",
      publisher:
        "84A P. Nguyễn Thanh Bình, Vạn Phúc, Hà Đông, Hà Nội • Ngày tham gia 05/06/2024",
      publisher1: null,
      year: "Ngày tham gia 20/01/2024",
      imageUrl: "/rank/mn-5.png",
      href: "http://c2chuvanan.edu.vn/"
    }
  ];

  return (
    <section className="space-y-10 pb-[3rem] lg:space-y-20 ">
      <div className="container relative mx-auto !mt-0 space-y-6 pt-6 lg:!px-0">
        <div className=" flex items-center justify-center  text-center">
          <div className="font-bold text-[#414B5B] md:text-[32px] text-[24px]">
            Hệ sinh thái học tập sáng tạo cấp mầm non
            <p className="md:text-[18px] text-[16px] font-[500] text-[#414B5B]">
              Những yếu tố tạo lên hệ sinh thái học tập sáng tạo cấp mầm non
            </p>
          </div>
        </div>
      </div>
      <div className="!mt-[36px] px-[1rem] text-[#414B5B] md:px-[86px]">
        <div className="flex flex-wrap items-center justify-center gap-[25px] xl:flex-nowrap">
          {dataCart.map((item) => (
            <div key={item.title} className="h-[355px] w-full rounded-[20px] shadow-[0px_1px_4px_0px_#00000040] md:h-[500px] md:w-[330px] 2xl:h-[390px]">
              <div className="flex h-[125px] items-center justify-center">
                <div className="aspect-square w-[88px] ">
                  <Image
                    src={item.imageUrl}
                    width={88}
                    height={88}
                    className="h-full w-full object-cover"
                    alt="img"
                  />
                </div>
              </div>
              <div className="space-y-[12px] px-[2rem] text-center">
                <h3 className="text-[24px] font-bold">{item.title}</h3>
                <p className={`font-[400]`}>{item.description}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
      <div className="container relative mx-auto !mt-[54px] space-y-6 pt-6 lg:!px-0">
        <div className=" flex items-center justify-center  text-center">
          <div className="font-bold text-[#414B5B] md:text-[32px]">
            Mô hình giáo dục sáng tạo
            <p className="text-[18px] font-[500] text-[#414B5B]">
              Danh sách các mô hình giáo dục cho trẻ mầm non tiên tiến
            </p>
          </div>
        </div>
      </div>
      <div className="!mt-[36px] px-[1rem] text-[#414B5B] md:px-[86px]">
        <div className="flex flex-wrap items-center justify-center gap-[25px] xl:flex-nowrap">
          {dataCart2.map((item) => (
            <div key={item.title} className="h-[217px] w-full overflow-y-auto rounded-[20px] shadow-[0px_1px_4px_0px_#00000040] lg:w-[330px]">
              <div className="h-[70%] rounded-lg">
                <div className="h-[143px] w-full">
                  <Image
                    src={item.imageUrl}
                    width={88}
                    height={88}
                    className="h-full w-full rounded-t-lg object-cover"
                    alt="img"
                  />
                </div>
              </div>
              <div className="h-[30%] px-[13px]">
                <h3 className="line-clamp-2 text-[18px] font-[600]">
                  {item.title}
                </h3>
              </div>
            </div>
          ))}
        </div>
      </div>
      <div className="relative flex h-fit md:h-[594px] items-center justify-center bg-[url('/mangluoi_2.png')] bg-cover bg-center py-[2rem]">
        {/* Overlay đen với độ mờ */}
        <div className="absolute inset-0 z-0 bg-black opacity-50"></div>

        {/* Nội dung */}
        <div className="relative z-10 h-full w-[90%] border-2 p-2 lg:p-[4rem]">
          <div className="space-y-4 text-white lg:w-[89%]">
            <h3 className="text-[28px] font-bold lg:text-4xl">
              HỆ SINH THÁI HỌC TẬP SÁNG TẠO CẤP MẦM NON
            </h3>
            <p className="text-[18px] lg:text-lg">
              Hệ sinh thái học tập sáng tạo cấp Mầm non là mạng lưới giáo dục
              trong thành phố sáng tạo lĩnh vực thiết kế của thành phố Hà Nội,
              cung cấp các nguồn tài nguyên hỗ trợ quá trình dạy – học trong
              trường mầm non, nhằm khơi gợi ý tưởng, tạo không gian khám phá,
              trải nghiệm thế giới và phát triển toàn diện cho trẻ em. (Đề án
              1217-Đại học Thủ đô Hà Nội thuộc Chương trình 07 của Thành phố Hà
              Nội về đẩy mạnh phát triển khoa học, công nghệ và đổi mới sáng tạo
              giai đoạn 2021-2025).
            </p>
          </div>
        </div>
      </div>
      <div className="px-[1rem] md:px-[92px]">
        <div className="h-[1px] bg-gray-300"></div>
        <div className="mt-[20px] ">
          <h1 className="text-4xl font-bold text-[#414B5B]">Tin tức</h1>
        </div>

        <div className="mt-[40px] grid grid-cols-1 gap-x-[30px] md:grid-cols-2 lg:grid-cols-4">
          {articles.map((article) => (
            <div key={article.id} className="border-t border-gray-300 pt-[8px]">
              <img
                src={article.imageUrl}
                alt={article.title}
                className="mb-[13px] h-40 w-full object-cover"
              />
              <a
                onClick={() => (window.location.href = article.link)}
                className="line-clamp-1 cursor-pointer font-medium text-[#414B5B] hover:text-blue-500"
              >
                {article.category}
              </a>
              <h3 className="mt-2 line-clamp-3 font-bold text-[#414B5B] transition-colors duration-300">
                {article.title}
              </h3>
            </div>
          ))}
        </div>

        <div className="mb-[4rem] mt-[50px] flex items-center justify-center">
          <button onClick={() => (window.location.href = "/kinder-garten/news")} className="flex items-center space-x-2 rounded-full border border-gray-400 px-[20px] py-[7px] text-gray-500 transition-all duration-300 hover:border-gray-700 hover:text-gray-700">
            <span>Xem thêm</span>
            <span className="text-xl text-gray-400">
              <BsArrowDownShort />
            </span>
          </button>
        </div>
      </div>
      <div className="px-[1rem] md:px-[85px]">
        <div className="h-[1px] bg-gray-300"></div>
        <div className="mt-[20px] ">
          <h1 className="text-4xl font-bold text-[#414B5B]">
            Bảng xếp hạng các trường mầm non
          </h1>
        </div>
        <div className=" mt-[2rem] h-[1px] bg-gray-300"></div>

        {articleList.map((article, index) => (
          <div key={article.id}>
            <div
              className="flex flex-col items-center gap-4 rounded-lg pb-[28px] pt-[33px]  lg:flex-row"
            >
              {/* Hình ảnh */}
              <div className="aspect-square h-[209px] min-w-[227.86px] max-w-[227.86px]">
                <Image
                  width={200}
                  height={200}
                  src={article.imageUrl}
                  alt={article.title}
                  className="h-full w-full rounded-md object-contain"
                />
              </div>

              {/* Thông tin bài viết */}
              <div className="flex w-full flex-col justify-between">
                <div>
                  <h2 className="text-xl font-bold text-[#414B5B]">
                    {article.title}
                  </h2>
                  <p className="mt-2 text-[#414B5B]">
                    <span className="font-semibold">{article.publisher}</span> •{" "}
                    {article.year}
                  </p>
                </div>

                {/* Nút Read More */}
                <div className="mt-4 flex justify-end">
                  <a
                    onClick={() => (window.location.href = article.href)}
                    className="cursor-pointer rounded-full border border-blue-500 px-4 py-2 text-blue-500 transition-all duration-300 hover:border-blue-800 hover:text-blue-800"
                  >
                    Xem chi tiết...
                  </a>
                </div>
              </div>
            </div>

            <div>
              {index !== articleList?.length - 1 && (
                <div className="mx-4 mb-[0.5rem] mt-[1rem] h-[1px] bg-gray-300"></div>
              )}
            </div>
          </div>
        ))}
      </div>
    </section>
  );
}
