import HeroSection from "@/components/ui/HeroSection";
import { BsArrowDownShort } from "react-icons/bs";

export default async function NewsPage() {
  const articles = [
    {
      id: 2,
      category: "Đề nghị xây dựng Nghị quyết của ...",
      title:
        '<PERSON><PERSON> Giáo dục và Đào tạo đang lấy ý kiến góp ý với dự thảo hồ sơ đề nghị xây dựng Nghị quyết của Quốc hội về đổi...',
      imageUrl: "/news/news5.png"
    },
    {
      id: 1,
      category: "Đổi mới và sáng tạo hướng tới sự phát ...",
      title:
        "Ngày 22/11, Bộ Giáo dục và Đào tạo (GDĐT) phối hợp với Trường Cao đẳng Sư phạm Trung ương tổ chức Hội thảo...",
      imageUrl: "/news/news6.png"
    },
    {
      id: 3,
      category: "<PERSON><PERSON><PERSON><PERSON> dục sáng tạo và thích ứng đối với...",
      title:
        "<PERSON><PERSON>t biểu tại hội thảo, Viện trưởng Viện Nghiên cứu giáo dục phát triển tiềm năng con người, Phó Chủ tịch Hội Giáo...",
      imageUrl: "/news/news7.png"
    },
    {
      id: 4,
      category: "Cô giáo mầm non tâm huyết và sáng tạo",
      title:
        "Hiểu trẻ, yêu trẻ và yêu nghề là những điều chúng tôi cảm nhận được khi trò chuyện với cô giáo Nguyễn Thị Hoa, si...",
      imageUrl: "/news/news8.png"
    }
  ];
  return (
    <div className="px-[1rem] md:px-[85px]">
      <div className="mt-[2rem] text-[#414B5B]">
        <div className="page_breadcrumb">
          <div className=" mx-auto">
            <h1 className="text-4xl font-bold">Tin tức</h1>
            <p className="mt-2 text-lg">Tin tức và sự kiện</p>

            {/* Phần dòng kẻ */}
            <div className="my-6 border-b border-gray-200"></div>

            {/* Phần tiêu đề Latest */}
            <h2 className="text-3xl font-bold">Hiển thị tin tức mới nhất</h2>

            {/* Phần tìm kiếm */}
            <div className="mt-4 flex items-center space-x-4">
              <input
                type="text"
                placeholder="Nhập tiêu đề ..."
                className="w-full max-w-[400px] rounded-full border border-gray-300 px-4 py-2 focus:outline-none"
              />
              <button className="rounded-full bg-blue-500 px-6 py-2 text-white">
                Tìm kiếm
              </button>
            </div>

            {/* Phần kết quả */}
            <div className="mt-4 text-gray-600">209 kết quả</div>
          </div>
        </div>
      </div>

      <div className=" mx-auto text-[#414B5B]">
        <div className="h-[1px] bg-gray-300"></div>
        <div className="mt-[20px] ">
          <h1 className="text-4xl font-bold">Tin tức</h1>
        </div>

        <div className="mt-[40px] grid grid-cols-1 gap-x-[30px] md:grid-cols-2 lg:grid-cols-4">
          {articles.map((article) => (
            <div
              key={article.id}
              className="border-t border-gray-300 pt-[20px]"
            >
              <img
                src={article.imageUrl}
                alt={article.title}
                className="mb-4 h-40 w-full object-cover"
              />
              <a
                href={`/kinder-garten/news/${article.id}`}
                className=" font-medium text-[#414B5B] hover:text-blue-500"
              >
                {article.category}
              </a>
              <h3 className="mt-2  font-bold text-[#414B5B] transition-colors duration-300">
                {article.title}
              </h3>
            </div>
          ))}
        </div>

        <div className="mb-[4rem] mt-[50px] flex items-center justify-center">
          <button className="flex items-center space-x-2 rounded-full border border-gray-400 px-[20px] py-[7px] text-gray-500 transition-all duration-300 hover:border-gray-700 hover:text-gray-700">
            <span>Xem thêm</span>
            <span className="text-xl text-gray-400">
              <BsArrowDownShort />
            </span>
          </button>
        </div>
      </div>
    </div>
  );
}
