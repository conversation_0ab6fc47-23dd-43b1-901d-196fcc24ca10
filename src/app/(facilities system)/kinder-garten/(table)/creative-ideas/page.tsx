import Image from "next/image";

export default function CreativeIdeasPage() {
  const data = [
    { stt: 1, link: "https://stemsmart.net/", img: "/creative-ideas/image-1.png" },
    { stt: 2, link: "https://kidsactivitiesblog.com/", img: "/creative-ideas/image-2.png" },
    { stt: 3, link: "https://happyhooligans.ca/", img: "/creative-ideas/image-3.png" },
    { stt: 4, link: "https://handsonaswegrow.com/", img: "/creative-ideas/image-4.png" },
    { stt: 5, link: "https://theimaginationtree.com/", img: "/creative-ideas/image-5.png" },
    { stt: 6, link: "https://www.prekinders.com/", img: "/creative-ideas/image-6.png" },
    { stt: 7, link: "https://www.adobe.com/vn_vi/products/photoshop.html", img: "/creative-ideas/image-7.png" },
    { stt: 8, link: "https://www.adobe.com/vn_vi/products/illustrator.html", img: "/creative-ideas/image-8.png" },
    { stt: 9, link: "https://www.sketchbook.com/", img: "/creative-ideas/image-9.png" },
    { stt: 10, link: "https://www.adobe.com/vn_vi/products/premiere.html", img: "/creative-ideas/image-10.png" },
    { stt: 11, link: "https://www.figma.com/", img: "/creative-ideas/image-11.png" },
    { stt: 12, link: "https://tlo.edu.vn/", img: "/creative-ideas/image-12.png" },
    { stt: 13, link: "https://thekidpage.com", img: "/creative-ideas/image-13.png" },
    { stt: 14, link: "https://nickjr.tv/", img: "/creative-ideas/image-14.png" }
];
  return (
    <section className="container mx-auto mt-[28px] space-y-10 px-[1rem] pb-[3rem] lg:space-y-20">
      <div className="space-y-[36px] ">
        <h3 className="text-center text-[36px] font-bold text-[#414B5B]">
          Ý tưởng sáng tạo
        </h3>
        <div className="relative space-y-[38px] text-[#414B5B]">
          {/* <OverlayText scale='scale-1'/> */}
          <div className="space-y-[10px] text-[20px]">
            <div className="space-y-[20px]">
              <div className="overflow-x-auto text-[16px] font-semibold">
                <table className="min-w-full table-auto border-collapse border border-black">
                  <thead className="h-[87px]">
                    <tr>
                      <th className="w-[10%] border border-black bg-[#FFE498] px-4 py-2 font-[400]">
                        STT
                      </th>
                      <th className="border border-black bg-[#FFE498] px-4 py-2 font-[400]">
                        Website
                      </th>
                      <th className="border border-black bg-[#FFE498] px-4 py-2 font-[400]">
                        Link website
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {data.map((row, index) => (
                      <tr key={index} className="h-[100px] text-[12px]">
                        <td className="border border-black px-4 py-2 text-center font-bold">
                          {index + 1}
                        </td>
                        <td className="w-[20%] border border-black px-4 py-2 font-bold">
                          <div className="flex h-full w-full items-center justify-center object-cover">
                            <Image
                              alt="logo"
                              width={112}
                              height={105}
                              src={row.img}
                            />
                          </div>
                        </td>
                        <td className="border border-black px-4 py-2">
                          <a
                            href={row.link}
                            className="font-bold text-[#659FD9] underline"
                            target="_blank"
                            rel="noopener noreferrer"
                          >
                            {row.link}
                          </a>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
