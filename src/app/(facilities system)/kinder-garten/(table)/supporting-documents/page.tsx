import Image from "next/image";

export default function EducationalTopicsPage() {
  const data = [
    {
      stt: 1,
      name: "ChatGPT",
      img: "/kinder-garten/supporting-documents/chatgpt.png",
      link: "https://chatgpt.com/",
      des: "Thiết kế như một chatbot để cung cấp câu trả lời và tương tác qua cuộc trò chuyện.",
    },
    {
      stt: 2,
      name: "Co<PERSON><PERSON>",
      link: "https://copilot.microsoft.com/",
      img: "/kinder-garten/supporting-documents/copilot-microsoft.png",
      des: "Trợ lý kỹ thuật số cá nhân, hỗ trợ các tác vụ cụ thể và đưa ra đề xuất.",
    },
    {
      stt: 3,
      name: "Tạo bài trình chiếu tự động",
      link: "https://gamma.app/",
      img: "/kinder-garten/supporting-documents/gammaapp.png",
      des: "Cho phép tạo free 10 bài/1 tài khoản email.",
    },
    {
      stt: 3,
      name: "<PERSON>y<PERSON>n văn bản thành giọng nói",
      link: "https://ttsopenai.com/",
      img: "/kinder-garten/supporting-documents/ttsopenai.png",
      des: "Công cụ chuyển văn bản thành giọng nói (Text-to-Speech) sử dụng công nghệ AI của OpenAI, giúp tạo ra giọng đọc tự nhiên từ văn bản.",
    },
    {
      stt: 3,
      name: "Sáng tác nhạc",
      link: "https://suno.com/",
      img: "/kinder-garten/supporting-documents/suno.png",
      des: "Một nền tảng trí tuệ nhân tạo giúp tạo và chia sẻ nội dung âm thanh, hỗ trợ các công cụ như chuyển văn bản thành giọng nói và tạo podcast hoặc bài học âm thanh.",
    },
    {
      stt: 3,
      name: "Tạo sách điện tử",
      link: "https://bookcreator.com/",
      img: "/kinder-garten/supporting-documents/bookcreator.jpg",
      des: "Một nền tảng trực tuyến cho phép người dùng tạo sách điện tử tương tác, bao gồm văn bản, hình ảnh, video và âm thanh, thích hợp cho giáo dục và sáng tạo cá nhân.",
    },
    {
      stt: 3,
      name: "Class Dojo",
      link: "https://www.classdojo.com/",
      img: "/kinder-garten/supporting-documents/classdojo.png",
      des: "Nền tảng giáo dục giúp kết nối giáo viên, học sinh và phụ huynh, hỗ trợ quản lý lớp học, theo dõi tiến độ học tập và trao đổi thông tin.",
    },
    {
      stt: 3,
      name: "Padlet",
      link: "http://padlet.com/",
      img: "/kinder-garten/supporting-documents/padlet.png",
      des: "Công cụ trực tuyến cho phép tạo bảng tương tác để chia sẻ ý tưởng, tài liệu và hợp tác trong giáo dục và công việc nhóm.",
    },
    {
      stt: 3,
      name: "Quizizz",
      link: "https://quizizz.com/",
      img: "/kinder-garten/supporting-documents/quizizz.png",
      des: "Một nền tảng học tập trực tuyến giúp giáo viên tạo các bài kiểm tra, trò chơi và câu hỏi trắc nghiệm để học sinh tham gia và theo dõi tiến độ học tập.",
    },
    {
      stt: 3,
      name: "Kahoot",
      link: "https://kahoot.com/",
      img: "/kinder-garten/supporting-documents/kahoot.png",
      des: "Công cụ tạo trò chơi học tập với câu hỏi trắc nghiệm, giúp học sinh tham gia vào các cuộc thi vui nhộn và tương tác trong lớp học.",
    },
    {
      stt: 3,
      name: "Blooket",
      link: "https://www.blooket.com/",
      img: "/kinder-garten/supporting-documents/blooket.png",
      des: "Nền tảng học tập cho phép tạo các trò chơi câu hỏi trắc nghiệm và biến học tập thành những cuộc thi thú vị, giúp học sinh ôn tập kiến thức hiệu quả.",
    },
    {
      stt: 3,
      name: "Wordwall",
      link: "https://wordwall.net/",
      img: "/kinder-garten/supporting-documents/wordwall.png",
      des: "Công cụ tạo các trò chơi và hoạt động học tập tương tác, bao gồm các bài kiểm tra, bài tập ghép từ, và nhiều dạng câu hỏi khác để nâng cao trải nghiệm học tập.",
    }
  ];
  return (
    <section className="container mx-auto mt-[28px] space-y-10 px-[1rem] pb-[3rem] lg:space-y-20">
      <div className="space-y-[36px] ">
        <h3 className="text-center text-[36px] font-bold text-[#414B5B]">
          Tài liệu hỗ trợ
        </h3>
        <div className="relative space-y-[38px] text-[#414B5B]">
          {/* <OverlayText scale='scale-1'/> */}
          <div className="space-y-[10px] text-[20px]">
            <div className="space-y-[20px]">
              <div className="overflow-x-auto text-[16px] font-semibold">
                <table className="min-w-full table-auto border-collapse border border-black">
                  <thead className="h-[87px]">
                    <tr>
                      <th className="border border-black bg-[#FFE498] px-4 py-2 font-[400]">
                        STT
                      </th>
                      <th className="border border-black bg-[#FFE498] px-4 py-2 font-[400]">
                        Website
                      </th>
                      <th className="border border-black bg-[#FFE498] px-4 py-2 font-[400]">
                        Link website
                      </th>
                      <th className="border border-black bg-[#FFE498] px-4 py-2 font-[400]">
                        Ghi chú
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {data.map((row, index) => (
                      <tr key={index} className="h-[100px] text-[12px]">
                        <td className="border border-black px-4 py-2 text-center font-bold">
                          {index + 1}
                        </td>
                        <td className="border border-black px-4 py-2 font-bold">
                          <div className="w-full h-full object-cover flex items-center justify-center">
                            <Image
                              alt="logo"
                              width={112}
                              height={105}
                              src={row.img}
                            />
                          </div>
                        </td>
                        <td className="border border-black px-4 py-2">
                          <a
                            href={row.link}
                            className="font-bold text-[#659FD9] underline"
                            target="_blank"
                            rel="noopener noreferrer"
                          >
                            {row.link}
                          </a>
                        </td>
                        <td className="border border-black px-4 py-2 text-left font-bold">
                          {row.des}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
