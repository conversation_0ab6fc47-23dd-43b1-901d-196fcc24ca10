import Image from "next/image";

export default function EducationalTopicsPage() {
  const data = [
    {
      stt: 1,
      name: "Kids Activities Blog",
      img: "/community/image-1.png",
      link: "http://kidsactivitiesblog.com/",
      payPlan: 1,
      activity: 3,
      sport: 3,
      members: 3
    },
    {
      stt: 2,
      name: "Red Ted Art",
      link: "http://redtedart.com/",
      payPlan: 5,
      activity: 3,
      sport: 11,
      members: 1,
      img: "/community/image-2.png"
    },
    {
      stt: 3,
      name: "Education Week's Blogs - Early Years",
      link: "http://blogs.edweek.org/edweek/early_years/",
      payPlan: 2,
      activity: 5,
      sport: 14,
      members: 2,
      img: "/community/image-3.png"
    },
    {
      stt: 3,
      name: "Happy Hooligans",
      link: "http://happyhooligans.ca/",
      payPlan: 4,
      activity: 9,
      sport: 9,
      members: 9,
      img: "/community/image-4.png"
    },
    {
      stt: 3,
      name: "Hands On - As We Grow",
      link: "http://handsonaswegrow.com/",
      payPlan: 3,
      activity: 9,
      sport: 21,
      members: 5,
      img: "/community/image-5.png"
    },
    {
      stt: 3,
      name: "The Imagination Tree",
      link: "http://theimaginationtree.com/",
      payPlan: 7,
      activity: 7,
      sport: 20,
      members: 4,
      img: "/community/image-6.png"
    },
    {
      stt: 3,
      name: "PreKinders",
      link: "http://prekinders.com/blog/",
      payPlan: 6,
      activity: 20,
      sport: 10,
      members: 13,
      img: "/community/image-7.png"
    },
    {
      stt: 3,
      name: "Childhood 101",
      link: "http://childhood101.com/",
      payPlan: 12,
      activity: 11,
      sport: 7,
      members: 16,
      img: "/community/image-8.png"
    },
    {
      stt: 3,
      name: "Teach Mama",
      link: "http://teachmama.com/",
      payPlan: 17,
      activity: 8,
      sport: 8,
      members: 12,
      img: "/community/image-9.png"
    },
    {
      stt: 3,
      name: "Living Montessori Now",
      link: "http://livingmontessorinow.com/",
      payPlan: 10,
      activity: 8,
      sport: 28,
      members: 6,
      img: "/community/image-10.png"
    },
    {
      stt: 3,
      name: "Tinkerlab",
      link: "http://tinkerlab.com/",
      payPlan: 14,
      activity: 9,
      sport: 23,
      members: 8,
      img: "/community/image-11.png"
    },
    {
      stt: 3,
      name: "Kaplan Early Learning Blog",
      link: "https://www.kaplanco.com/blog/",
      payPlan: 13,
      activity: 10,
      sport: 26,
      members: 14,
      img: "/community/image-12.png"
    },
    {
      stt: 3,
      name: "HiMama",
      link: "https://www.himama.com/blog/",
      payPlan: 11,
      activity: 27,
      sport: 15,
      members: 23,
      img: "/community/image-13.png"
    },
    {
      stt: 3,
      name: "Preschool Inspirations",
      link: "http://www.preschoolinspirations.com/",
      payPlan: 13,
      activity: 27,
      sport: 12,
      members: 12,
      img: "/community/image-14.png"
    },
    {
      stt: 3,
      name: "An Everyday Story",
      link: "http://www.aneverydaystory.com/",
      payPlan: 22,
      activity: 20,
      sport: 7,
      members: 28,
      img: "/community/image-15.png"
    },
    {
      stt: 3,
      name: "Teach Preschool",
      link: "http://www.teachpreschool.org/",
      payPlan: 15,
      activity: 13,
      sport: 29,
      members: 10,
      img: "/community/image-16.png"
    },
    {
      stt: 3,
      name: "Sharing Kindergarten",
      link: "http://www.sharingkindergarten.com/",
      payPlan: 24,
      activity: 24,
      sport: 6,
      members: 27,
      img: "/community/image-17.png"
    },
    {
      stt: 3,
      name: "Dakota County Technical College Program Blog",
      link: "http://blogs.dctc.edu/dawnbraa/",
      payPlan: 19,
      activity: 23,
      sport: 18,
      members: 20,
      img: "/community/image-18.png"
    },
    {
      stt: 3,
      name: "Teacher Tom",
      link: "http://blogs.dctc.edu/dawnbraa/",
      payPlan: 28,
      activity: 5,
      sport: 25,
      members: 19,
      img: "/community/image-19.png"
    },
    {
      stt: 3,
      name: "A Differentiated Kindergarten",
      link: "http://www.differentiatedkindergarten.com/",
      payPlan: 25,
      activity: 23,
      sport: 9,
      members: 26,
      img: "/community/image-20.png"
    },
    {
      stt: 3,
      name: "Kindergarten",
      link: "http://www.kindergartenkindergarten.com/",
      payPlan: 20,
      activity: 30,
      sport: 14,
      members: 21,
      img: "/community/image-21.png"
    },
    {
      stt: 3,
      name: "Mrs. Wills Kindergarten",
      link: "http://www.mrswillskindergarten.com/",
      payPlan: 23,
      activity: 19,
      sport: 24,
      members: 15,
      img: "/community/image-22.png"
    },
    {
      stt: 3,
      name: "Rainbows Within Reach",
      link: "http://www.rainbowswithinreach.blogspot.com/",
      payPlan: 33,
      activity: 13,
      sport: 15,
      members: 30,
      img: "/community/image-23.png"
    }
  ];
  return (
    <section className="container mx-auto mt-[28px] space-y-10 px-[1rem] pb-[3rem] lg:space-y-20">
      <div className="space-y-[36px] ">
        <h3 className="text-center text-[36px] font-bold text-[#414B5B]">
          Cộng đồng
        </h3>
        <div className="relative space-y-[38px] text-[#414B5B]">
          {/* <OverlayText scale='scale-1'/> */}
          <div className="space-y-[10px] text-[20px]">
            <div className="space-y-[20px]">
              <div className="overflow-x-auto text-[16px] font-semibold">
                <table className="min-w-full table-auto border-collapse border border-black">
                  <thead className="h-[87px]">
                    <tr>
                      <th className="border border-black bg-[#FFE498] px-4 py-2 font-[400]">
                        Ranking
                      </th>
                      <th className="border border-black bg-[#FFE498] px-4 py-2 font-[400]">
                        Early Childhood Education Blog
                      </th>
                      <th className="border border-black bg-[#FFE498] px-4 py-2 font-[400]">
                        Link website
                      </th>
                      <th className="border border-black bg-[#FFE498] px-4 py-2 font-[400]">
                        Poppularity
                      </th>
                      <th className="border border-black bg-[#FFE498] px-4 py-2 font-[400]">
                        Authority
                      </th>
                      <th className="border border-black bg-[#FFE498] px-4 py-2 font-[400]">
                        Growth
                      </th>
                      <th className="border border-black bg-[#FFE498] px-4 py-2 font-[400]">
                        Last Year Rank
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {data.map((row, index) => (
                      <tr key={index} className="h-[100px] text-[12px]">
                        <td className="border border-black px-4 py-2 text-center font-bold">
                          {index + 1}
                        </td>
                        <td className="border border-black px-4 py-2 font-bold">
                          <div className="w-full h-full object-cover flex items-center justify-center">
                            <Image
                              alt="logo"
                              width={112}
                              height={105}
                              src={row.img}
                            />
                          </div>
                        </td>
                        <td className="border border-black px-4 py-2">
                          <a
                            href={row.link}
                            className="font-bold text-[#659FD9] underline"
                            target="_blank"
                            rel="noopener noreferrer"
                          >
                            {row.link}
                          </a>
                        </td>
                        <td className="border border-black px-4 py-2 text-center font-bold">
                          {row.payPlan}
                        </td>
                        <td className="border border-black px-4 py-2 text-center font-bold">
                          {row.activity}
                        </td>
                        <td className="border border-black px-4 py-2 text-center font-bold">
                          {row.sport}
                        </td>
                        <td className="border border-black px-4 py-2 text-center font-bold">
                          {row.members}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
