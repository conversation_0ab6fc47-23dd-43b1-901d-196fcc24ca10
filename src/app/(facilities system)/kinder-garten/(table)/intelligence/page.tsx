import Image from "next/image";

export default function IntelligencePage() {
  const data = [
    {
      stt: 1,
      link: "https://www.clever.edu.vn/",
      img: "/intelligence/image-1.png"
    },
    {
      stt: 2,
      link: "https://www.youtube.com/@artforkidshub",
      img: "/intelligence/image-2.png"
    },
    {
      stt: 3,
      link: "https://www.youtube.com/@peepkids",
      img: "/intelligence/image-3.png"
    },
    {
      stt: 4,
      link: "https://www.youtube.com/@kiddievtvietnam",
      img: "/intelligence/image-4.png"
    },
    {
      stt: 5,
      link: "https://www.youtube.com/@VinaCartoon/featured",
      img: "/intelligence/image-5.png"
    },
    {
      stt: 6,
      link: "https://www.youtube.com/@ChuChuTV",
      img: "/intelligence/image-6.png"
    },
    { stt: 7, link: "https://tle.edu.vn", img: "/intelligence/image-7.png" },
    {
      stt: 8,
      link: "https://www.sesamestreet.org",
      img: "/intelligence/image-8.png"
    },
    {
      stt: 9,
      link: "https://www.makeitgenius.com",
      img: "/intelligence/image-9.png"
    },
    {
      stt: 10,
      link: "https://kids.nationalgeographic.com/",
      img: "/intelligence/image-10.png"
    },
    {
      stt: 11,
      link: "https://www.howstuffworks.com",
      img: "/intelligence/image-11.png"
    },
    {
      stt: 12,
      link: "https://www.almanac.com/kids",
      img: "/intelligence/image-12.png"
    }
  ];
  return (
    <section className="container mx-auto mt-[28px] space-y-10 px-[1rem] pb-[3rem] lg:space-y-20">
      <div className="space-y-[36px] ">
        <h3 className="text-center text-[36px] font-bold text-[#414B5B]">
          Trí tuệ
        </h3>
        <div className="relative space-y-[38px] text-[#414B5B]">
          {/* <OverlayText scale='scale-1'/> */}
          <div className="space-y-[10px] text-[20px]">
            <div className="space-y-[20px]">
              <div className="overflow-x-auto text-[16px] font-semibold">
                <table className="min-w-full table-auto border-collapse border border-black">
                  <thead className="h-[87px]">
                    <tr>
                      <th className="w-[10%] border border-black bg-[#FFE498] px-4 py-2 font-[400]">
                        STT
                      </th>
                      <th className="border border-black bg-[#FFE498] px-4 py-2 font-[400]">
                        Website
                      </th>
                      <th className="border border-black bg-[#FFE498] px-4 py-2 font-[400]">
                        Link website
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {data.map((row, index) => (
                      <tr key={index} className="h-[100px]">
                        <td className="border border-black px-4 py-2 text-center text-[12px] font-bold">
                          {index + 1}
                        </td>
                        <td className="border w-[20%] border-black px-4 py-2 font-bold">
                          <div className="flex h-full w-full items-center justify-center object-cover">
                            <Image
                              alt="logo"
                              width={112}
                              height={105}
                              src={row.img}
                            />
                          </div>
                        </td>
                        <td className="border border-black px-4 py-2 font-bold">
                          <a
                            href={row.link}
                            className="text-[12px] font-bold text-[#659FD9] underline"
                            target="_blank"
                            rel="noopener noreferrer"
                          >
                            {row.link}
                          </a>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
