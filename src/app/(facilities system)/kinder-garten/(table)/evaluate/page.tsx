import Image from "next/image";

export default function evaluatePage() {
  const data = [
    {
      stt: 1,
      link: "https://quizizz.com/admin/quiz/5e087890bc8ed0001b930406/trc-chin-thc-mt-s-k-nng-cho-tr-mm-non",
      img: "/evaluate/image-1.png"
    },
    {
      stt: 2,
      link: "https://testiq.vn/iq-test-cho-be.html",
      img: "/evaluate/image-2.png"
    },
    { stt: 3, link: "https://ia365.vn/", img: "/evaluate/image-3.png" },
    {
      stt: 4,
      link: "https://edusa.vn/bai-test-tieng-anh-cho-tre-mam-non-chi-tiet/",
      img: "/evaluate/image-4.png"
    },
    {
      stt: 12,
      link: "https://www.eslgamesplus.com/",
      img: "/evaluate/image-12.png"
    },
    {
      stt: 5,
      link: "https://danhgiatritue.vn/danh-gia/danh-gia-ki-nang-tri-tue",
      img: "/evaluate/image-5.png"
    },
    {
      stt: 6,
      link: "https://sangiaoducketnoi.org/",
      img: "/evaluate/image-6.png"
    },
    {
      stt: 7,
      link: "https://www.clever.edu.vn/",
      img: "/evaluate/image-7.png"
    },
    { stt: 8, link: "https://nearpod.com/", img: "/evaluate/image-8.png" },
    {
      stt: 9,
      link: "https://www.baamboozle.com/",
      img: "/evaluate/image-9.png"
    },
    {
      stt: 10,
      link: "https://kahoot.com/schools/",
      img: "/evaluate/image-10.png"
    },
    { stt: 11, link: "https://quizlet.com/", img: "/evaluate/image-11.png" },
    {
      stt: 12,
      link: "https://www.eslgamesplus.com/",
      img: "/evaluate/image-13.png"
    }
  ];
  return (
    <section className="container mx-auto mt-[28px] space-y-10 px-[1rem] pb-[3rem] lg:space-y-20">
      <div className="space-y-[36px] ">
        <h3 className="text-center text-[36px] font-bold text-[#414B5B]">
          Đánh giá
        </h3>
        <div className="relative space-y-[38px] text-[#414B5B]">
          {/* <OverlayText scale='scale-1'/> */}
          <div className="space-y-[10px] text-[20px]">
            <div className="space-y-[20px]">
              <div className="overflow-x-auto text-[16px] font-semibold">
                <table className="min-w-full table-auto border-collapse border border-black">
                  <thead className="h-[87px]">
                    <tr>
                      <th className="border border-black bg-[#FFE498] px-4 py-2 font-[400]">
                        STT
                      </th>
                      <th className="border border-black bg-[#FFE498] px-4 py-2 font-[400]">
                        Website
                      </th>
                      <th className="border border-black bg-[#FFE498] px-4 py-2 font-[400]">
                        Link website
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {data.map((row, index) => (
                      <tr key={index} className="h-[100px] text-[12px]">
                        <td className="border border-black px-4 py-2 text-center font-bold">
                          {index + 1}
                        </td>
                        <td className="border w-[20%] border-black px-4 py-2 font-bold">
                          <div className="flex h-full w-full items-center justify-center object-cover">
                            <Image
                              alt="logo"
                              width={112}
                              height={105}
                              src={row.img}
                            />
                          </div>
                        </td>
                        <td className="border border-black px-4 py-2">
                          <a
                            href={row.link}
                            className="font-bold text-[#659FD9] underline"
                            target="_blank"
                            rel="noopener noreferrer"
                          >
                            {row.link}
                          </a>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
