import Image from "next/image";

export default function ApplicationSoftwarePage() {
  const data = [
    {
      stt: 1,
      typeOfDoc: "Tài liệu số Việt Nam",
      dataLink: [
        "https://study.hanoi.edu.vn/hoc-truc-tuyen",
        "http://thuvienso.daihocthudo.edu.vn/",
        "https://thuvienso.net/tai-lieu/tu-khoa/Ng%c3%a0nh+Gi%c3%a1o+d%e1%bb%a5c+m%e1%ba%a7m+non",
        "https://igiaoduc.vn/",
        "https://www.youtube.com/channel/UC26h5Oyo0ktTVJ4M2fc_Vg",
        "https://www.youtube.com/@thuvienbaigiangmthinhliet5757",
        "https://edulive.net/mam-non?f=giaovien",
        "https://thuvienso.digilib.vn/",
        "https://baigiang.violet.vn/#google_vignette",
        "http://edu.net.vn/media/",
        "http://www.vjol.info/",
        "https://tapchigiaoduc.moet.edu.vn/",
        "http://www.ebook.edu.vn/",
        "https://sites.google.com/view/khohoclieuso23-24/trang-ch%E1%BB%A7",
        "https://sites.google.com/pgd-hoangmai.edu.vn/khohoclieutaomngzn?usp=sharing&utm_source=zalo&utm_medium=zalo&utm_campaign=zalo",
        "https://sites.google.com/pgd-hoangmai.edu.vn/lp-mgb-mn-hong-vn-th/trang-ch%E1%BB%A7",
        "https://sites.google.com/view/khohoclieumonkmnhapha/trang-ch%E1%BB%A7",
        "https://danggiavietnam.wordpress.com/",
        "https://sites.google.com/pgd-hoangmai.edu.vn/khamphamaugiaolon-mnthanhtri/",
        "https://khophammem.vn/"
      ]
    },
    {
      stt: 2,
      typeOfDoc: "Tài liệu số mở quốc tế",
      dataLink: [
        "http://library.truman.edu/search_articles/open-access.asp",
        "http://escholarship.org/",
        "http://www.doaj.org/",
        "http://scholar.google.com.vn/",
        "http://www.ergoobservatory.info/ejdirectory.html"
      ]
    }
  ];

  return (
    <section className="container mx-auto mt-[28px] space-y-10 px-[1rem] pb-[3rem] lg:space-y-20">
      <div className="space-y-[36px] ">
        <h3 className="text-center text-[36px] font-bold text-[#414B5B]">
          Thư viện số
        </h3>
        <div className="relative space-y-[38px] text-[#414B5B]">
          {/* <OverlayText scale='scale-1'/> */}
          <div className="space-y-[10px] text-[20px]">
            <div className="space-y-[20px]">
              <div className="overflow-x-auto text-[16px] font-semibold">
                <table className="min-w-full table-auto border-collapse border border-black">
                  <thead className="h-[87px]">
                    <tr>
                      <th className="w-[10%] border border-black bg-[#FFE498] px-4 py-2 font-[400]">
                        STT
                      </th>
                      <th className="border border-black bg-[#FFE498] px-4 py-2 font-[400]">
                        Loại tài liệu
                      </th>
                      <th className="border border-black bg-[#FFE498] px-4 py-2 font-[400]">
                        Link website
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {data.map((row, index) => (
                      <tr key={index} className="h-[100px]">
                        <td className="border border-black px-4 py-2 text-center text-[12px] font-bold">
                          {index + 1}
                        </td>
                        <td className="w-[20%] border border-black px-4 py-2 font-bold">
                          <div className="flex h-full w-full items-center justify-center object-cover text-[12px] font-bold text-[#414B5B]">
                            <p>{row.typeOfDoc}</p>
                          </div>
                        </td>
                        <td className="border border-black px-4 py-2 font-bold">
                          <ul className="list-inside">
                            {row.dataLink.map((link, index) => (
                              <li key={index}>
                                <a
                                  href={link}
                                  className="text-[12px] font-bold text-[#659FD9] underline"
                                  target="_blank"
                                  rel="noopener noreferrer"
                                >
                                  {link}
                                </a>
                              </li>
                            ))}
                          </ul>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
