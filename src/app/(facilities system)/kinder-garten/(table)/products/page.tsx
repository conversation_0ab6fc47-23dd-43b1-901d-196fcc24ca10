import Image from "next/image";

export default function ProductsPage() {
  const data = [
    { stt: 1, link: "http://csdl.moet.gov.vn", img: "/products/image-1.png" },
    {
      stt: 2,
      link: "https://topskkn.com/sang-kien-kinh-nghiem-mam-non/",
      img: "/products/image-2.png"
    },
    {
      stt: 3,
      link: "https://sangkienkinhnghiem.net/sang-kien-kinh-nghiem-mam-non-mau-giao/",
      img: "/products/image-3.png"
    },
    {
      stt: 4,
      link: "https://best4team.com/skkn/sang-kien-kinh-nghiem-mam-non/",
      img: "/products/image-4.png"
    },
    {
      stt: 5,
      link: "https://www.facebook.com/groups/1841719969015988/",
      img: "/products/image-5.png"
    },
    {
      stt: 6,
      link: "https://sangkienkinhnghiem.com.vn/sang-kien-kinh-nghiem-mam-non/",
      img: "/products/image-6.png"
    },
    {
      stt: 7,
      link: "https://luanvan99.com/sang-kien-kinh-nghiem-mam-non-bd557.html",
      img: "/products/image-7.png"
    },
    {
      stt: 8,
      link: "https://luanvanviet.com/sang-kien-kinh-nghiem-mam-non/",
      img: "/products/image-8.png"
    },
    {
      stt: 9,
      link: "https://skkn.vn/sang-kien-kinh-nghiem-mot-so-kinh-nghiem-boi-duong-nang-cao-chat-luong-doi-ngu-trong-truong-mam-non-2903/",
      img: "/products/image-9.png"
    }
  ];

  return (
    <section className="container mx-auto mt-[28px] space-y-10 px-[1rem] pb-[3rem] lg:space-y-20">
      <div className="space-y-[36px] ">
        <h3 className="text-center text-[36px] font-bold text-[#414B5B]">
          Sản phẩm
        </h3>
        <div className="relative space-y-[38px] text-[#414B5B]">
          {/* <OverlayText scale='scale-1'/> */}
          <div className="space-y-[10px] text-[20px]">
            <div className="space-y-[20px]">
              <div className="overflow-x-auto text-[16px] font-semibold">
                <table className="min-w-full table-auto border-collapse border border-black">
                  <thead className="h-[87px]">
                    <tr>
                      <th className="border border-black bg-[#FFE498] px-4 py-2 font-[400]">
                        STT
                      </th>
                      <th className="border border-black bg-[#FFE498] px-4 py-2 font-[400]">
                        Website
                      </th>
                      <th className="border border-black bg-[#FFE498] px-4 py-2 font-[400]">
                        Link website
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {data.map((row, index) => (
                      <tr key={index} className="h-[100px]">
                        <td className="border border-black px-4 py-2 text-center text-[12px] font-bold">
                          {index + 1}
                        </td>
                        <td className="border border-black px-4 py-2 font-bold">
                          <div className="flex h-full w-full items-center justify-center object-cover">
                            <Image
                              alt="logo"
                              width={112}
                              height={105}
                              src={row.img}
                            />
                          </div>
                        </td>
                        <td className="border border-black px-4 py-2 font-bold">
                          <a
                            href={row.link}
                            className="text-[12px] font-bold text-[#659FD9] underline"
                            target="_blank"
                            rel="noopener noreferrer"
                          >
                            {row.link}
                          </a>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
