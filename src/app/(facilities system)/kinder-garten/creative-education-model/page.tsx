import OverlayText from '@/components/ui/OverlayText';

export default function CreativeEducationModelPage() {
    return (
        <section className="space-y-10 mt-[28px] pb-[3rem] px-[1rem] md:px-[85px] lg:space-y-20">
            <div className="space-y-[36px] ">
                <h3 className="text-[36px] text-center text-[#414B5B] font-bold"><PERSON><PERSON> hình giáo dục sáng tạo</h3>
                <div className="space-y-[10px] relative text-[#414B5B]">
                    <OverlayText scale='scale-1'/>
                    <div className="text-[20px] space-y-[10px]">
                        <p className="font-bold text-[#414B5B]">1. <PERSON><PERSON><PERSON></p>
                        <p className="font-[400]">
                            <PERSON><PERSON><PERSON> là một phương pháp gi<PERSON><PERSON> dục bắt nguồn từ Ý, do tiến sĩ <PERSON> phát triển. Trong mô hình gi<PERSON><PERSON>ụ<PERSON>, trẻ em được lựa chọn hoạt động phù hợp với sở thích của mình thông qua hệ thống giáo cụ đa dạng, phong phú. Cách tiếp cận này được cho là sẽ thúc đẩy những giai đoạn vàng trong sự phát triển của trẻ, hình thành khái niệm, kiến thức thông qua trải nghiệm thực tế với mô hình học cụ để giúp trẻ phát triển năng lực cá nhân, độc lập hành động và chủ động khám phá.
                            Chương trình giáo dục Montessori cung cấp cho trẻ các nội dung giáo dục đầy đủ các lĩnh vực phát triển từ nhận thức, ngôn ngữ, thể chất, thẩm mỹ và tình cảm-kĩ năng xã hội. Các lớp học cho phép trộn độ tuổi sẽ khuyến khích  trẻ được trải nghiệm, thực hiện các bài tập với học cụ, học hỏi và hỗ trợ nhau trong quá trình khám phá. Môi trường học tập được thiết kế đặc thù làm nổi bật hệ thống giáo cụ, sắp xếp trật tự, phân chia các không gian hoạt động riêng biệt với các góc:
                        </p>
                        <ul className="list-disc space-y-[10px] pl-[2rem]">
                            <li>Giác quan.</li>
                            <li>Ngôn ngữ.</li>
                            <li>Khoa học.</li>
                            <li>Thực hành cuộc sống.</li>
                            <li>Văn hóa.</li>
                        </ul>
                    </div>
                    <div className="text-[20px] space-y-[10px]">
                        <p className="font-bold text-[#414B5B]">2. Reggio Emilia</p>
                        <p className="font-[400]">
                            Reggio Emilia tiếp cận phát triển trẻ em một cách toàn diện, quan tâm đến phát triển tư duy sáng tạo, kĩ năng xã hội và sự tự tin khám phá thế giới của đứa trẻ. Loris Malaguzzi đã phát triển phương pháp này vào những năm 1970. Hai mươi năm sau, tổ chức Reggio Emilia được thành lập ở Ý để ghi nhận và truyền bá phương pháp của ông trên toàn thế giới. Theo tổ chức Reggio, chương trình giảng dạy dựa trên cấu trúc của nó đề cao:
                        </p>
                        <ul className="list-disc space-y-[10px] pl-[2rem]">
                            <li>Trẻ là người học chủ động và có năng lực tự nhiên nên đứa trẻ được tôn trọng và khuyến khích sự thể hiện đa dạng của trẻ.</li>
                            <li>Môi trường học tập giàu tương tác và khám phá giúp trẻ trải nghiệm, tự do khám phá và học hỏi từ không quan xung quanh tạo nên không gian sáng tạo từ ánh sáng tự nhiên và các vật liệu gần gũi với thiên nhiên.</li>
                            <li>Giáo viên đóng vai trò là người hướng dẫn, thiết kế dự án, ghi lại quá trình học tập, khám phá của mỗi cá nhân trẻ bằng nhiều hình thức đa dạng, tích hợp công nghệ trong đánh giá sự tiến bộ và phát triển của trẻ.</li>
                            <li>Sự tham gia của cha mẹ trẻ và cộng đồng: khuyến khích tham gia tích cực vào quá trình học tập của trẻ. Phạm vi học tập mở rộng hơn, không chỉ trong phạm vi lớp học mà còn trong toàn bộ cộng đồng.</li>
                        </ul>
                    </div>
                    <div className="text-[20px] space-y-[10px]">
                        <p className="font-bold text-[#414B5B]">3. Môi trường</p>
                        <p className="font-[400]">
                            Chương trình giảng dạy mầm non của Waldorf đặt nền tảng học tập suốt đời cho đứa trẻ ham học hỏi. Các hoạt động tập trung vào:
                        </p>
                        <ul className="list-disc space-y-[10px] pl-[2rem]">
                            <li>Phát triển toàn diện về trí tuệ, thể chất và tinh thần cho trẻ, đồng thời phát triển nghệ thuật, các giá trị đạo đức và xã hội.</li>
                            <li>Chương trình học tập xây dựng phù hợp với từng giai đoạn phát triển của trẻ, khuyến khích chơi đùa tự do, sáng tạo và học qua các hoạt động thực hành, không hướng giáo dục kiến thức lý thuyết sớm mà thông qua câu chuyện, bài hát và trò chơi, liên kết mật thiết với thiên nhiên. Trẻ em được tiếp xúc với nghệ thuật và thủ công sớm lồng ghép trong chương trình, sẽ thúc đẩy khả năng sáng tạo, kỹ năng giải quyết vấn đề, kiên nhẫn, tỉ mỉ hơn trong công việc và tình huống thực tiễn. </li>
                            <li>Không gian học tập an toàn, được yêu thương và gần gũi với thiên nhiên: sử dụng vật liệu tự nhiên (gỗ, vải), ánh sáng tự nhiên, ấm áp, trải nghiệm các hoạt động ngoài trời trong lành sẽ kích thích trẻ sáng tạo và tư duy độc lập.</li>
                            <li>Tôn trọng và khuyến khích cá nhân hóa học tập: giáo viên hiểu rõ nhu cầu và tài năng riêng biệt của mỗi cá nhân trẻ, xây dựng kế hoạch học tập không theo khuôn mẫu, tạo ra môi trường học tập linh hoạt để mỗi trẻ phát triển theo nhịp độ và khả năng của mình. Giáo viên thiết kế các dự án và hoạt động nhóm giúp trẻ kích thích khám phá và phát triển kĩ năng xã hội, làm việc nhóm và tư duy độc lập từ sớm.</li>
                        </ul>
                        <p className="font-[400]">
                            Nhà khoa học người Áo Rudolf Steiner đã phát triển phương pháp giáo dục này vào đầu thế kỷ 20. Giáo dục Waldorf được tạo ra nhằm mục đích tạo cơ hội để tìm thấy tiềm năng trong mỗi đứa trẻ. Phương pháp học tập tự định hướng giúp xây dựng niềm đam mê giáo dục, thông qua các hoạt động nghệ thuật trong tất cả các môn học. Các bài học mang tính trải nghiệm, không chỉ nghiên cứu mà còn khuyến khích sự sáng tạo, độc lập và hiểu biết sâu sắc của trẻ về mọi chủ đề mà các em đang khám phá.
                        </p>
                    </div>
                    <div className="text-[20px] space-y-[10px]">
                        <p className="font-bold text-[#414B5B]">4. HighScope</p>
                        <p className="font-[400]">
                            Với hơn 50 năm kinh nghiệm trong giáo dục mầm non, HighScope được xem là một trong những phương pháp giáo dục thành công trong việc khuyến khích khả năng sáng tạo của các em nhỏ. David Weikart đã phát triển từ những năm 1960, xây dựng Chương trình giảng dạy dựa trên một loạt các chỉ số phát triển chính để phát triển lớp học, gọi là KDI. Các KDI này hướng dẫn giáo viên lựa chọn các hoạt động và dự án phù hợp với mọi lứa tuổi của trẻ, tạo ra môi trường thúc đẩy sự phát triển tư duy và kĩ năng của trẻ. Các chỉ số này bao quát mọi khía cạnh phát triển từ thể chất, xã hội đến nhận thức và cảm xúc. Chúng cũng giúp giáo viên hiểu và giải thích các nhu cầu và thắc mắc của trẻ để định hướng tốt nhất cho việc học của các em.
                        </p>
                        <p className="font-[400]">
                            Trong phương pháp HighScope, sử dụng chu trình “”Plan-Do-Review) (Lên kế hoạch-Thực hiện-Đánh giá) giúp trẻ tự quản lý, phát triển tư duy logic, và kỹ năng giải quyết vấn đề. Giáo viên đóng vai trò như một đối tác của trẻ, khuyến khích sự phát triển, hỗ trợ đưa ra chiến thuật giải quyết vấn đề và quyết những thắc mắc của học sinh qua các dự án thực hành, học tập trải nghiệm.
                        </p>
                        <p className="font-[400]">
                            Tổ chức HighScope nhắc lại rằng “chơi – lập – làm – xét” là trọng tâm của mỗi ngày học. Học sinh tự lựa chọn hoạt động hoặc dự án mong muốn, lập kế hoạch để đạt được mục tiêu và sau đó thảo luận, tương tác tích cực với giáo viên.
                        </p>

                    </div>
                    <div className="text-[20px] space-y-[10px]">
                        <p className="font-bold text-[#414B5B]">5. STEAM</p>
                        <p className="font-[400]">
                            Mô hình giáo dục mầm non STEAM (Science, Technology, Engineering, Arts, and Mathematics) là một phương pháp tiếp cận liên ngành trong giáo dục, kết hợp giữa khoa học, công nghệ, kỹ thuật, nghệ thuật và toán học. Mô hình này nhấn mạnh vào việc giúp trẻ nhỏ khám phá, trải nghiệm và học hỏi thông qua các hoạt động thực hành và tư duy sáng tạo. STEAM đang ngày càng được công nhận là một phương pháp giáo dục mầm non hiệu quả, chuẩn bị cho trẻ những kỹ năng cần thiết trong thế kỷ 21.
                        </p>
                        <ul className="list-disc space-y-[10px] pl-[2rem]">
                            <li>Phát triển tư duy khoa học và kĩ năng giải quyết vấn đề, thúc đẩy sáng tạo và nghệ thuật, tăng cường kĩ năng công nghệ và kĩ thuật thiết kế, tạo ra sản phẩm, đồng thời phát triển tư duy toán học và kĩ năng logic, giải quyết tình huống trong đời sống.</li>
                            <li>Môi trường học tập trong mô hình STEAM được thiết kế khuyến khích trẻ tự do khám phá, sáng tạo và thử nghiệm, như một “phòng thí nghiệm nhỏ” với nhiều vật liệu và công cụ đa dạng tạo điều kiện để trẻ thực hiện các thí nghiệm khoa học nhỏ, xây dựng mô hình kĩ thuật hoặc sáng tạo nghệ thuật.</li>
                            <li>Giáo viên khuyến khích và tạo điều kiện để trẻ được trải nghiệm và tự khám phá, tự thử nghiệm các ý tưởng sáng tạo và tư duy độc lập, phản biện, “đồng hành”quá trình học tập của trẻ.</li>
                            <li>Chương trình học tập được thiết kế theo hướng tích hợp, liên ngành, giúp trẻ kết nối các lĩnh vực về khoa học, nghệ thuật, kĩ thuật, công nghệ tạo thành hệ thống thông qua các chủ đề hoặc dự án cụ thể nhằm giải quyết vấn đề thực tế mà không tách rời môn học riêng biệt.</li>
                            <li>Tôn trọng sự phát triển của cá nhân, đa dạng trong cách học của mỗi trẻ, tạo điều kiện để phát triển theo năng lực riêng thông qua quan sát và đánh giá sự phát triển của trẻ, coi trọng quá trình học tập hơn kết quả, chấp nhận sự thất bại, chưa thành công tạo ra sản phẩm.</li>
                            <li>Chuẩn bị cho trẻ những kĩ năng cần thiết trong thế giới hiện đại, giúp trẻ phát triển toàn diện từ tư duy khoa học đến kĩ năng xã hội.</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>
    );
}
