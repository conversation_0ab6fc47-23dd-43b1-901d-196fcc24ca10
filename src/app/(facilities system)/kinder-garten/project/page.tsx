"use client";

import Image from "next/image";

import IconButton from "@/components/button/IconButton";

export default function ProjectPage() {
  return (
    <section className="container mx-auto mt-[28px] space-y-10 px-[1rem] pb-[3rem] lg:space-y-20">
      <div className="space-y-[36px] ">
        <h3 className="text-center text-[36px] font-bold text-[#414B5B]">
          Dự án
        </h3>
        <div className="space-y-[10px]">
          <div className="space-y-[10px]">
            <p className="text-[20px] font-bold text-[#414B5B]">
              1. Dự án chú ếch con
            </p>
            <div className="space-y-[10px]">
              <Image src="/frog.svg" width={1345} height={720} alt="image" />
              <div className="flex flex-wrap gap-3">
                <IconButton
                  fileUrl={"/kinder-garten/du-an/Du_An_Chu_Ech_Con.pdf"}
                />
              </div>
            </div>
          </div>
          <div className="space-y-[10px]">
            <p className="text-[20px] font-bold text-[#414B5B]">
              2. Dự án lúa nước
            </p>
            <div className="space-y-[10px]">
              <Image src="/luanuoc.svg" width={1345} height={720} alt="image" />
              <div className="flex flex-wrap gap-3">
                <IconButton
                  fileUrl={"/kinder-garten/du-an/Du_An_Lua_Nuoc.pdf"}
                />
              </div>
            </div>
          </div>
          <div className="space-y-[10px]">
            <p className="text-[20px] font-bold text-[#414B5B]">
              3. Dự án nước
            </p>
            <div className="space-y-[10px]">
              <Image src="/nuoc.svg" width={1345} height={720} alt="image" />
              <div className="flex flex-wrap gap-3">
                <IconButton
                  fileUrl={"/kinder-garten/du-an/Du_An_Nuoc.pptx"}
                />
              </div>
            </div>
          </div>
          <div className="space-y-[10px]">
            <p className="text-[20px] font-bold text-[#414B5B]">
              4. Dự án trứng
            </p>
            <div className="space-y-[10px]">
              <Image src="/trung.svg" width={1345} height={720} alt="image" />
              <div className="flex flex-wrap gap-3">
                <IconButton
                  fileUrl={"/kinder-garten/du-an/Du_An_Trung.pdf"}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
