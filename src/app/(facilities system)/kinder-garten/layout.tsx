import "@/style/globals.css";
import ClientKinderGartenLayout from "@/components/ClientKinderGartenLayout";

export const metadata = {
  title: "<PERSON><PERSON> sinh thái học tập, sáng tạo",
  icons: {
    icon: "/logo-2.svg"
  }
};

export default function KinderGartenLayout({
  children
}: Readonly<{
  children: React.ReactNode;
}>) {
  const dataMenu = [
    { name: "Trang chủ", href: "/kinder-garten" },
    { name: "Ý tưởng sáng tạo", href: "/kinder-garten/creative-ideas" },
    { name: "<PERSON><PERSON><PERSON><PERSON> trình", href: "" },
    { name: "<PERSON><PERSON><PERSON> trường", href: "" },
    { name: "<PERSON><PERSON><PERSON> nguyên học liệu", href: "" }
  ];
  const dataOfMenuChild = [
    {
      title:
        "<PERSON><PERSON> sinh thái học tập sáng tạo: kết nối kiến thức, phát triển tư duy, chia sẻ.",
      button: [],
      items: []
    },
    {
      title:
        "<PERSON> tức mới nhất về hoạt động của hệ sinh thái học tập, sáng tạo.",
      button: [],
      items: []
    },
    {
      title: "Chương trình trong hệ sinh thái học tập sáng tạo cấp mầm non.",
      button: [],
      items: [
        {
          name: "Chủ đề giáo dục",
          href: "/kinder-garten/educational-topics",
          image: "/kinder-garten/p1.jpg"
        },
        {
          name: "Kế hoạch năm học",
          href: "/kinder-garten/plan",
          image: "/kinder-garten/p2.jpg"
        },
        {
          name: "Hoạt động",
          href: "/kinder-garten/work",
          image: "/kinder-garten/p3.jpg"
        },
        {
          name: "Dự án",
          href: "/kinder-garten/project",
          image: "/kinder-garten/p4.jpg"
        },
        {
          name: "Chương trình nhà trường",
          href: "/kinder-garten/school-program",
          image: "/kinder-garten/p5.jpg"
        }
      ]
    },
    {
      title: "Môi trường hệ sinh thái học tập sáng tạo cấp mầm non.",
      button: [],
      items: [
        {
          name: "Đồ chơi-Giáo cụ",
          href: "/kinder-garten/educational-toys",
          image: "/kinder-garten/mn1.jpg"
        },
        {
          name: "Không gian sáng tạo",
          href: "/kinder-garten",
          image: "/kinder-garten/mn2.jpg"
        },
        {
          name: "Công nghệ",
          href: "/kinder-garten/technology",
          image: "/kinder-garten/mn3.jpg"
        },
        {
          name: "Gia đình",
          href: "/kinder-garten/family",
          image: "/kinder-garten/mn4.jpg"
        }
      ]
    },
    {
      title: "Tài nguyên học liệu hệ sinh thái học tập sáng tạo cấp mầm non.",
      button: [],
      items: [
        {
          name: "Thư viện số",
          href: "/kinder-garten/digital-library",
          image: "/kinder-garten/t1.JPG"
        },
        {
          name: "Phần mềm/ứng dụng",
          href: "/kinder-garten/application-software",
          image: "/kinder-garten/t2.JPG"
        },
        {
          name: "Trò chơi tương tác",
          href: "/kinder-garten",
          image: "/kinder-garten/t3.jpg"
        },
        {
          name: "Văn bản pháp lý",
          href: "/kinder-garten",
          image: "/kinder-garten/t4.jpg"
        }
      ]
    }
  ];
  const dataMenuSelected = [
    {
      title: "Giới thiệu",
      href: "/kinder-garten/about-us",
      child: []
    },
    {
      title: "Tin tức",
      href: "/kinder-garten/news",
      child: []
    },
    {
      title: "Mô hình GD sáng tạo",
      href: "/kinder-garten/creative-education-model",
      child: []
    },
    {
      title: "Giáo viên",
      href: "/",
      child: [
        { title: "Báo cáo", href: "/kinder-garten/report" },
        { title: "Tài liệu hỗ trợ", href: "/kinder-garten/supporting-documents" },
        {
          title: "Tư vấn giáo dục",
          href: "/kinder-garten/practical-applications"
        },
        { title: "Sản phẩm", href: "/kinder-garten/products" },
        { title: "Cộng đồng", href: "/kinder-garten/community" }
      ]
    },
    {
      title: "Sáng tạo",
      href: "/",
      child: [
        { title: "Nghệ thuật", href: "/kinder-garten/art" },
        { title: "Trí tuệ", href: "/kinder-garten/intelligence" },
        { title: "Ngôn ngữ", href: "/kinder-garten/language" },
        { title: "Thể chất", href: "/kinder-garten/physical" },
        { title: "Tình cảm xã hội", href: "/kinder-garten/social-emotions" },
        { title: "Đánh giá", href: "/kinder-garten/evaluate" }
      ]
    },
    {
      title: "Thống kê",
      href: "/kinder-garten",
      child: []
    },
    {
      title: "Đánh giá",
      href: "/kinder-garten",
      child: []
    },
    {
      title: "Xếp hạng",
      href: "/kinder-garten",
      child: []
    },
    {
      title: "Quản lý",
      href: "/",
      child: [
        { title: "Trường mầm non", href: "/kinder-garten" },
        { title: "Quận/ Huyện", href: "/kinder-garten" },
        { title: "Tỉnh/ Thành phố", href: "/kinder-garten" }
      ]
    }
  ];
  return (
    <ClientKinderGartenLayout 
      dataMenu={dataMenu} 
      dataOfMenuChild={dataOfMenuChild} 
      dataMenuSelected={dataMenuSelected}
    >
      {children}
    </ClientKinderGartenLayout>
  );
}
