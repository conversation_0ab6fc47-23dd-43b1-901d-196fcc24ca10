"use client";

import IconButton from "@/components/button/IconButton";

export default function WorkPage() {
  const dataAnimal = [
    {
      title: "Đề tài con rùa",
      fileUrl: "/kinder-garten/activities/ConRua.pdf"
    },
    {
      title: "Đ<PERSON> tài khám phá đại dương",
      fileUrl: "/kinder-garten/activities/KhamPhaDaiDuong.pdf"
    },
    {
      title: "Đ<PERSON> tài bò sát",
      fileUrl: "/kinder-garten/activities/BoSat.pdf"
    },
    {
      title: "Đ<PERSON> tài chim cách cụt",
      fileUrl: "/kinder-garten/activities/ChimCanhCut.pdf"
    },
    {
      title: "Đ<PERSON> tài làm nhà cho thú nuôi",
      fileUrl: "/kinder-garten/activities/LamNhaChoThuNuoi.pdf"
    }
  ];
  const dataTransportationTheme = [
    {
      title: "<PERSON><PERSON> tài khinh khí cầ<PERSON>",
      fileUrl: "/kinder-garten/activities/KhinhKhiCai.pdf"
    },
    {
      title: "Đề tài xe tải",
      fileUrl: "/kinder-garten/activities/XeTai.pdf"
    }
  ];
  const dataNaturalPhenomenonTheme = [
    {
      title: "Đề tài cối xay gió",
      fileUrl: "/kinder-garten/activities/CoiXayGio.pdf"
    },
    {
      title: "Đề tài cầu vồng",
      fileUrl: "/kinder-garten/activities/CauVong.pdf"
    },
    {
      title: "Đề tài núi lửa",
      fileUrl: "/kinder-garten/activities/NuiLua.pdf"
    },
    {
      title: "Đề tài vòng tuần hoàn của nước",
      fileUrl: "/kinder-garten/activities/VongTuanHoanCuaNuoc.pdf"
    }
  ];
  return (
    <section className="container mx-auto mt-[28px] space-y-10 px-[1rem] pb-[3rem] lg:space-y-20">
      <div className="space-y-[36px] ">
        <h3 className="text-center text-[36px] font-bold text-[#414B5B]">
          Hoạt động
        </h3>
        <div className="space-y-[10px]">
          <div className="space-y-[10px]">
            <p className="text-[20px] font-bold text-[#414B5B]">
              1. Chủ đề động vật
            </p>
            <div className="flex flex-wrap gap-3">
              {dataAnimal.map((item, index) => (
                <IconButton
                  key={index}
                  label={item.title}
                  isBtnDownload={false}
                  fileUrl={item.fileUrl}
                />
              ))}
            </div>
          </div>
          <div className="space-y-[10px]">
            <p className="text-[20px] font-bold text-[#414B5B]">
              2. Chủ đề lễ hội
            </p>
            <div className="flex flex-wrap gap-3">
              <IconButton
                label={"Đề tài quà 8 - 3"}
                isBtnDownload={false}
                fileUrl={"/kinder-garten/activities/Qua83.pdf"}
              />
            </div>
          </div>
          <div className="space-y-[10px]">
            <p className="text-[20px] font-bold text-[#414B5B]">
              3. Chủ đề phương tiện giao thông
            </p>
            <div className="flex flex-wrap gap-3">
              {dataTransportationTheme.map((item, index) => (
                <IconButton
                  key={index}
                  label={item.title}
                  isBtnDownload={false}
                  fileUrl={item.fileUrl}
                />
              ))}
            </div>
          </div>
          <div className="space-y-[10px]">
            <p className="text-[20px] font-bold text-[#414B5B]"></p>
            <div className="flex flex-wrap gap-3">
              {dataNaturalPhenomenonTheme.map((item) => (
                <IconButton
                  label={item.title}
                  isBtnDownload={false}
                  fileUrl={item.fileUrl}
                />
              ))}
            </div>
          </div>
          <div className="space-y-[10px]">
            <p className="text-[20px] font-bold text-[#414B5B]">
              5. Chủ đề quê hương đất nước
            </p>
            <div className="flex flex-wrap gap-3">
              <IconButton
                label={"Đề tài chùa một cột"}
                isBtnDownload={false}
                fileUrl={"/kinder-garten/activities/ChuaMotCot.pdf"}
              />
            </div>
          </div>
          <div className="space-y-[10px]">
            <p className="text-[20px] font-bold text-[#414B5B]">
              6. Chủ đề trung thu
            </p>
            <div className="flex flex-wrap gap-3">
              <IconButton
                label={"Đề tài vui hội trung thu"}
                isBtnDownload={false}
                fileUrl={"/kinder-garten/activities/VuiHoiTrungThu.pdf"}
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
