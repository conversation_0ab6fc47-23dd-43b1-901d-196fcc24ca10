"use client";

import Image from "next/image";

import IconButton from "@/components/button/IconButton";

export default function RePortPage() {
  return (
    <section className="container mx-auto mt-[28px] space-y-10 px-[1rem] pb-[3rem] lg:space-y-20">
      <div className="space-y-[36px] ">
        <h3 className="text-center text-[36px] font-bold text-[#414B5B]">
          Báo cáo
        </h3>
        <div className="space-y-[10px]">
          <div className="space-y-[10px]">
            <p className="text-[20px] font-bold text-[#414B5B]">
              1. Mẫu 01:KHNH_GVMN
            </p>
            <div className="space-y-[10px]">
              <Image src="/mau-1.svg" width={1345} height={720} alt="image" />
              <div className="flex flex-wrap gap-3">
                <IconButton
                  isPreview={false}
                  fileUrl={"/kinder-garten/giao-vien/bao-cao/mau1.docx"}
                />
              </div>
            </div>
          </div>
          <div className="space-y-[10px]">
            <p className="text-[20px] font-bold text-[#414B5B]">
              2. Mẫu 02: KHT_GVMN
            </p>
            <div className="space-y-[10px]">
              <Image src="/mau-2.svg" width={1345} height={720} alt="image" />
              <div className="flex flex-wrap gap-3">
                <IconButton
                  isPreview={false}
                  fileUrl={"/kinder-garten/giao-vien/bao-cao/mau2.docx"}
                />
              </div>
            </div>
          </div>
          <div className="space-y-[10px]">
            <p className="text-[20px] font-bold text-[#414B5B]">
              3. Mẫu 03: KHH/GVMN
            </p>
            <div className="space-y-[10px]">
              <Image src="/mau-3.svg" width={1345} height={720} alt="image" />
              <div className="flex flex-wrap gap-3">
                <IconButton
                  isPreview={false}
                  fileUrl={"/kinder-garten/giao-vien/bao-cao/mau3.docx"}
                />
              </div>
            </div>
          </div>
          <div className="space-y-[10px]">
            <p className="text-[20px] font-bold text-[#414B5B]">
              4. Mẫu 04: BCCD
            </p>
            <div className="space-y-[10px]">
              <Image src="/mau-4.svg" width={1345} height={720} alt="image" />
              <div className="flex flex-wrap gap-3">
                <IconButton
                  isPreview={false}
                  fileUrl={"/kinder-garten/giao-vien/bao-cao/mau4.docx"}
                />
              </div>
            </div>
          </div>
          <div className="space-y-[10px]">
            <p className="text-[20px] font-bold text-[#414B5B]">
              5. Mẫu 05: Phiếu đánh giá hoạt động giáo dục
            </p>
            <div className="space-y-[10px]">
              <Image src="/mau-5.svg" width={1345} height={720} alt="image" />
              <div className="flex flex-wrap gap-3">
                <IconButton
                  isPreview={false}
                  fileUrl={"/kinder-garten/giao-vien/bao-cao/mau5.docx"}
                />
              </div>
            </div>
          </div>
          <div className="space-y-[10px]">
            <p className="text-[20px] font-bold text-[#414B5B]">
              6. Mẫu 06: Phiếu đánh giá hoạt động chăm sóc, nuôi dưỡng
            </p>
            <div className="space-y-[10px]">
              <Image src="/mau-6.svg" width={1345} height={720} alt="image" />
              <div className="flex flex-wrap gap-3">
                <IconButton
                  isPreview={false}
                  fileUrl={"/kinder-garten/giao-vien/bao-cao/mau6.docx"}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
