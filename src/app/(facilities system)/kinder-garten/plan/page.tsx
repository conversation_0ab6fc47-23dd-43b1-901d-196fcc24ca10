"use client";

import Image from "next/image";
import { useEffect, useState, useMemo } from "react";

import TabNavigation from "./components/TabSelected";
import Table from "./components/Table";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import ExploreSociety from "./components/doc/ExploreSociety";
import Exercise from "./components/doc/Exercise";
import GettingKnowWork from "./components/doc/GettingKnowWork";
import Story from "./components/doc/Story";
import Shape from "./components/doc/Shape";
import GettingKnowWriting from "./components/doc/GettingKnowWriting";
import GettingKnowMath from "./components/doc/GettingKnowMath";

import Shape2 from "./components/doc/Shape2";
import GettingKnowWork2 from "./components/doc/GettingKnowWork2";
import GettingKnowWriting2 from "./components/doc/GettingKnowWriting2";
import Story2 from "./components/doc/Story2";

import DailyDiet from "./components/doc/DailyDiet";
import Recognize from "./components/doc/Recognize";
import GettingKnowWork3 from "./components/doc/GettingKnowWork3";
import GettingKnowWork4 from "./components/doc/GettingKnowWork4";
import ReadPoetry from "./components/doc/ReadPoetry";
import Music1 from "./components/doc/Music1";
import Music2 from "./components/doc/Music2";
import Music3 from "./components/doc/Music3";
import Exercise2 from "./components/doc/Exercise2";
import Exercise3 from "./components/doc/Exercise3";
import GettingKnowWriting3 from "./components/doc/GettingKnowWriting3";
import GettingKnowWriting4 from "./components/doc/GettingKnowWriting4";
import SchoolProgramPage from "./components/schoolYearPlan/SchoolProgramPage";

export default function PlantPage() {
  const [themeId, setThemeId] = useState("1");
  const [planId, setPlanId] = useState<"1" | "2" | "3">("1");
  const [activeTab, setActiveTab] = useState<any>(1);
  const [activeOption, setActiveOption] = useState<string>("cs20");

  const dataPlan: {
    [key in "1" | "2" | "3"]: {
      id: number;
      label: string;
      dataChild: { id: string; label: string; content?: JSX.Element }[];
      content?: JSX.Element;
    }[];
  } = {
    "1": [
      {
        id: 1,
        label: "Khám Phá Xã Hội",
        dataChild: [],
        content: <ExploreSociety />
      },
      {
        id: 2,
        label: "Thể Dục",
        dataChild: [],
        content: <Exercise />
      },
      {
        id: 3,
        label: "Làm Quen Chữ Cái",
        dataChild: [],
        content: <GettingKnowWork />
      },
      {
        id: 4,
        label: "Kể Chuyện",
        dataChild: [],
        content: <Story />
      },
      {
        id: 5,
        label: "Tạo Hình",
        dataChild: [],
        content: <Shape />
      },
      {
        id: 6,
        label: "Làm Quen Chữ Viết",
        dataChild: [],
        content: <GettingKnowWriting />
      },
      {
        id: 7,
        label: "Làm Quen Với Toán",
        dataChild: [],
        content: <GettingKnowMath />
      }
    ],
    "2": [
      {
        id: 1,
        label: "Tạo Hình",
        dataChild: [],
        content: <Shape2 />
      },
      {
        id: 9,
        label: "Làm Quen Chữ Cái",
        dataChild: [],
        content: <GettingKnowWork2 />
      },
      {
        id: 10,
        label: "Kể Chuyện",
        dataChild: [],
        content: <Story2 />
      },
      {
        id: 11,
        label: "Làm Quen Chữ Viết",
        dataChild: [],
        content: <GettingKnowWriting2 />
      }
    ],
    "3": [
      {
        id: 1,
        label: "Khám Phá Xã Hội",
        dataChild: [
          {
            id: "cs20",
            label: "Nhu Cầu Ăn Uống Hằng Ngày Của Bé",
            content: <DailyDiet />
          },
          {
            id: "cs21",
            label: "Nhận Biết Và Phòng Tránh Tai Nạn Thương Tích (Cs 21)",
            content: <Recognize />
          }
        ]
      },
      {
        id: 13,
        label: "Làm Quen Chữ Cái",
        dataChild: [
          {
            id: "cs20",
            label: "Tập Tô Chữ A, Ă, Â (Cs 91)",
            content: <GettingKnowWork3 />
          },
          {
            id: "cs23",
            label: "Làm Quen Chữ C (Cs 90)",
            content: <GettingKnowWork4 />
          }
        ]
      },
      {
        id: 14,
        label: "Đọc Thơ",
        dataChild: [],
        content: <ReadPoetry />
      },
      {
        id: 15,
        label: "Âm Nhạc",
        dataChild: [
          {
            id: "cs20",
            label:
              "Vận Động Theo Tiết Tấu Chậm Bài “Chiếc Đèn Ông Sao” (Cs 101)",
            content: <Music1 />
          },
          {
            id: "cs21",
            label: "Vận Động Vỗ Tiết Tấu Chậm Bài “Tìm Bạn Thân” (Cs 101)",
            content: <Music2 />
          },
          {
            id: "cs22",
            label:
              "Vận Động Theo Tiết Tấu Chậm Bài “Chiếc Đèn Ông Sao” (Cs 101)",
            content: <Music3 />
          }
        ]
      },
      {
        id: 16,
        label: "Thể Dục",
        dataChild: [
          {
            id: "cs20",
            label: "Bật Liên Tục Vào 5 Vòng Thể Dục (Cs 1)",
            content: <Exercise2 />
          },
          {
            id: "cs21",
            label: "Đi Khụy Gối (Cs 11)",
            content: <Exercise3 />
          }
        ]
      },
      {
        id: 17,
        label: "Làm Quen Chữ Viết",
        dataChild: [
          {
            id: "cs20",
            label: "Tập Viết Nét Xiên Trái Kết Hợp Nét Ngang (Cs 90)",
            content: <GettingKnowWriting3 />
          },
          {
            id: "cs21",
            label: "Viết Nét Sổ Thẳng Nối Với Nét Cong (Cs 90)",
            content: <GettingKnowWriting4 />
          }
        ]
      }
    ]
  };

  const getActivity = (id: number) => {
    setActiveTab(id);
  };
  const getActivityOption = (optionId: string) => {
    setActiveOption(optionId);
  };

  const memoizedId = useMemo(() => {
    const selectedItem = dataPlan[planId].find(
      (item: {
        id: number;
        label: string;
        dataChild: any[];
        content?: JSX.Element;
      }) => {
        return item.id === activeTab;
      }
    );

    if (selectedItem?.dataChild?.length) {
      return selectedItem.dataChild.find(
        (child: { id: string; label: string; content?: JSX.Element }) => {
          return child.id === activeOption;
        }
      )?.content;
    }

    return selectedItem?.content;
  }, [planId, activeOption, activeTab]);
  const title = useMemo(() => {
    const data: any =
      {
        1: {
          title: "BÉ LÀ AI",
          time: "Từ ngày 08/09/2014 đến ngày 13/09/2014"
        },
        2: {
          title: " CƠ THỂ CỦA BÉ",
          time: "Từ ngày 16/09/2014 đến ngày 22/09/2014"
        },
        3: {
          title: "BÉ CẦN GÌ ĐỂ LỚN LÊN VÀ KHỎE MẠNH",
          time: "Từ ngày 23/09/2014 đến ngày 28/09/2014"
        }
      };
    return data[planId];
  }, [planId]);
  useEffect(() => {
    setActiveTab(1);
  }, [planId]);

  return (
    <section className="container mx-auto mt-[28px] space-y-10 px-[1rem] pb-[3rem] lg:space-y-20">
      <div className="space-y-[36px] ">
        <div className="flex items-center justify-center">
          <Select value={themeId} onValueChange={setThemeId}>
            <SelectTrigger className="w-fit text-[36px] font-bold text-[#414B5B]">
              <SelectValue placeholder="Kế hoạch chủ đề" />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                <SelectItem value="1">Kế hoạch chủ đề</SelectItem>
                <SelectItem value="2">Kế hoạch năm học</SelectItem>
              </SelectGroup>
            </SelectContent>
          </Select>
        </div>
        {themeId === "1" ? (
          <>
            <div className="relative space-y-[38px] text-[#414B5B]">
              <div className="space-y-[10px] text-[20px]">
                <div className="space-y-[10px] text-center">
                  <div className="flex items-center justify-center">
                    <Select value={planId} onValueChange={(value) => setPlanId(value as "1" | "2" | "3")}>
                      <SelectTrigger className="w-fit text-[20px] font-bold text-[#414B5B]">
                        <SelectValue placeholder="KẾ HOẠCH TUẦN I" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectGroup>
                          <SelectItem value="1">KẾ HOẠCH TUẦN I</SelectItem>
                          <SelectItem value="2">KẾ HOẠCH TUẦN II</SelectItem>
                          <SelectItem value="3">KẾ HOẠCH TUẦN III</SelectItem>
                        </SelectGroup>
                      </SelectContent>
                    </Select>
                  </div>
                  <p className="font-bold text-[#414B5B]">
                    Chủ đề nhánh: {title.title}
                  </p>
                  <p className="font-bold text-[#414B5B]">
                    Độ tuổi: 5-6 tuổi - MG Lớn
                  </p>
                  <p className="font-bold text-[#414B5B]">
                    Thời gian: {title.time}
                  </p>
                </div>
                <Table />
              </div>
            </div>
            <div className="flex flex-col md:flex-row md:items-center gap-3">
              <p className="text-[20px] text-[#414B5B] text-[600]">
                Hoạt động:
              </p>
              <TabNavigation
                getActivityOption={getActivityOption}
                getActivity={getActivity}
                value={planId}
                valueOption={activeOption}
                data={dataPlan[planId]}
              />
            </div>
            {memoizedId}
          </>
        ) : (
          <SchoolProgramPage />
        )}
      </div>
    </section>
  );
}
