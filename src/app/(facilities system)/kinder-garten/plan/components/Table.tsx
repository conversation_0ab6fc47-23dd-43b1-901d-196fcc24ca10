const Table = () => {
  return (
    <div className="overflow-x-auto text-[12px] font-semibold">
      <table className="min-w-full table-auto border-collapse border border-black">
        <tbody>
          <tr>
            <td className="border border-black bg-[#FFE498] px-4 py-2 text-center font-bold">
              Đón trẻ
            </td>
            <td className="border border-black px-4 py-2">
              Trò chuyện với trẻ về sở thích, ngày sinh của trẻ
            </td>
            <td className="whitespace-nowrap border border-black px-4 py-2 text-start">
              <ul className="list-disc pl-[1rem]">
                <li>Hướng dẫn trẻ đi trên dây</li>
                <li>Cho trẻ phát âm a, ă, â</li>
              </ul>
            </td>
            <td className="border border-black px-4 py-2">
              Cho trẻ xem tranh các bạn vẽ về bản thân
            </td>
            <td className="border border-black px-4 py-2">
              <PERSON> trẻ hát theo nhạc bài “Tìm bạn thân”
            </td>
            <td className="border border-black px-4 py-2">
              Trò chuyện với trẻ về các bộ phận của cơ thể và cách bảo vệ
            </td>
          </tr>
          <tr>
            <td className="border border-black bg-[#FFE498] px-4 py-2 text-center font-bold">
              Thể dục buổi sáng
            </td>
            <td colSpan={5} className="border border-black px-4 py-2">
              <ul className="list-disc pl-[1rem]">
                <li>Hô hấp: Hít vào, thở ra (4 lần 8 nhịp)</li>
                <li>Tay vai: Đưa tay ra trước sang hai bên (4 lần 8 nhịp)</li>
                <li>Bụng: Cúi người về phía trước (4 lần 8 nhịp)</li>
                <li>
                  Chân: Đưa chân ra phía trước, chân trước khuỵu, chân sau thẳng
                  (4 lần 8 nhịp)
                </li>
              </ul>
            </td>
          </tr>
          <tr>
            <td className="border border-black bg-[#FFE498] px-4 py-2 text-center font-bold">
              Hoạt động học
            </td>
            <td className="border border-black px-4 py-2">
              <span className="font-[700]">KPXH</span>: Sự lớn lên của bé
            </td>
            <td className="border border-black px-4 py-2">
              <p>
                <span className="font-[700]">TD</span>: Đi trên dây
              </p>
              <p>
                <span className="font-[700]">LQCC</span>: Nhận biết chữ a, ă, â
              </p>
            </td>
            <td className="border border-black px-4 py-2">
              <p>
                <span className="font-[700]">LQVH</span>: Kể chuyện: “Giấc mơ kỳ
                lạ”
              </p>
            </td>
            <td className="border border-black px-4 py-2">
              <p>
                <span className="font-[700]">Tạo hình</span>: Cắt dán hình
                vuông, hình tròn, hình tam giác
              </p>
            </td>
            <td className="border border-black px-4 py-2">
              <p>
                <span className="font-[700]">LQVT</span>: Đếm đến 6. Nhận biết
                chữ số 6
              </p>
              <p>
                <span className="font-[700]">LQCV</span>: Tập viết nét cong
                trái, cong phải
              </p>
            </td>
          </tr>
          <tr>
            <td className="border border-black bg-[#FFE498] px-4 py-2 text-center font-bold">
              Chơi và hoạt động ở các góc
            </td>
            <td colSpan={5} className="border border-black px-4 py-2">
              <ul className="list-disc pl-[1rem]">
                <li>
                  <strong>Góc PV:</strong> Gia đình, cửa hàng vật liệu xây dựng
                </li>
                <li>
                  <strong>Góc XD:</strong> Xây nhà cho bé
                </li>
                <li>
                  <strong>Góc NT:</strong> Tô màu bạn trai, bạn gái
                </li>
                <li>
                  <strong>Góc KH:</strong> Chơi với các chữ cái, chữ số, đôminô
                </li>
                <li>
                  <strong>Góc TV:</strong> Xem tranh vẽ của các bạn về đặc điểm
                  bản thân
                </li>
                <li>
                  <strong>Góc TN:</strong> Chăm sóc cây xanh
                </li>
              </ul>
            </td>
          </tr>
          <tr>
            <td className="border border-black bg-[#FFE498] px-4 py-2 text-center font-bold">
              Hoạt động ngoài trời
            </td>
            <td className="border border-black px-4 py-2">
              <p>
                <span className="font-[700]">HĐCMĐ</span>: Đi trên dây
              </p>
              <p>
                <span className="font-[700]">TCVD</span>: Đi trên dây
              </p>
              <p>
                <span className="font-[700]">Chơi tự do </span> : Đi trên dây
              </p>
            </td>
            <td className="border border-black px-4 py-2">
              <p>
                <span className="font-[700]">HĐCMĐ</span>: Làm quen với Truyện
                “Giấc mơ kỳ lạ”
              </p>
              <p>
                <span className="font-[700]">TCVD</span>: Kéo co
              </p>
              <p>
                <span className="font-[700]">Chơi tự do </span> : Cho trẻ chơi
                với các đồ chơi vận động ngoài sân
              </p>
            </td>
            <td className="border border-black px-4 py-2">
              <p>
                <span className="font-[700]">HĐCMĐ</span>: Tập cho trẻ tự măc và
                cởi được áo
              </p>
              <p>
                <span className="font-[700]">TCVD</span>: Lộn cầu vồng
              </p>
              <p>
                <span className="font-[700]">Chơi tự do </span> : Cho trẻ chơi
                với các đồ chơi vận động ngoài sân
              </p>
            </td>
            <td className="border border-black px-4 py-2">
              <p>
                <span className="font-[700]">HĐCMĐ</span>: Cho trẻ thể hiện cảm
                xúc và VĐ phù hợp với nhịp điệu bài hát
              </p>
              <p>
                <span className="font-[700]">TCVD</span>: Kéo co
              </p>
              <p>
                <span className="font-[700]">Chơi tự do </span> : Cho trẻ chơi
                với các đồ chơi vận động ngoài sân
              </p>
            </td>
            <td className="border border-black px-4 py-2">
              <p>
                <span className="font-[700]">HĐCMĐ</span>: Cho trẻ tập hát bài
                “Đường và chân”
              </p>
              <p>
                <span className="font-[700]">TCVD</span>: Lộn cầu vồng
              </p>
              <p>
                <span className="font-[700]">Chơi tự do </span> : Cho trẻ chơi
                với các đồ chơi vận động ngoài sân
              </p>
            </td>
          </tr>
          <tr>
            <td className="border border-black bg-[#FFE498] px-4 py-2 text-center font-bold">
              Vệ sinh ăn trưa Ngủ trưa
            </td>
            <td colSpan={5} className="border border-black px-4 py-2">
              <ul className="list-disc pl-[1rem]">
                <li>
                  <div className="flex gap-1">
                    Cho trẻ vệ sinh:
                    <div className="">
                      <p>+ Rửa tay bằng xà phòng trước khi ăn.</p>
                      <p>+ Đánh răng sau khi ăn.</p>
                    </div>
                  </div>
                </li>
                <li>
                  Tập trẻ ngồi ngay ngắn khi ăn, không nói chuyện trong khi ăn,
                  không rơi vãi.
                </li>
                <li>
                  Mỗi trẻ ngủ 1 sạp riêng, tự lấy gối đúng ký hiệu, giúp cô kê
                  và dọn sạp ngủ gọn gàng, cất đúng nơi quy định.
                </li>
              </ul>
            </td>
          </tr>
          <tr>
            <td className="border border-black bg-[#FFE498] px-4 py-2 text-center font-bold">
              Hoạt động chiều
            </td>
            <td className="border border-black px-4 py-2">
              <ul className="list-disc pl-[1rem]">
                <li>Ôn bài: Sự lớn lên của bé</li>
                <li>Rèn kĩ năng đi trên dây</li>
                <li>Chơi tự do với các đồ chơi lắp ghép bằng nhựa</li>
              </ul>
            </td>
            <td className="border border-black px-4 py-2">
              {" "}
              <ul className="list-disc pl-[1rem]">
                <li>Ôn bài: Đi trên dây</li>
                <li>Rèn trẻ kể chuyện “Giấc mơ kỳ lạ”</li>
                <li>Chơi “Tự giới thiệu về sở thích</li>
              </ul>
            </td>
            <td className="border border-black px-4 py-2">
              <ul className="list-disc pl-[1rem]">
                <li>Ôn bài: Tập trẻ kể lại chuyện “Giấc mơ kỳ lạ”</li>
                <li>Rèn trẻ tập cắt các hình vuông, tròn, tam giác</li>
                <li>Cho trẻ chơi tự do với các đồ chơi lắp ghép nhựa</li>
              </ul>
            </td>
            <td className="border border-black px-4 py-2">
              {" "}
              <ul className="list-disc pl-[1rem]">
                <li>Ôn bài: Cho trẻ hoàn chỉnh sản phẩm cắt dán.</li>
                <li>Rèn trẻ đếm xuôi, ngược, thêm bớt trong phạm vi 6</li>
                <li>Chơi tự do với các đồ chơi ở các góc</li>
              </ul>
            </td>
            <td className="border border-black px-4 py-2">
              {" "}
              <ul className="list-disc pl-[1rem]">
                <li>
                  Ôn nhận biết chữ số 6 và đếm các nhóm đối tượng trong phạm vi
                  6.
                </li>
                <li>Cho trẻ dọn vệ sinh các góc</li>
              </ul>
            </td>
          </tr>
          <tr>
            <td className="border border-black bg-[#FFE498] px-4 py-2 text-center font-bold">
              Trả trẻ
            </td>
            <td colSpan={5} className="border border-black px-4 py-2">
              <ul className="list-disc pl-[1rem]">
                <li>Vệ sinh trước khi về.</li>
                <li>Sữa lại tóc, trang phục gọn gàng</li>
                <li>Trao đổi với phụ huynh về hoạt động của trẻ trong ngày.</li>
              </ul>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  );
};

export default Table;
