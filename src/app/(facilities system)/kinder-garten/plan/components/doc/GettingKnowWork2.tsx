// (pages)/khampaxahoi.js
import React from "react";

export default function GettingKnowWork2() {
  return (
    <div className="mx-auto p-6 text-[#414B5B] overflow-clip">
      <h1 className="text-center text-[20px]">
        Hoạt động: <span className="font-bold">LÀM QUEN CHỮ CÁI</span>
      </h1>
      <h2 className="text-center text-[20px]">
        Tên đề tài: <span className="font-bold">TRÒ CHƠI VỚI CHỮ A, Ă, Â (cs 91)</span>
      </h2>

      <section className="leading-[2rem] mt-[12px]">
        <h3 className="text-lg font-bold underline underline-offset-4">I- Mục đích yêu cầu:</h3>
        <ul>
          <li>- Trẻ nhận biết được chữ a, ă, â và nhận biết chữ a, ă, â trong từ.</li>
          <li>- Trẻ phát âm chính xác chữ a, ă, â.</li>
          <li>- B<PERSON>ết hợp tác trong nhóm khi tham gia hoạt động và thi đua học tập tốt.</li>
        </ul>

        <h3 className="text-lg font-bold underline underline-offset-4">II- Chuẩn bị:</h3>
        <ul>
          <li>- Thẻ chữ cái a, ă, â đủ cho cô và trẻ.</li>
          <li>- Các từ có chứa chữ a, ă, â xung quanh lớp.</li>
          <li>- Bảng con, đất nặn đủ cho mỗi trẻ.</li>
        </ul>

        <h3 className="text-lg font-bold underline underline-offset-4">III- Tiến trình hoạt động:</h3>

        <div>
          <h4 className="font-bold">1- Hoạt động mở đầu:</h4>
          <p>- Cho trẻ hát bài "Em học chữ"</p>
        </div>

        <div>
          <h4 className="font-bold">2- Hoạt động trọng tâm:</h4>

          <div>
            <h5 className="font-bold">* Hoạt động 1: Ôn chữ a, ă, â.</h5>
            <ul>
              <li>- Cô đố! Cô đố!</li>
              <li>- Có các con chữ gì có 1 nét cong kín kết hợp với 1 nét thẳng ở bên phải?</li>
              <li>- Cho trẻ phát âm 3 lần.</li>
              <li>- Chữ a thêm miệng cười ở trên đầu là chữ gì? (Cho trẻ phát âm 3 lần).</li>
              <li>- Chữ a thêm miệng khóc ở trên đầu là chữ gì? (Cho trẻ phát âm 3 lần).</li>
              <li>- Hôm nay chúng mình cùng chơi trò chơi với chữ a, ă, â nhé!</li>
            </ul>
          </div>

          <div>
            <h5 className="font-bold">* Hoạt động 2: Trò chơi với chữ a, ă, â.</h5>
            <p><strong>Trò chơi 1: Ghép nét thành chữ.</strong></p>
            <ul>
              <li>- Yêu cầu: Dùng nét rời ghép thành chữ a, ă, â, mỗi lần chỉ được ghép 1 chữ.</li>
              <li>- Cách chơi: Chia trẻ thành 2 đội, thi đua nhau chạy lên ghép nét rời thành chữ a, ă, â. Đội nào ghép đúng, nhanh và nhiều là thắng.</li>
            </ul>

            <p><strong>Trò chơi 2: "Nghe âm chọn chữ cái và phát âm".</strong></p>
            <ul>
              <li>- Yêu cầu: Cô phát âm, trẻ chọn chữ cái đúng và phát âm.</li>
              <li>- Cách chơi: Cô hát "Tính tình tính tình tang tang tang"</li>
              <li>- Cô hát tiếp "Châu tìm xem âm gì nào? (2 lần)"</li>
              <li>- Trẻ hát lại và chọn chữ cái "Châu tìm xem âm...."</li>
              <li>- Cô cho các trẻ yếu chọn nhiều lần theo yêu cầu của cô gọi lên và phát âm</li>
              <li>- Cho cả lớp, nhóm, cá nhân nhiều lần.</li>
            </ul>

            <p><strong>Trò chơi 3: Nặn chữ a, ă, â.</strong></p>
            <ul>
              <li>- Yêu cầu: Trẻ nặn đúng nét chữ a, ă, â</li>
              <li>- Cô tổ chức cho cả lớp và thi đua nặn chữ a, ă, â nhóm nào nặn nhanh và đúng là thắng.</li>
            </ul>
          </div>
        </div>

        <div>
          <h4 className="font-bold">3- Hoạt động kết thúc:</h4>
          <p>- Cô nhắc trẻ giữ cho đồ dùng học tập cất đúng nơi quy định.</p>
        </div>

        <div>
          <h4 className="font-bold">* Nhận xét cuối ngày:</h4>
          <p className="italic">
            ................................................................................................................................................................................................................................................................................................................................................................................................................................................
          </p>
          <p className="italic">
            ................................................................................................................................................................................................................................................................................................................................................................................................................................................
          </p>
          <p className="italic">
            ................................................................................................................................................................................................................................................................................................................................................................................................................................................
          </p>
        </div>
      </section>
    </div>
  );
}
