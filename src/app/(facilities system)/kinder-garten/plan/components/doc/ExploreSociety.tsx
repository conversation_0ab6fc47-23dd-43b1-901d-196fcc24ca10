// (pages)/khampaxahoi.js
import React from "react";

export default function ExploreSociety() {
  return (
    <div className="mx-auto p-6 text-[#414B5B] overflow-clip">
      <h1 className="text-center text-[20px]">
        Hoạt động: <span className="font-bold ">KHÁM PHÁ XÃ HỘI</span>{" "}
      </h1>
      <h2 className="text-center text-[20px]">
        Tên đề tài:{" "}
        <span className="font-bold ">SỰ LỚN LÊN CỦA BÉ (cs 27)</span>
      </h2>

      <section className="mt-[12px]">
        <section className="leading-[2rem]">
          <h3 className="text-lg font-bold underline underline-offset-4">I- <PERSON>ục đích yêu cầu:</h3>
          <ul className=" ">
            <li>
              - Trẻ biết quá trình lớn lên của cơ thể bé theo trình tự thời
              gian, biết một số đặc điểm của bản thân: <PERSON><PERSON> tên, tu<PERSON><PERSON>, ng<PERSON><PERSON>,
              gi<PERSON><PERSON> t<PERSON>, hình dáng và diện mạo bên ngoài.
            </li>
            <li>
              - Trẻ biết tự giới thiệu về mình, sắp xếp đúng quá trình lớn lên
              của bé, trả lời được các câu hỏi của cô, thực hiện được các yêu
              cầu của cô.
            </li>
            <li>
              - Trẻ yêu hòa bình và thân thiện, cảm xúc cho bản thân và biết yêu
              quý mọi người.
            </li>
          </ul>
        </section>

        <section className="leading-[2rem]">
          <h3 className="text-lg font-bold underline underline-offset-4">II- Chuẩn bị:</h3>
          <ul className="">
            <li>- Trang trí nhiều hình của các bạn trong lớp.</li>
            <li>- 2 băng rôn: Sự lớn lên của bé.</li>
            <li>- Máy caster, băng nhạc: "Mừng sinh nhật".</li>
          </ul>
        </section>

        <section className="leading-[2rem]">
          <h3 className="text-lg font-bold underline underline-offset-4">III- Tiến trình hoạt động:</h3>

          <div className="">
            <h4 className="font-bold">1- Hoạt động mở đầu:</h4>
            <ul className="">
              <li>- Đố các con hôm nay lớp mình có gì lạ?</li>
              <li>- Cô hướng trẻ đến xem hình các bạn.</li>
            </ul>
          </div>

          <div className="">
            <h4 className="font-bold">2- Hoạt động trọng tâm:</h4>

            <div className="">
              <h5 className="font-bold">
                * Hoạt động 1: Trò chuyện và đàm thoại
              </h5>
              <p>- Cho trẻ xem hình các bạn trong lớp và nêu lên ý kiến:</p>
              <ul className="">
                <li>+ Con thích hình bạn nào nhất? Vì sao?</li>
                <li>
                  + Con hãy giới thiệu về đặc điểm của bản thân cho các bạn cùng
                  biết nào!
                </li>
              </ul>
              <p>
                Có gì ở cơ thể bé một số đặc điểm của bản thân: tên, tuổi, giới
                tính, sở thích, hình dáng và diện mạo bên ngoài. Sau đó cho trẻ
                quan sát tranh quá trình lớn lên của bé.
              </p>
              <p>- Bé được sinh ra từ ai?</p>
              <p>- Bé lớn lên như thế nào?</p>
              <p>- Những vận động nào gắn liền với sự lớn dần lên của bé?</p>
              <p>- Vậy bé lớn lên nhờ tình yêu thương, chăm sóc của ai?</p>
              <p>- Hằng ngày các con được bố mẹ chăm sóc như thế nào?</p>
              <p>
                - Để thể hiện tình cảm của bé đối với công ơn của bố, mẹ các con
                phải làm gì?
              </p>
            </div>

            <div className=" ">
              <h5 className="font-bold">
                * Hoạt động 2: Sắp xếp tranh sự lớn lên của bé.
              </h5>
              <p>
                - Cô chia trẻ thành 2 nhóm thi đua sắp xếp tranh sự lớn dần lên
                của bé.
              </p>
              <p>
                Khi 2 nhóm đã hoàn thành cho các bạn lướt từng nhóm kể theo nội
                dung tranh mình vừa xếp. Nhóm nào xếp đúng kể rõ ràng thì nhớ
                lấy siêng thưởng.
              </p>
            </div>

            <div className=" ">
              <h5 className="font-bold">
                * Hoạt động 3: Trò chơi “Bé nào thông minh hơn?”
              </h5>
              <p>
                - Cô cho 5 trẻ chọn đồ dùng, đồ chơi quanh lớp mình thích tự
                giới thiệu cho cả lớp cùng biết. Sau đó đem trả về lại chỗ cũ,
                gọi 5 trẻ khác lên lấy các đồ dùng hoặc đồ chơi mà 5 trẻ trước
                đã giới thiệu đem đến tặng cho từng bạn theo đúng sở thích của
                bạn. Nếu bạn nào chọn và tặng đúng thì bạn ấy thắng. Cho trẻ
                chơi nhiều lần.
              </p>
              <p>
                - Khi trẻ chơi cô có thể nhắc nhở và nhận xét, khen ngợi trẻ kịp
                thời để động viên trẻ tích cực tham gia hoạt động.
              </p>
            </div>
          </div>

          <div className="">
            <h4 className="font-bold">3- Hoạt động kết thúc:</h4>
            <p className="font-bold">
              - Cô cùng trẻ hát bài: "Gặp nhau là quen".
            </p>
          </div>

          <div className="">
            <h4 className="font-bold">* Nhận xét cuối ngày:</h4>
            <p className="italic">
              ................................................................................................................................................................................................................................................................................................................................................................................................................................................
            </p>
            <p className="italic">
              ................................................................................................................................................................................................................................................................................................................................................................................................................................................
            </p>
            <p className="italic">
              ................................................................................................................................................................................................................................................................................................................................................................................................................................................
            </p>
          </div>
        </section>
      </section>
    </div>
  );
}
