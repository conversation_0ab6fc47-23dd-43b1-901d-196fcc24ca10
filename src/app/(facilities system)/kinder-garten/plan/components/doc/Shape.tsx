// (pages)/khampaxahoi.js
import React from "react";

export default function ShapeWork() {
  return (
    <div className="mx-auto p-6 text-[#414B5B] overflow-clip">
      <h1 className="text-center text-[20px]">
        Hoạt động: <span className="font-bold">TẠO HÌNH</span>
      </h1>
      <h2 className="text-center text-[20px]">
        Tên đề tài: <span className="font-bold">CẮT DÁN HÌNH CHỮ NHẬT, HÌNH TAM GIÁC (cs 7)</span>
      </h2>

      <section className="leading-[2rem] mt-[12px]">
        <h3 className="text-lg font-bold underline underline-offset-4">I- <PERSON><PERSON><PERSON> đích yêu cầu:</h3>
        <ul>
          <li>- Trẻ biết cắt và dán các hình vuông, tròn, tam giác theo mẫu.</li>
          <li>- Trẻ c<PERSON>t, dán thành thạo các hình vuông, tr<PERSON><PERSON>, tam gi<PERSON>c đ<PERSON>g.</li>
          <li>- <PERSON><PERSON> gợi ý sử dụng sản phẩm của mình và của bạn. Biết bỏ giấy loại vào thùng không vứt lung tung ra sàn.</li>
        </ul>

        <h3 className="text-lg font-bold underline underline-offset-4">II- Chuẩn bị:</h3>
        <ul>
          <li>- Màu của cô, giấy A4, giấy màu, hồ dán, khăn lau tay.</li>
          <li>- Máy cassette, băng nhạc.</li>
          <li>- Bàn ghế đủ dùng phù hợp với diện tích lớp và số lượng trẻ.</li>
          <li>- 1 bảng lật.</li>
        </ul>

        <h3 className="text-lg font-bold underline underline-offset-4">III- Tiến trình hoạt động:</h3>

        <div>
          <h4 className="font-bold">1- Hoạt động mở đầu:</h4>
          <p>- Cô cho trẻ xem tranh mẫu và hỏi trẻ: Trong tranh của cô có những hình gì?</p>
          <p>- Có cắt và dán những hình đó như thế nào?</p>
          <p>- Hôm nay cô sẽ cho các con cắt dán hình vuông, hình tròn, hình tam giác như mẫu nhé!</p>
        </div>

        <div>
          <h4 className="font-bold">2- Hoạt động trọng tâm:</h4>

          <div>
            <h5 className="font-bold">* Hoạt động 1: Hướng dẫn cắt và dán hình chữ nhật, hình tam giác.</h5>
            <ul>
              <li>- Hướng dẫn: Cô vừa cắt vừa hướng dẫn cho trẻ cách cắt 3 hình vuông màu đỏ, 4 hình tròn màu xanh, 2 hình tam giác màu xanh lá cây. Sau đó bôi hồ vào mặt trái của hình và dán vào giấy.</li>
              <li>- Nhắc trẻ chỉ bôi không cách giữa các hình khi dán sao cho đều nhau.</li>
            </ul>
          </div>

          <div>
            <h5 className="font-bold">* Hoạt động 2: Trẻ thực hiện.</h5>
            <ul>
              <li>- Trẻ thực hiện cắt, dán.</li>
              <li>- Khi trẻ cắt cô chú ý quan sát để nhắc trẻ cẩn thận sử dụng kéo xong bỏ ngay vào rổ đồ dùng.</li>
              <li>- Nhắc trẻ bỏ giấy vụn gọn gàng không vứt lung tung ra sàn nhà.</li>
              <li>- Cô khuyến khích các trẻ yếu cố gắng hoàn thành sản phẩm đúng thời gian quy định. - Trẻ thực hiện xong cô cho trẻ lần lượt đem trưng bày vào góc nghệ thuật.</li>
            </ul>
          </div>

          <div>
            <h5 className="font-bold">* Hoạt động 3: Nhận xét sản phẩm.</h5>
            <ul>
              <li>- Cô cho quan sát sản phẩm của mình, của bạn và cùng nhận xét:</li>
              <li>+ Thẻ con sản phẩm nào đẹp nhất? Vì sao?</li>
              <li>+ Để sản phẩm đẹp, bền các con phải làm gì?</li>
            </ul>
          </div>
        </div>

        <div>
          <h4 className="font-bold">3- Hoạt động kết thúc:</h4>
          <p>- Cho trẻ hát bài "Tìm bạn thân".</p>
        </div>

        <div>
          <h4 className="font-bold">* Nhận xét cuối ngày:</h4>
          <p className="italic">
            ................................................................................................................................................................................................................................................................................................................................................................................................................................................
          </p>
          <p className="italic">
            ................................................................................................................................................................................................................................................................................................................................................................................................................................................
          </p>
          <p className="italic">
            ................................................................................................................................................................................................................................................................................................................................................................................................................................................
          </p>
        </div>
      </section>
    </div>
  );
}
