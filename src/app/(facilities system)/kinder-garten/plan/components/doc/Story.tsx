// (pages)/khampaxahoi.js
import React from "react";

export default function StoryWork() {
  return (
    <div className="mx-auto p-6 text-[#414B5B] overflow-clip">
      <h1 className="text-center text-[20px]">
        Hoạ<PERSON> động: <span className="font-bold">KỂ CHUYỆN</span>
      </h1>
      <h2 className="text-center text-[20px]">
        Tên đề tài: <span className="font-bold">GIẤC MƠ KỲ LẠ (cs 71)</span>
      </h2>

      <section className="leading-[2rem] mt-[12px]">
        <h3 className="text-lg font-bold underline underline-offset-4">I- <PERSON>ục đích yêu cầu:</h3>
        <ul>
          <li>- Trẻ hiểu nội dung, ý nghĩa của câu chuyện.</li>
          <li>
            - R<PERSON>n kỹ năng ghi nhớ, nói có chủ định, tr<PERSON> lời trọn câu, r<PERSON> ràng,
            mạch lạc.
          </li>
          <li>
            - Biết ăn uống đủ đầy, luyện tập thể dục thường xuyên để cơ thể khỏe
            mạnh.
          </li>
        </ul>

        <h3 className="text-lg font-bold underline underline-offset-4">II- Chuẩn bị:</h3>
        <ul>
          <li>- Mô hình ngôi nhà.</li>
          <li>- Một số tranh các vận động của chủ đề bản thân.</li>
          <li>- 1 bảng lật.</li>
          <li>- Băng nhạc, máy cassette, máy vi tính.</li>
        </ul>

        <h3 className="text-lg font-bold underline underline-offset-4">III- Tiến trình hoạt động:</h3>

        <div>
          <h4 className="font-bold">1- Hoạt động mở đầu:</h4>
          <p>- Cho trẻ chơi trò "Trời tối, trời sáng".</p>
          <p>
            - Khi nào các con có hay nằm mơ không? Các con thường mơ thấy gì?
          </p>
          <p>
            - Có một bạn nhỏ có một giấc mơ kỳ lạ! Các con có muốn biết giấc mơ
            của bạn ấy là gì không? Cô sẽ kể cho các con nghe câu chuyện ấy nhé!
          </p>
          <p>- Câu chuyện có tên là "Giấc mơ kỳ lạ".</p>
        </div>

        <div>
          <h4 className="font-bold">2- Hoạt động trọng tâm:</h4>

          <div>
            <h5 className="font-bold">
              * Hoạt động 1: Trẻ hiểu và nhớ nội dung câu chuyện "Giấc mơ kỳ lạ"
            </h5>
            <ul>
              <li>- Cô kể lần 1 diễn cảm.</li>
              <li>
                - Cô kể lần 2 kết hợp tranh ảnh mô hình để làm rõ nội dung
                chuyện.
              </li>
              <li>- Cô vừa kể cho các con nghe câu chuyện tên là gì?</li>
              <li>- Câu chuyện có những nhân vật nào?</li>
              <li>- Bé Mi là một em bé như thế nào?</li>
              <li>
                - Vì sao bé Mi suốt ngày nằm mơ? Bé Mi đã mơ giấc mơ về cái gì?
              </li>
            </ul>
            <p>- Cô dẫn lời: "Từ đầu...trở chuyến được với nhau".</p>
            <ul>
              <li>+ Cô dẫn đoạn 1: “Từ đầu…trò chuyện được với nhau”.</li>
              <li>
                - Các bộ phận của cơ thể bé Mi đã nói gì với nhau? Vì sao chúng
                đều cảm thấy mệt mỏi và không muốn làm việc gì cả?
              </li>
              <li>
                - Cuối cùng các bộ phận của cơ thể đã làm gì?
              </li>
              <li>
                + Cô dẫn đoạn 2: "Cố ấy chẳng anh Tay...chúng ta một khỏe khoắn
                được".
              </li>
              <li>- Khi tỉnh dậy bé Mi đã làm gì? Kết quả ra sao?</li>
              <li>
                + Cô kể đoạn còn lại.
              </li>
              <li>- Theo con ăn uống đầy đủ chất, luyện tập thể dục có ích gì?</li>
            </ul>
         
          </div>

          <div>
            <h5 className="font-bold">* Hoạt động 2: Dạy trẻ kể, trẻ tập kể</h5>
            <p>
              - Cho trẻ tập kể theo cô, theo nhóm, theo cá nhân. Khi trẻ tập kể,
              cô lắng nghe và sửa sai để trẻ nói rõ ràng và tự tin hơn.
            </p>
          </div>

          <div>
            <h5 className="font-bold">
              * Hoạt động 3: Trò chơi "Ai nhanh nhất"
            </h5>
            <p>
              <strong>- Yêu cầu:</strong>
              Trẻ chọn những việc làm có lợi và không có lợi cho sức khỏe.
            </p>
            <p>
              <strong>- Cách chơi:</strong> Cô chia trẻ thành 2 đội, thi đua
              nhau chạy lên gắn các bức tranh vào bảng theo yêu cầu của cô.
              Những bức tranh liên quan đến việc có lợi cho sức khỏe được gắn
              vào bảng "Có lợi cho sức khỏe", còn các bức tranh thể hiện các
              hoạt động không có lợi cho sức khỏe được gắn vào bảng "Không có
              lợi cho sức khỏe". Đội nào gắn đúng và nhanh nhất sẽ chiến thắng.
            </p>
          </div>
        </div>

        <div>
          <h4 className="font-bold">3- Hoạt động kết thúc:</h4>
          <p>
            - Cho trẻ nghỉ ngơi, hướng dẫn trẻ thu dọn đồ dùng và chuẩn bị ra
            về.
          </p>
        </div>

        <div>
          <h4 className="font-bold">* Nhận xét cuối ngày:</h4>
          <p className="italic">
            ................................................................................................................................................................................................................................................................................................................................................................................................................................................
          </p>
          <p className="italic">
            ................................................................................................................................................................................................................................................................................................................................................................................................................................................
          </p>
          <p className="italic">
            ................................................................................................................................................................................................................................................................................................................................................................................................................................................
          </p>
        </div>
      </section>
    </div>
  );
}
