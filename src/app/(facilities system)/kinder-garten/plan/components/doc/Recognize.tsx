// (pages)/khambhaxahoi.js
import React from "react";

export default function Recognize() {
  return (
    <div className="mx-auto p-6 text-[#414B5B] overflow-clip">
      <h1 className="text-center text-[20px]">
        Hoạt động: <span className="font-bold">KHÁM PHÁ XÃ HỘI</span>
      </h1>
      <h2 className="text-center text-[20px]">
        Tên đề tài:{" "}
        <span className="font-bold">
          NHẬN BIẾT VÀ PHÒNG TRÁNH TAI NẠN THƯƠNG TÍCH (cs 21)
        </span>
      </h2>

      <section className="leading-[2rem] mt-[12px]">
        <h3 className="text-lg font-bold underline underline-offset-4 underline underline-offset-4">I- <PERSON>ục đích yêu cầu:</h3>
        <ul>
          <li>
            - Trẻ nhận biết và phòng tránh một số tai nạn thương tích thường gặp
            ở nhà và ở trường: giật điện, ngã, tai nạn giao thông, bỏng.
          </li>
          <li>
            - <PERSON><PERSON><PERSON> cho trẻ thói năng quan sát, phân đoán, ghi nhớ có chủ định qua
            các hoạt động.
          </li>
          <li>
            - Biết gợi người lớn giúp đỡ khi gặp nguy hiểm, không chơi những trò
            chơi nguy hiểm để tránh tai nạn cho bản thân và cho bạn bè.
          </li>
        </ul>

        <h3 className="text-lg font-bold underline underline-offset-4 underline underline-offset-4">II- Chuẩn bị:</h3>
        <ul>
          <li>
            - Tranh thể tai nạn gây đau điện, đồ giặt, ngồi nghểnh, chơi các đồ
            chơi và trò chơi nguy hiểm, chạy qua đường, lại gần nơi nguy hiểm...
          </li>
          <li>- Một số tranh lô tô nên và không nên làm.</li>
          <li>- Một số tranh cảnh báo nguy hiểm.</li>
        </ul>

        <h3 className="text-lg font-bold underline underline-offset-4 underline underline-offset-4">III- Tiến trình hoạt động:</h3>

        <div>
          <h4 className="font-bold">1- Hoạt động mở đầu:</h4>
          <p>- Cho trẻ hát bài "Mèo ba bố áo".</p>
        </div>

        <div>
          <h4 className="font-bold">2- Hoạt động trọng tâm:</h4>

          <h5 className="font-bold">* Hoạt động 1: Quan sát và đàm thoại.</h5>
          <ul>
            <li>- Cô cho trẻ quan sát tranh và hỏi trẻ:</li>
            <li>+ Tranh vẽ gì?</li>
            <li>
              + Điều gì sẽ xảy ra khi các con sờ tay vào quạt, vào ổ điện?
            </li>
            <li>+ Nếu thấy tay ta sờ vào điện, con phải làm gì?</li>
            <li>
              + Người nghểnh đến thẳng điều gì sẽ xảy ra? Hậu quả như thế nào?
            </li>
            <li>+ Các con phải làm gì để không bị nguy hiểm?</li>
          </ul>
          <p>- Nếu có cảm nhận đồ chơi sẽ xảy ra sẽ sẵn sàng gì?</p>
          <p>- Các con nếu đi qua đường một mình không?</p>
          <p>- Khi sờ nước nóng, các con nhớ gì vào ngón tay nữa hay không?</p>
          <p>- Các con đi ngoài trên không và chú lý định nên làm gì?</p>
        </div>

        <div>
          <h4 className="font-bold">3- Hoạt động kết thúc:</h4>
          <p>- Cho trẻ hát lại bài "Mời bạn ăn".</p>
        </div>

        <div>
          <h4 className="font-bold">* Nhận xét cuối ngày:</h4>
          <p className="italic">
            ................................................................................................................................................................................................................................................................................................................................................................................................................................................
          </p>
          <p className="italic">
            ................................................................................................................................................................................................................................................................................................................................................................................................................................................
          </p>
          <p className="italic">
            ................................................................................................................................................................................................................................................................................................................................................................................................................................................
          </p>
        </div>
      </section>
    </div>
  );
}
