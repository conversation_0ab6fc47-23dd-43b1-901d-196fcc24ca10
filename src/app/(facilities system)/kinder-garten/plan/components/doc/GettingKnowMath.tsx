// (pages)/khampaxahoi.js
import React from "react";

export default function GettingKnowMath() {
  return (
    <div className="mx-auto p-6 text-[#414B5B] overflow-clip">
      <h1 className="text-center text-[20px]">
        Hoạt động: <span className="font-bold">LÀM QUEN VỚI TOÁN</span>
      </h1>
      <h2 className="text-center text-[20px]">
        Tên đề tài: <span className="font-bold">ĐẾM ĐẾN 6, NHẬN BIẾT CHỮ SỐ 6</span>
      </h2>

      <section className="leading-[2rem] mt-[12px]">
        <h3 className="text-lg font-bold underline underline-offset-4">I- <PERSON><PERSON><PERSON> đích yêu cầu:</h3>
        <ul>
          <li>- Trẻ biết đếm đến số lượng 6, nhận biết được nhóm có 6 đối tượng. Nhận biết chữ số 6.</li>
          <li>- <PERSON><PERSON><PERSON> kỹ năng đếm xuôi, ng<PERSON><PERSON><PERSON> và xếp đúng vị trí các chữ số từ 1- 6.</li>
          <li>- Phát huy tính cộng bằng thực hiện các bài tập được giao, tích cực tham gia hoạt động.</li>
        </ul>

        <h3 className="text-lg font-bold underline underline-offset-4">II- Chuẩn bị:</h3>
        <ul>
          <li>- Thẻ chữ số từ 1 - 6 đủ cho cô và trẻ nhưng kích thước khác nhau.</li>
          <li>- Một số thẻ đồ dùng có số lượng 6.</li>
          <li>- Đồ dùng của cô: quà, đĩa nhựa, nội nhồi bông bằng bìa cắt rời có số lượng 6.</li>
          <li>- Một số đồ dùng đồ chơi sắp xếp quanh lớp có số lượng trong phạm vi 6.</li>
        </ul>

        <h3 className="text-lg font-bold underline underline-offset-4">III- Tiến trình hoạt động:</h3>

        <div>
          <h4 className="font-bold">1- Hoạt động mở đầu:</h4>
          <p>- Cho trẻ hát bài "Tập đếm"</p>
        </div>

        <div>
          <h4 className="font-bold">2- Hoạt động trọng tâm:</h4>

          <div>
            <h5 className="font-bold">* Hoạt động 1: Ôn số lượng 5, đếm đến 6. Nhận biết chữ số 6.</h5>
            <ul>
              <li>- Các con xếp cho cô 5 đồ dùng mình thích nào!</li>
              <li>- Để có số lượng 6 đồ dùng này con dùng chữ số mấy?</li>
              <li>- Hãy chọn và xếp nào.</li>
              <li>- Cô tặng thêm cho con 1 đồ dùng nữa, tất cả có bao nhiêu đồ dùng?</li>
              <li>- Cho cả lớp cùng đếm.</li>
              <li>- Để có số lượng 6 đồ dùng thì chúng chỉ con chữ số mấy?</li>
              <li>- Chữ số 6 như thế nào?</li>
              <li>- Gọi 1 trẻ lên xếp dãy số tự nhiên từ 1- 6.</li>
            </ul>
          </div>

          <div>
            <h5 className="font-bold">* Hoạt động 2: Luyện tập.</h5>
            <ul>
              <li>- Chia trẻ làm các đội chơi sắp xếp số tương ứng vào các nhóm đồ dùng xung quanh lớp.</li>
              <li>- Cô vẽ 1 số nhóm lấy đồ và đếm, gắn chữ số tương ứng.</li>
              <li>- Trẻ lần lượt nhận từ 1- 6.</li>
              <li>- Khi trẻ luyện tập cô đi quanh bao quát và kiểm tra. Để kịp thời sửa sai cho trẻ.</li>
              <li>- Xếp dãy số tự nhiên từ 1 đến 6 trên thảm, tập trung vào bài tập.</li>
            </ul>
          </div>

          <div>
            <h5 className="font-bold">* Hoạt động 3: Trò chơi "Về đúng nhà"</h5>
            <ul>
              <li>- Cô hướng dẫn bị ngồi nhà từ 1- 6. Trẻ vừa đi vừa hát, khi có hiệu lệnh của cô thì chạy về nhà sao cho số lượng trẻ tương ứng với chữ số trên mỗi ngôi nhà. Cho trẻ chơi nhiều lần.</li>
              <li>- Trò chơi "Hãy xếp đúng"</li>
              <li>- Mỗi trẻ cầm một chữ số cầm trên tay, khi nghe hiệu lệnh của cô trẻ xếp đúng vị trí các số tự nhiên từ 1- 6.</li>
              <li>- Trẻ nào sai phải nhảy lò cò quanh lớp 1 vòng.</li>
            </ul>
          </div>
        </div>

        <div>
          <h4 className="font-bold">3- Hoạt động kết thúc:</h4>
          <p>- Cho trẻ hát bài "Tập đếm".</p>
        </div>

        <div>
          <h4 className="font-bold">* Nhận xét cuối ngày:</h4>
          <p className="italic">
            ................................................................................................................................................................................................................................................................................................................................................................................................................................................
          </p>
          <p className="italic">
            ................................................................................................................................................................................................................................................................................................................................................................................................................................................
          </p>
          <p className="italic">
            ................................................................................................................................................................................................................................................................................................................................................................................................................................................
          </p>
        </div>
      </section>
    </div>
  );
}
