import React, { useEffect, useState } from "react";
import Image from "next/image";

function TabNavigation({
  data,
  value,
  valueOption,
  getActivity,
  getActivityOption
}: {
  data: any;
  value: any;
  valueOption: any;
  getActivity: any;
  getActivityOption: any;
}) {
  const initValue = {
    "1": {
      cs20: true
    },
    "13": {
      cs20: true
    },
    "15": {
      cs20: true
    },
    "16": {
      cs20: true
    },
    "17": {
      cs20: true
    }
  };
  const [activeTab, setActiveTab] = useState(value);
  const [isShow, setIsShow] = useState(false);
  const [selectedOptions, setSelectedOptions] = useState<{
    [key: number]: { [key: string]: boolean };
  }>({
    "1": {
      cs20: true
    },
    "13": {
      cs20: true
    },
    "15": {
      cs20: true
    },
    "16": {
      cs20: true
    },
    "17": {
      cs20: true
    }
  });

  const handleTabClick = (tabId: any) => {
    setIsShow(!isShow);
    getActivity(tabId);
    setActiveTab(tabId);
    // if (!selectedOptions[activeTab] && tab?.dataChild?.length) {
    //   setSelectedOptions((prev) => ({
    //     ...prev,
    //     [activeTab]: {
    //       [tab.dataChild[0].id]: true
    //     }
    //   }));
    // }
  };

  const handleCheckboxChange = (tabId: number, optionId: string) => {
    getActivityOption(optionId);
    setSelectedOptions((prev) => ({
      ...prev,
      [tabId]: {
        [optionId]: !prev[tabId]?.[optionId] // Toggle checkbox state
      }
    }));
    setIsShow(!isShow);
  };

  useEffect(() => {
    setActiveTab(1);
  }, [value]);
  useEffect(() => {
    setIsShow(false);
  }, [activeTab]);

  return (
    <div className="flex flex-wrap gap-4">
      {data?.map(
        (tab: {
          id: React.Key | null | undefined;
          label:
            | string
            | number
            | bigint
            | boolean
            | React.ReactElement<any, string | React.JSXElementConstructor<any>>
            | Iterable<React.ReactNode>
            | React.ReactPortal
            | Promise<React.AwaitedReactNode>
            | null
            | undefined;
          dataChild: any[];
        }) => (
          <div key={tab.id} className="relative">
            <button
              onClick={() => handleTabClick(tab.id)}
              className={`flex items-center  gap-2 whitespace-nowrap rounded-[10px] px-4 py-2 text-[13.5px] ${
                activeTab == tab.id
                  ? "border-2 border-orange-500 bg-orange-100 !text-orange-500"
                  : "border border-gray-300 bg-gray-100"
              } transition duration-200`}
            >
              <p className="capitalize">{tab.label}</p>
              {!!tab?.dataChild?.length && (
                <Image
                  className={`ml-2 ${isShow && activeTab === tab?.id ? "rotate-180" : ""}`}
                  src="/arrow.svg"
                  width={19}
                  height={11}
                  alt="icon"
                />
              )}
            </button>

            {activeTab === tab?.id && !!tab?.dataChild?.length && isShow && (
              <div className="absolute left-0 top-full z-10 mt-2 w-fit rounded-lg border border-gray-200 bg-white shadow-lg">
                <ul className="py-2">
                  {tab?.dataChild.map((option) => (
                    <li
                      key={option.id}
                      className="flex items-center gap-2 px-4 py-2"
                    >
                      <input
                        type="checkbox"
                        id={option.id}
                        checked={
                          selectedOptions[Number(tab?.id)]?.[option.id] || false
                        }
                        onChange={() =>
                          handleCheckboxChange(Number(tab.id), option.id)
                        }
                        className="form-checkbox text-orange-500 accent-[#D36D46]"
                      />
                      <label
                        htmlFor={option.id}
                        className="cursor-pointer whitespace-nowrap text-[13.5px] font-[600] text-[#414B5B]"
                      >
                        {option.label} ({option.id})
                      </label>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        )
      )}
    </div>
  );
}

export default TabNavigation;
