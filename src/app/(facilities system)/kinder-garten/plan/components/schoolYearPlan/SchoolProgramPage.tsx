"use client";


export default function SchoolProgramPage() {
  const data = [
    {
      STT: 1,
      targetName: "MT1",
      detailTargetName:
        "Thực hiện đủ các động tác trong bài tập thể dục theo hướng dẫn.",
      educationalGoals: `<ul>
    <li><strong>Tháng 8:</strong> 
        <ul>
            <li>+ <PERSON><PERSON> hấp: Thổi bóng</li>
            <li>+ Hô hấp: Thổi lông</li>
            <li>+ Tay: <PERSON><PERSON><PERSON> tay sang ngang lên cao.</li>
            <li>+ Lưng: Bước về phía trước.</li>
            <li>+ Chân: <PERSON>h<PERSON>c chân đứng lên.</li>
            <li>+ Bật tại chỗ.</li>
        </ul>
    </li>
    <li><strong>Tháng 9:</strong>
        <ul>
            <li>+ <PERSON><PERSON> hấp: Thổ<PERSON> bóng</li>
            <li>+ <PERSON><PERSON> hấp: Thổi lông</li>
            <li>+ Tay: <PERSON><PERSON><PERSON> tay sang ngang lên cao.</li>
            <li>+ Lưng: <PERSON><PERSON>ớc về phía tr<PERSON>ớc.</li>
            <li>+ Chân: Nhấc chân đứng lên.</li>
            <li>+ Bật tại chỗ.</li>
        </ul>
    </li>
    <li><strong>Tháng 10:</strong>
        <ul>
            <li>+ Hô hấp: Hít vào, thở ra.</li>
            <li>+ Tay: Đưa tay lên cao, ra phía trước, sang 2 bên.</li>
            <li>+ Lưng: Bước quay sang trái sang phải.</li>
            <li>+ Chân: Bước thay gối.</li>
            <li>+ Bật: Bật chụm chân tại chỗ.</li>
        </ul>
    </li>
    <li><strong>Tháng 11:</strong>
        <ul>
            <li>+ Hô hấp: Hít vào, thở ra.</li>
            <li>+ Tay: Đưa tay lên cao, ra phía trước, sang 2 bên.</li>
            <li>+ Lưng: Bước quay sang trái sang phải.</li>
            <li>+ Chân: Bước thay gối.</li>
            <li>+ Bật: Bật chụm chân tại chỗ.</li>
        </ul>
    </li>
    <li><strong>Tháng 12:</strong>
        <ul>
            <li>+ Hô hấp: Hít vào, thở ra.</li>
            <li>+ Tay: Đưa tay lên cao, ra phía trước, sang 2 bên.</li>
            <li>+ Lưng: Bước quay sang trái sang phải.</li>
            <li>+ Chân: Bước thay gối.</li>
            <li>+ Bật: Bật chụm chân tại chỗ.</li>
        </ul>
    </li>
    <li><strong>Tháng 1:</strong>
        <ul>
            <li>+ Hô hấp: Thổi bóng, hít vào, thở ra.</li>
            <li>+ Tay: Đưa tay lên cao, ra phía trước, ra ngoài, sang ngang.</li>
            <li>+ Lưng: Nghiêng ngả phía trước, sau, phải, trái.</li>
            <li>+ Chân: Nhấc chân bước lên trước, sau, sang ngang.</li>
            <li>+ Bật: Bật chụm chân tại chỗ.</li>
        </ul>
    </li>
    <li><strong>Tháng 2:</strong>
        <ul>
            <li>+ Hô hấp: Thổi bóng, hít vào, thở ra.</li>
            <li>+ Tay: Đưa tay lên cao, ra phía trước, ra ngoài, sang ngang.</li>
            <li>+ Lưng: Nghiêng ngả phía trước, sau, phải, trái.</li>
            <li>+ Chân: Nhấc chân bước lên trước, sau, sang ngang.</li>
            <li>+ Bật: Bật chụm chân tại chỗ.</li>
        </ul>
    </li>
    <li><strong>Tháng 3:</strong>
        <ul>
            <li>+ Hô hấp: Thổi bóng, hít vào, thở ra.</li>
            <li>+ Tay: Đưa tay lên cao, ra phía trước, ra ngoài, sang ngang.</li>
            <li>+ Lưng: Nghiêng ngả phía trước, sau, phải, trái.</li>
            <li>+ Chân: Nhấc chân bước lên trước, sau, sang ngang.</li>
            <li>+ Bật: Bật chụm chân tại chỗ.</li>
        </ul>
    </li>
    <li><strong>Tháng 4:</strong>
        <ul>
            <li>+ Hô hấp: Hít vào, thở ra.</li>
            <li>+ Tay: Đưa tay lên cao, ra phía trước, ra ngoài, sang ngang.</li>
            <li>+ Lưng: Nghiêng ngả phía trước, sau, phải, trái.</li>
            <li>+ Chân: Nhấc chân bước lên trước, sau, sang ngang.</li>
            <li>+ Bật: Bật chụm chân tại chỗ.</li>
        </ul>
    </li>
    <li><strong>Tháng 5:</strong>
        <ul>
            <li>+ Hô hấp: Hít vào, thở ra.</li>
            <li>+ Tay: Đưa tay lên cao, ra phía trước, ra ngoài, sang ngang.</li>
            <li>+ Lưng: Nghiêng ngả phía trước, sau, phải, trái.</li>
            <li>+ Chân: Nhấc chân bước lên trước, sau, sang ngang.</li>
            <li>+ Bật: Bật chụm chân tại chỗ.</li>
        </ul>
    </li>
</ul>
`
    }
  ];

  const data2 = [
    {
      STT: 2,
      targetName: "MT2",
      detailTargetName:
        "2.1. Giữ được thăng bằng cơ thể khi thực hiện vận động:- Đi hết đoạn đường hẹp (3m x 0,2m)- Đi kiểng gót liên tục 3m.",
      educationalGoals: `<ul>
    <li><strong>Hoạt động học:</strong></li>
    <ul>
        <li>- Đứng theo đường chỉ định.</li>
        <li>- Thực hiện bài bóng và cổ tay 2m (MT1.4).</li>
        <li>- Lần lượt lăn bóng xa và lùi theo bóng.</li>
        <li>- Tiếp xúc bóng tại điểm cách 2m.</li>
        <li>- Thực hiện trong hai trò chơi (MT2.1).</li>
        <li>- Nhảy tách chụm chân (5 lần).</li>
        <li>- Ném qua đầu tạ.</li>
        <li>- Tránh rào thấp 20cm.</li>
        <li>- Đưa ném hoặc gậy xa 3m.</li>
        <li>- Ném nón hoặc gậy xa 1m.</li>
        <li>- Bịt mắt đứng lên một chân.</li>
        <li>- Giữ thăng bằng đứng gót chân.</li>
    </ul>
    <li><strong>Hoạt động học:</strong></li>
    <ul>
        <li>- Ném bóng qua vạch đích (MT3.2).</li>
        <li>- Hướng quạt cầm trên đầu.</li>
        <li>- Bịt lỗ mũi hoặc tai giữ khí.</li>
        <li>- Tự lùi hoặc tiến bước đối diện.</li>
        <li>- Thực hiện động tác đứng, duỗi chân.</li>
        <li>- Chuyển tạ (3 lần hoặc 5 lần).</li>
        <li>- Đứng thẳng tại đích với tay giữ ngang.</li>
        <li>- Tay hướng đẩy bên trái theo hướng ngang.</li>
        <li>- Bước bật lên cao 1 lần bên trái.</li>
        <li>- Trụ bóng bằng gót chân 3 lần, giữ khoảng cách 2.5cm (MT2.4).</li>
        <li>- Tạo góc bật bóng và lăn bóng. Đường kính bóng 18cm.</li>
        <li>- Giữ thăng bằng hoặc ngồi xuống.</li>
    </ul>
    <li><strong>Hoạt động học:</strong></li>
    <ul>
        <li>- Nhảy lùi.</li>
        <li>- Nhảy sào và tránh xa.</li>
        <li>- Quan sát dấu sau lưng.</li>
        <li>- Quan sát màu sắc của nón.</li>
        <li>- Vỗ chân tại chỗ (MT1.2).</li>
        <li>- Nhận diện màu sắc bộ bài xem có bộ bài được 3 lần khoảng cách 2.5cm (MT4.1).</li>
        <li>- Phân biệt hoặc nhận biết các màu nước, vị trí chân và lối (MT2.1).</li>
    </ul>
    <li><strong>Hoạt động thể dục:</strong></li>
    <ul>
        <li>- Leo đồi.</li>
        <li>- Vượt chướng ngại.</li>
        <li>- Nhảy qua vòng.</li>
        <li>- Bật nhảy chân.</li>
        <li>- Chạy vòng quanh rào.</li>
        <li>- Chạy tại chỗ.</li>
        <li>- Đá và bật chụm chân.</li>
        <li>- Nhảy bật lùi.</li>
        <li>- Đứng ngồi tại chỗ.</li>
    </ul>
    <li><strong>Hoạt động học:</strong></li>
    <ul>
        <li>- Ném trái và đếm bước.</li>
        <li>- Tay đổi lên ngang trái.</li>
        <li>- Bật xa 25cm: Lực lệch theo hướng thẳng (MT3.5).</li>
        <li>- Đứng 30cm - 25cm.</li>
    </ul>
    <li><strong>Tư thế cơ thể:</strong></li>
    <ul>
        <li>- Chân giữ thăng bằng, mắt điều chỉnh, tự tạo bóng, đập, kéo vững, giữ thắt lưng, di chuyển, đứng ngang giữa nền.</li>
        <li>- Tư thế đứng chân chùng, chéo qua sàn, giữ thắt lưng, đặt các đồ chơi gần các chỗ lùi hoặc tiến, đo nhắm, đặt tay vào các đồ chơi thăng bằng.</li>
        <li>- Bước lùi, đứng vững.</li>
    </ul>
    <li><strong>Chơi vận động:</strong></li>
    <ul>
        <li>- Đứng lùi, duỗi thẳng, dựa vào cạnh, cầm bóng, tự kéo nắm, tập cầm, kéo dây, giữ chân, giữ vị trí chậm rãi.</li>
        <li>- Điều chỉnh tư thế: Giữ động tác nhịp nhàng, kiểm soát nhịp, điều chỉnh, giữ thăng bằng, nhảy, vỗ tay, đếm nhịp nhàng.</li>
        <li>- Ngồi tựa vào gương, giữ thăng bằng phía trước, điều chỉnh nhịp điệu.</li>
        <li>- Nhảy hoặc lăn, giữ thăng bằng hai tay.</li>
        <li>- Đi bộ theo hướng nghiêng về phía trước, sau, bên phải, bên trái.</li>
        <li>- Điều khiển trọng lượng, điều chỉnh độ vững, chạy theo các chướng ngại vật, giữ thăng bằng chân trái.</li>
    </ul>
</ul>

`
    },
    {
      STT: 3,
      targetName: "MT3",
      detailTargetName:
        "2.1. Giữ được thăng bằng cơ thể khi thực hiện vận động:- Đi hết đoạn đường hẹp (3m x 0,2m)- Đi kiểng gót liên tục 3m."
    },
    {
      STT: 3,
      targetName: "MT4",
      detailTargetName:
        "2.1. Giữ được thăng bằng cơ thể khi thực hiện vận động:- Đi hết đoạn đường hẹp (3m x 0,2m)- Đi kiểng gót liên tục 3m."
    },
    {
      STT: 3,
      targetName: "MT5",
      detailTargetName:
        "2.1. Giữ được thăng bằng cơ thể khi thực hiện vận động:- Đi hết đoạn đường hẹp (3m x 0,2m)- Đi kiểng gót liên tục 3m."
    }
  ];

  const data3 = [
    {
      STT: 6,
      targetName: "MT6",
      detailTargetName: `<ul>
    <li>3.1. Thực hiện được các vận động:</li>
    <ul>
        <li>- Xoay tròn cổ tay.</li>
        <li>- Gập, dang ngón tay vào nhau.</li>
    </ul>
</ul>
`,
      educationalGoals: `<ul>
    <li><strong>Hoạt động học:</strong></li>
    <ul>
        <li>- Vẽ gà con.</li>
        <li>- Vẽ con ếch.</li>
        <li>- Cắt dán trang phục bé thích.</li>
        <li>- Vẽ ông mặt trời (MT7).</li>
    </ul>
    <li><strong>Hoạt động khác:</strong></li>
    <ul>
        <li>- Cho trẻ thực hiện các vận động: Xoay tròn cổ tay, Gập, duỗi ngón tay vào nhau (MT6)</li>
        <li>- Xếp chồng 8-10 khối không đổ.</li>
        <li>- Cách cài, cởi khuy áo.</li>
    </ul>
</ul>
`
    },
    {
      STT: 7,
      targetName: "MT7",
      detailTargetName: `<ul>
    <li>3.2. Phối hợp được cử động bàn tay, ngón tay, phối hợp tay - mắt trong một số hoạt động:</li>
    <ul>
        <li>- Vẽ được hình tròn theo mẫu.</li>
        <li>- Cắt thẳng được một đoạn 10cm - Xếp chồng 8-10 khối không đổ.</li>
        <li>- Tự cài, cởi cúc.</li>
    </ul>
</ul>
`
    }
  ];

  const data4 = [
    {
      STT: 8,
      targetName: "MT8",
      detailTargetName: `1.1. Nói đúng tên một số thực phẩm quen thuộc khi nhìn vật thật hoặc tranh ảnh (thịt, cá, trứng, sữa, rau...).`,
      educationalGoals: `<ul>
    <li><strong>Hoạt động học:</strong></li>
    <ul>
        <li>- Bé cần gì để lớn lên và khỏe mạnh (MT10).</li>
    </ul>
    <li><strong>Hoạt động khác:</strong></li>
    <ul>
        <li>- Nhận biết một số thực phẩm và món ăn quen thuộc. (MT8)</li>
        <li>-  Nhận biết các bữa ăn trong ngày và ích lợi của ăn uống đủ lượng và đủ chất.</li>
        <li>- Trẻ biết tên một số món ăn hàng ngày như: Thịt, cá, trứng, rau... mang lại nhiều chất dinh dưỡng để cho cơ thể khỏe mạnh, thông minh. (MT9)</li>
    </ul>
</ul>

`
    },
    {
      STT: 9,
      targetName: "MT9",
      detailTargetName: `1.2. Biết tên một số món ăn hàng ngày: trứng rán, cá kho, canh rau...`
    },
    {
      STT: 10,
      targetName: "MT10",
      detailTargetName: `1.3. Biết ăn để chóng lớn, khoẻ mạnh và chấp nhận ăn nhiều loại thức ăn khác nhau.`
    }
  ];

  const data5 = [
    {
      STT: 11,
      targetName: "MT11",
      detailTargetName: `<ul>
    <li>2.1. Thực hiện được một số việc đơn giản với sự giúp đỡ của người lớn:</li>
    <ul>
        <li>- Rửa tay, lau mặt, súc miệng.</li>
        <li>- Tháo tất, cởi quần, áo...</li>
    </ul>
</ul>
`,
      educationalGoals: `<ul>
    <li><strong>Hoạt động khác:</strong></li>
    <ul>
        <li>- Làm quen cách đánh răng, lau mặt. (MT11)</li>
        <li>- Cách cầm thìa, cách xúc cơm, cách bê bát, cất bát.</li>
    </ul>
</ul>
`
    },
    {
      STT: 12,
      targetName: "MT12",
      detailTargetName: `2.2. Sử dụng bát, thìa, cốc đúng cách.
`
    }
  ];

  const data6 = [
    {
      STT: 13,
      targetName: "MT13",
      detailTargetName: `3.1. Có một số hành vi tốt trong ăn uống khi được nhắc nhở: uống nước đã đun sôi…
`,
      educationalGoals: `<ul>
    <li><strong>Hoạt động khác:</strong></li>
    <ul>
        <li>- Trẻ biết một  số hành vi tốt trong ăn uống, ăn chín uống sôi.(MT13)
Có một số hành vi tốt trong vệ  sinh, phòng bệnh khi được nhắc nhở.(MT14)</li>
    </ul>
</ul>
`
    },
    {
      STT: 14,
      targetName: "MT14",
      detailTargetName: `3.2. Có một số hành vi tốt trong vệ  sinh, phòng bệnh khi được nhắc nhở:- Chấp nhận: Vệ sinh răng miệng, đội mũ khi ra nắng, mặc áo ấm, đi tất khi trời lạnh, đi dép, giầy khi đi học.- Biết nói với người lớn khi bị đau, chảy máu.
`
    }
  ];

  const data7 = [
    {
      STT: 15,
      targetName: "MT15",
      detailTargetName: `4.1. Nhận ra và tránh một số vật dụng nguy hiểm (bàn là, bếp đang đun, phích nước nóng ... ) khi được nhắc nhở.
`,
      educationalGoals: `<ul>
    <li><strong>Hoạt động học:</strong></li>
    <ul>
        <li>- Trò chuyện với trẻ về bé cần làm gì để bảo vệ bản thân. (MT15)</li>
        <li>- Trò chuyện với trẻ về bé cần làm gì để an toàn. (MT16)</li>
    </ul>
    <li><strong>Hoạt động khác:</strong></li>
    <ul>
        <li>- Trong khi ăn trẻ không cười đùa trong khi ăn, uống hoặc khi ăn các loại quả có hạt dễ bị hóc sắc... không tự ý uống thuốc, không được trèo lan can, không nghịch các vật sắc, nhọn.</li>
        <li>- Không đi theo người lạ, khi người lạ rủ đi chơi; ra khỏi nhà, khu vực trường, lớp khi không được phép của người lớn, cô giáo. (MT17)</li>
    </ul>
</ul>
`
    },
    {
      STT: 16,
      targetName: "MT16",
      detailTargetName: `4.2. Biết tránh nơi nguy hiểm (hồ, ao, bể chứa nước, giếng, hố vôi …) khi được nhắc nhở.
`
    },
    {
      STT: 17,
      targetName: "MT17",
      detailTargetName: `<ul>
    <li>4.3. Biết tránh một số hành động nguy hiểm khi được nhắc nhở:</li>
    <ul>
        <li>- Không cười đùa trong khi ăn, uống hoặc khi ăn các loại quả có hạt...</li>
        <li>- Không tự lấy thuốc uống.</li>
        <li>- Không leo trèo bàn ghế, lan can.</li>
        <li>- Không nghịch các vật sắc nhọn.</li>
        <li>- Không theo người lạ ra khỏi khu vực trường lớp.</li>
    </ul>
</ul>
`
    }
  ];

  const data8 = [
    {
      STT: 18,
      targetName: "MT18",
      detailTargetName: `1.1. Quan tâm, hứng thú với các sự vật, hiện tượng gần gũi, như chăm chú quan sát sự vật, hiện tượng; hay đặt câu hỏi về đối tượng.
`,
      educationalGoals: `<ul>
    <li><strong>Hoạt động học:</strong></li>
    <ul>
        <li>- Tìm hiểu về xe đạp (MT18).</li>
        <li>- Tìm hiểu về con cá chép.</li>
        <li>- Tìm hiểu về con voi.</li>
        <li>- Tìm hiểu về con ong.</li>
        <li>- Sự kì diệu của quả trứng.</li>
        <li>- Tìm hiểu về hoa hồng, hoa cúc (MT19).</li>
        <li>- Trò chuyện về một số món ăn trong ngày tết.</li>
        <li>- Tìm hiểu về quả cam.</li>
        <li>- Phân thành 2 nhóm theo 1 dấu hiệu màu sắc.</li>
        <li>- Phân thành 2 nhóm theo 1 dấu hiệu kích thước.</li>
        <li>- Phân thành 2 nhóm theo 1 dấu hiệu kích thước (MT22).</li>
        <li>- Phân thành 2 nhóm theo 2 dấu hiệu màu sắc và kích thước.</li>
        <li>- Phân thành 2 nhóm theo 2 dấu hiệu hình dạng và màu sắc.</li>
        <li>- Phân thành 2 nhóm theo 2 dấu hiệu màu sắc và kích thước.</li>
        <li>- Phân thành 2 nhóm theo 2 dấu hiệu kích thước và hình dạng.</li>
    </ul>
    <li><strong>Hoạt động khác:</strong></li>
    <ul>
        <li>- Làm thí nghiệm biến đổi của màu nước, vật chìm vật nổi (MT20).</li>
        <li>- Xem tranh, vật thật một số các con vật.</li>
        <li>- Quan sát các loại cây, hoa, quả, rau, cách chăm sóc và bảo vệ chúng.</li>
        <li>- Tổ chức cho trẻ chơi các trò chơi dân gian trong ngày tết, quan sát cây đào, cách gói bánh chưng.</li>
        <li>- Quan sát tranh, vật thật các loại phương tiện giao thông đường bộ.</li>
        <li>- Xem clip về cách tham gia giao thông của các em bé (MT21).</li>
        <li>- Quan sát thời tiết các mùa trong năm.</li>
    </ul>
</ul>

`
    },
    {
      STT: 19,
      targetName: "MT19",
      detailTargetName: `1.2. Sử dụng các giác quan để xem xét, tìm hiểu đối tượng: nhìn, nghe, ngửi, sờ,.. để nhận ra đặc điểm nổi bật của đối tượng.
`
    },
    {
      STT: 20,
      targetName: "MT20",
      detailTargetName: `1.3. Làm thử nghiệm đơn giản với sự giúp đỡ của người lớn để quan sát, tìm hiểu đối tượng. Ví dụ: Thả các vật vào nước để nhận biết vật chìm hay nổi.
`
    },
    {
      STT: 21,
      targetName: "MT21",
      detailTargetName: `1.4. Thu thập thông tin về đối tượng bằng nhiều cách khác nhau có sự gợi mở của cô giáo như xem sách, tranh ảnh và trò chuyện về đối tượng.
`
    },
    {
      STT: 22,
      targetName: "MT22",
      detailTargetName: `1.5. Phân loại các đối tượng theo một dấu hiệu nổi bật.
`
    }
  ];

  const data9 = [
    {
      STT: 23,
      targetName: "MT23",
      detailTargetName: `2. Nhận ra một vài mối quan hệ đơn giản của sự vật, hiện tượng quen thuộc khi được hỏi.
`,
      educationalGoals: `<ul>
    <li><strong>Hoạt động học:</strong></li>
    <ul>
        <li>- Tìm hiểu về mùa hè.</li>
        <li>- Tìm hiểu về sự hòa tan của đường và muối ở trong nước (MT23).</li>
        <li>- Tìm hiểu về hiện tượng tự nhiên: Trời mưa, nắng, gió, sấm, sét ảnh hưởng của nó đến sinh hoạt của trẻ.</li>
    </ul>
</ul>
`
    }
  ];

  const data10 = [
    {
      STT: 24,
      targetName: "MT24",
      detailTargetName: `3.1. Mô tả những dấu hiệu nổi bật của đối tượng được quan sát với sự gợi mở của cô giáo.
`,
      educationalGoals: `<ul>
    <li><strong>Hoạt động học:</strong></li>
    <ul>
        <li>- Trò chuyện về mùa xuân. (MT24)</li>
    </ul>
    <li><strong>Hoạt động khác:</strong></li>
    <ul>
        <li>- Hát, múa, vận động những bài hát, chơi đóng vai vẽ, nặn, xé, dán theo chủ đề. (MT25)</li>
        <li>- Xem tranh, vật thật một số các con vật.</li>
        <li>- Tổ chức cho trẻ chơi các trò chơi dân gian trong ngày tết, quan sát cây đào, cách gói bánh chưng.</li>
        <li>- Trò chơi: Gắn đúng tranh, hoa nào quả nấy, đoán cây qua lá, chọn cây, nhanh mắt nhanh tay.</li>
        <li>- Quan sát các loại cây hoa, quả, rau, cách chăm sóc và bảo vệ chúng.</li>
        <li>- Quan sát tranh, vật thật các loại phương tiện giao thông đường bộ.</li>
        <li>- Xem clip về cách tham gia giao thông.</li>
        <li>- Quan sát thời tiết các mùa trong năm.</li>
        <li>- Làm thí nghiệm vật nổi, vật chìm.</li>
        <li>- Trò chơi: Con gì kêu, con gì bịt mắt, đố biết con gì, đoán cây qua lá, chọn cây, hoa nào quả nấy, chiếc túi kỳ lạ, thăm nhà bạn, thuyền vào bến, đèn xanh đèn đỏ, vật gì nổi, vật gì chìm, trời sáng trời tối, thả thuyền giấy, thổi bong bóng xà phòng, vẽ đứng tên địa danh, ai có tranh giống tôi, gắn đúng tranh, kể đủ 5 thứ, đố và giải đố, thử trí thông minh, ai nhanh hơn...</li>
    </ul>
</ul>

`
    },
    {
      STT: 25,
      targetName: "MT25",
      detailTargetName: `3.2. Thể hiện một số điều quan sát được qua các hoạt động chơi, âm nhạc, tạo hình…
`
    }
  ];

  const data11 = [
    {
      STT: 26,
      targetName: "MT26",
      detailTargetName:
        "1.1. Quan tâm đến số lượng và đếm như hay hỏi về số lượng, đếm vật, biết sử dụng ngón tay để biểu thị số lượng.",
      educationalGoals: `<ul>
            <li><strong>Hoạt động học:</strong></li>
            <ul>
                <li>- Đếm trên đối tượng trong phạm vi 5 (MT26)</li>
                <li>- Đo lượng nước</li>
                <li>- Đếm trên đối tượng trong phạm vi 1</li>
                <li>- Đếm trên đối tượng trong phạm vi 2</li>
                <li>- Đếm trên đối tượng trong phạm vi 3</li>
                <li>- Đếm trên đối tượng trong phạm vi 4</li>
                <li>- Đếm trên đối tượng trong phạm vi 5</li>
                <li>- Một và nhiều</li>
                <li>- Đếm trên đối tượng trong phạm vi 5 (MT27)</li>
                <li>- Tách, gộp 2 nhóm đối tượng có tổng bằng 2</li>
                <li>- Tách, gộp 2 nhóm đối tượng có tổng bằng 3</li>
                <li>- Tách, gộp 2 nhóm đối tượng có tổng bằng 4</li>
                <li>- Tách, gộp 2 nhóm đối tượng có tổng bằng 5</li>
                <li>- Ghép 2 nhóm đối tượng có tổng là 5 (MT29, 30)</li>
            </ul>
            <li><strong>Hoạt động khác:</strong></li>
            <ul>
                <li>- So sánh số lượng 2 nhóm đối tượng trong phạm vi 5 bằng các cách khác nhau</li>
                <li>- Vận động nhẹ</li>
                <li>- So sánh số lượng 2 nhóm đối tượng trong phạm vi 5 bằng các cách khác nhau (MT28)</li>
                <li>- Rèn các kỹ năng đếm trên đối tượng trong phạm vi 5</li>
                <li>- Xem bảng hình các bài thơ, truyền cảm xúc liên quan đến chủ đề</li>
                <li>- Ôn bài buổi sáng</li>
                <li>- Cho trẻ tập thao tác với máy tính</li>
                <li>- Chơi tự do</li>
            </ul>
        </ul>`
    },
    {
      STT: 27,
      targetName: "MT27",
      detailTargetName: "1.2. Đếm trên các đối tượng giống nhau và đếm đến 5."
    },
    {
      STT: 28,
      targetName: "MT28",
      detailTargetName:
        "1.3. So sánh số lượng hai nhóm đối tượng trong phạm vi 5 bằng các cách khác nhau và nói được các từ: bằng nhau, nhiều hơn, ít hơn."
    },
    {
      STT: 29,
      targetName: "MT29",
      detailTargetName:
        "1.4. Biết gộp và đếm hai nhóm đối tượng cùng loại có tổng trong phạm vi 5."
    },
    {
      STT: 30,
      targetName: "MT30",
      detailTargetName:
        "1.5. Tách một nhóm đối tượng có số lượng trong phạm vi 5 thành hai nhóm."
    }
  ];

  const data12 = [
    {
      STT: 31,
      targetName: "MT31",
      detailTargetName: `2. Nhận ra qui tắc sắp xếp đơn giản (mẫu) và sao chép lại.
`,
      educationalGoals: `<ul>
    <li><strong>Hoạt động học:</strong></li>
    <ul>
        <li>- Sắp xếp theo quy tắc 1-1.</li>
        <li>- Xếp tương ứng 1-1</li>
        <li>- Sắp xếp theo quy tắc 1-2.</li>
        <li>- Ghép đôi.</li>
        <li>- Sắp xếp theo quy tắc 2-2.(MT31).</li>
    </ul>
</ul>
`
    }
  ];

  const data13 = [
    {
      STT: 32,
      targetName: "MT32",
      detailTargetName: `3. So sánh  hai đối tượng về kích thước và nói được các từ: to hơn/ nhỏ hơn; dài hơn/ ngắn hơn; cao hơn/ thấp hơn; bằng nhau.
`,
      educationalGoals: `<ul>
    <li><strong>Hoạt động học:</strong></li>
    <ul>
        <li>- Phân biệt dài hơn, ngắn hơn.</li>
        <li>- Phân biệt to, nhỏ.</li>
        <li>- Phân biệt cao, thấp, bằng nhau.</li>
    </ul>
</ul>
`
    }
  ];

  const data14 = [
    {
      STT: 33,
      targetName: "MT33",
      detailTargetName: `4. Nhận dạng và gọi tên các hình: tròn, vuông, tam giác, chữ nhật.
`,
      educationalGoals: `<ul>
    <li><strong>Hoạt động học:</strong></li>
    <ul>
        <li>- Phân biệt hình tròn, hình vuông.</li>
        <li>- Phân biệt hình tam giác, hình chữ nhật.</li>
        <li>- Phân biệt hình tròn, hình vuông, hình tam giác, hình chữ nhật.(MT33).</li>
        <li>- Sử dụng các hình học để chắp ghép.</li>
    </ul>
</ul>
`
    }
  ];

  const data15 = [
    {
      STT: 34,
      targetName: "MT34",
      detailTargetName:
        "5. Sử dụng lời nói và hành động để chỉ vị trí của đối tượng trong không gian so với bản thân.",
      educationalGoals: `<ul>
          <li><strong>Hoạt động học:</strong></li>
          <ul>
              <li>- Phân biệt phía trên, phía dưới.</li>
              <li>- Phân biệt tay phải, tay trái.</li>
              <li>- Phân biệt phía trên, phía dưới, trước, sau của bản thân trẻ (MT34).</li>
              <li>- Phân biệt sáng và chiều.</li>
              <li>- Phân biệt ngày, đêm, sáng, chiều.</li>
          </ul>
          <li><strong>Hoạt động khác:</strong></li>
          <ul>
              <li>- Đếm trên ngón tay, bài tập giấy nhận biết đếm từ 1-5, đếm vẹt, đếm đồ chơi, đếm rau, hoa, quả, đếm bạn....</li>
              <li>- Tìm và tạo nhóm số lượng 1 và nhiều, sự khác nhau về số lượng.... giỏi hơn, bạn chọn thế nào, gộp tách, tạo nhóm đồ dùng đồ chơi có dạng hình vuông - hình tròn, tô màu các hình theo yêu cầu.</li>
              <li>- Bài tập giấy: Ai đoán giỏi, ai nhanh hơn, đố biết gì? Xếp hình, ghép hình, tô hình, bé hãy nối 2 nhóm đối tượng sao cho có số lượng 1..., nối, xếp con vật thành 2 hàng ... xâu vòng, tặng quà cho từng bạn, hãy sắp xếp theo tôi, tìm bạn, ai nói đúng, ai tìm giỏi, ai cao hơn, hay cùng đo, hay tìm đúng... bé cảm thế nào cho đúng, đồ vật đang ở đâu?</li>
              <li>- Trò chơi: Cái túi bí mật, giúp cô tìm đồ vật, đoán xem ai vào, vẽ đúng nhà, tối có những gì, chúng gieo ở đâu, thành viên nhà mèo và nhà cú ai nhanh hơn, ít hơn, ai chọn đúng, tìm đồ vật theo hình dạng, ai đếm đúng, có bao nhiêu đồ dùng, nhìn hình gọi tên, đếm thêm nữa, hãy xem nhóm nào nhiều hơn ít hơn, phân loại các con vật theo kích thước, đoán xem con nào to hơn, nhỏ hơn, xem ai tinh mắt, thêm bớt vật gì, ai nhanh nhất.</li>
          </ul>
      </ul>`
    }
  ];

  const data16 = [
    {
      STT: 35,
      targetName: "MT35",
      detailTargetName:
        "1.1. Nói được tên, tuổi, giới tính của bản thân khi được hỏi, trò chuyện.",
      educationalGoals: `<ul>
            <li><strong>Hoạt động học:</strong></li>
            <ul>
                <li>- Trò chuyện về gia đình bé (MT36).</li>
                <li>- Tìm hiểu về ngôi nhà thân yêu của bé (MT37).</li>
                <li>- Tìm hiểu về một số đồ dùng trong gia đình bé.</li>
                <li>- Bé đi học lớp mẫu giáo 3 tuổi (MT38).</li>
                <li>- Đồ dùng đồ chơi của lớp.</li>
            </ul>
            <li><strong>Hoạt động khác:</strong></li>
            <ul>
                <li>- 1.1. Nói được tên, tuổi, giới tính của bản thân khi được hỏi, trò chuyện (MT35).</li>
            </ul>
        </ul>`
    },
    {
      STT: 36,
      targetName: "MT36",
      detailTargetName:
        "1.2. Nói được tên của bố mẹ và các thành viên trong gia đình."
    },
    {
      STT: 37,
      targetName: "MT37",
      detailTargetName:
        "1.3. Nói được địa chỉ của gia đình khi được hỏi, trò chuyện, xem ảnh về gia đình."
    },
    {
      STT: 38,
      targetName: "MT38",
      detailTargetName:
        "1.4. Nói được tên trường/lớp, cô giáo, bạn, đồ chơi, đồ dùng trong lớp khi được hỏi, trò chuyện."
    }
  ];

  const data17 = [
    {
      STT: 39,
      targetName: "MT39",
      detailTargetName:
        "2. Kể tên và nói được sản phẩm của nghề nông, nghề xây dựng... khi được hỏi, xem tranh.",
      educationalGoals: `<ul>
        <li><strong>Hoạt động học:</strong></li>
        <ul>
            <li>- Tìm hiểu về nghề nông.</li>
            <li>- Trò chuyện về nghề của bố mẹ trẻ.</li>
            <li>- Tìm hiểu về nghề y.(MT39)</li>
            <li>- Trò chuyện về chú bộ đội bộ binh.</li>
        </ul>
    </ul>`
    }
  ];

  const data18 = [
    {
      STT: 40,
      targetName: "MT40",
      detailTargetName:
        "3.1. Kể tên một số lễ hội: Ngày khai giảng, Tết Trung thu... qua trò chuyện, tranh ảnh.",
      educationalGoals: `<ul>
          <li><strong>Hoạt động học:</strong></li>
          <ul>
              <li>- Tặng quà mừng tuổi bà và mẹ vào dịp lễ 8/3.</li>
              <li>- Chúc mừng thầy cô giáo nhân ngày Nhà giáo Việt Nam 20/11.</li>
              <li>- Trò chuyện về ngày 22/12 (Ngày Quân đội Nhân dân Việt Nam).</li>
              <li>- Trò chuyện về ngày sinh nhật của bé (MT40).</li>
              <li>- Tìm hiểu về các ngày hội lớn của đất nước (MT40).</li>
              <li>- Sự kỳ diệu của nước (Giáo án STEAM).</li>
              <li>- Sự kỳ diệu của quả táo (Giáo án STEAM).</li>
              <li>- Động đất núi lửa (Giáo án STEAM).</li>
              <li>- Hành tinh của chúng ta (Giáo án STEAM).</li>
          </ul>
          <li><strong>Hoạt động khác:</strong></li>
          <ul>
              <li>- Xem tranh ảnh một số lễ hội của các dân tộc tại địa phương (chùa, lễ hội).</li>
              <li>- Tổ chức các hoạt động trải nghiệm theo các mùa, động vật, đồ chơi trong từng năm học.</li>
              <li>- Nhận biết, phân biệt các ngày hội và lễ hội trong năm học.</li>
              <li>- Tìm hiểu và kể lại các truyền thống văn hóa, biểu diễn múa hát, đọc thơ của dân gian.</li>
              <li>- Nhận biết các ngày hội và các lễ hội thông qua các tranh ảnh và phim tài liệu.</li>
              <li>- Làm quen với các hoạt động vui chơi của gia đình trẻ em.</li>
              <li>- Xem tranh về các ngày hội quốc tế và cách sống vui vẻ, hòa đồng với cộng đồng và xã hội.</li>
              <li>- Xem và vẽ thêm các bức tranh ngôi nhà truyền thống Việt Nam, Nhật Bản.</li>
              <li>- Nhận biết và thực hành tốt các lễ hội truyền thống.</li>
              <li>- Xem và vẽ tranh về các ngày hội Việt Nam.</li>
              <li>- Tham gia trò chơi văn nghệ ngày hội của trường.</li>
              <li>- Lễ hội trồng cây và ngày hội gia đình.</li>
              <li>- Xem clip về các lễ hội nổi tiếng của đất nước.</li>
              <li>- Tham quan các địa danh, thắng cảnh nổi tiếng.</li>
              <li>- Trò chuyện về các công trình kiến trúc đặc trưng của địa phương (MT41).</li>
              <li>- Xem tranh về các địa danh nổi tiếng ở Hà Nội.</li>
              <li>- Trò chuyện về khu di tích văn hóa của dân tộc (MT41).</li>
              <li>- Lễ hội trồng cây tại các địa điểm nổi tiếng.</li>
              <li>- Tham quan các khu di tích văn hóa và danh lam thắng cảnh.</li>
              <li>- Tham gia hoạt động ngoại khóa tìm hiểu về các địa danh nổi tiếng trong khu vực.</li>
              <li>- Thực hành kể tên các địa danh và danh lam thắng cảnh mà bé từng đến.</li>
              <li>- Xem ảnh và làm quen các địa điểm nổi tiếng tại Hà Nội.</li>
              <li>- Hát, múa, vận động những bài hát, chơi đóng vai vẽ, nặn, xé, dán theo chủ đề (MT25).</li>
              <li>- Xem tranh, vật thật một số các con vật.</li>
              <li>- Tổ chức cho trẻ chơi các trò chơi dân gian trong ngày tết, quan sát cây đào, cách gói bánh chưng.</li>
              <li>- Trò chơi: Gắn đúng tranh, hoa nào quả nấy, đoán cây qua lá, chọn cây, nhanh mắt nhanh tay.</li>
              <li>- Quan sát các loại cây hoa, quả, rau, cách chăm sóc và bảo vệ chúng.</li>
              <li>- Quan sát tranh, vật thật các loại phương tiện giao thông đường bộ.</li>
              <li>- Xem clip về cách tham gia giao thông.</li>
              <li>- Quan sát thời tiết các mùa trong năm.</li>
              <li>- Làm thí nghiệm vật nổi, vật chìm.</li>
              <li>- Trò chơi: Con gì kêu, con gì bịt mắt, đố biết con gì, đoán cây qua lá, chọn cây, hoa nào quả nấy, chiếc túi kỳ lạ, thăm nhà bạn, thuyền vào bến, đèn xanh đèn đỏ, vật gì nổi, vật gì chìm, trời sáng trời tối, thả thuyền giấy, thổi bong bóng xà phòng, vẽ đứng tên địa danh, ai có tranh giống tôi, gắn đúng tranh, kể đủ 5 thứ, đố và giải đố, thử trí thông minh, ai nhanh hơn...</li>
          </ul>
      </ul>`
    },
    {
      STT: 41,
      targetName: "MT41",
      detailTargetName: "3.2. Kể tên một vài danh lam, thắng cảnh ở địa phương."
    }
  ];

  const data19 = [
    {
      STT: 42,
      targetName: "MT42",
      detailTargetName:
        "1.1. Thực hiện được yêu cầu đơn giản, ví dụ: 'Cháu hãy lấy quả bóng, ném vào rổ'.",
      educationalGoals: `<ul>
          <li><strong>Hoạt động học:</strong></li>
          <ul>
              <li>- Khám phá đôi bàn tay kỳ diệu (MT44).</li>
          </ul>
          <li><strong>Hoạt động khác:</strong></li>
          <ul>
              <li>- Ôn một số trò chơi (MT42).</li>
              <li>- Trò chuyện với trẻ về 1 số quần áo, đồ chơi, hoa, quả quen thuộc (MT43).</li>
              <li>- Trò chuyện về các hoạt động diễn ra trong ngày tết trung thu.</li>
          </ul>
      </ul>`
    },
    {
      STT: 43,
      targetName: "MT43",
      detailTargetName:
        "1.2. Hiểu nghĩa từ khái quát gần gũi: quần áo, đồ chơi, hoa, quả..."
    },
    {
      STT: 44,
      targetName: "MT44",
      detailTargetName:
        "1.3. Lắng nghe và trả lời được câu hỏi của người đối thoại."
    }
  ];

  const data20 = [
    {
      STT: 45,
      targetName: "MT45",
      detailTargetName: "2.1. Nói rõ các tiếng.",
      educationalGoals: `<ul>
          <li><strong>Hoạt động học:</strong></li>
          <ul>
              <li>Theo chủ đề:</li>
              <ul>
                  <li>- Thời tiết lạnh của mùa đông, mùa hè, mùa thu, mùa xuân.</li>
                  <li>- Các loại cây và con vật: xe đạp, ô tô, xe buýt, xe tải, xe khách.</li>
                  <li>- Mặt trời mọc vào buổi sáng, cây xanh và lá xanh (MT46).</li>
              </ul>
              <li>- Truyện: Ai ngoan sẽ được thưởng.</li>
              <li>- Truyện: Hãy để lại nước. Đừng uống nước.</li>
              <li>- Truyện: Cái cặp màu xanh của em.</li>
              <li>- Truyện: Chuyến xe buýt đầu tiên của bé.</li>
              <li>- Truyện: Hoa lá xanh của mùa hè.</li>
              <li>- Truyện: Trời mưa và xe cộ đi lại.</li>
              <li>- Thơ: Con mèo con.</li>
              <li>- Thơ: Con chó nhà bé.</li>
              <li>- Thơ: Chú gà trống gáy.</li>
              <li>- Thơ: Em yêu thiên nhiên.</li>
              <li>- Thơ: Mẹ và bé cùng làm vườn.</li>
              <li>- Truyện: Rùa con học bài.</li>
              <li>- Truyện: Gấu con sợ bóng đêm.</li>
              <li>- Thơ: Em chào các bạn.</li>
              <li>- Thơ: Mẹ của em là mẹ của bé.</li>
              <li>- Thơ: Cây cầu bắc qua sông.</li>
              <li>- Truyện: Chú mèo con và chú chuột.</li>
          </ul>
          <li><strong>Hoạt động khác:</strong></li>
          <ul>
              <li>- Hát, múa, vận động những bài hát, chơi đóng vai vẽ, nặn, xé, dán theo chủ đề.</li>
              <li>- Xem tranh, vật thật một số các con vật.</li>
              <li>- Tổ chức cho trẻ chơi các trò chơi dân gian trong ngày tết, quan sát cây đào, cách gói bánh chưng.</li>
              <li>- Trò chơi: Gắn đúng tranh, hoa nào quả nấy, đoán cây qua lá, chọn cây, nhanh mắt nhanh tay.</li>
              <li>- Quan sát các loại cây hoa, quả, rau, cách chăm sóc và bảo vệ chúng.</li>
              <li>- Quan sát tranh, vật thật các loại phương tiện giao thông đường bộ.</li>
              <li>- Xem clip về cách tham gia giao thông.</li>
              <li>- Quan sát thời tiết các mùa trong năm.</li>
              <li>- Làm thí nghiệm vật nổi, vật chìm.</li>
              <li>- Trò chơi: Con gì kêu, con gì bịt mắt, đố biết con gì, đoán cây qua lá, chọn cây, hoa nào quả nấy, chiếc túi kỳ lạ, thăm nhà bạn, thuyền vào bến, đèn xanh đèn đỏ, vật gì nổi, vật gì chìm, trời sáng trời tối, thả thuyền giấy, thổi bong bóng xà phòng, vẽ đứng tên địa danh, ai có tranh giống tôi, gắn đúng tranh, kể đủ 5 thứ, đố và giải đố, thử trí thông minh, ai nhanh hơn...</li>
          </ul>
      </ul>`
    },
    {
      STT: 46,
      targetName: "MT46",
      detailTargetName:
        "2.2. Sử dụng được các từ thông dụng chỉ sự vật, hoạt động, đặc điểm..."
    },
    {
      STT: 47,
      targetName: "MT47",
      detailTargetName: "2.3. Sử dụng được các dấu, dấu ghép."
    },
    {
      STT: 48,
      targetName: "MT48",
      detailTargetName:
        "2.4. Kể lại được những sự việc đơn giản đã diễn ra của bản thân như: thăm ông bà, đi chơi, xem phim..."
    },
    {
      STT: 49,
      targetName: "MT49",
      detailTargetName: "2.5. Đọc thuộc bài thơ, ca dao, đồng dao..."
    },
    {
      STT: 50,
      targetName: "MT50",
      detailTargetName:
        "2.6. Kể lại truyện dân gian đã được nghe và sự giúp đỡ của người lớn."
    },
    {
      STT: 51,
      targetName: "MT51",
      detailTargetName: "2.7. Bắt chước giọng nói của nhân vật trong truyện."
    },
    {
      STT: 52,
      targetName: "MT52",
      detailTargetName: "2.8. Nói rõ nghe, không nói lí nhí."
    },
    {
      STT: 53,
      targetName: "MT53",
      detailTargetName: "3.1. Nghe và hiểu một số từ chỉ sự vật, hiện tượng."
    }
  ];

  const data21 = [
    {
      STT: 54,
      targetName: "MT54",
      detailTargetName:
        "3.1. Đề nghị người khác đọc sách cho nghe, tự giờ sách xem tranh.",
      educationalGoals: `<ul>
          <li><strong>Hoạt động học:</strong></li>
          <ul>
              <li>- Khám phá đôi bàn tay kỳ diệu (MT44).</li>
              <li>- Truyện: Chú bé giọt nước (MT55).</li>
              <li>- Trò chuyện với trẻ về 1 số quần áo, đồ chơi, hoa, quả quen thuộc (MT43).</li>
              <li>- Trò chuyện về các hoạt động diễn ra trong ngày tết trung thu.</li>
          </ul>
          <li><strong>Hoạt động khác:</strong></li>
          <ul>
              <li>- Có tổ chức cho trẻ chơi trò chơi "làm theo yêu cầu của tôi": Đề nghị người khác đọc sách cho nghe, tự giờ sách ra xem (MT54).</li>
              <li>- Truyện: Chú bé giọt nước (MT55).</li>
              <li>- Vận động nhẹ.</li>
              <li>- Chơi, tổ, vẽ, cắt, dán, xếp hình theo ý thích.</li>
              <li>- Đọc các câu đố về chủ đề.</li>
              <li>- Quan sát thời tiết các mùa trong năm.</li>
              <li>- Trò chuyện cách sống hợp tác, thân thiện quan tâm chia sẻ với bạn bè qua tranh ảnh, qua các câu chuyện, bài thơ để trẻ nhận biết được một số đức tính: Thật thà, vâng lời, thích làm người tốt...</li>
              <li>- Chơi tự do.</li>
              <li>- Cho trẻ chơi với giấy bút (MT56).</li>
              <li>- Đồng dao - ca dao - trò chơi dân gian: Ông giăng ông giăng, ông sào ông sào, trời mưa trời gió, thả địa ba ba, đích đắc đác đác...</li>
              <li>- Mèo đuổi chuột, trồng rơm trồng hoa, dùng dụng cụ để đi lại, chui qua ô cửa, lộn cầu vồng, kéo cua lửa xé, rèn rãnh ràng ràng, đếm sao, lúa ngô lá cỏ đậu lảnh, cây đồng, chú Cuội.</li>
              <li>- Hoạt động đọc sách, ngày hội sách.</li>
              <li>- Bé làm sách, bé sưu tập của bé, kể chuyện theo tranh.</li>
              <li>- Tạo hình: Vẽ trên sân trường, vẽ theo ý thích, làm quen với màu nước.</li>
              <li>- Trò chơi: Hai bàn tay xinh, những ngón tay kỳ diệu.</li>
          </ul>
      </ul>`
    },
    {
      STT: 55,
      targetName: "MT55",
      detailTargetName:
        "3.2. Nhìn vào tranh minh họa và gọi tên nhân vật trong tranh."
    },
    {
      STT: 56,
      targetName: "MT56",
      detailTargetName: "3.3. Thích vẽ, ‘viết’ nguệch ngoạc."
    }
  ];

  const data22 = [
    {
      STT: 57,
      targetName: "MT57",
      detailTargetName: "1.1. Nói được tên, tuổi, giới tính của bản thân.",
      educationalGoals: `<ul>
          <li><strong>Hoạt động học:</strong></li>
          <ul>
              <li>- Trò chuyện về bản thân (MT57)</li>
          </ul>
          <li><strong>Hoạt động khác:</strong></li>
          <ul>
              <li>- Nói được điều bé thích và không thích (MT58).</li>
          </ul>
      </ul>`
    },
    {
      STT: 58,
      targetName: "MT58",
      detailTargetName: "1.2. Nói được điều bé thích, không thích."
    }
  ];

  const data23 = [
    {
      STT: 59,
      targetName: "MT59",
      detailTargetName:
        "2.1. Mạnh dạn tham gia vào các hoạt động, mạnh dạn khi trả lời câu hỏi.",
      educationalGoals: `<ul>
          <li><strong>Hoạt động học:</strong></li>
          <ul>
              <li>- Tìm hiểu về đèn giao thông (MT59)</li>
              <li>- Xé dán con thuyền (Mẫu) (MT60)</li>
          </ul>
          <li><strong>Hoạt động khác:</strong></li>
          <ul>
              <li>- Cố gắng thực hiện công việc được giao (Xếp đồ chơi...) (MT60).</li>
          </ul>
      </ul>`
    },
    {
      STT: 60,
      targetName: "MT60",
      detailTargetName:
        "2.2. Cố gắng thực hiện công việc đơn giản được giao (chia giấy vẽ, xếp đồ chơi, ...)."
    }
  ];

  const data24 = [
    {
      STT: 61,
      targetName: "MT61",
      detailTargetName:
        "3.1. Nhận ra cảm xúc: vui, buồn, sợ hãi, tức giận qua nét mặt, giọng nói, qua tranh ảnh.",
      educationalGoals: `<ul>
          <li><strong>Hoạt động học:</strong></li>
          <ul>
              <li>- Truyện: Gấu con bị đau răng (MT61)</li>
          </ul>
          <li><strong>Hoạt động khác:</strong></li>
          <ul>
              <li>- Quan sát tranh ảnh và trò chuyện về cách biểu lộ cảm xúc vui, buồn, sợ hãi, tức giận... (MT62)</li>
              <li>- Cho trẻ xem hình ảnh Bác Hồ (MT63)</li>
              <li>- Cho trẻ nghe truyện, nghe hát, đọc thơ, xem tranh ảnh về Bác Hồ (MT64)</li>
              <li>- Lao động tự phục vụ, vệ sinh cá nhân</li>
              <li>- Hoạt động trực nhật: Lấy cất gối, lấy cất ghế, xếp đồ chơi...</li>
              <li>- Bé soi gương, xem ảnh, vẽ tranh về bản thân, về những người thân, bạn bè, cùng trò chuyện về những biểu cảm: cười, buồn rầu, xem tranh ảnh về Bác Hồ, trang trí ảnh Bác, tham quan lăng Bác, tô màu tranh ảnh Bác Hồ, nghe kể chuyện về Bác, trò chuyện về tình cảm của Bác Hồ với thiếu nhi, dạy trẻ những bài hát về Bác.</li>
              <li>- Trò chơi: Bắt bóng, nói tên, đồ dùng của tôi , đây là ai,  bạn ở đâu? ai đang hát, hãy nhận đúng tên mình, ( thông qua ảnh hoặc kí hiệu),tìm đôi, bạn mặc gì?, trông tôi thế nào?, giúp cô tìm bạn, bạn là ai, nhìn hình đoán tâm trạng của người khác vui hay buồn, khuôn mặt bạn thế nào?, trò chơi tìm và nối đúng cảm xúc với khuôn mặt, bắt chước biểu lộ cảm xúc theo tranh ảnh.</li>
              <li>- Vận động nhẹ.</li>
              <li>- Xem tranh truyện và đặt tên về những nhân vật yêu thích.</li>
              <li>- Ôn ký năng nhận biết ký hiệu.</li>
              <li>- Cho trẻ nghe truyện, nghe hát, đọc thơ, xem tranh ảnh về Bác Hồ<( MT64)</li>
              <li>- Ôn lại bài buổi sáng.</li>
              <li>- Chơi tự do.</li>
          </ul>
      </ul>`
    },
    {
      STT: 62,
      targetName: "MT62",
      detailTargetName:
        "3.2. Biết biểu lộ các cảm xúc vui, buồn, sợ hãi, tức giận.",
      educationalGoals: ""
    },
    {
      STT: 63,
      targetName: "MT63",
      detailTargetName: "3.3. Nhận ra hình ảnh Bác Hồ.",
      educationalGoals: ""
    },
    {
      STT: 64,
      targetName: "MT64",
      detailTargetName:
        "3.4. Thích nghe kể chuyện, nghe hát, đọc thơ, xem tranh ảnh về Bác Hồ.",
      educationalGoals: ""
    }
  ];

  const data25 = [
    {
      STT: 65,
      targetName: "MT65",
      detailTargetName:
        "4.1. Thực hiện được một số quy định ở lớp và gia đình: Sau khi chơi xếp cất đồ chơi, không tranh giành đồ chơi, vâng lời bố mẹ.",
      educationalGoals: `<ul>
          <li><strong>Hoạt động khác:</strong></li>
          <ul class="font-bold">
              <li>- Cất, xếp đồ chơi theo hướng dẫn của cô (MT65).</li>
              <li>- Chào cô, chào ông bà, bố mẹ, chào các bạn khi đến lớp và ra về (MT66).</li>
              <li>- Trò chuyện về hoa quả, bánh kẹo trong ngày Tết (MT67).</li>
              <li>- Trò chuyện về ngày hội của cô giáo.</li>
              <li>- Trò chuyện về công việc của nghề thợ xây.</li>
              <li>- Xem clip tranh ảnh lao động, giúp đỡ các bạn và thực hành tham gia hoạt động các trò chơi theo nhóm nhỏ (MT68).</li>
          </ul>
      </ul>`
    },
    {
      STT: 66,
      targetName: "MT66",
      detailTargetName:
        "4.2. Biết chào hỏi và nói cảm ơn, xin lỗi khi được nhắc nhở.",
      educationalGoals: ""
    },
    {
      STT: 67,
      targetName: "MT67",
      detailTargetName: "4.3. Chú ý nghe khi cô, bạn nói.",
      educationalGoals: ""
    },
    {
      STT: 68,
      targetName: "MT68",
      detailTargetName:
        "4.4. Cùng chơi với các bạn trong các trò chơi theo nhóm nhỏ.",
      educationalGoals: ""
    }
  ];

  const data26 = [
    {
      STT: 65,
      targetName: "MT65",
      detailTargetName:
        "4.1. Thực hiện được một số quy định ở lớp và gia đình: Sau khi chơi xếp cất đồ chơi, không tranh giành đồ chơi, vâng lời bố mẹ.",
      educationalGoals: `<ul>
          <li><strong>Hoạt động khác:</strong></li>
          <ul>
              <li>- Cất, xếp đồ chơi theo hướng dẫn của cô (MT65).</li>
              <li>- Chào cô, chào ông bà, bố mẹ, chào các bạn khi đến lớp và ra về (MT66).</li>
              <li>- Trò chuyện về hoa quả, bánh kẹo trong ngày Tết (MT67).</li>
              <li>- Trò chuyện về ngày hội của cô giáo.</li>
              <li>- Trò chuyện về công việc của nghề thợ xây.</li>
              <li>- Xem clip tranh ảnh lao động, giúp đỡ các bạn và thực hành tham gia hoạt động các trò chơi theo nhóm nhỏ (MT68).</li>
              <li>- Cho trẻ quan sát cảnh vật thiên nhiên và thực hành chăm sóc cây (MT69).</li>
              <li>- Nhặt rác bỏ rác đúng nơi quy định (MT70).</li>
              <li>- Hoạt động vui chơi trong giờ đón trẻ, giờ chơi góc, sinh hoạt chiều.</li>
              <li>- Xem hình ảnh nên - không nên.</li>
              <li>- Các hoạt động giờ học, trò chuyện đầu giờ.</li>
              <li>- Trao đổi, khảo sát với phụ huynh về mức độ thực hiện các quy định của trẻ tại gia đình.</li>
              <li>- Đọc thơ, kể chuyện,... có nội dung giáo dục lễ giáo.</li>
              <li>- Cho trẻ xem bảng hình về các hoạt động giáo dục lễ giáo, kỹ năng sống.</li>
              <li>- Thông qua hoạt động rèn nề nếp xếp hàng, thực hiện việc xếp hàng đúng quy định (Biết chờ đến lượt khi được nhắc nhở), xếp hàng đi vào lớp, xếp hàng đi rửa tay.</li>
              <li>- Chăm sóc góc thiên nhiên, hoạt động ngoài trời có chủ đích, thói quen vệ sinh trong lớp học, nhặt rác xung quanh khu vực trẻ chơi.</li>
              <li>- Xem bảng đĩa các hành động đúng, sai.</li>
              <li>- Xem video về giữ gìn vệ sinh nơi công cộng.</li>
              <li>- Xem clip quy trình phát triển của cây.</li>
              <li>- Trò chơi: Tai ai tinh, nghe thấu đoán tai, ai đoán đúng bé nào ngoan, ai nhanh nhất, ai ngoan sẽ được thưởng, gieo hạt, thử trí thông minh, nhanh mắt nhanh tay, đội nào giỏi hơn...</li>
          </ul>
      </ul>`
    },
    {
      STT: 66,
      targetName: "MT66",
      detailTargetName:
        "4.2. Biết chào hỏi và nói cảm ơn, xin lỗi khi được nhắc nhở.",
      educationalGoals: ""
    },
    {
      STT: 67,
      targetName: "MT67",
      detailTargetName: "4.3. Chú ý nghe khi cô, bạn nói.",
      educationalGoals: ""
    },
    {
      STT: 68,
      targetName: "MT68",
      detailTargetName:
        "4.4. Cùng chơi với các bạn trong các trò chơi theo nhóm nhỏ.",
      educationalGoals: ""
    },
    {
      STT: 69,
      targetName: "MT69",
      detailTargetName:
        "5.1. Thích quan sát cảnh vật thiên nhiên và chăm sóc cây.",
      educationalGoals: ""
    },
    {
      STT: 70,
      targetName: "MT70",
      detailTargetName: "5.2. Bỏ rác đúng nơi quy định.",
      educationalGoals: ""
    }
  ];

  const data27 = [
    {
      STT: 71,
      targetName: "MT71",
      detailTargetName:
        "1.1. Vui sướng, vỗ tay, nói lên cảm nhận của mình khi nghe các âm thanh gợi cảm và ngắm nhìn vẻ đẹp nổi bật của các sự vật, hiện tượng.",
      educationalGoals: `<ul>
          <li><strong>Hoạt động học:</strong></li>
          <ul>
              <li>- In ngón tay hình con chim (Mẫu).</li>
              <li>- Dán ngôi nhà (Mẫu).</li>
              <li>- Nhận hoa mùa xuân (Mẫu) (MT73).</li>
              <li>- Dán hoa tặng mẹ (Đề tài).</li>
              <li>- In ngón tay tạo thành bông lúa (Mẫu).</li>
              <li>- Tô màu tranh con ong (Mẫu).</li>
              <li>- In ngón tay tạo thành pháo hoa (Đề tài)</li>
          </ul>
          <li><strong>Hoạt động khác:</strong></li>
          <ul>
              <li>- Xem bảng hình đánh khơi gợi cho trẻ nói lên cảm nhận của mình trước âm thang gợi cảm, trước vẻ đẹp của các loài hoa, các bộ trang phục rực rỡ đón chào ngày Tết.(MT71)</li>
              <li>- Cho trẻ nghe, hát các bài hát trong chủ đề.</li>
              <li>- Hát, đọc thơ, đồng dao, ca dao, tục ngữ, kể chuyện cho trẻ nghe và cho trẻ hát theo, vỗ tay, nhún nhảy, lắc lư theo bài hát bản nhạc (MT72).</li>
              <li>- Hát, đọc thơ, đồng dao, ca dao, tục ngữ, kể chuyện cho trẻ nghe và cho trẻ hát theo, vỗ tay, nhún nhảy, lắc lư theo bài hát bản nhạc.</li>
              <li>- Triển lãm tranh, sản phẩm tạo hình, cho trẻ xem bảng hình tranh vẽ, phong cảnh…</li>
              <li>- Trò chơi: Ai nhanh nhất, đoán tên bài hát, những nốt nhạc vui, nghe âm thanh tìm đồ vật, nghe giai điệu đoán tên bài hát, tiếng hát ở đâu?, tai ai tinh, đi theo tiếng nhạc, tiếng gì vậy, âm thanh từ phía nào?, làm theo tiếng nhạc, nghe tiếng kêu đoán con vật, trời nắng to quá, đoán xem tiếng gì?, mưa to mưa nhỏ, nhanh và chậm, to và nhỏ, những chiếc bút nháy múa, đoán giói, âm thanh gì đấy?, vẽ đúng nhà, vẽ đúng hình, đoán tên bài hát.</li>
              <li>- Vẽ bộ lông cừu (MT73).</li>
          </ul>
      </ul>`
    },
    {
      STT: 72,
      targetName: "MT72",
      detailTargetName:
        "1.2. Chú ý nghe, thích được hát theo, vỗ tay, nhún nhảy, lắc lư theo bài hát, bản nhạc; thích nghe đọc thơ, đồng dao, ca dao, tục ngữ; thích nghe kể câu chuyện.",
      educationalGoals: ""
    },
    {
      STT: 73,
      targetName: "MT73",
      detailTargetName:
        "1.3. Vui sướng, chỉ, sờ, ngắm nhìn và nói lên cảm nhận của mình trước vẻ đẹp nổi bật (về màu sắc, hình dáng...) của các tác phẩm tạo hình.",
      educationalGoals: ""
    }
  ];

  const data28 = [
    {
      STT: 74,
      targetName: "MT74",
      detailTargetName:
        "2.1. Hát tự nhiên, hát được theo giai điệu bài hát quen thuộc.",
      educationalGoals: `<div>
    <h3 class="font-bold">Hoạt động học:</h3>
    <ul>
        <li>- Dạy vận động bài: Cháu đi mẫu giáo. Nghe: Ngày đầu tiên đi học. Trò chơi: Đoán tên bạn hát.</li>
        <li>- DH bài: Rước đèn dưới ánh trăng. Nghe: Chiếc đèn ông sao. Trò chơi: Đi theo tiếng nhạc.</li>
        <li>- DH bài: Quả bóng. Nghe: Trường mẫu giáo yêu thương. Trò chơi: Ai nhanh hơn.</li>
        <li>- DH bài: Chiếc khăn tay. Nghe: Bàn tay mẹ. Trò chơi: Ai nhanh nhất.</li>
        <li>- DH bài: Múa cho mẹ xem. Nghe: Đôi và một. Trò chơi: Đi theo tiếng nhạc.</li>
        <li>- DH bài: Mời bạn ăn. Nghe: Gia đình nhỏ hạnh phúc to. Trò chơi: Đoán tên bạn hát.</li>
        <li>- Dạy hát bài: Nhà của tôi. Nghe hát: Lời ru trên nương. Trò chơi: Đoán tên bạn hát.</li>
        <li>- Nghe hát: Gia đình nhỏ hạnh phúc to. VĐTH: Cả nhà thương nhau. Trò chơi: Tai ai tinh.</li>
        <li>- DH bài: Bé quét nhà. Nghe: Cái ấm trà. Trò chơi: Ai nhanh nhất.</li>
        <li>- Dạy hát bài: Lớn lên cháu lái máy cày. Nghe: Đi cấy. TC: Ai nhanh hơn.</li>
        <li>- Dạy hát bài: Làm bác sĩ. Nghe: Ước mơ xanh. Trò chơi: Đoán tên bạn hát.</li>
        <li>- DH bài: Chú bộ đội. Nghe: Màu áo chú bộ đội. Trò chơi: Đi theo tiếng nhạc.</li>
        <li>- VĐ vỗ tay theo phách bài: Cháu yêu cô chú công nhân (MT74). Nghe: Bác đưa thư vui tính. Trò chơi: Ai nhanh nhất.</li>
        <li>- Nghe hát: Chú ếch con. VĐMH bài: Một con vịt. Trò chơi: Ai nhanh hơn.</li>
        <li>- DH bài: Đố bạn. Nghe: Chú voi con ở bản đôn. Trò chơi: Hát theo hình ảnh.</li>
        <li>- DH bài: Sắp đến tết rồi. Nghe: Ngày tết quê em. Trò chơi: Đi theo tiếng nhạc.</li>
        <li>- Dạy hát: Mùa xuân đến rồi. Nghe: Mùa xuân của bé. Trò chơi: Những chiếc bút nhảy múa.</li>
        <li>- Dạy hát: Hoa bé ngoan. Nghe: Hoa trong vườn. Trò chơi: Tai ai tinh.</li>
        <li>- Dạy hát: Em đi chơi thuyền. Nghe hát: Bạn ơi có biết không. Trò chơi: Nghe âm thanh đoán tên dụng cụ âm nhạc.</li>
        <li>- Dạy hát bài: Em đi qua ngã tư đường phố. Nghe hát: Từ một ngã tư đường phố. Trò chơi: Âm thanh từ phía nào?.</li>
        <li>- Dạy hát bài: Mùa hè đến. Nghe hát: Đếm sao. Trò chơi: Mưa to, mưa nhỏ.</li>
        <li>- Dạy hát bài: Đừng đi đằng kia có mưa. Nghe hát: Hạt mưa và em bé. Trò chơi: Hát theo hình ảnh.</li>
        <li>- Dạy hát bài: Quê hương tươi đẹp. Nghe hát: Yêu Hà Nội. Trò chơi: Hát theo hình ảnh.</li>
        <li>- VĐMH: Tay thơm tay ngoan. Nghe: Cho con. Trò chơi: Tiếng hát ở đâu.</li>
        <li>- VĐ theo phách bài: Cả nhà thương nhau. Nghe: Nhà mình rất vui. Trò chơi: Làm theo tiếng nhạc.</li>
        <li>- Dạy VĐ vỗ tay theo phách bài: Cô và mẹ. Nghe hát: Thương lắm thầy cô ơi. Trò chơi: Ai nhanh hơn.</li>
        <li>- VĐMH bài: Một con vịt. Nghe: Gà gáy lé te. Trò chơi: Những nốt nhạc vui.</li>
        <li>- VĐ theo phách bài: Ba con bướm. Nghe: Chị ong nâu và em bé. Trò chơi: Nghe tiếng kêu đoán con vật.</li>
        <li>- VĐ vỗ theo phách bài: Sắp đến tết rồi. Nghe: Ngày tết quê em. Trò chơi: Nhanh và chậm.</li>
        <li>- Dạy VĐ vỗ: Quà mùng 8-3. Nghe hát: Bông hoa mừng cô. Trò chơi: To và nhỏ.</li>
        <li>- Vận động vỗ tay theo phách: Đi xe đạp. Nghe hát: Anh phi công ơi. Trò chơi: Làm theo tiếng nhạc.</li>
        <li>- Dạy hát: Đừng đi đằng kia có mưa. Nghe hát: Mưa rơi. Trò chơi: Bạn nào hát (MT75).</li>
        <li>- Dạy VĐ theo phách bài: Mây và gió. Nghe hát: Nắng sớm. Trò chơi: Đoán tên bạn hát (MT75).</li>
        <li>- Trang trí bưu thiếp tặng chú bộ đội (Mẫu) (MT76).</li>
        <li>- Nặn một số loại thuốc (Đề tài).</li>
        <li>- Nặn dụng cụ của nghề nông (Cái cuốc) (Mẫu).</li>
        <li>- In chùm bóng (Mẫu).</li>
        <li>- Tô màu chiếc đèn ông sao (Mẫu).</li>
        <li>- Trang trí khuôn mặt (Mẫu).</li>
        <li>- Tô màu trang phục bạn trai, bạn gái (Mẫu).</li>
        <li>- Tô màu bức tranh gia đình bé (Mẫu).</li>
        <li>- Tô màu đồ dùng nhà bé có (Đề tài).</li>
        <li>- Tô nét tô màu quả táo (Mẫu).</li>
        <li>- Tô theo nét chấm mờ và tô màu nải chuối (Đề tài).</li>
        <li>- Tô màu lá cờ (Mẫu).</li>
        <li>- Tô màu tranh quê em (Đề tài).</li>
        <li>- Vẽ bông hoa chào mừng ngày 8/3 (Đề tài) (MT77).</li>
        <li>- Trang trí chiếc mũ (Mẫu) (MT78).</li>
        <li>- Trang trí đèn lồng (Giáo án E5).</li>
        <li>- Tô màu mặt nạ (Giáo án E5).</li>
        <li>- Dán hoa tặng mẹ (Giáo án E5).</li>
        <li>- Trang trí bưu thiếp (Giáo án E5).</li>
        <li>- Nặn bánh trôi sắc màu (Giáo án E5).</li>
        <li>- Tô màu chiếc xe đạp (Giáo án E5).</li>
        <li>- Trang trí lá cờ (Giáo án E5).</li>
        <li>- Dán vảy cá chép (giáo án STEAM).</li>
        <li>- Nặn quả cam (Giáo án STEAM).</li>
        <li>- Vẽ giọt nước (Giáo án STEAM).</li>
        <li>- Tô màu các loại hoa (Giáo án STEAM).</li>
        <li>- Tô màu con ong (MT79).</li>
        <li>- Nặn các loại bánh.</li>
        <li>- Tô nét và tô màu chùm nho (Mẫu).</li>
        <li>- Tô màu đèn giao thông (Mẫu) (MT81).</li>
    </ul>
    <h3 class="font-bold">Hoạt động khác:</h3>
    <ul>
        <li>- Tô theo nét chấm mờ tạo thành con đường (Mẫu).</li>
        <li>- Cho trẻ vẽ các nét thẳng, xiên, ngang.</li>
        <li>- Cho trẻ chơi với giấy màu, xé theo dải, xé vụn tạo thành bông hoa.</li>
        <li>- Xé dán mắt quả dứa.</li>
        <li>- Vẽ theo nét chấm mờ và tô màu tranh xe đạp.</li>
        <li>- Chơi với đất nặn.</li>
        <li>- Cho trẻ chơi với các khối gỗ xếp tạo thành ngôi nhà, ô tô, tàu… (MT80).</li>
        <li>- Làm quen với bút sáp và giấy màu, làm quen với cách cầm bút, vẽ trên không, vẽ trên sân trường, xem tranh ảnh,...Triển lãm các sản phẩm tạo hình. Trưng bày sản phẩm tạo hình.</li>
    </ul>
</div>
`
    },
    {
      STT: 75,
      targetName: "MT75",
      detailTargetName:
        "2.2. Vận động theo nhịp điệu bài hát, bản nhạc (vỗ tay theo phách, nhịp, vận động minh hoạ).",
      educationalGoals: ""
    },
    {
      STT: 76,
      targetName: "MT76",
      detailTargetName:
        "2.3. Sử dụng các nguyên vật liệu tạo hình để tạo ra sản phẩm theo sự gợi ý.",
      educationalGoals: ""
    },
    {
      STT: 77,
      targetName: "MT77",
      detailTargetName:
        "2.4. Vẽ các nét thẳng, xiên, ngang, tạo thành bức tranh đơn giản.",
      educationalGoals: ""
    },
    {
      STT: 78,
      targetName: "MT78",
      detailTargetName:
        "2.5. Xé theo dải, xé vụn và dán thành sản phẩm đơn giản.",
      educationalGoals: ""
    },
    {
      STT: 79,
      targetName: "MT79",
      detailTargetName:
        "2.6. Lăn dọc, xoay tròn, ấn dẹt đất nặn để tạo thành các sản phẩm có 1 khối hoặc 2 khối.",
      educationalGoals: ""
    },
    {
      STT: 80,
      targetName: "MT80",
      detailTargetName:
        "2.7. Xếp chồng, xếp cạnh, xếp cách tạo thành các sản phẩm có cấu trúc đơn giản.",
      educationalGoals: ""
    },
    {
      STT: 81,
      targetName: "MT81",
      detailTargetName: "2.8. Nhận xét các sản phẩm tạo hình.",
      educationalGoals: ""
    }
  ];

  const data29 = [
    {
      STT: 82,
      targetName: "MT82",
      detailTargetName:
        "3.1. Vận động theo ý thích các bài hát, bản nhạc quen thuộc.",
      educationalGoals: `
            <ul>
                <li><strong>Hoạt động học:</strong></li>
                <ul>
                    <li>- Biểu diễn văn nghệ. (MT82)</li>
                    <li>- Biểu diễn tổng hợp:</li>
                    <ul>
                        <li>- VĐ theo nhạc: Em mơ gặp Bác Hồ.</li>
                        <li>- VĐ vỗ: Quê hương tươi đẹp.</li>
                        <li>- Nghe hát: Ai yêu Bác Hồ Chí Minh hơn thiếu niên nhi đồng.</li>
                        <li>- Trò chơi: Đoán tên bạn hát.</li>
                        <li>- In bông hoa tặng cô nhân ngày 20/11 (Mẫu) (MT84).</li>
                        <li>- Tô màu gấu bông (Mẫu).</li>
                        <li>- Tô theo nét chấm mờ và tô màu bạn gái (Đề tài).</li>
                    </ul>
                </ul>
                <li><strong>Hoạt động khác:</strong></li>
                <ul>
                    <li>- Cho trẻ làm đồ dùng, đồ chơi từ các nguyên vật liệu phế thải (MT83).</li>
                </ul>
            </ul>
        `
    },
    {
      STT: 83,
      targetName: "MT83",
      detailTargetName: "3.2. Tạo ra các sản phẩm tạo hình theo ý thích."
    },
    {
      STT: 84,
      targetName: "MT84",
      detailTargetName: "3.3. Đặt tên cho sản phẩm tạo hình."
    }
  ];

  return (
    <section className="container mx-auto mt-[28px] space-y-10 px-[1rem] pb-[3rem] lg:space-y-20">
      <div className="space-y-[36px] ">
        <div className="">
          <div className="space-y-[10px]">
            <p className="text-center text-[20px] font-bold leading-6 text-[#414B5B]">
              KẾ HOẠCH GIÁO DỤC NĂM HỌC 2024 - 2025
              <br /> MẪU GIÁO BÉ 3-4 TUỔI
            </p>
            <div className="space-y-[10px]">
              <div className="relative space-y-[38px] text-[#414B5B]">
                {/* <OverlayText scale='scale-1'/> */}
                <div className="space-y-[10px] text-[20px]">
                  <div className="space-y-[20px]">
                    <div className="overflow-x-auto text-[16px] font-semibold">
                      <table className="min-w-full table-auto border-collapse border border-black">
                        <thead className="h-[87px]">
                          <tr>
                            <th className="w-[10%] border border-black bg-[#FFE498] px-4 py-2 font-[400]">
                              STT
                            </th>
                            <th className="w-[10%] border border-black bg-[#FFE498] px-4 py-2 font-[400]">
                              Tên mục tiêu
                            </th>
                            <th className="border border-black bg-[#FFE498] px-4 py-2 font-[400]">
                              Tên mục tiêu
                            </th>
                            <th className="border border-black bg-[#FFE498] px-4 py-2 font-[400]">
                              Mục tiêu giáo dục
                            </th>
                          </tr>
                        </thead>
                        <tbody className="text-[12px]">
                          <tr className="px-[20px] py-[10px]">
                            <td
                              className="px-4 py-2 text-start font-bold text-[#414B5B]"
                              colSpan={4}
                            >
                              I. Giáo dục phát triển thể chất
                              <br /> a) Phát triển vận động
                              <br /> 1. Thực hiện được các động tác phát triển
                              các nhóm cơ và hô hấp
                            </td>
                          </tr>
                          {data.map((row, index) => (
                            <tr key={index} className="h-[100px] text-[12px]">
                              <td className="border border-black px-4 py-2 text-center font-bold">
                                {row.STT}
                              </td>
                              <td className="border border-black px-4 py-2 font-bold">
                                {row.targetName}
                              </td>
                              <td
                                dangerouslySetInnerHTML={{
                                  __html: row?.detailTargetName
                                }}
                                className="border  border-black px-4 py-2"
                              ></td>
                              <td
                                dangerouslySetInnerHTML={{
                                  __html: row?.educationalGoals
                                }}
                                className="border  border-black px-4 py-2"
                              ></td>
                            </tr>
                          ))}
                          <tr className="px-[20px] py-[10px]">
                            <td
                              className="px-4 py-2 text-start font-bold text-[#414B5B]"
                              colSpan={4}
                            >
                              2. Thể hiện kỹ năng vận động cơ bản và các tố chất
                              trong vận động
                            </td>
                          </tr>
                          {data2.map((row, index) => (
                            <tr key={index} className="h-[100px] text-[12px]">
                              <td className="border border-black px-4 py-2 text-center font-bold">
                                {row.STT}
                              </td>
                              <td className="border border-black px-4 py-2 font-bold">
                                {row.targetName}
                              </td>
                              <td
                                dangerouslySetInnerHTML={{
                                  __html: row?.detailTargetName
                                }}
                                className="border  border-black px-4 py-2"
                              ></td>
                              {row?.educationalGoals && (
                                <td
                                  rowSpan={4}
                                  dangerouslySetInnerHTML={{
                                    __html: row?.educationalGoals
                                  }}
                                  className="border  border-black px-4 py-2"
                                ></td>
                              )}
                            </tr>
                          ))}
                          <tr className="px-[20px] py-[10px]">
                            <td
                              className="px-4 py-2 text-start font-bold text-[#414B5B]"
                              colSpan={4}
                            >
                              3. Thực hiện và phối hợp được các cử động của bàn
                              tay ngón tay, phối hợp tay - mắt
                            </td>
                          </tr>
                          {data3.map((row, index) => (
                            <tr key={index} className="h-[100px] text-[12px]">
                              <td className="border border-black px-4 py-2 text-center font-bold">
                                {row.STT}
                              </td>
                              <td className="border border-black px-4 py-2 font-bold">
                                {row.targetName}
                              </td>
                              <td
                                dangerouslySetInnerHTML={{
                                  __html: row?.detailTargetName
                                }}
                                className="border  border-black px-4 py-2"
                              ></td>
                              {row?.educationalGoals && (
                                <td
                                  rowSpan={2}
                                  dangerouslySetInnerHTML={{
                                    __html: row?.educationalGoals
                                  }}
                                  className="border  border-black px-4 py-2"
                                ></td>
                              )}
                            </tr>
                          ))}
                          <tr className="border-b border-black px-[20px] py-[10px]">
                            <td
                              className="px-4 py-2 text-start font-bold text-[#414B5B]"
                              colSpan={4}
                            >
                              b) Giáo dục dinh dưỡng và sức khỏe
                            </td>
                          </tr>
                          <tr className="px-[20px] py-[10px]">
                            <td
                              className="px-4 py-2 text-start font-bold text-[#414B5B]"
                              colSpan={4}
                            >
                              1. Biết một số món ăn, thực phẩm thông thường và
                              ích lợi của chúng đối với sức khỏe
                            </td>
                          </tr>
                          {data4.map((row, index) => (
                            <tr key={index} className="h-[100px] text-[12px]">
                              <td className="border border-black px-4 py-2 text-center font-bold">
                                {row.STT}
                              </td>
                              <td className="border border-black px-4 py-2 font-bold">
                                {row.targetName}
                              </td>
                              <td
                                dangerouslySetInnerHTML={{
                                  __html: row?.detailTargetName
                                }}
                                className="border  border-black px-4 py-2"
                              ></td>
                              {row?.educationalGoals && (
                                <td
                                  rowSpan={3}
                                  dangerouslySetInnerHTML={{
                                    __html: row?.educationalGoals
                                  }}
                                  className="border  border-black px-4 py-2"
                                ></td>
                              )}
                            </tr>
                          ))}
                          <tr className="px-[20px] py-[10px]">
                            <td
                              className="px-4 py-2 text-start font-bold text-[#414B5B]"
                              colSpan={4}
                            >
                              2. Thực hiện được một số việc tự phục vụ trong
                              sinh hoạt
                            </td>
                          </tr>
                          {data5.map((row, index) => (
                            <tr key={index} className="h-[100px] text-[12px]">
                              <td className="border border-black px-4 py-2 text-center font-bold">
                                {row.STT}
                              </td>
                              <td className="border border-black px-4 py-2 font-bold">
                                {row.targetName}
                              </td>
                              <td
                                dangerouslySetInnerHTML={{
                                  __html: row?.detailTargetName
                                }}
                                className="border  border-black px-4 py-2"
                              ></td>
                              {row?.educationalGoals && (
                                <td
                                  rowSpan={3}
                                  dangerouslySetInnerHTML={{
                                    __html: row?.educationalGoals
                                  }}
                                  className="border  border-black px-4 py-2"
                                ></td>
                              )}
                            </tr>
                          ))}
                          <tr className="px-[20px] py-[10px]">
                            <td
                              className="px-4 py-2 text-start font-bold text-[#414B5B]"
                              colSpan={4}
                            >
                              3. Có một số hành vi và thói quen tốt trong sinh
                              hoạt và giữ gìn sức khoẻ
                            </td>
                          </tr>
                          {data6.map((row, index) => (
                            <tr key={index} className="h-[100px] text-[12px]">
                              <td className="border border-black px-4 py-2 text-center font-bold">
                                {row.STT}
                              </td>
                              <td className="border border-black px-4 py-2 font-bold">
                                {row.targetName}
                              </td>
                              <td
                                dangerouslySetInnerHTML={{
                                  __html: row?.detailTargetName
                                }}
                                className="border  border-black px-4 py-2"
                              ></td>
                              {row?.educationalGoals && (
                                <td
                                  rowSpan={3}
                                  dangerouslySetInnerHTML={{
                                    __html: row?.educationalGoals
                                  }}
                                  className="border  border-black px-4 py-2"
                                ></td>
                              )}
                            </tr>
                          ))}
                          <tr className="px-[20px] py-[10px]">
                            <td
                              className="px-4 py-2 text-start font-bold text-[#414B5B]"
                              colSpan={4}
                            >
                              4. Biết một số nguy cơ không an toàn và phòng
                              tránh
                            </td>
                          </tr>
                          {data7.map((row, index) => (
                            <tr key={index} className="h-[100px] text-[12px]">
                              <td className="border border-black px-4 py-2 text-center font-bold">
                                {row.STT}
                              </td>
                              <td className="border border-black px-4 py-2 font-bold">
                                {row.targetName}
                              </td>
                              <td
                                dangerouslySetInnerHTML={{
                                  __html: row?.detailTargetName
                                }}
                                className="border  border-black px-4 py-2"
                              ></td>
                              {row?.educationalGoals && (
                                <td
                                  rowSpan={3}
                                  dangerouslySetInnerHTML={{
                                    __html: row?.educationalGoals
                                  }}
                                  className="border  border-black px-4 py-2"
                                ></td>
                              )}
                            </tr>
                          ))}
                          <tr className="px-[20px] py-[10px]">
                            <td
                              className="border-b border-black px-4 py-2 text-start font-bold text-[#414B5B]"
                              colSpan={4}
                            >
                              II. Giáo dục phát triển nhận thức
                            </td>
                          </tr>
                          <tr className="px-[20px] py-[10px]">
                            <td
                              className="border-b border-black px-4 py-2 text-start font-bold text-[#414B5B]"
                              colSpan={4}
                            >
                              a) Khám phá khoa học
                            </td>
                          </tr>
                          <tr className="px-[20px] py-[10px]">
                            <td
                              className="px-4 py-2 text-start font-bold text-[#414B5B]"
                              colSpan={4}
                            >
                              1. Xem xét và tìm hiểu đặc điểm của các sự vật,
                              hiện tượng
                            </td>
                          </tr>
                          {data8.map((row, index) => (
                            <tr key={index} className="h-[100px] text-[12px]">
                              <td className="border border-black px-4 py-2 text-center font-bold">
                                {row.STT}
                              </td>
                              <td className="border border-black px-4 py-2 font-bold">
                                {row.targetName}
                              </td>
                              <td
                                dangerouslySetInnerHTML={{
                                  __html: row?.detailTargetName
                                }}
                                className="border  border-black px-4 py-2"
                              ></td>
                              {row?.educationalGoals && (
                                <td
                                  rowSpan={5}
                                  dangerouslySetInnerHTML={{
                                    __html: row?.educationalGoals
                                  }}
                                  className="border  border-black px-4 py-2"
                                ></td>
                              )}
                            </tr>
                          ))}
                          <tr className="px-[20px] py-[10px]">
                            <td
                              className="px-4 py-2 text-start font-bold text-[#414B5B]"
                              colSpan={4}
                            >
                              2. Nhận biết mối quan hệ đơn giản của sự vật, hiện
                              tượng và giải quyết vấn đề đơn giản
                            </td>
                          </tr>
                          {data9.map((row, index) => (
                            <tr key={index} className="h-[100px] text-[12px]">
                              <td className="border border-black px-4 py-2 text-center font-bold">
                                {row.STT}
                              </td>
                              <td className="border border-black px-4 py-2 font-bold">
                                {row.targetName}
                              </td>
                              <td
                                dangerouslySetInnerHTML={{
                                  __html: row?.detailTargetName
                                }}
                                className="border  border-black px-4 py-2"
                              ></td>
                              {row?.educationalGoals && (
                                <td
                                  dangerouslySetInnerHTML={{
                                    __html: row?.educationalGoals
                                  }}
                                  className="border  border-black px-4 py-2"
                                ></td>
                              )}
                            </tr>
                          ))}
                          <tr className="px-[20px] py-[10px]">
                            <td
                              className="px-4 py-2 text-start font-bold text-[#414B5B]"
                              colSpan={4}
                            >
                              3. Thể hiện hiểu biết về đối tượng bằng các cách
                              khác nhau
                            </td>
                          </tr>
                          {data10.map((row, index) => (
                            <tr key={index} className="h-[100px] text-[12px]">
                              <td className="border border-black px-4 py-2 text-center font-bold">
                                {row.STT}
                              </td>
                              <td className="border border-black px-4 py-2 font-bold">
                                {row.targetName}
                              </td>
                              <td
                                dangerouslySetInnerHTML={{
                                  __html: row?.detailTargetName
                                }}
                                className="border  border-black px-4 py-2"
                              ></td>
                              {row?.educationalGoals && (
                                <td
                                  rowSpan={2}
                                  dangerouslySetInnerHTML={{
                                    __html: row?.educationalGoals
                                  }}
                                  className="border  border-black px-4 py-2"
                                ></td>
                              )}
                            </tr>
                          ))}
                          <tr className="px-[20px] py-[10px]">
                            <td
                              className="border-b border-black px-4 py-2 text-start font-bold text-[#414B5B]"
                              colSpan={4}
                            >
                              b) Làm quen với một số khái niệm sơ đẳng về toán
                            </td>
                          </tr>
                          <tr className="px-[20px] py-[10px]">
                            <td
                              className="px-4 py-2 text-start font-bold text-[#414B5B]"
                              colSpan={4}
                            >
                              1. Nhận biết số đếm, số lượng
                            </td>
                          </tr>
                          {data11.map((row, index) => (
                            <tr key={index} className="h-[100px] text-[12px]">
                              <td className="border border-black px-4 py-2 text-center font-bold">
                                {row.STT}
                              </td>
                              <td className="border border-black px-4 py-2 font-bold">
                                {row.targetName}
                              </td>
                              <td
                                dangerouslySetInnerHTML={{
                                  __html: row?.detailTargetName
                                }}
                                className="border  border-black px-4 py-2"
                              ></td>
                              {row?.educationalGoals && (
                                <td
                                  rowSpan={5}
                                  dangerouslySetInnerHTML={{
                                    __html: row?.educationalGoals
                                  }}
                                  className="border  border-black px-4 py-2"
                                ></td>
                              )}
                            </tr>
                          ))}
                          <tr className="px-[20px] py-[10px]">
                            <td
                              className="px-4 py-2 text-start font-bold text-[#414B5B]"
                              colSpan={4}
                            >
                              2. Sắp xếp theo qui tắc
                            </td>
                          </tr>
                          {data12.map((row, index) => (
                            <tr key={index} className="h-[100px] text-[12px]">
                              <td className="border border-black px-4 py-2 text-center font-bold">
                                {row.STT}
                              </td>
                              <td className="border border-black px-4 py-2 font-bold">
                                {row.targetName}
                              </td>
                              <td
                                dangerouslySetInnerHTML={{
                                  __html: row?.detailTargetName
                                }}
                                className="border  border-black px-4 py-2"
                              ></td>
                              {row?.educationalGoals && (
                                <td
                                  dangerouslySetInnerHTML={{
                                    __html: row?.educationalGoals
                                  }}
                                  className="border  border-black px-4 py-2"
                                ></td>
                              )}
                            </tr>
                          ))}
                          <tr className="px-[20px] py-[10px]">
                            <td
                              className="px-4 py-2 text-start font-bold text-[#414B5B]"
                              colSpan={4}
                            >
                              3. So sánh hai đối tượng
                            </td>
                          </tr>
                          {data13.map((row, index) => (
                            <tr key={index} className="h-[100px] text-[12px]">
                              <td className="border border-black px-4 py-2 text-center font-bold">
                                {row.STT}
                              </td>
                              <td className="border border-black px-4 py-2 font-bold">
                                {row.targetName}
                              </td>
                              <td
                                dangerouslySetInnerHTML={{
                                  __html: row?.detailTargetName
                                }}
                                className="border  border-black px-4 py-2"
                              ></td>
                              {row?.educationalGoals && (
                                <td
                                  dangerouslySetInnerHTML={{
                                    __html: row?.educationalGoals
                                  }}
                                  className="border  border-black px-4 py-2"
                                ></td>
                              )}
                            </tr>
                          ))}
                          <tr className="px-[20px] py-[10px]">
                            <td
                              className="px-4 py-2 text-start font-bold text-[#414B5B]"
                              colSpan={4}
                            >
                              4. Nhận biết hình dạng
                            </td>
                          </tr>
                          {data14.map((row, index) => (
                            <tr key={index} className="h-[100px] text-[12px]">
                              <td className="border border-black px-4 py-2 text-center font-bold">
                                {row.STT}
                              </td>
                              <td className="border border-black px-4 py-2 font-bold">
                                {row.targetName}
                              </td>
                              <td
                                dangerouslySetInnerHTML={{
                                  __html: row?.detailTargetName
                                }}
                                className="border  border-black px-4 py-2"
                              ></td>
                              {row?.educationalGoals && (
                                <td
                                  dangerouslySetInnerHTML={{
                                    __html: row?.educationalGoals
                                  }}
                                  className="border  border-black px-4 py-2"
                                ></td>
                              )}
                            </tr>
                          ))}
                          <tr className="px-[20px] py-[10px]">
                            <td
                              className="px-4 py-2 text-start font-bold text-[#414B5B]"
                              colSpan={4}
                            >
                              5. Nhận biết vị trí trong không gian và định hướng
                              thời gian
                            </td>
                          </tr>
                          {data15.map((row, index) => (
                            <tr key={index} className="h-[100px] text-[12px]">
                              <td className="border border-black px-4 py-2 text-center font-bold">
                                {row.STT}
                              </td>
                              <td className="border border-black px-4 py-2 font-bold">
                                {row.targetName}
                              </td>
                              <td
                                dangerouslySetInnerHTML={{
                                  __html: row?.detailTargetName
                                }}
                                className="border  border-black px-4 py-2"
                              ></td>
                              {row?.educationalGoals && (
                                <td
                                  dangerouslySetInnerHTML={{
                                    __html: row?.educationalGoals
                                  }}
                                  className="border  border-black px-4 py-2"
                                ></td>
                              )}
                            </tr>
                          ))}
                          <tr className="px-[20px] py-[10px]">
                            <td
                              className="border-b border-black px-4 py-2 text-start font-bold text-[#414B5B]"
                              colSpan={4}
                            >
                              c) Khám phá xã hội
                            </td>
                          </tr>
                          <tr className="px-[20px] py-[10px]">
                            <td
                              className="px-4 py-2 text-start font-bold text-[#414B5B]"
                              colSpan={4}
                            >
                              1. Nhận biết bản thân, gia đình, trường lớp mầm
                              non và cộng đồng
                            </td>
                          </tr>
                          {data16.map((row, index) => (
                            <tr key={index} className="h-[100px] text-[12px]">
                              <td className="border border-black px-4 py-2 text-center font-bold">
                                {row.STT}
                              </td>
                              <td className="border border-black px-4 py-2 font-bold">
                                {row.targetName}
                              </td>
                              <td
                                dangerouslySetInnerHTML={{
                                  __html: row?.detailTargetName
                                }}
                                className="border  border-black px-4 py-2"
                              ></td>
                              {row?.educationalGoals && (
                                <td
                                  rowSpan={4}
                                  dangerouslySetInnerHTML={{
                                    __html: row?.educationalGoals
                                  }}
                                  className="border  border-black px-4 py-2"
                                ></td>
                              )}
                            </tr>
                          ))}
                          <tr className="px-[20px] py-[10px]">
                            <td
                              className="px-4 py-2 text-start font-bold text-[#414B5B]"
                              colSpan={4}
                            >
                              2. Nhận biết một số nghề phổ biến và nghề truyền
                              thống ở địa phương
                            </td>
                          </tr>
                          {data17.map((row, index) => (
                            <tr key={index} className="h-[100px] text-[12px]">
                              <td className="border border-black px-4 py-2 text-center font-bold">
                                {row.STT}
                              </td>
                              <td className="border border-black px-4 py-2 font-bold">
                                {row.targetName}
                              </td>
                              <td
                                dangerouslySetInnerHTML={{
                                  __html: row?.detailTargetName
                                }}
                                className="border  border-black px-4 py-2"
                              ></td>
                              {row?.educationalGoals && (
                                <td
                                  dangerouslySetInnerHTML={{
                                    __html: row?.educationalGoals
                                  }}
                                  className="border  border-black px-4 py-2"
                                ></td>
                              )}
                            </tr>
                          ))}
                          <tr className="px-[20px] py-[10px]">
                            <td
                              className="px-4 py-2 text-start font-bold text-[#414B5B]"
                              colSpan={4}
                            >
                              3. Nhận biết một số lễ hội và danh lam, thắng cảnh
                            </td>
                          </tr>
                          {data18.map((row, index) => (
                            <tr key={index} className="h-[100px] text-[12px]">
                              <td className="border border-black px-4 py-2 text-center font-bold">
                                {row.STT}
                              </td>
                              <td className="border border-black px-4 py-2 font-bold">
                                {row.targetName}
                              </td>
                              <td
                                dangerouslySetInnerHTML={{
                                  __html: row?.detailTargetName
                                }}
                                className="border  border-black px-4 py-2"
                              ></td>
                              {row?.educationalGoals && (
                                <td
                                  rowSpan={2}
                                  dangerouslySetInnerHTML={{
                                    __html: row?.educationalGoals
                                  }}
                                  className="border  border-black px-4 py-2"
                                ></td>
                              )}
                            </tr>
                          ))}
                          <tr className="px-[20px] py-[10px]">
                            <td
                              className="border-b border-black px-4 py-2 text-start font-bold text-[#414B5B]"
                              colSpan={4}
                            >
                              III. Giáo dục phát triển ngôn ngữ
                            </td>
                          </tr>
                          <tr className="px-[20px] py-[10px]">
                            <td
                              className="px-4 py-2 text-start font-bold text-[#414B5B]"
                              colSpan={4}
                            >
                              1. Nghe hiểu lời nói
                            </td>
                          </tr>
                          {data19.map((row, index) => (
                            <tr key={index} className="h-[100px] text-[12px]">
                              <td className="border border-black px-4 py-2 text-center font-bold">
                                {row.STT}
                              </td>
                              <td className="border border-black px-4 py-2 font-bold">
                                {row.targetName}
                              </td>
                              <td
                                dangerouslySetInnerHTML={{
                                  __html: row?.detailTargetName
                                }}
                                className="border  border-black px-4 py-2"
                              ></td>
                              {row?.educationalGoals && (
                                <td
                                  rowSpan={3}
                                  dangerouslySetInnerHTML={{
                                    __html: row?.educationalGoals
                                  }}
                                  className="border  border-black px-4 py-2"
                                ></td>
                              )}
                            </tr>
                          ))}
                          <tr className="px-[20px] py-[10px]">
                            <td
                              className="px-4 py-2 text-start font-bold text-[#414B5B]"
                              colSpan={4}
                            >
                              2. Sử dụng lời nói trong cuộc sống hàng ngày
                            </td>
                          </tr>
                          {data20.map((row, index) => (
                            <tr key={index} className="h-[100px] text-[12px]">
                              <td className="border border-black px-4 py-2 text-center font-bold">
                                {row.STT}
                              </td>
                              <td className="border border-black px-4 py-2 font-bold">
                                {row.targetName}
                              </td>
                              <td
                                dangerouslySetInnerHTML={{
                                  __html: row?.detailTargetName
                                }}
                                className="border  border-black px-4 py-2"
                              ></td>
                              {row?.educationalGoals && (
                                <td
                                  rowSpan={9}
                                  dangerouslySetInnerHTML={{
                                    __html: row?.educationalGoals
                                  }}
                                  className="border  border-black px-4 py-2"
                                ></td>
                              )}
                            </tr>
                          ))}
                          <tr className="px-[20px] py-[10px]">
                            <td
                              className="px-4 py-2 text-start font-bold text-[#414B5B]"
                              colSpan={4}
                            >
                              3. Làm quen với đọc, viết
                            </td>
                          </tr>
                          {data21.map((row, index) => (
                            <tr key={index} className="h-[100px] text-[12px]">
                              <td className="border border-black px-4 py-2 text-center font-bold">
                                {row.STT}
                              </td>
                              <td className="border border-black px-4 py-2 font-bold">
                                {row.targetName}
                              </td>
                              <td
                                dangerouslySetInnerHTML={{
                                  __html: row?.detailTargetName
                                }}
                                className="border  border-black px-4 py-2"
                              ></td>
                              {row?.educationalGoals && (
                                <td
                                  rowSpan={3}
                                  dangerouslySetInnerHTML={{
                                    __html: row?.educationalGoals
                                  }}
                                  className="border  border-black px-4 py-2"
                                ></td>
                              )}
                            </tr>
                          ))}
                          <tr className="px-[20px] py-[10px]">
                            <td
                              className="border-b border-black px-4 py-2 text-start font-bold text-[#414B5B]"
                              colSpan={4}
                            >
                              IV. Giáo dục phát triển tình cảm, kỹ năng xã hội
                            </td>
                          </tr>
                          <tr className="px-[20px] py-[10px]">
                            <td
                              className="px-4 py-2 text-start font-bold text-[#414B5B]"
                              colSpan={4}
                            >
                              1. Thể hiện ý thức về bản thân
                            </td>
                          </tr>
                          {data22.map((row, index) => (
                            <tr key={index} className="h-[100px] text-[12px]">
                              <td className="border border-black px-4 py-2 text-center font-bold">
                                {row.STT}
                              </td>
                              <td className="border border-black px-4 py-2 font-bold">
                                {row.targetName}
                              </td>
                              <td
                                dangerouslySetInnerHTML={{
                                  __html: row?.detailTargetName
                                }}
                                className="border  border-black px-4 py-2"
                              ></td>
                              {row?.educationalGoals && (
                                <td
                                  rowSpan={2}
                                  dangerouslySetInnerHTML={{
                                    __html: row?.educationalGoals
                                  }}
                                  className="border  border-black px-4 py-2"
                                ></td>
                              )}
                            </tr>
                          ))}
                          <tr className="px-[20px] py-[10px]">
                            <td
                              className="px-4 py-2 text-start font-bold text-[#414B5B]"
                              colSpan={4}
                            >
                              2. Thể hiện sự tự tin, tự lực
                            </td>
                          </tr>
                          {data23.map((row, index) => (
                            <tr key={index} className="h-[100px] text-[12px]">
                              <td className="border border-black px-4 py-2 text-center font-bold">
                                {row.STT}
                              </td>
                              <td className="border border-black px-4 py-2 font-bold">
                                {row.targetName}
                              </td>
                              <td
                                dangerouslySetInnerHTML={{
                                  __html: row?.detailTargetName
                                }}
                                className="border  border-black px-4 py-2"
                              ></td>
                              {row?.educationalGoals && (
                                <td
                                  rowSpan={2}
                                  dangerouslySetInnerHTML={{
                                    __html: row?.educationalGoals
                                  }}
                                  className="border  border-black px-4 py-2"
                                ></td>
                              )}
                            </tr>
                          ))}
                          <tr className="px-[20px] py-[10px]">
                            <td
                              className="px-4 py-2 text-start font-bold text-[#414B5B]"
                              colSpan={4}
                            >
                              3. Nhận biết và thể hiện cảm xúc, tình cảm với con
                              người, sự vật, hiện tượng xung quanh
                            </td>
                          </tr>
                          {data24.map((row, index) => (
                            <tr key={index} className="h-[100px] text-[12px]">
                              <td className="border border-black px-4 py-2 text-center font-bold">
                                {row.STT}
                              </td>
                              <td className="border border-black px-4 py-2 font-bold">
                                {row.targetName}
                              </td>
                              <td
                                dangerouslySetInnerHTML={{
                                  __html: row?.detailTargetName
                                }}
                                className="border  border-black px-4 py-2"
                              ></td>
                              {row?.educationalGoals && (
                                <td
                                  rowSpan={4}
                                  dangerouslySetInnerHTML={{
                                    __html: row?.educationalGoals
                                  }}
                                  className="border  border-black px-4 py-2"
                                ></td>
                              )}
                            </tr>
                          ))}
                          <tr className="px-[20px] py-[10px]">
                            <td
                              className="px-4 py-2 text-start font-bold text-[#414B5B]"
                              colSpan={4}
                            >
                              4. Hành vi và quy tắc ứng xử xã hội
                            </td>
                          </tr>
                          {data25.map((row, index) => (
                            <tr key={index} className="text-[12px]">
                              <td className="border border-black px-4 py-2 text-center font-bold">
                                {row.STT}
                              </td>
                              <td className="border border-black px-4 py-2 font-bold">
                                {row.targetName}
                              </td>
                              <td
                                dangerouslySetInnerHTML={{
                                  __html: row?.detailTargetName
                                }}
                                className="border  border-black px-4 py-2"
                              ></td>
                              {row?.educationalGoals && (
                                <td
                                  rowSpan={4}
                                  dangerouslySetInnerHTML={{
                                    __html: row?.educationalGoals
                                  }}
                                  className="border  border-black px-4 py-2"
                                ></td>
                              )}
                            </tr>
                          ))}
                          <tr className="px-[20px] py-[10px]">
                            <td
                              className="px-4 py-2 text-start font-bold text-[#414B5B]"
                              colSpan={4}
                            >
                              5. Quan tâm đến môi trường
                            </td>
                          </tr>
                          {data26.map((row, index) => (
                            <tr key={index} className="text-[12px]">
                              <td className="border border-black px-4 py-2 text-center font-bold">
                                {row.STT}
                              </td>
                              <td className="border border-black px-4 py-2 font-bold">
                                {row.targetName}
                              </td>
                              <td
                                dangerouslySetInnerHTML={{
                                  __html: row?.detailTargetName
                                }}
                                className="border  border-black px-4 py-2"
                              ></td>
                              {row?.educationalGoals && (
                                <td
                                  rowSpan={6}
                                  dangerouslySetInnerHTML={{
                                    __html: row?.educationalGoals
                                  }}
                                  className="border  border-black px-4 py-2"
                                ></td>
                              )}
                            </tr>
                          ))}
                          <tr className="px-[20px] py-[10px]">
                            <td
                              className="border-b border-black px-4 py-2 text-start font-bold text-[#414B5B]"
                              colSpan={4}
                            >
                              V. Giáo dục phát triển thẩm mỹ
                            </td>
                          </tr>
                          <tr className="px-[20px] py-[10px]">
                            <td
                              className="px-4 py-2 text-start font-bold text-[#414B5B]"
                              colSpan={4}
                            >
                              1. Cảm nhận và thể hiện cảm xúc trước vẻ đẹp của
                              thiên nhiên, cuộc sống và các tác phẩm nghệ thuật
                            </td>
                          </tr>
                          {data27.map((row, index) => (
                            <tr key={index} className="text-[12px]">
                              <td className="border border-black px-4 py-2 text-center font-bold">
                                {row.STT}
                              </td>
                              <td className="border border-black px-4 py-2 font-bold">
                                {row.targetName}
                              </td>
                              <td
                                dangerouslySetInnerHTML={{
                                  __html: row?.detailTargetName
                                }}
                                className="border  border-black px-4 py-2"
                              ></td>
                              {row?.educationalGoals && (
                                <td
                                  rowSpan={3}
                                  dangerouslySetInnerHTML={{
                                    __html: row?.educationalGoals
                                  }}
                                  className="border  border-black px-4 py-2"
                                ></td>
                              )}
                            </tr>
                          ))}
                          <tr className="px-[20px] py-[10px]">
                            <td
                              className="px-4 py-2 text-start font-bold text-[#414B5B]"
                              colSpan={4}
                            >
                              2. Một số kĩ năng trong hoạt động âm nhạc và hoạt
                              động tạo hình
                            </td>
                          </tr>
                          {data28.map((row, index) => (
                            <tr key={index} className="text-[12px]">
                              <td className="border border-black px-4 py-2 text-center font-bold">
                                {row.STT}
                              </td>
                              <td className="border border-black px-4 py-2 font-bold">
                                {row.targetName}
                              </td>
                              <td
                                dangerouslySetInnerHTML={{
                                  __html: row?.detailTargetName
                                }}
                                className="border  border-black px-4 py-2"
                              ></td>
                              {row?.educationalGoals && (
                                <td
                                  rowSpan={8}
                                  dangerouslySetInnerHTML={{
                                    __html: row?.educationalGoals
                                  }}
                                  className="border  border-black px-4 py-2"
                                ></td>
                              )}
                            </tr>
                          ))}
                          <tr className="px-[20px] py-[10px]">
                            <td
                              className="px-4 py-2 text-start font-bold text-[#414B5B]"
                              colSpan={4}
                            >
                              3. Thể hiện sự sáng tạo khi tham gia các hoạt động
                              nghệ thuật (âm nhạc, tạo hình)
                            </td>
                          </tr>
                          {data29.map((row, index) => (
                            <tr key={index} className="text-[12px]">
                              <td className="border border-black px-4 py-2 text-center font-bold">
                                {row.STT}
                              </td>
                              <td className="border border-black px-4 py-2 font-bold">
                                {row.targetName}
                              </td>
                              <td
                                dangerouslySetInnerHTML={{
                                  __html: row?.detailTargetName
                                }}
                                className="border  border-black px-4 py-2"
                              ></td>
                              {row?.educationalGoals && (
                                <td
                                  rowSpan={13}
                                  dangerouslySetInnerHTML={{
                                    __html: row?.educationalGoals
                                  }}
                                  className="border  border-black px-4 py-2"
                                ></td>
                              )}
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
