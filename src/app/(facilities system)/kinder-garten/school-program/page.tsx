"use client";

import Image from "next/image";

import IconButton from "@/components/button/IconButton";

export default function SchoolProgramPage() {
  const data = [
    {
      time: "Tháng 8",
      topic: "Mùa thu và thiên nhiên",
      content:
        "- <PERSON><PERSON><PERSON> thu\n<br/>- <PERSON><PERSON><PERSON> nhẹ đầu thu, gi<PERSON> rét cuối mùa\n<br/>- <PERSON><PERSON><PERSON> rừng\n<br/>- <PERSON>hi mùa hè mờ nhạt từ từ biến mất\n<br/>- <PERSON><PERSON> tích ánh sáng, trạm cần tích luỹ năng lượng, cá<PERSON> c<PERSON> quan trọng cơ thể, phản lực lực...",
      key: '- Truyện: "Chú bé mùa thu đợi mẹ đi đâu?", "Ai là mẹ phật?"\n<br/>- Tô tượng\n<br/>- <PERSON><PERSON> tạo gió\n<br/>- <PERSON><PERSON><PERSON><PERSON> rèn tròn, <PERSON><PERSON> sát phù tiêu, <PERSON><PERSON><PERSON> viện, <PERSON><PERSON><PERSON> trạm dưỡng dục'
    },
    {
      time: "Tháng 9",
      topic: "Lớ<PERSON> học và bạn bè của bé",
      content:
        "- Đẹp học\n<br/>- Xen lên phù tố của tay\n<br/>- Số cơ bản và mẫu số\n<br/>- Đồ vật băng cho gần gũi đến thân thân\n<br/>- Sử dụng các máy về cảm giác phân loại giác giác, phân chia cao thấp sáng, xúc giác là trải nghiệm bước kĩ hơn của bước phát triển mạnh",
      key: '- Truyện: "Phép chia sân thầy", "Con trai học với đứa gió bạn"\n<br/>- Bàn cơ quan sổ\n<br/>- Bộ gõ sờ\n<br/>- Bảng con số, Khum đặt lấy 10, Nghi bột bằng tay, Khép đôi ba hình sáng khác nhau, Ai đều mạnh hơn'
    },
    {
      time: "Tháng 10",
      topic: "Bé và gia đình",
      content:
        "- Gia đình, lịch sử đeo\n<br/>- Người thầm giao việc giao phó, phát triển về duy logic\n<br/>- Tình thân của mẹ\n<br/>- Sự duy truyền, phát sinh giữa các mẹ và con con đối với các loại động ý, bất lợi của hành sự, sự dụng vào đối sánh sáng và sử dụng như là nhu cầu giữa sự về đại thân với trong gia đình",
      key: '- Truyện: "Rủa ó làm bạn với mình hả?", "Suy nghĩ một mình"\n<br/>- Thơ cất đêm lên\n<br/>- Tình mẫu Mascara\n<br/>- Con việc con nói, "Phòng lò biển đêm", Hành động của các thành viên trong gia đình là "Cây gia đình"'
    },
    {
      time: "Tháng 11",
      topic: "Giao thông",
      content:
        "- Tuần thì thú thú\n<br/>- Khi sáng điều chuyển ô tô bờ vào xa thế\n<br/>- Đội xương không gian\n<br/>- Kiểu sức chịu\n<br/>- Nơi sắt vị trí ước định, gặp lộ",
      key: '- Truyện: "Chuyển ôi đón thì là nội ra đi về Tỉnh", "Chuyển thú vui về"\n<br/>- Đường hành trình\n<br/>- Đặc biệt gặp hình tròn\n<br/>- Nơi thang dài bản đen\n<br/>- "Nơi đằng gió biển xứ", "Anh thanh của ô tô bên cửa gặp khác nhau", "Lường tiện tiện", "Tỉnh vận đồi"'
    }
    // Thêm các tháng còn lại theo cấu trúc tương tự
  ];
  const data2 = [
    {
      time: "Tháng 8",
      topic: "Mùa thu và thiên nhiên",
      content:
        "- Mùa thu và ý nghĩa của mùa thu\n<br/>- Vén mây và giữ thăng bằng gió thăng bằng.\n<br/>- Tìm hiểu về côn trùng\n<br/>- Phân tích ánh sáng, nhiệt và sức nóng.\n<br/>- Ánh sáng, kết cấu hình ảnh.",
      key: "Câu chuyện: “Ngày nhỏ chiếc cỏ màu vàng”, “Trốn và làm gì đây?”.\n<br/>- Bảng giữ thăng bằng\n<br/>- Đọc sách: Tìm hiểu về thiên nhiên, sự xa cách và tiềm năng mùa thu, cuộc hành trình không tưởng khi ưu tiên chọn lọc và khám phá\n<br/>- Nghe chuyện tại Bệnh viện Dương Sư - Bài học phù hợp, trải nghiệm xã hội, phục vụ."
    },
    {
      time: "Tháng 9",
      topic: "Lớp học và bạn bè của bé",
      content:
        "- Giữ kỷ luật\n<br/>- Khám phá các giác quan cơ bản (và nâng cấp phương diện các giác quan khác)\n<br/>- Tìm hiểu sâu sắc trong chế độ điều chỉnh của trẻ đối với các chức năng, sự chuẩn bị của các bạn đồng hành\n<br/>- Bắt đầu phát triển khả năng giữ thăng bằng, sự điều chỉnh với các công cụ (động lực).",
      key: "Bộ nhớ dạy học\n<br/>- Cảm giác giữ thăng bằng và âm thanh\n<br/>- Được phân loại hình, chuẩn bị điều kiện chung\n<br/>- Bảng sáng tạo, nâng cao sự điều chỉnh để đánh thức khái niệm bền vững"
    },
    {
      time: "Tháng 10",
      topic: "Bé và gia đình",
      content:
        "- Tăng cường khả năng quan sát và xúc tiếp trung\n<br/>- Tìm hiểu mối liên quan về các giác quan của các người, sự chuyển động của ánh sáng, sự tiếp xúc của các người với nhau.",
      key: "- Bộ đồ chơi bố mẹ lựa chọn, đánh giá\n<br/>- Nói thầm ngày thứ 7\n<br/>- Hình dạng xung quanh: “Bảng tay phước nhất” và “Nhìn thấy, không nhìn thấy”"
    },
    {
      time: "Tháng 11",
      topic: "Giao thông",
      content:
        "- Thái quen giữ thăng bằng\n<br/>- Phát triển kỹ năng vào xe và giữ thăng bằng khi chơi trong sân cỏ\n<br/>- Tìm hiểu xe lăn, leo trèo và kỹ năng.\n<br/>- Đặc điểm kỹ thuật, kiểm tra các điểm cách\n<br/>- Định hình chỗ vị trí, mặt đất và phương tiện giao thông khi có nhu cầu di chuyển.",
      key: "- Khởi sự kiểm soát địa hình\n<br/>- Đồ chơi giữ an toàn\n<br/>- Kĩ năng kiểm soát an toàn cho người trẻ"
    },
    {
      time: "Tháng 12",
      topic: "Mùa đông và những trò chơi",
      content:
        "- Tìm hiểu khả năng giữ thăng bằng khi thời tiết lạnh.\n<br/>- Trò chơi mùa đông ngày xưa, dưới giá cho kỹ năng giữ thăng bằng an toàn.",
      key: "- Sách viết cho mùa đông lạnh.\n<br/>- Chọn ra người giữ vững với công cụ chính (tay giữ thăng bằng)"
    },
    {
      time: "Tháng 1",
      topic: "Dụng cụ sinh hoạt",
      content:
        "- Giải trí gia vị cho cảm giác.\n<br/>- Định hình cảm giác đầu đời khi di chuyển của trẻ với điều kiện ánh sáng.\n<br/>- Tạo cho trẻ kỹ năng cắt dán và tạo hình.\n<br/>- Phát triển khả năng cảm nhận của âm thanh và phấn.",
      key: "- Truyện: “Lấy đồ chơi an toàn để cắt dán”\n<br/>- Mẹo bài kiểm tra cắt dán của trẻ\n<br/>- Hình vuông và ngôi sao: tạo phấn đường dẫn để vẽ lên."
    },
    {
      time: "Tháng 2",
      topic: "Đất nước của mẹ",
      content:
        "- Khám phá quốc gia về nghề nghiệp,\n<br/>- Trò chơi hướng dẫn khả năng giữ đất nước không ngừng tiến bộ.\n<br/>- Phát triển triết lý bảo vệ quốc gia,\n<br/>- Luyện tập kỹ năng an toàn xã hội.",
      key: "- Phân tích đất nước, lớp học trẻ\n<br/>- Hình thành đất nước"
    },
    {
      time: "Tháng 3",
      topic: "Mùa xuân và động thực vật",
      content:
        "- Sắc thái của đất nước\n<br/>- Phát triển tình bạn xã hội\n<br/>- Các trò chơi phát triển và thực hiện môi trường xanh.",
      key: "- Quan sát cây cối phát triển\n<br/>- Phát triển môi trường xanh"
    },
    {
      time: "Tháng 4",
      topic: "Môi trường và cuộc sống",
      content: "- Tạo dựng bền vững\n<br/>- Tạo điều kiện nền tảng",
      key: "- Cây cối và hoa lá"
    },
    {
      time: "Tháng 5",
      topic: "Bé đã lớn khôn",
      content:
        "- Khám phá thực vật, xây dựng cộng đồng\n<br/>- Thăm quan gia đình.",
      key: "- Gia đình hạnh phúc"
    },
    {
      time: "Tháng 6",
      topic: "Khu phố của bé",
      content:
        "- Luyện tập kỹ năng xây dựng nhóm,\n<br/>- Luyện tập kỹ năng vận động,\n<br/>- Giữ gìn khu phố xinh xắn.",
      key: "- Khu phố và giao thông"
    },
    {
      time: "Tháng 7",
      topic: "Mùa hè, sức khỏe và an toàn",
      content:
        "- Khám phá vị trí và vận động của các bạn.\n<br/>- Luyện tập thể dục thể thao\n<br/>- Cùng giữ sức khoẻ.",
      key: "- Chăm sóc sức khỏe và giữ an toàn\n<br/>- Hoạt động vui chơi mùa hè."
    }
  ];
  const data3 = [
    {
      time: "Tháng 8",
      topic: "Mùa thu và thiên nhiên",
      content:
        "Cấu tạo của cây cầu, phản hồi trọng lượng, âm thanh, hợp tác với chất.",
      key: "Hành động chạy tiếp sức, “Cây cầu dài”, “Kỹ thuật thám hiểm cây cầu”, “Chơi bóng và sân chơi”"
    },
    {
      time: "Tháng 9",
      topic: "Lớp học và bạn bè của bé",
      content:
        "Lắp ráp các bộ phận và kiểm định theo tiêu chuẩn, sắp xếp các vị trí và màu sắc để truyền đạt ý nghĩa và sự khác biệt.",
      key: "Lập trình các câu chuyện sáng tạo như thế nào?, “Xác định quảng màu”, “Những người bạn cùng nhau sáng tạo” và “Mỗi người đều khác biệt”"
    },
    {
      time: "Tháng 10",
      topic: "Bé và gia đình",
      content:
        "Các cơ quan trong cơ thể, điều vận tay và điều điểm của các chi tiết, quy mô quản lý và mục đích sử dụng dụng cụ trong gia đình.",
      key: "“Cơ quan gia đình trong cơ thể”, “Bàn tay và bàn chân”, “Quản đốc của mọi người trong gia đình” và “Quản sổ bé nhớ”"
    },
    {
      time: "Tháng 11",
      topic: "Giao thông",
      content: "Lực ma sát, độ bền bỉ, tốc độ, sự tải trọng, lực nâng.",
      key: "“Hành trình giao lộ lớn”, “Máy bay tại tuyến”, “Con đường thám hiểm tới vui vẻ” và “Chiếc ô tô di chuyển bằng gió”"
    },
    {
      time: "Tháng 12",
      topic: "Mùa đông và những trò chơi",
      content:
        "Tìm hiểu và quốc kỳ các nước, quân lĩnh, đặc điểm của chất lượng.",
      key: "“Đèn kỳ diệu”, “Bờ mái”, “Bảng số nhấn bằng ngôi sao” và “Chỉ số phép dịch”"
    },
    {
      time: "Tháng 1",
      topic: "Dụng cụ sinh hoạt",
      content: `Tự kiềm chế, <br/>
- phát triển cảm giác cân bằng  <br/>
- sự đối xứng giữa số và lượng , đếm số lượng, làm các phép toán cơ bản, phân số<br/>
- tìm hiểu về nhạc cụ <br/>
- tìm hiểu về dư ảnh, âm thanh, sự truyền tải sức mạnh và tính đàn hồi`,
      key: "Dùng cụ giữ thăng bằng Domino thả đinh nhọn, “Làm truyện sáng?”, “Cửa của sáng thực”, “Kỹ thuật điều hành Domino”"
    },
    {
      time: "Tháng 2",
      topic: "Đất nước của mẹ",
      content:
        "Tìm hiểu về hướng dẫn - bố cục gia đình, vật dụng cần phân, một số chất lượng trong nhà.",
      key: "“Văn phần Thăng”, “Hoa cánh buồm ánh đèn”, “Ngọn tháp sáng chất lượng” và “Cơn gió”"
    },
    {
      time: "Tháng 3",
      topic: "Mùa xuân và động thực vật",
      content: `- Các loài hoa mùa xuân, <br/>
phát triển khả năng phối hợp tay và mắt <br/>
- Phát triển cảm giác về không gian <br/>
- Tìm hiểu về nhạc cụ <br/>
- Tìm hiểu về quán tính, phản ứng thoát nhiệt, động vật sống ở khí hậu lạnh, nhiệt độ.`,
      key: `Trò chơi ném tiêu <br/>
Pentomino hình khối<br/>
Chuông tam giác<br/>
“Những loài động vật sống ở vùng khí hậu lạnh”, “Lạnh hay nóng?”, “Cùng quay nào”, “Túi sưởi tay ấm áp”`
    },
    {
      time: "Tháng 4",
      topic: "Môi trường và cuộc sống",
      content: "Mục tiêu vệ sinh môi trường (âm thanh, hương sắc và ánh sáng).",
      key: "“Cửa sáng đỏ ra khu phố”, “Bảng thăm quan cho các hoạt động”, “Đồ tạo dấu vào mưa”"
    },
    {
      time: "Tháng 5",
      topic: "Bé đã lớn khôn",
      content:
        "Khám phá khả năng xã hội, sự tách biệt an toàn, giữ gìn vệ sinh cá nhân.",
      key: "Gia đình của bé hạnh phúc, “Thế giới trẻ em”, “Trách nhiệm gia đình”"
    },
    {
      time: "Tháng 6",
      topic: "Khu phố của bé",
      content:
        "Tiếng trống và tòa sáng trong bóng tối, phát triển các kỹ năng sáng tạo và giữ vững an toàn.",
      key: "Tình cảm gia đình bạn bè và hàng xóm, “Góc sinh hoạt”, “Lựa chọn hoạt động tích cực và sáng tạo”"
    },
    {
      time: "Tháng 7",
      topic: "Mùa hè, sức khỏe và an toàn",
      content:
        "Tự bảo vệ sức khỏe trong hoạt động ngoài trời và dưới nước, giữ vệ sinh.",
      key: "Giữ sức khỏe và vệ sinh an toàn, “Hoạt động mùa hè”, “Cùng sáng tạo với bạn bè”"
    }
  ];

  return (
    <section className="container mx-auto mt-[28px] space-y-10 px-[1rem] pb-[3rem] lg:space-y-20">
      <div className="space-y-[36px] ">
        <h3 className="text-center text-[36px] font-bold text-[#414B5B]">
          Chương trình nhà trường
        </h3>
        <div className="">
          <div className="space-y-[10px]">
            <p className="text-[20px] font-bold text-[#414B5B]">
              1. Chương trình GDMN của Bộ GD&ĐT
            </p>
            <IconButton fileUrl="/kinder-garten/GDMN.pdf" />
          </div>
          <div className="space-y-[10px]">
            <p className="text-[20px] font-bold text-[#414B5B]">
              2. Chủ đề STEAM của mẫu giáo theo chương trình STEM-ART của Jello
            </p>
            <div className="space-y-[10px]">
              <p className="text-[20px] font-bold text-[#414B5B]">
                2.1. Mẫu giáo bé
              </p>
              <div className="relative space-y-[38px] text-[#414B5B]">
                {/* <OverlayText scale='scale-1'/> */}
                <div className="space-y-[10px] text-[20px]">
                  <div className="space-y-[20px]">
                    <div className="overflow-x-auto text-[16px] font-semibold">
                      <table className="min-w-full table-auto border-collapse border border-black">
                        <thead className="h-[87px]">
                          <tr>
                            <th className="w-[10%] border border-black bg-[#FFE498] px-4 py-2 font-[400]">
                              Thời gian
                            </th>
                            <th className="w-[10%] border border-black bg-[#FFE498] px-4 py-2 font-[400]">
                              Chủ đề
                            </th>
                            <th className="border border-black bg-[#FFE498] px-4 py-2 font-[400]">
                              Nội dung
                            </th>
                            <th className="border border-black bg-[#FFE498] px-4 py-2 font-[400]">
                              Chìa khóa
                            </th>
                          </tr>
                        </thead>
                        <tbody>
                          {data.map((row, index) => (
                            <tr key={index} className="h-[100px] text-[12px]">
                              <td className="border border-black px-4 py-2 text-center font-bold">
                                {row.time}
                              </td>
                              <td className="border border-black px-4 py-2 font-bold">
                                {row.topic}
                              </td>
                              <td
                                dangerouslySetInnerHTML={{
                                  __html: row?.content
                                }}
                                className="border  border-black px-4 py-2"
                              ></td>
                              <td
                                dangerouslySetInnerHTML={{ __html: row?.key }}
                                className="border  border-black px-4 py-2"
                              ></td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="space-y-[10px]">
              <p className="text-[20px] font-bold text-[#414B5B]">
                2.2. Mẫu giáo nhỡ
              </p>
              <div className="relative space-y-[38px] text-[#414B5B]">
                {/* <OverlayText scale='scale-1'/> */}
                <div className="space-y-[10px] text-[20px]">
                  <div className="space-y-[20px]">
                    <div className="overflow-x-auto text-[16px] font-semibold">
                      <table className="min-w-full table-auto border-collapse border border-black">
                        <thead className="h-[87px]">
                          <tr>
                            <th className="w-[10%] border border-black bg-[#FFE498] px-4 py-2 font-[400]">
                              Thời gian
                            </th>
                            <th className="w-[10%] border border-black bg-[#FFE498] px-4 py-2 font-[400]">
                              Chủ đề
                            </th>
                            <th className="border border-black bg-[#FFE498] px-4 py-2 font-[400]">
                              Nội dung
                            </th>
                            <th className="border border-black bg-[#FFE498] px-4 py-2 font-[400]">
                              Chìa khóa
                            </th>
                          </tr>
                        </thead>
                        <tbody>
                          {data2.map((row, index) => (
                            <tr key={index} className="h-[100px] text-[12px]">
                              <td className="border border-black px-4 py-2 text-center font-bold">
                                {row.time}
                              </td>
                              <td className="border border-black px-4 py-2 font-bold">
                                {row.topic}
                              </td>
                              <td
                                dangerouslySetInnerHTML={{
                                  __html: row?.content
                                }}
                                className="border  border-black px-4 py-2"
                              ></td>
                              <td
                                dangerouslySetInnerHTML={{ __html: row?.key }}
                                className="border  border-black px-4 py-2"
                              ></td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="space-y-[10px]">
              <p className="text-[20px] font-bold text-[#414B5B]">
                2.3. Mẫu giáo lớn
              </p>
              <div className="relative space-y-[38px] text-[#414B5B]">
                {/* <OverlayText scale='scale-1'/> */}
                <div className="space-y-[10px] text-[20px]">
                  <div className="space-y-[20px]">
                    <div className="overflow-x-auto text-[16px] font-semibold">
                      <table className="min-w-full table-auto border-collapse border border-black">
                        <thead className="h-[87px]">
                          <tr>
                            <th className="w-[10%] border border-black bg-[#FFE498] px-4 py-2 font-[400]">
                              Thời gian
                            </th>
                            <th className="w-[10%] border border-black bg-[#FFE498] px-4 py-2 font-[400]">
                              Chủ đề
                            </th>
                            <th className="border border-black bg-[#FFE498] px-4 py-2 font-[400]">
                              Nội dung
                            </th>
                            <th className="border border-black bg-[#FFE498] px-4 py-2 font-[400]">
                              Chìa khóa
                            </th>
                          </tr>
                        </thead>
                        <tbody>
                          {data3.map((row, index) => (
                            <tr key={index} className="h-[100px] text-[12px]">
                              <td className="border border-black px-4 py-2 text-center font-bold">
                                {row.time}
                              </td>
                              <td className="border border-black px-4 py-2 text-center font-bold">
                                {row.topic}
                              </td>
                              <td
                                dangerouslySetInnerHTML={{
                                  __html: row?.content
                                }}
                                className="border  border-black px-4 py-2"
                              ></td>
                              <td
                                dangerouslySetInnerHTML={{ __html: row?.key }}
                                className="border  border-black px-4 py-2"
                              ></td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="space-y-[10px]">
              <p className="text-[20px] font-bold text-[#414B5B]">
                3. Khung chương trình đào tạo cho trẻ làm quen với STEM ứng dụng
              </p>
              <div className="relative flex items-center justify-center space-y-[38px] text-[#414B5B]">
                <Image
                  width={700}
                  height={1000}
                  alt="mang-luoi5"
                  src="/mang-luoi5.png"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
