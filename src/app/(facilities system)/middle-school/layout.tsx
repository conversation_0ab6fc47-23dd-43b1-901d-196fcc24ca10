import "@/style/globals.css";
import ClientMiddleSchoolLayout from "@/components/ClientMiddleSchoolLayout";

export const metadata = {
  title: "<PERSON><PERSON> sinh thái học tập, sáng tạo",
  icons: {
    icon: "/logo-middle-school.svg"
  }
};

export default function MiddleSchoolLayout({
  children
}: Readonly<{
  children: React.ReactNode;
}>) {
  const dataMenu = [
    { name: "Trang chủ", href: "/middle-school" },
    { name: "<PERSON><PERSON><PERSON> tập", href: "" },
    { name: "<PERSON><PERSON><PERSON><PERSON> pháp", href: "/middle-school/teaching-methods" },
    { name: "<PERSON><PERSON><PERSON> nghệ", href: "" },
    { name: "<PERSON><PERSON><PERSON> đàn", href: "" },
    { name: "<PERSON>h<PERSON> viện", href: "" },
  ];
  const dataOfMenuChild = [
    {
      title:
        "<PERSON><PERSON> sinh thái học tập sáng tạo: kết nối kiến thức, ph<PERSON>t triển tư du<PERSON>, chia sẻ.",
      button: [],
      items: []
    },
    {
      title:
        "<PERSON><PERSON><PERSON> tập trong hệ sinh thái học tập sáng tạo cấp THCS.",
      button: [],
      items: [
        {
          name: "CT chính khóa",
          href: "",
          image: "/middle-school/hoctap1.jpg"
        },
        {
          name: "CT ngoại khóa",
          href: "",
          image: "/middle-school/hoctap2.jpg"
        },
        {
          name: "Các cuộc thi",
          href: "",
          image: "/middle-school/hoctap3.jpg"
        },
        {
          name: "Trải nghiệm và hướng nghiệm",
          href: "",
          image: "/middle-school/hoctap4.jpg"
        },
      ]
    },
    {
      title:
        "Thống kê các trường các cấp của thuộc hệ sinh thái học tập, sáng tạo.",
      button: [],
      items: []
    },
    {
      title:
        "Nơi thảo luận các vấn đề về học tập, sáng tạo của học sinh, phụ huynh và giáo viên.",
      button: [],
      items: []
    },
    {
      title: "Thư viện trong hệ sinh thái học tập sáng tạo cấp THCS.",
      button: [],
      items: [
        {
          name: "Thư viện số",
          href: "#",
          image: "/tieuhoc.jpg"
        },
        {
          name: "Video bài giảng",
          href: "#",
          image: "/tieuhoc.jpg"
        },
      ]
    }
  ];
  const dataMenuSelected = [
    {
      title: "Giới thiệu",
      href: "/middle-school/about-us",
      child: []
    },
    {
      title: "Tin tức",
      href: "/middle-school/news",
      child: []
    },
    {
      title: "Mô hình GD sáng tạo",
      href: "/middle-school/creative-minds-academy",
      child: []
    },
    {
      title: "Giáo viên",
      href: "/",
      child: []
    },
    {
      title: "Thống kê",
      href: "/",
      child: []
    },
    {
      title: "Đánh giá",
      href: "/",
      child: []
    },
    {
      title: "Xếp hạng",
      href: "/",
      child: []
    }
  ];
  return (
    <ClientMiddleSchoolLayout 
      dataMenu={dataMenu} 
      dataOfMenuChild={dataOfMenuChild} 
      dataMenuSelected={dataMenuSelected}
    >
      {children}
    </ClientMiddleSchoolLayout>
  );
}
