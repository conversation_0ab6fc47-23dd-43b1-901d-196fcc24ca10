import OverlayText from "@/components/ui/OverlayText";

export default function CreativeMindsAcademyMiddleSchoolPage() {
  return (
    <section className="container mx-auto mt-[28px] space-y-10 pb-[3rem] lg:space-y-20">
      <div className="space-y-[36px] ">
        <h3 className="text-center text-[36px] font-bold text-[#414B5B]">
          Mô hình giáo dục học tập sáng tạo cấp THCS
        </h3>
        <div className="relative space-y-[10px] text-[20px] text-[#414B5B] text-[400]">
          <OverlayText url="/middle-school/logo.png"/>
          <p className="leading-[29px] text-[#414B5B]">
            Mô hình giáo dục sáng tạo ở cấp THCS thường hướng tới việc phát
            triển tư duy phản biện, kh<PERSON> năng sáng tạo và kỹ năng thực hành cho
            học sinh. Dưới đây là một số đặc điểm và phương pháp của mô hình này
          </p>
          <div className="space-y-[10px] leading-[25px] text-[#414B5B]">
            <p className="font-bold">
              1. Học tập dựa trên dự án (Project-Based Learning - PBL)
            </p>
            <ul className="list-disc space-y-[10px] pl-[2rem]">
              <li>
                Học sinh tham gia vào các dự án thực tế, từ đó tìm hiểu, nghiên
                cứu và giải quyết vấn đề. Phương pháp này giúp học sinh phát
                triển kỹ năng làm việc nhóm và tự học.
              </li>
            </ul>
            <p className="font-bold">2. Học theo chủ đề liên môn</p>
            <ul className="list-disc space-y-[10px] pl-[2rem]">
              <li>
                Kết hợp các môn học khác nhau để giải quyết vấn đề hoặc tìm hiểu
                một chủ đề nhất định. Việc này giúp học sinh thấy được sự liên
                kết giữa các kiến thức.
              </li>
            </ul>
            <p className="font-bold">
              3. Ứng dụng công nghệ thông tin
            </p>
            <ul className="list-disc space-y-[10px] pl-[2rem]">
              <li>
                Tích cực áp dụng công nghệ trong giảng dạy và học tập, như sử
                dụng phần mềm học tập, nền tảng trực tuyến hay các công cụ số để
                nâng cao hiệu quả học tập.
              </li>
            </ul>
            <p className="font-bold">4. Học tập theo trải nghiệm</p>
            <ul className="list-disc space-y-[10px] pl-[2rem]">
              <li>
                Tổ chức các hoạt động ngoại khóa, tham quan thực tế hoặc các kỹ
                năng sống để học sinh có cơ hội trải nghiệm trực tiếp, từ đó
                liên hệ với thực tiễn.
              </li>
            </ul>
            <p className="font-bold">
              5. Khuyến khích phát triển phẩm chất cá nhân
            </p>
            <ul className="list-disc space-y-[10px] pl-[2rem]">
              <li>
                Tập trung vào việc phát triển các phẩm chất như tự chủ, trách
                nhiệm, sáng tạo và khả năng giao tiếp.
              </li>
            </ul>
            <p className="font-bold">6. Dạy học cá thể hóa</p>
            <ul className="list-disc space-y-[10px] pl-[2rem]">
              <li>
                Điều chỉnh phương pháp giảng dạy phù hợp với năng lực và sở
                thích của từng học sinh, từ đó nâng cao hiệu quả học tập.
              </li>
            </ul>
            <p className="font-bold">
              7. Phản hồi và đánh giá thường xuyên
            </p>
            <ul className="list-disc space-y-[10px] pl-[2rem]">
              <li>
                Sử dụng hình thức đánh giá đa dạng, bao gồm đánh giá qua quá
                trình học tập, giúp học sinh nhận biết điểm mạnh và điểm cần cải
                thiện.
              </li>
            </ul>
            <p className="font-[400] text-[#414B5B]">
              Những mô hình này không chỉ giúp nâng cao kiến thức mà còn tạo
              điều kiện cho học sinh phát triển toàn diện, trở thành những cá
              nhân tự tin và sáng tạo trong tương lai.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}
