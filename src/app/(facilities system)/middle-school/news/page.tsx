import HeroSection from "@/components/ui/HeroSection";
import { BsArrowDownShort } from "react-icons/bs";

export default async function NewsPage() {
  const articles = [
    {
      id: 2,
      category: "Hà Nội nhân rộng mô hình giáo dục thông minh sử dụng trí tuệ nhân tạo",
      title:
          "Sáng 16/10, Sở GD&ĐT Hà Nội thí điểm mô hình giáo dục thông minh sử dụng trí tuệ nhân tạo ...",
      imageUrl: "https://**********.vws.vegacdn.vn/UploadImages/haydung/hanoi/anhhaydung/10.10/9.10/z593730803465954b21e780cb16f837f9ecad065864d4c_17102024825.jpg?w=1130",
      link: "/middle-school/news/2"
    },
    {
      id: 1,
      category: "<PERSON><PERSON> N<PERSON>i phát động <PERSON><PERSON> lễ hưởng <PERSON>ng học tập suốt đời năm 2024",
      title:
          "<PERSON>áng 2/10, tại Trường THCS Giảng Võ, quậ<PERSON>nh, Sở GD&Đ<PERSON> <PERSON>à Nội tổ chức lễ khai mạc và ...",
      imageUrl: "https://cdn.giaoducthoidai.vn/images/95b90f63c7a8d187b8dd97e8a06f6abc8bc7b0578eee194e89522ee1ba4d132ba2ae417e533549245c1a6668661aa6b8/img-0329-7148-7944.jpg?w=1130",
      link: "/middle-school/news/1"
    },
    {
      id: 3,
      category: "Quận Ba Đình: 91 học sinh THCS đoạt giải Nhất kỳ thi Olympic cấp quận",
      title:
          "Tham dự kỳ thi Olympic các môn văn hoá lớp 6, 7, 8 cấp Trung học cơ sở (THCS) quận Ba Đình ...",
      imageUrl: "https://laodongthudo.vn/stores/news_dataimages/2024/052024/20/13/0557215c40e5e1bbb8f420240520133844.jpg?rt=20240520133911",
      link: "/middle-school/news/3"
    },
    {
      id: 4,
      category: "Thầy trò trường THPT Trần Phú - Hoàn Kiếm tích cực tham gia các hoạt...",
      title:
          "Hướng tới kỉ niệm 70 năm Ngày Giải phóng Thủ đô (10/10/1954 -10/10/2024), trường THPT Trần...",
      imageUrl: "https://**********.vws.vegacdn.vn/UploadImages/news//2024/hanoi/quachthanhson/quachthanhson/2024_10/11/01-tp_111020241452.jpg?w=1130",
      link: "/middle-school/news/4"
    }
  ];
  return (
    <div>
      <div className="mt-[2rem] text-[#414B5B]">
        <div className="page_breadcrumb">
          <div className="container mx-auto px-4">
            <h1 className="text-4xl font-bold">Tin tức</h1>
            <p className="mt-2 text-lg">Tin tức và sự kiện</p>

            {/* Phần dòng kẻ */}
            <div className="my-6 border-b border-gray-200"></div>

            {/* Phần tiêu đề Latest */}
            <h2 className="text-3xl font-bold">Hiển thị tin tức mới nhất</h2>

            {/* Phần tìm kiếm */}
            <div className="mt-4 flex items-center space-x-4">
              <input
                type="text"
                placeholder="Nhập tiêu đề ..."
                className="w-full max-w-[400px] rounded-full border border-gray-300 px-4 py-2 focus:outline-none"
              />
              <button className="rounded-full bg-blue-500 px-6 py-2 text-white">
                Tìm kiếm
              </button>
            </div>

            {/* Phần kết quả */}
            <div className="mt-4 text-gray-600">209 kết quả</div>
          </div>
        </div>
      </div>

      <div className="container mx-auto text-[#414B5B]">
        <div className="h-[1px] bg-gray-300"></div>
        <div className="mt-[20px] ">
          <h1 className="text-4xl font-bold">Tin tức</h1>
        </div>

        <div className="mt-[40px] grid grid-cols-1 gap-x-[30px] md:grid-cols-2 lg:grid-cols-4">
          {articles.map((article) => (
            <div
              key={article.id}
              className="border-t border-gray-300 pt-[20px]"
            >
              <img
                src={article.imageUrl}
                alt={article.title}
                className="mb-4 h-40 w-full object-cover"
              />
              <a
                href={`/middle-school/news/${article.id}`}
                className=" font-medium text-[#414B5B] hover:text-blue-500"
              >
                {article.category}
              </a>
              <h3 className="mt-2 font-bold text-[#414B5B] transition-colors duration-300">
                {article.title}
              </h3>
            </div>
          ))}
        </div>

        <div className="mb-[4rem] mt-[50px] flex items-center justify-center">
          <button className="flex items-center space-x-2 rounded-full border border-gray-400 px-[20px] py-[7px] text-gray-500 transition-all duration-300 hover:border-gray-700 hover:text-gray-700">
            <span>Xem thêm</span>
            <span className="text-xl text-gray-400">
              <BsArrowDownShort />
            </span>
          </button>
        </div>
      </div>
    </div>
  );
}
