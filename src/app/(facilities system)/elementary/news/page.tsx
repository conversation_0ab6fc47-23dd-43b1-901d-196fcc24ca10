import HeroSection from "@/components/ui/HeroSection";
import { BsArrowDownShort } from "react-icons/bs";

export default async function NewsPage() {
  const articles = [
    {
      id: 2,
      category: "Tập huấn sử dụng hệ sinh thái học liệu số cấp Tiểu học tại tỉnh Đồng Tháp",
      title:
          '<PERSON><PERSON> dục và Đào tạo đang lấy ý kiến góp ý với dự thảo hồ sơ đề nghị xây dựng Nghị quyết của Quốc hội về đổi mới Chương trình giáo ...',
      imageUrl: "https://www.nxbgd.vn/Attachments/images/Tap%20huan%20SGk/284-9585.jpg",
      link: '/elementary/news/2'
    },
    {
      id: 1,
      category: "Phát triển mô hình “<PERSON><PERSON> sinh thái học tập, s<PERSON>g tạo” trong trường học",
      title:
          "<PERSON><PERSON>y 22/11, <PERSON><PERSON> dụ<PERSON> và <PERSON> (GDĐT) phối hợp với <PERSON>r<PERSON> đẳng Sư phạm Trung ương tổ chức Hội thảo “Đổi mới và sáng tạo hướng tới ...",
      imageUrl: "https://hnm.1cdn.vn/2024/09/17/d3.jpg",
      link: '/elementary/news/1'
    },
    {
      id: 3,
      category: "Hà Nội: xây dựng và phát triển mô hình “Hệ sinh thái học tập, sáng tạo”",
      title:
          "Phát biểu tại hội thảo, Viện trưởng Viện Nghiên cứu giáo dục phát triển tiềm năng con người, Phó Chủ tịch Hội Giáo dục chăm sóc sức khỏe cộng đồng ...",
      imageUrl: "https://static.kinhtedothi.vn/w960/images/upload/2024/09/17/img-3981.jpeg",
      link: '/elementary/news/3'
    },
    {
      id: 4,
      category: "Xây dựng trường học thông minh cần phải có một hệ sinh thái giáo dục toàn diện về dạy học",
      title:
          "Hiểu trẻ, yêu trẻ và yêu nghề là những điều chúng tôi cảm nhận được khi trò chuyện với cô giáo Nguyễn Thị Hoa, sinh năm 1991, đang công tác ...",
      imageUrl: "https://cdn.daibieunhandan.vn/images/de2ce54bdb7a59a5f7ed9fad7e3b3f20cf8a7059977f428fafcbf1144a57fd2e55b1913af8697b234b7667bc96ec1340a1e5bda417fdf0dfe35b0071f1eb47395795381c8dbc7e36db6cbf0da5f86ec2092e60fcc33c04fcfdcf60c3968ef108/Anh-chup-Man-hinh-2023-09-23-luc-1695462148592.png",
      link: '/elementary/news/4'
    }
  ];

  return (
    <div>
      <div className="mt-[2rem] text-[#414B5B]">
        <div className="page_breadcrumb">
          <div className="container mx-auto px-4">
            <h1 className="text-4xl font-bold">Tin tức</h1>
            <p className="mt-2 text-lg">Tin tức và sự kiện</p>

            {/* Phần dòng kẻ */}
            <div className="my-6 border-b border-gray-200"></div>

            {/* Phần tiêu đề Latest */}
            <h2 className="text-3xl font-bold">Hiển thị tin tức mới nhất</h2>

            {/* Phần tìm kiếm */}
            <div className="mt-4 flex items-center space-x-4">
              <input
                type="text"
                placeholder="Nhập tiêu đề ..."
                className="w-full max-w-[400px] rounded-full border border-gray-300 px-4 py-2 focus:outline-none"
              />
              <button className="rounded-full bg-blue-500 px-6 py-2 text-white">
                Tìm kiếm
              </button>
            </div>

            {/* Phần kết quả */}
            <div className="mt-4 text-gray-600">4 kết quả</div>
          </div>
        </div>
      </div>

      <div className="container mx-auto text-[#414B5B]">
        <div className="h-[1px] bg-gray-300"></div>
        <div className="mt-[20px] ">
          <h1 className="text-4xl font-bold">Tin tức</h1>
        </div>

        <div className="mt-[40px] grid grid-cols-1 gap-x-[30px] md:grid-cols-2 lg:grid-cols-4">
          {articles.map((article) => (
            <div
              key={article.id}
              className="border-t border-gray-300 pt-[20px]"
            >
              <img
                src={article.imageUrl}
                alt={article.title}
                className="mb-4 h-40 w-full object-cover"
              />
              <a
                href={`/kinder-garten/news/${article.id}`}
                className=" font-medium text-[#414B5B] hover:text-blue-500"
              >
                {article.category}
              </a>
              <h3 className="mt-2  font-bold text-[#414B5B] transition-colors duration-300">
                {article.title}
              </h3>
            </div>
          ))}
        </div>

        <div className="mb-[4rem] mt-[50px] flex items-center justify-center">
          <button className="flex items-center space-x-2 rounded-full border border-gray-400 px-[20px] py-[7px] text-gray-500 transition-all duration-300 hover:border-gray-700 hover:text-gray-700">
            <span>Xem thêm</span>
            <span className="text-xl text-gray-400">
              <BsArrowDownShort />
            </span>
          </button>
        </div>
      </div>
    </div>
  );
}
