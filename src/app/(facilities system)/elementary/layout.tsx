import "@/style/globals.css";
import ClientElementaryLayout from "@/components/ClientElementaryLayout";

export const metadata = {
  title: "<PERSON><PERSON> sinh thái học tập, sáng tạo",
  icons: {
    icon: "/logo-elementary.svg"
  }
};

export default function ElementaryLayout({
  children
}: Readonly<{
  children: React.ReactNode;
}>) {
  const dataMenu = [
    { name: "Trang chủ", href: "/elementary" },
    { name: "<PERSON><PERSON><PERSON> tập", href: "" },
    { name: "<PERSON><PERSON><PERSON> tạo", href: "" },
    { name: "<PERSON><PERSON><PERSON> chơ<PERSON>", href: "" },
    { name: "<PERSON><PERSON><PERSON> viện", href: "" },
    { name: "<PERSON><PERSON> huynh và GV", href: "" },
  ];
  const dataOfMenuChild = [
    {
      title:
        "<PERSON><PERSON> sinh thái học tập sáng tạo: kết n<PERSON>i kiến thức, ph<PERSON>t triển tư du<PERSON>, chia sẻ.",
      button: [],
      items: []
    },
    {
      title:
        "<PERSON>ọ<PERSON> tập trong hệ sinh thái học tập sáng tạo cấp tiểu học.",
      button: [],
      items: [
        {
          name: "Học tập",
          href: "",
          image: "/elementary/hoctap1.jpg"
        },
        {
          name: "Bài học tương tác",
          href: "",
          image: "/elementary/hoctap2.jpg"
        },
        {
          name: "Kho bài tập",
          href: "",
          image: "/elementary/hoctap3.jpg"
        },
        {
          name: "Học tập với bạn bè",
          href: "",
          image: "/elementary/hoctap4.jpg"
        }
      ]
    },
    {
      title:
        "Sáng tạo trong hệ sinh thái học tập sáng tạo cấp tiểu học.",
      button: [],
      items: [
        {
          name: "Dự án sáng tạo",
          href: "",
          image: "/elementary/sangtao1.jpg"
        },
        {
          name: "Thư viện sáng tạo",
          href: "",
          image: "/elementary/sangtao2.jpg"
        },
        {
          name: "Thách thức sáng tạo",
          href: "",
          image: "/elementary/sangtao3.jpg"
        },
      ]
    },
    {
      title:
        "Trò chơi trong hệ sinh thái học tập sáng tạo cấp tiểu học.",
      button: [],
      items: [
        {
          name: "Trò chơi giáo dục",
          href: "",
          image: "/elementary/trochoi1.jpg"
        },
        {
          name: "Trò chơi phát triển kỹ năng",
          href: "",
          image: "/elementary/trochoi2.jpg"
        },
        {
          name: "Giải đấu mini",
          href: "",
          image: "/elementary/trochoi3.jpg"
        },
      ]
    },
    {
      title:
        "Nơi thảo luận các vấn đề về học tập, sáng tạo của học sinh, phụ huynh và giáo viên.",
      button: [],
      items: [
        {
          name: "Thư viện số",
          href: "",
          image: "/elementary/thuvien1.jpg"
        },
        {
          name: "Video bài giảng",
          href: "",
          image: "/elementary/thuvien2.jpg"
        },
      ]
    },
    {
      title: "Tổng hợp các phần mềm hỗ trợ trong học tập, giảng dạy.",
      button: [],
      items: [
        {
          name: "Báo cáo học tập",
          href: "",
          image: "/elementary/phuhuynh1.jpg"
        },
        {
          name: "Tài liệu hỗ trợ",
          href: "",
          image: "/elementary/phuhuynh2.jpg"
        },
        {
          name: "Tư vấn giáo dục",
          href: "",
          image: "/elementary/phuhuynh3.jpg"
        },
      ]
    }
  ];
  const dataMenuSelected = [
    {
      title: "Giới thiệu",
      href: "/elementary/about-us",
      child: []
    },
    {
      title: "Tin tức",
      href: "/elementary/news",
      child: []
    },
    {
      title: "Mô hình GD sáng tạo",
      href: "/elementary/creative-minds-academy",
      child: []
    },
    {
      title: "Tích hợp và kết nối",
      href: "/",
      child: [
        { title: "Lộ trình học tập", href: "/elementary" },
        { title: "Bài học chuẩn bị", href: "/elementary" },
        { title: "Dự án liên cấp", href: "/elementary" },
        { title: "Khám phá cấp học mới", href: "/elementary" },
      ]
    },
    {
      title: "Thống kê",
      href: "/",
      child: []
    },
    {
      title: "Đánh giá",
      href: "/",
      child: []
    },
    {
      title: "Xếp hạng",
      href: "/",
      child: []
    }
  ];
  return (
    <ClientElementaryLayout 
      dataMenu={dataMenu} 
      dataOfMenuChild={dataOfMenuChild} 
      dataMenuSelected={dataMenuSelected}
    >
      {children}
    </ClientElementaryLayout>
  );
}
