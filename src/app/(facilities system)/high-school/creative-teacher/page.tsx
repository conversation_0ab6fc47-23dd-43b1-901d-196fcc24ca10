"use client";

import IconButton from "@/components/button/IconButton";

export default function CreativeTeacherPage() {
  const data1 = [
    {
      title: "05-THPT_ Quản trị CLGD (30.10.2021)",
      fileUrl: "/high-school/gvst/1/1.pdf"
    }
  ];
  const data2 = [
    {
      title: "0.9.8.2021. THPT",
      fileUrl: "/high-school/gvst/2/1.pdf"
    }
  ];
  const data3 = [
    {
      title: "DIA LI THPT",
      fileUrl: "/high-school/gvst/3/1.pdf"
    },
    {
      title: "GDTC THPT",
      fileUrl: "/high-school/gvst/3/2.pdf"
    },
    {
      title: "GIAO DUC KTPL THPT",
      fileUrl: "/high-school/gvst/3/3.pdf"
    },
    {
      title: "HOAT DONG TN THPT",
      fileUrl: "/high-school/gvst/3/4.pdf"
    },
    {
      title: "HOAT DONG TN THPT",
      fileUrl: "/high-school/gvst/3/5.pdf"
    },
    {
      title: "LỊCH SỬ THPT",
      fileUrl: "/high-school/gvst/3/6.pdf"
    },
    {
      title: "Modun 4 - Etep Toán",
      fileUrl: "/high-school/gvst/3/7.pdf"
    },
    {
      title: "NGU VAN THPT",
      fileUrl: "/high-school/gvst/3/8.pdf"
    },
    {
      title: "SINH HỌC THPT",
      fileUrl: "/high-school/gvst/3/9.pdf"
    },
    {
      title: "VAT LI THPT",
      fileUrl: "/high-school/gvst/3/10.pdf"
    }
  ];
  const data4 = [
    {
      title: "Modun 5 _Etep",
      fileUrl: "/high-school/gvst/4/1.pdf"
    }
  ];
  const data5 = [
    {
      title: "Tài liệu MĐ6",
      fileUrl: "/high-school/gvst/5/1.pdf"
    }
  ];
  const data6 = [
    {
      title: "Tài liệu MĐ7",
      fileUrl: "/high-school/gvst/6/1.pdf"
    }
  ];
  const data7 = [
    {
      title: "1.TLQL - THPT-Fulltext M8 SNT",
      fileUrl: "/high-school/gvst/7/1.pdf"
    },
    {
      title: "Tài liệu MĐ8",
      fileUrl: "/high-school/gvst/7/2.pdf"
    }
  ];
  const data8 = [
    {
      title: "HDTN THPT",
      fileUrl: "/high-school/gvst/8/1.pdf"
    },
    {
      title: "M9_CBQL_THPT_15.11.2021",
      fileUrl: "/high-school/gvst/8/2.pdf"
    }
  ];
  return (
    <section className="container mx-auto mt-[28px] space-y-10 px-[1rem] pb-[3rem] lg:space-y-20">
      <div className="space-y-[36px] ">
        <h3 className="text-center text-[36px] font-bold text-[#414B5B]">
          Giáo viên sáng tạo
        </h3>
        <div className="space-y-[10px]">
          <div className="space-y-[10px]">
            <p className="text-[20px] font-bold text-[#414B5B]">
              1. Quản trị chất lượng giáo dục
            </p>
            <div className="flex flex-wrap gap-3">
              {data1.map((item, index) => (
                <IconButton
                  key={index}
                  label={item.title}
                  isBtnDownload={false}
                  fileUrl={item.fileUrl}
                />
              ))}
            </div>
          </div>
          <div className="space-y-[10px]">
            <p className="text-[20px] font-bold text-[#414B5B]">
              2. Quản trị cơ sở vật chất, thiết bị và công nghệ
            </p>
            <div className="flex flex-wrap gap-3">
              {data2.map((item, index) => (
                <IconButton
                  key={index}
                  label={item.title}
                  isBtnDownload={false}
                  fileUrl={item.fileUrl}
                />
              ))}
            </div>
          </div>
          <div className="space-y-[10px]">
            <p className="text-[20px] font-bold text-[#414B5B]">
              3. Xây dựng kế hoạch dạy học và giáo dục
            </p>
            <div className="flex flex-wrap gap-3">
              {data3.map((item, index) => (
                <IconButton
                  key={index}
                  label={item.title}
                  isBtnDownload={false}
                  fileUrl={item.fileUrl}
                />
              ))}
            </div>
          </div>
          <div className="space-y-[10px]">
            <p className="text-[20px] font-bold text-[#414B5B]">
              4. Tư vấn hỗ trợ học sinh
            </p>
            <div className="flex flex-wrap gap-3">
              {data4.map((item, index) => (
                <IconButton
                  key={index}
                  label={item.title}
                  isBtnDownload={false}
                  fileUrl={item.fileUrl}
                />
              ))}
            </div>
          </div>
          <div className="space-y-[10px]">
            <p className="text-[20px] font-bold text-[#414B5B]">
              5. Xây dựng văn hóa nhà trường
            </p>
            <div className="flex flex-wrap gap-3">
              {data5.map((item, index) => (
                <IconButton
                  key={index}
                  label={item.title}
                  isBtnDownload={false}
                  fileUrl={item.fileUrl}
                />
              ))}
            </div>
          </div>
          <div className="space-y-[10px]">
            <p className="text-[20px] font-bold text-[#414B5B]">
              6. Thực hiện và xây dựng trường học an toàn
            </p>
            <div className="flex flex-wrap gap-3">
              {data6.map((item, index) => (
                <IconButton
                  key={index}
                  label={item.title}
                  isBtnDownload={false}
                  fileUrl={item.fileUrl}
                />
              ))}
            </div>
          </div>
          <div className="space-y-[10px]">
            <p className="text-[20px] font-bold text-[#414B5B]">
              7. Phối hợp giữa nhà trường, gia đình và xã hội
            </p>
            <div className="flex flex-wrap gap-3">
              {data7.map((item, index) => (
                <IconButton
                  key={index}
                  label={item.title}
                  isBtnDownload={false}
                  fileUrl={item.fileUrl}
                />
              ))}
            </div>
          </div>
          <div className="space-y-[10px]">
            <p className="text-[20px] font-bold text-[#414B5B]">
              8. Ứng dụng công nghệ thông tin và truyền thông trong quản trị
            </p>
            <div className="flex flex-wrap gap-3">
              {data8.map((item, index) => (
                <IconButton
                  key={index}
                  label={item.title}
                  isBtnDownload={false}
                  fileUrl={item.fileUrl}
                />
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
