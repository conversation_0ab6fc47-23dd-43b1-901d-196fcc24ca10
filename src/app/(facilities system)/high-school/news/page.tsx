import HeroSection from "@/components/ui/HeroSection";
import { BsArrowDownShort } from "react-icons/bs";

export default async function NewsPage() {
  const articles = [
    {
      id: 2,
      category: "Trường tiểu học- THCS Thăng Long (Trường ĐH Thủ đô <PERSON>) có thêm cấp THPT",
      title:
          "Ngày 4/9, Trường ĐH Thủ đô Hà Nội tổ chức lễ công bố quyết định tổ chức...",
      imageUrl: "https://static.kinhtedothi.vn/w960/images/upload/2024/09/04/a-thu-do-1.jpg",
      link: "/high-school/news/2"
    },
    {
      id: 1,
      category: "Teen THPT Trần Phú - Hoàn Kiếm sáng tạo sách mô hình 3D Một vòng Hoàn Kiếm",
      title:
          "<PERSON><PERSON><PERSON> mừng lễ <PERSON>ố<PERSON> khánh 2/9 và khai giảng năm học mới, <PERSON> đoàn 12A6...",
      imageUrl: "https://image.tienphong.vn/w1966/Uploaded/2024/ttf-ztmfxuzt/2024_08_28/bia-4809.jpg",
      link: "/high-school/news/1"
    },
    {
      id: 3,
      category: "Trường THPT Kim Liên – 48 năm sen vàng tỏa sáng",
      title:
          "Trường THPT Kim Liên được thành lập từ năm 1974. Trải qua 48 năm xây...",
      imageUrl: "data:image/jpeg;base64,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",
      link: "/high-school/news/3"
    },
    {
      id: 4,
      category: "Thầy trò Trường THPT Việt Đức tự hào truyền thống, sải cánh vươn xa",
      title:
          "Sáng nay (5/9), toàn thể cán bộ, giáo viên và hơn 2.550 học sinh Trường...",
      imageUrl: "https://cdn.tuoitrethudo.vn/stores/news_dataimages/2024/092024/05/11/2235-1f6fb37822b585ebdca420240905110050.7276050.jpg",
      link: "/high-school/news/4"
    }
  ];
  return (
    <div>
      <div className="mt-[2rem] text-[#414B5B]">
        <div className="page_breadcrumb">
          <div className="container mx-auto px-4">
            <h1 className="text-4xl font-bold">Tin tức</h1>
            <p className="mt-2 text-lg">Tin tức và sự kiện</p>

            {/* Phần dòng kẻ */}
            <div className="my-6 border-b border-gray-200"></div>

            {/* Phần tiêu đề Latest */}
            <h2 className="text-3xl font-bold">Hiển thị tin tức mới nhất</h2>

            {/* Phần tìm kiếm */}
            <div className="mt-4 flex items-center space-x-4">
              <input
                type="text"
                placeholder="Nhập tiêu đề ..."
                className="w-full max-w-[400px] rounded-full border border-gray-300 px-4 py-2 focus:outline-none"
              />
              <button className="rounded-full bg-blue-500 px-6 py-2 text-white">
                Tìm kiếm
              </button>
            </div>

            {/* Phần kết quả */}
            <div className="mt-4 text-gray-600">209 kết quả</div>
          </div>
        </div>
      </div>

      <div className="container mx-auto text-[#414B5B]">
        <div className="h-[1px] bg-gray-300"></div>
        <div className="mt-[20px] ">
          <h1 className="text-4xl font-bold">Tin tức</h1>
        </div>

        <div className="mt-[40px] grid grid-cols-1 gap-x-[30px] md:grid-cols-2 lg:grid-cols-4">
          {articles.map((article) => (
            <div
              key={article.id}
              className="border-t border-gray-300 pt-[20px]"
            >
              <img
                src={article.imageUrl}
                alt={article.title}
                className="mb-4 h-40 w-full object-cover"
              />
              <a
                href={`/high-school/news/${article.id}`}
                className=" font-medium text-[#414B5B] hover:text-blue-500"
              >
                {article.category}
              </a>
              <h3 className="mt-2  font-bold text-[#414B5B] transition-colors duration-300">
                {article.title}
              </h3>
            </div>
          ))}
        </div>

        <div className="mb-[4rem] mt-[50px] flex items-center justify-center">
          <button className="flex items-center space-x-2 rounded-full border border-gray-400 px-[20px] py-[7px] text-gray-500 transition-all duration-300 hover:border-gray-700 hover:text-gray-700">
            <span>Xem thêm</span>
            <span className="text-xl text-gray-400">
              <BsArrowDownShort />
            </span>
          </button>
        </div>
      </div>
    </div>
  );
}
