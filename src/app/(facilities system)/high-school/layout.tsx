import "@/style/globals.css";
import ClientHighSchoolLayout from "@/components/ClientHighSchoolLayout";

export const metadata = {
  title: "<PERSON><PERSON> sinh thái học tập, sáng tạo",
  icons: {
    icon: "/logo-high-school.svg"
  }
};

export default function HighSchoolLayout({
  children
}: Readonly<{
  children: React.ReactNode;
}>) {
  const dataMenu = [
    { name: "Trang chủ", href: "/high-school" },
    { name: "<PERSON><PERSON><PERSON> tập", href: "" },
    { name: "<PERSON><PERSON><PERSON><PERSON> nghiệp", href: "/high-school/career-guidance" },
    { name: "<PERSON><PERSON><PERSON> nghệ", href: "" },
    { name: "<PERSON><PERSON><PERSON> đàn", href: "" },
    { name: "<PERSON>h<PERSON> viện ", href: "" },
  ];
  const dataOfMenuChild = [
    {
      title:
        "<PERSON><PERSON> sinh thái học tập sáng tạo: kết nối kiến thức, ph<PERSON>t triển tư du<PERSON>, chia sẻ.",
      button: [],
      items: []
    },
    {
      title:
        "<PERSON><PERSON><PERSON> tập trong hệ sinh thái học tập sáng tạo cấp THPT",
      button: [],
      items: [
        {
          name: "CT chính khóa",
          href: "#",
          image: "/high-school/hoctap1.jpg"
        },
        {
          name: "CT ngoại khóa",
          href: "#",
          image: "/high-school/hoctap2.jpg"
        },
        {
          name: "Các cuộc thi",
          href: "#",
          image: "/high-school/hoctap3.jpg"
        },
        {
          name: "Phương pháp học tập",
          href: "#",
          image: "/high-school/hoctap4.jpg"
        },
      ]
    },
    {
      title:
        "Thống kê các trường các cấp của thuộc hệ sinh thái học tập, sáng tạo.",
      button: [],
      items: []
    },
    {
      title:
        "Nơi thảo luận các vấn đề về học tập, sáng tạo của học sinh, phụ huynh và giáo viên.",
      button: [],
      items: []
    },
    {
      title: "Thư viện số và các video bài giảng trong hệ sinh thái học tập sáng tạo cấp THPT.",
      button: [],
      items: [
        {
          name: "Thư viện số",
          href: "#",
          image: "/high-school/thuvien1.png"
        },
        {
          name: "Video bài giảng",
          href: "#",
          image: "/high-school/thuvien2.png"
        },
      ]
    }
  ];
  const dataMenuSelected = [
    {
      title: "Giới thiệu",
      href: "/high-school/about-us",
      child: []
    },
    {
      title: "Tin tức",
      href: "/high-school/news",
      child: []
    },
    {
      title: "Mô hình GD sáng tạo",
      href: "/high-school/creative-learning-education-model-highschool",
      child: []
    },
    {
      title: "Giáo viên",
      href: "/",
      child: []
    },
    {
      title: "Thống kê",
      href: "/",
      child: []
    },
    {
      title: "Đánh giá",
      href: "/",
      child: []
    },
    {
      title: "Xếp hạng",
      href: "/",
      child: []
    }
  ];
  return (
    <ClientHighSchoolLayout 
      dataMenu={dataMenu} 
      dataOfMenuChild={dataOfMenuChild} 
      dataMenuSelected={dataMenuSelected}
    >
      {children}
    </ClientHighSchoolLayout>
  );
}
