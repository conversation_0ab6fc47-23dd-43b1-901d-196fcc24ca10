"use client";

import Link from "next/link";
import Image from "next/image";

const AccountDeactivatedPage = () => {
  return (
    <div className="flex flex-col items-center justify-center min-h-svh px-4 py-10">
      <div className="max-w-md w-full text-center space-y-6">
        <div className="mb-6">
          <Image 
            src="/403.png" 
            alt="Tài khoản bị khóa" 
            width={120} 
            height={120} 
            className="mx-auto"
            onError={(e) => {
              const target = e.target as HTMLImageElement;
              target.src = "/img/signup/door.png";
            }}
          />
        </div>
        
        <h1 className="text-3xl font-bold text-[#1d2746]">Tài khoản đã bị khóa</h1>
        
        <p className="text-[#6b707f] mt-4">
          T<PERSON><PERSON> <PERSON><PERSON><PERSON>n của bạn đã bị vô hiệu hóa hoặc tạm khóa. Điều này có thể xảy ra vì một số lý do:
        </p>
        
        <ul className="text-left text-[#6b707f] mt-4 space-y-2">
          <li className="flex items-start">
            <span className="mr-2">•</span>
            <span>Vi phạm điều khoản dịch vụ của chúng tôi</span>
          </li>
          <li className="flex items-start">
            <span className="mr-2">•</span>
            <span>Hoạt động đáng ngờ đã được phát hiện</span>
          </li>
          <li className="flex items-start">
            <span className="mr-2">•</span>
            <span>Tài khoản đã bị báo cáo nhiều lần</span>
          </li>
        </ul>
        
        <p className="text-[#6b707f] mt-4">
          Nếu bạn tin rằng đây là lỗi, vui lòng liên hệ với bộ phận hỗ trợ để được giúp đỡ.
        </p>
        
        <div className="mt-8 flex flex-col space-y-3">
          <Link
            href="/contact"
            className="btn action_btn thm_btn inline-block rounded-[4px] bg-[#10b3d6] px-[28px] py-[14px] text-[16px] font-[400] text-white shadow-[0_20px_30px_0_rgba(12,118,142,0.24)] transition-colors transition-shadow duration-150 ease-in-out"
          >
            Liên hệ hỗ trợ
          </Link>
          
          <Link
            href="/"
            className="font-[500] text-[#10b3d6] hover:underline"
          >
            Quay lại trang chủ
          </Link>
        </div>
      </div>
    </div>
  );
};

export default AccountDeactivatedPage; 