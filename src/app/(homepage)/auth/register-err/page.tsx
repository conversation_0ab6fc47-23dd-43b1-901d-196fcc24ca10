"use client";


import Link from "next/link";
import Image from "next/image";
import { useState, useEffect, useRef } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { toast } from "react-toastify";

import authServices from "@/services/authServices/authServies";
import Loading from "@/components/ui/Loading";

const RegisterErrPage = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [isConfirmed, setIsConfirmed] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const router = useRouter();
  const searchParams = useSearchParams();
  const hasConfirmed = useRef(false);
  
  const token = searchParams.get("token");

  useEffect(() => {
    // Sử dụng biến cục bộ để tránh race conditions
    let isMounted = true;
    
    const confirmRegistration = async () => {
      // <PERSON><PERSON>m tra nếu đã xác nhận hoặc component đã unmount
      if (hasConfirmed.current || !isMounted) return;
      
      if (!token) {
        if (isMounted) {
          setError("Token không hợp lệ hoặc đã hết hạn");
          setIsLoading(false);
        }
        return;
      }

      try {
        hasConfirmed.current = true;
        await authServices.confirmRegistration({ token });
        
        if (isMounted) {
          setIsConfirmed(true);
          toast.success("Xác nhận đăng ký thành công!");
          setIsLoading(false);
        }
      } catch (e) {
        console.error('API error:', e);
        if (isMounted) {
          setError("Không thể xác nhận tài khoản. Token không hợp lệ hoặc đã hết hạn.");
          toast.error("Xác nhận đăng ký thất bại!");
          setIsLoading(false);
        }
      }
    };

    confirmRegistration();
    
    // Cleanup function để đánh dấu component đã unmount
    return () => {
      isMounted = false;
      hasConfirmed.current = true;
    };
  }, [token]);

  return (
    <div className="flex gap-8 h-svh">
      <div className="sign_left hidden md:block signin_left relative h-full w-full max-w-[600px] bg-[#10b3d6] px-[70px] pt-[100px]">
        <div
          style={{
            content: '""',
            left: 0,
            width: "100%",
            height: "100px",
            backgroundImage: "linear-gradient(180deg, #10b3d6, #1d2746)",
            position: "absolute",
            bottom: 0,
            zIndex: 0
          }}
        ></div>
        <h2 className="text-[36px] font-bold text-white">
          Xác nhận đăng ký tài khoản!
        </h2>
        <Image
          className="absolute left-0 top-0"
          src="/img/signup/top_ornamate.png"
          alt="top"
          width={200}
          height={200}
        />
        <Image
          className="absolute bottom-[6rem] right-0"
          src="/img/signup/bottom_ornamate.png"
          alt="bottom"
          width={200}
          height={200}
        />
        <Image
          className="absolute bottom-[3rem] right-1/2 translate-x-[50%]"
          src="/img/signup/door.png"
          alt="bottom"
          width={200}
          height={200}
        />
        <div className="round"></div>
      </div>
      <div className="sign_right px-4 signup_right flex w-full items-center justify-center">
        <div className="sign_inner signup_inner w-[500px] space-y-7">
          <div className="text-center">
            <h3 className="text-[26px] font-bold mb-3">Xác nhận Đăng ký</h3>
          </div>
          
          <div className="flex flex-col items-center space-y-6 py-4">
            {isLoading ? (
              <div className="flex flex-col items-center justify-center py-8">
                <Loading color="primary" size="lg" variant="spinner" text="Đang xác nhận..." />
              </div>
            ) : isConfirmed ? (
              <>
                <div className="rounded-full bg-green-100 p-6">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <h4 className="text-xl font-semibold text-gray-700">Xác nhận thành công!</h4>
                <p className="text-center text-gray-600">
                  Tài khoản của bạn đã được xác nhận thành công. Bạn có thể đăng nhập ngay bây giờ.
                </p>
                <button
                  onClick={() => router.push("/login")}
                  className="mt-4 rounded-md bg-[#10b3d6] px-6 py-3 text-white hover:bg-[#0e9cbf] focus:outline-none"
                >
                  Đăng nhập ngay
                </button>
              </>
            ) : (
              <>
                <div className="rounded-full bg-red-100 p-6">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </div>
                <h4 className="text-xl font-semibold text-gray-700">Xác nhận thất bại!</h4>
                <p className="text-center text-gray-600">
                  {error || "Đã có lỗi xảy ra khi xác nhận tài khoản. Vui lòng thử lại sau."}
                </p>
                <div className="flex gap-4">
                  <button
                    onClick={() => router.push("/register")}
                    className="mt-4 rounded-md border border-[#10b3d6] bg-white px-6 py-3 text-[#10b3d6] hover:bg-gray-50 focus:outline-none"
                  >
                    Đăng ký lại
                  </button>
                  <button
                    onClick={() => router.push("/login")}
                    className="mt-4 rounded-md bg-[#10b3d6] px-6 py-3 text-white hover:bg-[#0e9cbf] focus:outline-none"
                  >
                    Quay lại đăng nhập
                  </button>
                </div>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default RegisterErrPage; 