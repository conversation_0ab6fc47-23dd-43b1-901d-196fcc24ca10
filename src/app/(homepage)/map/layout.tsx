import "@/style/globals.css";
import ClientMapLayout from "@/components/ClientMapLayout";

export const metadata = {
  title: "<PERSON><PERSON>n đồ hệ sinh thái học tập, sáng tạo",
  description: "<PERSON><PERSON><PERSON> đồ hệ sinh thái học tập, sáng tạo"
};

export default function MapLayout({
  children
}: Readonly<{
  children: React.ReactNode;
}>) {
  const dataMenu = [
    { name: "Trang chủ", href: "/" },
    { name: "<PERSON><PERSON> sinh thái", href: "/forums" },
    { name: "<PERSON><PERSON><PERSON> đàn", href: "/forums" },
    { name: "<PERSON><PERSON><PERSON> đồ", href: "/map" },
    { name: "<PERSON><PERSON><PERSON> hệ", href: "/contact" },
  ];
  const dataOfMenuChild = [
    {
      title:
        "Tổng quan về hệ sinh thái học tập, sáng tạo.",
      button: [],
      items: []
    },
    {
      title:
        "<PERSON><PERSON> sinh thái học tập, sáng tạo.",
      button: [],
      items: []
    },
    {
      title:
        "<PERSON><PERSON><PERSON> đàn trao đổi, chia sẻ về học tập, sáng tạo.",
      button: [],
      items: []
    },
    {
      title:
        "Bản đồ hệ sinh thái học tập, sáng tạo.",
      button: [],
      items: []
    },
    {
      title:
        "Thông tin liên hệ với các đơn vị trong hệ sinh thái.",
      button: [],
      items: []
    },
  ];
  
  const dataMenuSelected = [
    {
      title: "Giới thiệu",
      href: "/map/about",
      child: []
    },
    {
      title: "Danh sách các đơn vị",
      href: "/map/list",
      child: []
    },
    {
      title: "Xem theo bản đồ",
      href: "/map",
      child: []
    }
  ];
  
  return (
    <ClientMapLayout 
      dataMenu={dataMenu} 
      dataOfMenuChild={dataOfMenuChild}
      dataMenuSelected={dataMenuSelected}
    >
      {children}
    </ClientMapLayout>
  );
}
