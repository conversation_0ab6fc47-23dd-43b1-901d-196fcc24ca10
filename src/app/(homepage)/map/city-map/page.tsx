import Image from "next/image";


export default function CityMapPage() {
  const dataCityMap = [
    {
      name: "Quận Ba Đình",
      area: "9,21",
      population: "226.315",
      ward: "14 phường",
    },
    {
      name: "Quận <PERSON>ầ<PERSON>ấ<PERSON>",
      area: "12,44",
      population: "294.235",
      ward: "8 phường",
    },
    {
      name: "Quận Hoàn Kiếm",
      area: "5,35",
      population: "141.687",
      ward: "18 phường",
    },
    {
      name: "Quận Hai Bà Trưng",
      area: "10,26",
      population: "304.101",
      ward: "18 phường",
    },
    {
      name: "<PERSON>u<PERSON><PERSON> Hoàng Mai",
      area: "40,19",
      population: "540.732",
      ward: "14 phường",
    },
    {
      name: "Quận Đống Đa",
      area: "9,95",
      population: "376.709",
      ward: "21 phường",
    },
    {
      name: "Quận Tây Hồ",
      area: "24,38",
      population: "167.851",
      ward: "8 phường",
    },
    {
      name: "<PERSON>uậ<PERSON> Xuân",
      area: "9,17",
      population: "293.292",
      ward: "11 phường",
    },
    {
      name: "Quận Bắc Từ Liêm",
      area: "45,24",
      population: "354.364",
      ward: "13 phường",
    },
    {
      name: "Quận Hà Đông",
      area: "49,64",
      population: "382.637",
      ward: "17 phường",
    },
    {
      name: "Quận Long Biên",
      area: "60,09",
      population: "337.982",
      ward: "14 phường",
    },
    {
      name: "Quận Nam Từ Liêm",
      area: "32,17",
      population: "282.444",
      ward: "10 phường",
    },
    {
      name: "Huyện Ba Vì",
      area: "421,8",
      population: "305.933",
      ward: "1 thị trấn, 30 xã",
    },
    {
      name: "Huyện Chương Mỹ",
      area: "237,48",
      population: "347.564",
      ward: "2 thị trấn, 30 xã ",
    },
    {
      name: "Huyện Đan Phượng",
      area: "77,83",
      population: "185.653",
      ward: "1 thị trấn, 15 xã ",
    },
    {
      name: "Huyện Đông Anh",
      area: "185,68",
      population: "437.308",
      ward: "1 thị trấn, 23 xã",
    },
    {
      name: "Huyện Gia Lâm",
      area: "116,64",
      population: "292.943",
      ward: "2 thị trấn, 20 xã ",
    },
    {
      name: "Huyện Hoài Đức",
      area: "84,92",
      population: "257.633",
      ward: "1 thị trấn, 19 xã ",
    },
    {
      name: "Huyện Mê Linh",
      area: "141,29",
      population: "241.633",
      ward: "2 thị trấn, 16 xã ",
    },
    {
      name: "Huyện Mỹ Đức",
      area: "226,31",
      population: "203.778",
      ward: "1 thị trấn, 21 xã ",
    },
    {
      name: "Huyện Phú Xuyên",
      area: "173,56",
      population: "229.847",
      ward: "2 thị trấn, 25 xã ",
    },
    {
      name: "Huyện Phúc Thọ",
      area: "118,5",
      population: "194.754",
      ward: "1 thị trấn, 20 xã ",
    },
    {
      name: "Huyện Quốc Oai",
      area: "151,22",
      population: "203.079",
      ward: "1 thị trấn, 20 xã ",
    },
    {
      name: "Huyện Sóc Sơn",
      area: "305,51",
      population: "357.652",
      ward: "1 thị trấn, 25 xã ",
    },
    {
      name: "Huyện Thạch Thất",
      area: "187,53",
      population: "223.844",
      ward: "1 thị trấn, 22 xã ",
    },
    {
      name: "Huyện Thanh Oai",
      area: "124,47",
      population: "227.541",
      ward: "1 thị trấn, 20 xã",
    },
    {
      name: "Huyện Thanh Trì",
      area: "63,49",
      population: "288.839",
      ward: "1 thị trấn, 15 xã",
    },
    {
      name: "Huyện Thường Tín",
      area: "130,13",
      population: "262.222",
      ward: "1 thị trấn, 28 xã",
    },
    {
      name: "Huyện Ứng Hòa",
      area: "188,24",
      population: "212.224",
      ward: "1 thị trấn, 28 xã",
    },
    {
      name: "Thị xã Sơn Tây",
      area: "117,2",
      population: "151.090",
      ward: "9 phường, 6 xã ",
    },
  ]
  return (
    <section className="container mx-auto mt-[28px] space-y-10 pb-[3rem] lg:space-y-20">
      <div className="space-y-[36px] ">
        <h3 className="text-center text-[36px] font-bold text-[#414B5B]">
          Bản đồ thành phố
        </h3>
        <div className="space-y-[28px]">
          <div className="xl:h-[1004px]">
            <Image className="h-full w-full object-cover" src="/img/map/map32.svg" width={1345} height={1004} alt="map" />
          </div>
          <div className="overflow-x-auto text-[12px] font-semibold">
            <table className="min-w-full table-auto border-collapse border border-black">
              <thead>
                <tr>
                  <th rowSpan={2} className="border font-bold border-black bg-[#FFE498] px-4 py-2">STT</th>
                  <th rowSpan={2} className="border font-bold row-span-2 border-black bg-[#92D050] px-4 py-2">Quận, huyện, thị xã</th>
                  <th rowSpan={2} className="border font-bold border-black bg-[#92D050] px-4 py-2">Diện tích 2022 (km²)</th>
                  <th rowSpan={2} className="border font-bold border-black bg-[#92D050] px-4 py-2">Dân số 2022 (người)</th>
                  <th rowSpan={2} className="border font-bold border-black bg-[#92D050] px-4 py-2">Số phường, xã</th>
                  <th colSpan={4} className="border font-bold border-black bg-[#92D050] px-4 py-2">Số trường trên địa bàn</th>
                </tr>
                <tr>
                  <th className="border font-bold border-black bg-[#92D050] px-4 py-2">MN</th>
                  <th className="border font-bold border-black bg-[#92D050] px-4 py-2">TH</th>
                  <th className="border font-bold border-black bg-[#92D050] px-4 py-2">THCS</th>
                  <th className="border font-bold border-black bg-[#92D050] px-4 py-2">THPT</th>
                </tr>
              </thead>
              <tbody>
                {dataCityMap.map((city, index) => (
                  <tr className="font-semibold text-[12px]" key={index}>
                    <td className="border border-black px-4 py-2 text-center">{index + 1}</td>
                    <td className="border border-black px-4 py-2">{city.name}</td>
                    <td className="border border-black px-4 py-2 text-center">{city.area}</td>
                    <td className="border border-black px-4 py-2 text-center">{city.population}</td>
                    <td className="border border-black px-4 py-2 text-center">{city.ward}</td>
                    <td className="border border-black px-4 py-2"></td>
                    <td className="border border-black px-4 py-2"></td>
                    <td className="border border-black px-4 py-2"></td>
                    <td className="border border-black px-4 py-2"></td>
                  </tr>
                ))}
              </tbody>
            </table>

          </div>
        </div>
      </div>
    </section>
  );
}
