'use client';
import React, { useState } from 'react';
import {toast} from "react-toastify";
import authServices from "@/services/authServices/authServies";

import {useRouter, useSearchParams} from "next/navigation";
import * as yup from "yup";
import {useForm} from "react-hook-form";
import {yupResolver} from "@hookform/resolvers/yup";
import Loading from "@/components/ui/Loading";
const schema = yup
    .object({
        password: yup
            .string()
            .min(6, "Mật khẩu phải có ít nhất 6 ký tự")
            .required("Mật khẩu là bắt buộc"),
        confirmPassword: yup
            .string()
            .oneOf([yup.ref("password")], "Mật khẩu xác nhận không khớp")
            .required("Xác nhận mật khẩu là bắt buộc")
    })
    .required();
type FormData = yup.InferType<typeof schema>;


const ResetPassword = () => {

    const [isLoading, setIsLoading] = useState(false);
    const [isReset, setIsReset] = useState(false);

    const router = useRouter();
    const searchParams = useSearchParams();
    const token = searchParams.get("token");

    const {
        register,
        handleSubmit,
        formState: { errors }
    } = useForm<FormData>({
        resolver: yupResolver(schema)
    });

    const onSubmit = async (data: FormData) => {
        try {
            setIsLoading(true);
            if (!token) {
                toast.error("Token không hợp lệ!");
                setIsLoading(false);
                return;
            }
            const payload = {
                password: data.password,
                token
            };
            await authServices.resetPassword(payload);

            setIsReset(true);
            toast.success("Đặt lại mật khẩu thành công!");
            router.push("/reset-password-success");
        } catch (e: any) {
            console.error(e.response.data.responseCode === "AUTH-ERR-408");
            if (e.response.data.responseCode === "AUTH-ERR-408") {
                toast.error("Mã không hợp lệ!");
            } else if (e.response.data.responseCode === "AUTH-ERR-407") {
                toast.error("Mã đã hết hạn!");
            } else {
                toast.error("Đã có lỗi xảy ra!");
            }
            setIsLoading(false);
        }
    };

    return (
        <div className="flex flex-col py-6">
            <div className="flex items-center justify-center mb-6">
                <div className="w-16 h-16 rounded-full bg-blue-100 flex items-center justify-center">
                    <i className="ri-lock-password-line text-primary text-3xl"></i>
                </div>
            </div>
            <h2 className="text-2xl font-bold text-gray-800 text-center mb-6">Đổi mật khẩu mới</h2>
            <p className="text-center text-gray-600 mb-8">Vui lòng nhập mật khẩu mới của bạn</p>
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                <div>
                    <label htmlFor="new-password" className="block text-sm font-medium text-gray-700 mb-1">Mật khẩu
                        mới</label>
                    <input
                        {...register("password")}
                        type="password"
                        id="new-password"
                        className="form-input w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:border-primary transition-colors text-sm"
                        placeholder="Nhập mật khẩu mới"
                        required
                    />
                    {errors.password && (
                        <p className="mt-1 text-sm text-red-500">
                            {errors.password.message}
                        </p>
                    )}
                </div>
                <div>
                    <label htmlFor="confirm-password" className="block text-sm font-medium text-gray-700 mb-1">Xác nhận
                        mật khẩu mới</label>
                    <input
                        {...register("confirmPassword")}
                        type="password"
                        id="confirm-password"
                        className="form-input w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:border-primary transition-colors text-sm"
                        placeholder="Nhập lại mật khẩu mới"
                        required
                    />
                    {errors.confirmPassword && (
                        <p className="mt-1 text-sm text-red-500">
                            {errors.confirmPassword.message}
                        </p>
                    )}
                </div>
                <div>
                    <button
                        type="submit"
                        disabled={isLoading}
                        className={`w-full font-medium py-3 px-4 rounded-md transition-colors !rounded-button whitespace-nowrap ${
                            isLoading ? 'bg-gray-200 text-gray-400' : 'bg-primary text-white'
                        }`}
                    >
                        {isLoading ? (
                            <Loading />
                        ) : (
                            <div>Xác Nhận</div>
                        )}
                    </button>
                </div>
            </form>
        </div>
    );
};

export default ResetPassword;