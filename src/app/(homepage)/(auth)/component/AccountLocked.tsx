'use client';
import {RiCloseLine} from "react-icons/ri";
const AccountLocked = () => {

    return (
            <div
                className="flex flex-col py-8 items-center justify-center success-animation"
            >
                <div
                    className="w-20 h-20 rounded-full bg-red-100 flex items-center justify-center mb-8 success-icon"
                >
                    <RiCloseLine className="text-red-500 text-4xl" />
                </div>
                <h2 className="text-2xl font-bold text-gray-800 text-center mb-4">
                    Tài khoản của bạn đã bị khóa
                </h2>
                <p className="text-center text-gray-600 mb-8">
                    Liên hệ quản trị viên để mở khóa tài khoản.
                </p>
                <div className="w-full max-w-xs">
                    <a
                        href="/login"
                        className="w-full flex justify-center items-center bg-primary hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-md transition-colors !rounded-button whitespace-nowrap"
                    >
                        <PERSON><PERSON><PERSON> nhập
                    </a>
                </div>
            </div>
    );
};

export default AccountLocked;