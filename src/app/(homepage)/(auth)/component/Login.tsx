"use client";
import React, {useState} from 'react';
import {But<PERSON>} from "@/components/ui/button";
import {useRouter} from "next/navigation";
import {useAuth} from "@/context/AuthContext";
import {useForm} from "react-hook-form";
import {yupResolver} from "@hookform/resolvers/yup";
import authServices from "@/services/authServices/authServies";
import {setCookie} from "cookies-next";
import {toast} from "react-toastify";
import * as yup from "yup";
import Loading from "@/components/ui/Loading";
const schema = yup.object({
    email: yup.string().required("Email là bắt buộc"),
    password: yup.string().required("Mật khẩu là bắt buộc")
}).required();

type FormData = yup.InferType<typeof schema>;

const Login = () => {
    const [isLoading, setIsLoading] = useState(false);
    const router = useRouter();
    const { login } = useAuth();


    const [showPassword , setShowPassword] = useState(false)
    const {
        register,
        handleSubmit,
        formState: { errors }
    } = useForm<FormData>({
        resolver: yupResolver(schema)
    });

    const redirectGoogle = () => {
        const baseUrl = (process.env.NEXT_PUBLIC_SERVER_URL || "").replace("/api/v1", "");
        const url = `${baseUrl}/oauth2/authorization/google`;
        window.location.href = url;
    };

    const onSubmit = async (data: FormData) => {
        try {
            setIsLoading(true);
            const payload = {
                username: data.email,
                password: data.password
            }

            const response = await authServices.login(payload);
            // Đăng nhập thành công, lưu thông tin user vào context
            if (response?.data) {
                // Lưu token vào cookie
                if (response.data.data.token) {
                    setCookie("auth_token", response.data.data.token, {
                        maxAge: 30 * 24 * 60 * 60,
                        path: '/'
                    });
                }

                login({
                    id: response.data.id || "",
                    username: response.data.username || data.email,
                    firstName: response.data.firstName || "",
                    lastName: response.data.lastName || ""
                });
                router.push("/");
            }

        } catch (e: any) {
            console.log(e);
            toast.error("Đăng nhập thất bại. Vui lòng kiểm tra lại thông tin!");
        } finally {
            setIsLoading(false);
        }
    };


    return (
        <div>
            <div className="flex justify-center mb-8">
                <div className="inline-flex rounded-full p-1 bg-gray-100">
                    <button onClick={() => router.push("/login")}
                        id="login-tab"
                            type={"button"}
                        className="px-6 py-2 rounded-full bg-primary text-white font-medium text-sm transition-all !rounded-button whitespace-nowrap"
                    >
                        Đăng nhập
                    </button>
                    <button onClick={() => router.push("/register")}
                        id="register-tab"
                            type={"button"}
                        className="px-6 py-2 rounded-full text-gray-600 font-medium text-sm transition-all hover:bg-gray-200 !rounded-button whitespace-nowrap"
                    >
                        Đăng ký
                    </button>
                </div>
            </div>
            <div id="login-form" className="space-y-6">
                <form
                    onSubmit={(e) => {
                        e.preventDefault();
                        handleSubmit(onSubmit)(e);
                    }}
                >
                    <h2 className="text-2xl font-bold text-gray-800 text-center">
                        Đăng nhập vào tài khoản
                    </h2>
                    <div className="space-y-4 mt-5 mb-3">
                        <div>
                            <label
                                htmlFor="email"
                                className="block text-sm font-medium text-gray-700 mb-1"
                            >Email hoặc tên đăng nhập</label
                            >
                            <div className="relative">
                                <div
                                    className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
                                >
                                    <i className="ri-mail-line text-gray-400"></i>
                                </div>
                                <input
                                    {...register("email")}
                                    type="email"
                                    id="email"
                                    className="form-input w-full pl-10 pr-3 py-3 border border-gray-300 rounded-md focus:outline-none !rounded-button"
                                    placeholder="Nhập email hoặc tên đăng nhập"
                                />
                            </div>
                        </div>
                        <div>
                            <div className="flex items-center justify-between mb-1">
                                <label
                                    htmlFor="password"
                                    className="block text-sm font-medium text-gray-700"
                                >Mật khẩu</label
                                >
                                <button
                                    type={"button"}
                                    onClick={() => router.push("/forgot-password")}
                                    id="forgot-password-link"
                                    className="text-sm text-primary hover:text-blue-700 "
                                >
                                    Quên mật khẩu?
                                </button>
                            </div>
                            <div className="relative">
                                <div
                                    className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
                                >
                                    <i className="ri-lock-line text-gray-400"></i>
                                </div>
                                <input
                                    {...register("password")}
                                    type={showPassword ? "text" : "password"}
                                    id="password"
                                    className="form-input w-full pl-10 pr-10 py-3 border border-gray-300 rounded-md focus:outline-none !rounded-button"
                                    placeholder="Nhập mật khẩu"
                                />
                                <div
                                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                                >
                                    <button
                                        onClick={()=> setShowPassword(!showPassword)}
                                        type="button"
                                        id="toggle-password"
                                        className="text-gray-400 hover:text-gray-600 focus:outline-none"
                                    >
                                        <i className={!showPassword ? "ri-eye-off-line" : "ri-eye-line"}></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <Button
                        disabled={isLoading}
                        type="submit"
                        className="mt-5 w-full bg-primary hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-md transition-colors !rounded-button whitespace-nowrap"
                    >
                        {isLoading ? (
                            <Loading />
                        ) : (
                            <div>Đăng nhập</div>
                        )}
                    </Button>
                    <div className="relative flex items-center justify-center mt-3 mb-3">
                        <div className="border-t border-gray-300 flex-grow"></div>
                        <span className="mx-4 text-sm text-gray-500">Hoặc</span>
                        <div className="border-t border-gray-300 flex-grow"></div>
                    </div>
                    <Button
                        type={"button"}
                        onClick={()=>{
                            if (!isLoading){
                                redirectGoogle()
                            }
                        }}
                        className="bg-white w-full flex justify-center items-center py-2 px-4 border border-gray-300 rounded-md  !rounded-button whitespace-nowrap hover:bg-blue-700 hover:text-white"
                    >
                        <i className="ri-google-fill text-red-500 mr-2"></i>
                        <span  className="text-sm">Đăng nhập với Google</span>
                    </Button>
                </form>
            </div>

        </div>
    );
};

export default Login;