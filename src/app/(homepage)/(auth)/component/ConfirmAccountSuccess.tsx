'use client';
import { RiCheckLine } from "react-icons/ri";
const ConfirmAccountSuccess = () => {

    return (
            <div
                className="flex flex-col py-8 items-center justify-center success-animation"
            >
                <div
                    className="w-20 h-20 rounded-full bg-green-100 flex items-center justify-center mb-8 success-icon"
                >
                    <RiCheckLine className="text-green-500 text-4xl" />
                </div>
                <h2 className="text-2xl font-bold text-gray-800 text-center mb-4">
                    Xá<PERSON> thực tài khoản thành công
                </h2>
                <p className="text-center text-gray-600 mb-8">
                    Tài khoản của bạn đã được xác thực, vui lòng đăng nhập.
                </p>
                <div className="w-full max-w-xs">
                    <a
                        href="/login"
                        className="w-full flex justify-center items-center bg-primary hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-md transition-colors !rounded-button whitespace-nowrap"
                    >
                        Đ<PERSON>ng nhập
                    </a>
                </div>
            </div>
    );
};

export default ConfirmAccountSuccess;