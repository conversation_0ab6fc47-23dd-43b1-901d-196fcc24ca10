"use client";
import React, {useState} from 'react';
import {useRouter} from "next/navigation";
import {useForm} from "react-hook-form";
import {yupResolver} from "@hookform/resolvers/yup";
import authServices from "@/services/authServices/authServies";
import {toast} from "react-toastify";
import * as yup from "yup";
import {Button} from "@/components/ui/button";
import Loading from "@/components/ui/Loading";

const schema = yup.object({
    email: yup.string().email("Email không hợp lệ").required("Email là bắt buộc"),
}).required();

type FormData = yup.InferType<typeof schema>;

const ForgotPassword = () => {
    const [isLoading, setIsLoading] = useState(false);
    const [isSent, setIsSent] = useState(false);
    const router = useRouter();

    const {
        register,
        handleSubmit,
        formState: { errors }
    } = useForm<FormData>({
        resolver: yupResolver(schema)
    });

    const onSubmit = async (data: FormData) => {
        try {
            setIsLoading(true);
            const payload = {
                username: data.email
            }
            await authServices.forgotPassword(payload);
            toast.success("Yêu cầu đặt lại mật khẩu đã được gửi tới email của bạn!");
            router.push("/forgot-password-success");
            setIsSent(true);
        } catch (e) {
            console.log(e);
            toast.error("Không thể gửi yêu cầu đặt lại mật khẩu. Vui lòng thử lại!");
        } finally {
            setIsLoading(false);
        }
    };
    return (
        <div
        >
            <form
                onSubmit={(e) => {
                    e.preventDefault();
                    handleSubmit(onSubmit)(e);
                }}
            >
                <div>
                    <h2 className="text-2xl font-bold text-gray-800 text-center mb-4">
                        Khôi phục mật khẩu
                    </h2>
                    <div className={"w-full"}>
                        <label
                            htmlFor="recovery-email"
                            className="block text-sm font-medium text-gray-700 mb-1"
                        >Email</label
                        >
                        <div className="relative">
                            <div
                                className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
                            >
                                <i className="ri-mail-line text-gray-400"></i>
                            </div>
                            <input
                                {...register("email")}
                                type="email"
                                id="recovery-email"
                                className="form-input w-full pl-10 pr-3 py-3 border border-gray-300 rounded-md focus:outline-none !rounded-button"
                                placeholder="Nhập địa chỉ email"
                            />
                            {errors.email && (<div className="text-red-500 text-sm mt-1">{errors.email.message}</div>)}
                        </div>
                    </div>
                </div>
                {/*<div>*/}
                {/*    <h2 className="text-2xl font-bold text-gray-800 text-center mb-4">*/}
                {/*        Đã gửi email khôi phục*/}
                {/*    </h2>*/}
                {/*    <p className="text-center text-gray-600 mb-2">*/}
                {/*        Chúng tôi đã gửi hướng dẫn khôi phục mật khẩu đến email của*/}
                {/*        bạn*/}
                {/*    </p>*/}
                {/*    <div className="bg-blue-50 px-6 py-3 rounded-md mb-6 text-center">*/}
                {/*        <span className="text-blue-800 font-medium"*/}
                {/*        ><EMAIL></span*/}
                {/*        >*/}
                {/*    </div>*/}
                {/*    <p className="text-center text-gray-600 mb-8">*/}
                {/*        Vui lòng kiểm tra hòm thư của bạn và làm theo hướng dẫn để*/}
                {/*        đặt lại mật khẩu.*/}
                {/*    </p>*/}
                {/* */}
                {/*</div>*/}
                <Button
                    type="submit"
                    className="mt-5 w-full bg-blue-600 text-white font-medium py-3 px-4 rounded-md transition-colors hover:bg-blue-700 focus:outline-none"
                >
                    {isLoading ? (
                        <Loading />
                    ) : (
                        <div>Gửi email</div>
                    )}
                </Button>
                <div className="text-left mt-5">
                    <a
                        href="/login"
                        id="back-to-login"
                        className="text-primary hover:text-blue-700 font-medium"
                    >Quay lại đăng nhập</a
                    >
                </div>
            </form>
        </div>
    );
};

export default ForgotPassword;