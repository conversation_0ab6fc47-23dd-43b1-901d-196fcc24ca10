'use client';
import {RiCloseLine} from "react-icons/ri";
const LoginFail = () => {

    return (
            <div
                className="flex flex-col py-8 items-center justify-center success-animation"
            >
                <div
                    className="w-20 h-20 rounded-full bg-red-100 flex items-center justify-center mb-8 success-icon"
                >
                    <RiCloseLine className="text-red-500 text-4xl" />
                </div>
                <h2 className="text-2xl font-bold text-gray-800 text-center mb-4">
                    Đã có lỗi xảy ra trong quá trình xác thực tài khoản
                </h2>
                <p className="text-center text-gray-600 mb-8">
                    Liên hệ quản trị viên để được giúp đỡ.
                </p>
                <div className="w-full max-w-xs">
                    <a
                        href="/login"
                        className="w-full flex justify-center items-center bg-primary hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-md transition-colors !rounded-button whitespace-nowrap"
                    >
                        <PERSON><PERSON><PERSON> nhập
                    </a>
                </div>
            </div>
    );
};

export default LoginFail;