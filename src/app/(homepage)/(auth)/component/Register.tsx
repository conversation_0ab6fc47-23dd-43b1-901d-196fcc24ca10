"use client";

import React, {useState} from 'react';
import * as yup from "yup";
import {RegisterFormData} from "@/app/(homepage)/(auth)/register/types/RegisterFormData";
import {useRouter} from "next/navigation";
import {useForm} from "react-hook-form";
import {yupResolver} from "@hookform/resolvers/yup";
import authServices from "@/services/authServices/authServies";
import {toast} from "react-toastify";
import {Button} from "@/components/ui/button";
import Loading from "@/components/ui/Loading";
const schema: yup.ObjectSchema<Omit<RegisterFormData, "acceptTerms">> = yup.object({
    firstName: yup.string().required(" Tên là bắt buộc"),
    lastName: yup.string().required(" Họ là bắt buộc"),
    email: yup.string().email("Email không hợp lệ").required("<PERSON><PERSON> là bắt buộc"),
    password: yup
        .string()
        .min(6, "Mật khẩu phải có ít nhất 6 ký tự")
        .required("Mật khẩu là bắt buộc"),
    confirmPassword: yup
        .string()
        .oneOf([yup.ref("password")], "Mật khẩu xác nhận không khớp")
        .required("Xác nhận mật khẩu là bắt buộc"),
}).required();

const Register = () => {
    const [showPassword , setShowPassword] = useState(false)
    const [showPasswordConfirm , setShowPasswordConfirm] = useState(false)
    const [isLoading, setIsLoading] = useState(false);
    const router = useRouter();

    const {
        register,
        handleSubmit,
        formState: { errors }
    } = useForm<Omit<RegisterFormData, "acceptTerms">>({
        resolver: yupResolver(schema),
        defaultValues: {
            firstName: "",
            lastName: "",
            email: "",
            password: "",
            confirmPassword: "",
        }
    });

    const onSubmit = async (data: RegisterFormData) => {
        try {
            setIsLoading(true);
            const payload = {
                username: data.email,
                password: data.password,
                firstName: data.firstName,
                lastName: data.lastName
            };

            const response = await authServices.register(payload);
            toast.success("Đăng ký tài khoản thành công!");
            router.push("/register-success");
        } catch (e: any) {
            console.log(e);
            if (e.response?.data?.responseCode === "AUTH-ERR-405") {
                toast.warning("Email này đã được sử dụng. Vui lòng sử dụng email khác!");
            } else {
                toast.error("Đăng ký tài khoản thất bại. Vui lòng thử lại!");
            }
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <div id="register-form" className="space-y-6">
            <form  onSubmit={(e) => {
                e.preventDefault();
                handleSubmit(onSubmit)(e);
            }}>
                <div className="flex justify-center mb-8">
                    <div className="inline-flex rounded-full p-1 bg-gray-100">
                        <button onClick={() => router.push("/login")}
                                type={"button"}
                                id="login-tab"
                                className="px-6 py-2 rounded-full text-gray-600 font-medium text-sm transition-all hover:bg-gray-200 !rounded-button whitespace-nowrap"
                        >
                            Đăng nhập
                        </button>
                        <button onClick={() => router.push("/register")}
                                type={"button"}
                                id="register-tab"
                                className="px-6 py-2 rounded-full bg-primary text-white font-medium text-sm transition-all !rounded-button whitespace-nowrap"
                        >
                            Đăng ký
                        </button>
                    </div>
                </div>
                <h2 className="text-2xl font-bold  text-gray-800 text-center">
                    Tạo tài khoản mới
                </h2>
                <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4 mt-5 " >
                        <div>
                            <label
                                htmlFor="register-lastname"
                                className="block text-sm font-medium text-gray-700 mb-1"
                            >Họ</label
                            >
                            <div className="relative">
                                <div
                                    className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
                                >
                                    <i className="ri-user-line text-gray-400"></i>
                                </div>
                                <input
                                    {...register("lastName")}
                                    type="text"
                                    id="register-lastname"
                                    className="form-input w-full pl-10 pr-3 py-3 border border-gray-300 rounded-md focus:outline-none !rounded-button"
                                    placeholder="Nhập họ"
                                />
                                {errors.lastName && (
                                    <p className="text-red-500 text-sm mt-1">{errors.lastName.message}</p>
                                )}
                            </div>
                        </div>
                        <div>
                            <label
                                htmlFor="register-firstname"
                                className="block text-sm font-medium text-gray-700 mb-1"
                            >Tên</label
                            >
                            <div className="relative">
                                <div
                                    className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
                                >
                                    <i className="ri-user-line text-gray-400"></i>
                                </div>
                                <input
                                    {...register("firstName")}

                                    type="text"
                                    id="register-firstname"
                                    className="form-input w-full pl-10 pr-3 py-3 border border-gray-300 rounded-md focus:outline-none !rounded-button"
                                    placeholder="Nhập tên"
                                />
                                {errors.firstName && (
                                    <p className="text-red-500 text-sm mt-1">{errors.firstName.message}</p>
                                )}
                            </div>
                        </div>
                    </div>
                    <div>
                        <label
                            htmlFor="register-email"
                            className="block text-sm font-medium text-gray-700 mb-1"
                        >Email</label
                        >
                        <div className="relative">
                            <div
                                className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
                            >
                                <i className="ri-mail-line text-gray-400"></i>
                            </div>
                            <input
                                {...register("email")}
                                type="email"
                                id="register-email"
                                className="form-input w-full pl-10 pr-3 py-3 border border-gray-300 rounded-md focus:outline-none !rounded-button"
                                placeholder="Nhập địa chỉ email"
                            />
                            {errors.email && (
                                <p className="text-red-500 text-sm mt-1">{errors.email.message}</p>
                            )}
                        </div>
                    </div>
                    <div>
                        <label
                            htmlFor="register-password"
                            className="block text-sm font-medium text-gray-700 mb-1"
                        >Mật khẩu</label
                        >
                        <div className="relative">
                            <div
                                className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
                            >
                                <i className="ri-lock-line text-gray-400"></i>
                            </div>
                            <input
                                {...register("password")}
                                type={showPassword ? "text" : "password"}
                                id="register-password"
                                className="form-input w-full pl-10 pr-10 py-3 border border-gray-300 rounded-md focus:outline-none !rounded-button"
                                placeholder="Tạo mật khẩu"
                            />
                            {errors.password && (
                                <p className="text-red-500 text-sm mt-1">{errors.password.message}</p>
                            )}
                            <div
                                className="absolute inset-y-0 right-0 pr-3 flex items-center"
                            >
                                <button
                                    onClick={()=> setShowPassword(!showPassword)}
                                    type="button"
                                    className="toggle-password text-gray-400 hover:text-gray-600 focus:outline-none"
                                >
                                    <i className={!showPassword ? "ri-eye-off-line" : "ri-eye-line"}></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div>
                        <label
                            htmlFor="register-confirm-password"
                            className="block text-sm font-medium text-gray-700 mb-1"
                        >Xác nhận mật khẩu</label
                        >
                        <div className="relative">
                            <div
                                className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
                            >
                                <i className={ "ri-eye-line"}></i>
                            </div>
                            <input
                                {...register("confirmPassword")}
                                type={showPasswordConfirm ? "text" : "password"}
                                id="register-confirm-password"
                                className="form-input w-full pl-10 pr-10 py-3 border border-gray-300 rounded-md focus:outline-none !rounded-button"
                                placeholder="Nhập lại mật khẩu"
                            />
                            {errors.confirmPassword && (
                                <p className="text-red-500 text-sm mt-1">{errors.confirmPassword.message}</p>
                            )}
                            <div
                                className="absolute inset-y-0 right-0 pr-3 flex items-center"
                            >
                                <button
                                    onClick={()=> setShowPasswordConfirm(!showPasswordConfirm)}
                                    type="button"
                                    className="toggle-password text-gray-400 hover:text-gray-600 focus:outline-none"
                                >
                                    <i className={!showPasswordConfirm ? "ri-eye-off-line" : "ri-eye-line"}></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <Button
                    disabled={isLoading}
                    type="submit"
                    className=" mt-5  w-full bg-primary hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-md transition-colors !rounded-button whitespace-nowrap"
                >
                    {isLoading ? (
                        <Loading />
                    ) : (
                        <div>Đăng ký</div>
                    )}
                </Button>
            </form>
        </div>
    );
};

export default Register;