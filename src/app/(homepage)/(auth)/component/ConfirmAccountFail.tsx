'use client';
import {RiCloseLine} from "react-icons/ri";
const ConfirmAccountFail = () => {

    return (
            <div
                className="flex flex-col py-8 items-center justify-center success-animation"
            >
                <div
                    className="w-20 h-20 rounded-full bg-red-100 flex items-center justify-center mb-8 success-icon"
                >
                    <RiCloseLine className="text-red-500 text-4xl" />
                </div>
                <h2 className="text-2xl font-bold text-gray-800 text-center mb-4">
                    <PERSON><PERSON><PERSON> thực tài khoản thất bại
                </h2>
                <p className="text-center text-gray-600 mb-8">
                    Đã có lỗi xảy ra trong quá trình xác thực, vui lòng đăng ký lại.
                </p>
                <div className="w-full max-w-xs">
                    <a
                        href="/register"
                        className="w-full flex justify-center items-center bg-primary hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-md transition-colors !rounded-button whitespace-nowrap"
                    >
                        <PERSON><PERSON><PERSON> ký
                    </a>
                </div>
            </div>
    );
};

export default ConfirmAccountFail;