"use client";
import React from "react";
import { RiUserLine, RiHome3Line, RiBuildingLine } from "react-icons/ri";
const Layout = ({ children }: { children: React.ReactNode }) => {
  return (
    <div
      className="relative min-h-screen w-full bg-cover bg-center bg-no-repeat flex flex-col"
      style={{
        backgroundImage:
          'url("https://readdy.ai/api/search-image?query=Beautiful%252520night%252520cityscape%252520of%252520Hanoi%252520with%252520West%252520Lake%252520and%252520city%252520skyline%252C%252520aerial%252520view%252520of%252520urban%252520landscape%252520with%252520lights%252520reflecting%252520on%252520water%252520surface%252C%252520modern%252520buildings%252520and%252520traditional%252520architecture%252C%252520peaceful%252520atmosphere%252520with%252520soft%252520lighting&width=1920&height=1080&seq=auth-bg-1&orientation=landscape")',
      }}
    >
      <div className="absolute left-4 top-4">
        <a
          href="/"
          className="!rounded-button inline-flex items-center whitespace-nowrap rounded-full bg-white bg-opacity-90 px-4 py-2 text-gray-700 shadow-sm transition-all hover:bg-opacity-100"
        >
          <i className="ri-arrow-left-line mr-2"></i>
          Quay lại trang chủ
        </a>
      </div>
      <div className="auth-container flex flex-grow items-center justify-center py-16">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-5xl overflow-hidden rounded-lg bg-white bg-opacity-95 shadow-xl">
            <div className="flex flex-col md:flex-row">
              <div className="flex flex-col justify-center bg-gradient-to-br from-blue-600 to-indigo-800 p-8 text-white md:w-1/2">
                <h2 className="mb-6 text-3xl font-bold">
                  Hệ sinh thái học tập sáng tạo Hà Nội
                </h2>
                <p className="mb-8">
                  Kết nối, thúc đẩy sự phát triển bền vững của mạng lưới giáo
                  dục sáng tạo hướng tới xây dựng Hà Nội - Thành phố sáng tạo.
                </p>
                <div className="flex flex-col space-y-4">
                  <div className="flex items-center">
                    <div className="mr-3 flex h-10 w-10 items-center justify-center rounded-full bg-white bg-opacity-20">
                      <RiUserLine className="text-2xl text-white" />
                    </div>
                    <div>
                      <h3 className="font-medium">Dành cho học sinh</h3>
                      <p className="text-sm text-blue-100">
                        Truy cập tài nguyên học tập sáng tạo
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <div className="mr-3 flex h-10 w-10 items-center justify-center rounded-full bg-white bg-opacity-20">
                      <RiHome3Line className="text-2xl text-white" />
                    </div>
                    <div>
                      <h3 className="font-medium">Dành cho giáo viên</h3>
                      <p className="text-sm text-blue-100">
                        Quản lý lớp học và nội dung giảng dạy
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <div className="mr-3 flex h-10 w-10 items-center justify-center rounded-full bg-white bg-opacity-20">
                      <RiBuildingLine className="text-2xl text-white" />
                    </div>
                    <div>
                      <h3 className="font-medium">Dành cho trường học</h3>
                      <p className="text-sm text-blue-100">
                        Quản lý hệ thống giáo dục sáng tạo
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div className="p-8 md:w-1/2">{children}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Layout;
