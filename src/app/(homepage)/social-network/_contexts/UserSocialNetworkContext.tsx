"use client";

import useUploadFile from "@/hook/useUploadFile";
import socialNetworkServices from "@/services/social-network/socialNetworkServices";
import { UserSocialNetwork } from "@/services/social-network/types/types";
import React, { createContext, useContext, useEffect, useState } from "react";

// Đ<PERSON>nh nghĩa kiểu dữ liệu cho UserData
export type UserData = Partial<UserSocialNetwork> & {
  avatarUrl?: string;
  avatarFile?: File;
  coverUrl?: string;
  coverFile?: File;
};

// Đ<PERSON>nh nghĩa kiểu dữ liệu cho context
interface UserSocialNetworkContextType {
  userData: UserData | null;
  isLoading: boolean;
  error: string | null;
  fetchUserData: () => Promise<void>;
  updateUserData: (data: UserData) => Promise<void>;
  setUserData: (data: UserData) => void;
}

// Tạo context
const UserSocialNetworkContext = createContext<UserSocialNetworkContextType | undefined>(undefined);

// Hook để sử dụng context
export const useUserSocialNetwork = () => {
  const context = useContext(UserSocialNetworkContext);
  if (context === undefined) {
    throw new Error("useUserSocialNetwork phải được sử dụng trong UserSocialNetworkProvider");
  }
  return context;
};

// Provider component
export function UserSocialNetworkProvider({ children }: { children: React.ReactNode }) {
  const [userData, setUserDataState] = useState<UserData | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const { viewFile } = useUploadFile();

  // Hàm lấy thông tin người dùng từ API
  const fetchUserData = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      // Gọi API lấy thông tin người dùng
      const response = await socialNetworkServices.getUserSocialNetwork();
      const userSocialNetwork: UserSocialNetwork = response.data.data;
      
      // Khởi tạo biến lưu URL avatar và cover
      let avatarUrl = "";
      let coverUrl = "";
      
      // Lấy URL cho avatar
      if (userSocialNetwork.thumbnail) {
        try {
          const thumbnailUrl = await viewFile(
            userSocialNetwork.thumbnail,
            "social-network"
          );
          avatarUrl = thumbnailUrl as string;
        } catch (error) {
          console.error("Lỗi khi lấy URL cho avatar:", error);
        }
      }

      // Lấy URL cho cover photo
      if (userSocialNetwork.background) {
        try {
          const backgroundUrl = await viewFile(
            userSocialNetwork.background,
            "social-network"
          );
          coverUrl = backgroundUrl as string;
        } catch (error) {
          console.error("Lỗi khi lấy URL cho cover photo:", error);
        }
      }
      
      // Cập nhật dữ liệu người dùng
      setUserDataState({
        ...userSocialNetwork,
        avatarUrl,
        coverUrl
      });
    } catch (error) {
      console.error("Lỗi khi lấy dữ liệu người dùng:", error);
      setError("Không thể lấy thông tin người dùng. Vui lòng thử lại sau.");
    } finally {
      setIsLoading(false);
    }
  };

  // Hàm cập nhật thông tin người dùng lên API
  const updateUserData = async (data: UserData) => {
    try {
      setIsLoading(true);
      setError(null);
      
      // Tạo đối tượng dữ liệu để gửi lên API
      const updateData: Partial<UserSocialNetwork> = { ...data };
      
      // Loại bỏ các trường không thuộc về API
      delete (updateData as any).avatarUrl;
      delete (updateData as any).avatarFile;
      delete (updateData as any).coverUrl;
      delete (updateData as any).coverFile;
      
      // Gọi API cập nhật thông tin người dùng
      await socialNetworkServices.updateUserSocialNetwork(updateData as UserSocialNetwork);
      
      // Cập nhật lại state
      setUserDataState(prevData => ({ ...prevData, ...data }));
    } catch (error) {
      console.error("Lỗi khi cập nhật dữ liệu người dùng:", error);
      setError("Không thể cập nhật thông tin người dùng. Vui lòng thử lại sau.");
    } finally {
      setIsLoading(false);
    }
  };

  // Hàm cập nhật trực tiếp vào state (không gọi API)
  const setUserData = (data: UserData) => {
    setUserDataState(prevData => ({ ...prevData, ...data }));
  };

  // Gọi API khi component được mount
  useEffect(() => {
    fetchUserData();
  }, []);

  // Giá trị context
  const value = {
    userData,
    isLoading,
    error,
    fetchUserData,
    updateUserData,
    setUserData
  };

  return (
    <UserSocialNetworkContext.Provider value={value}>
      {children}
    </UserSocialNetworkContext.Provider>
  );
}
