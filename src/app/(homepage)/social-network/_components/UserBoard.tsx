'use client'

import DefaultAvatar from "@/components/ui/DefaultAvatar";
import useUploadFile from "@/hook/useUploadFile";
import friendServices from "@/services/social-network/friendServices";
import socialNetworkServices from "@/services/social-network/socialNetworkServices";
import Image from "next/image";
import { useRouter, useSearchParams } from "next/navigation";
import { useCallback, useEffect, useRef, useState } from "react";
import { FaBriefcase, FaCalendarAlt, FaEnvelope, FaUserCheck, FaUserClock, FaUserFriends, FaUserPlus, FaUserTimes, FaUsers } from "react-icons/fa";
import { toast } from "react-toastify";
import { formatDateForDisplay } from "../utils/dateUtils";

// Helper function để hiển thị trạng thái kết bạn
const getFriendStatusDisplay = (status?: string) => {
  switch (status) {
    case "ACCEPTED":
      return {
        status,
        text: "Bạn bè",
        color: "text-emerald-700",
        bgColor: "bg-emerald-50",
        borderColor: "border-emerald-200",
        icon: FaUsers
      };
    case "REQUESTED":
      return {
        status,
        text: "Đã gửi yêu cầu",
        color: "text-amber-700",
        bgColor: "bg-amber-50",
        borderColor: "border-amber-200",
        icon: FaUserClock
      };
    case "PENDING":
      return {
        status,
        text: "Nhận được yêu cầu",
        color: "text-blue-700",
        bgColor: "bg-blue-50",
        borderColor: "border-blue-200",
        icon: FaUserPlus
      };
    case "NOT_FRIEND":
      return {
        status,
        text: "Chưa kết bạn",
        color: "text-slate-600",
        bgColor: "bg-slate-50",
        borderColor: "border-slate-200",
        icon: FaUserPlus
      };
    default:
      return null;
  }
};

// Định nghĩa kiểu dữ liệu cho người dùng
interface User {
  firstName: string;
  lastName: string;
  email: string;
  bio: string | null;
  job: string | null;
  thumbnail: string | null;
  numberOfFriend: number;
  registerDate: string;
  friendStatus: "ACCEPTED" | "PENDING" | "NOT_FRIEND" | "REQUESTED";
  avatarUrl?: string; // URL hình ảnh sau khi xử lý
}

export function UserBoard() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(false);
  const [page, setPage] = useState(0);
  const [processingUsers, setProcessingUsers] = useState<{[email: string]: boolean}>({});
  const observerTarget = useRef<HTMLDivElement>(null);

  const { viewFile } = useUploadFile();

  // Hàm gọi API tìm kiếm người dùng
  const fetchUsers = useCallback(async (
    type: "reset" | "infinite",
    pageNumber: number = 0,
    size: number = 10,
    userSearch: string = '',
  ) => {
    // Nếu đang tải dữ liệu, không gọi API thêm
    if (loading) return;

    setLoading(true);
    try {
      // Gọi API searchUserSocialNetwork với các tham số tìm kiếm
      const response = await socialNetworkServices.searchUserSocialNetwork({
        page: pageNumber,
        size: size,
        postSearch: userSearch,
      });

      // Kiểm tra dữ liệu trả về
      if (response.data?.data?.content) {
        const usersData = response.data.data.content;

        // Chuyển đổi dữ liệu trả về sang định dạng User
        const formattedUsers = await Promise.all(usersData.map(async (user: User) => {
          let avatarUrl = undefined;

          // Nếu có thumbnail, gọi API viewFile để lấy URL
          if (user.thumbnail) {
            try {
              avatarUrl = (await viewFile(
                user.thumbnail,
                "social-network"
              )) as string;
            } catch (error) {
              console.error("Lỗi khi lấy URL cho thumbnail:", error);
            }
          }

          // Trả về user với thêm trường avatarUrl
          return {
            ...user,
            avatarUrl
          };
        }));
        
        // Cập nhật danh sách người dùng
        if (type === "reset") {
          setUsers(formattedUsers);
        } else {
          setUsers(prev => [...prev, ...formattedUsers]);
        }
        
        // Cập nhật số trang và trạng thái có thêm dữ liệu hay không
        setPage(pageNumber);
        setHasMore(!response.data.data.last);
      } else {
        // Nếu không có dữ liệu hoặc có lỗi, hiển thị danh sách rỗng
        if (type === "reset") {
          setUsers([]);
        }
        setHasMore(false);
      }
    } catch (error) {
      console.error("Lỗi khi tải danh sách người dùng:", error);
      // Trong trường hợp lỗi, hiển thị danh sách rỗng nếu đang reset list
      if (type === "reset") {
        setUsers([]);
      }
      toast.error("Không thể tải danh sách người dùng");
    } finally {
      setLoading(false);
    }
  }, [loading]);

  // Xử lý gửi lời mời kết bạn
  const handleSendFriendRequest = async (email: string) => {
    if (processingUsers[email]) return;
    
    setProcessingUsers(prev => ({ ...prev, [email]: true }));
    try {
      await friendServices.sendFriendRequest(email);
      toast.success("Đã gửi lời mời kết bạn");
      fetchUsers("reset", 0, 10 * (page + 1), searchParams.get('q') || '');
    } catch (error) {
      console.error("Lỗi khi gửi lời mời kết bạn:", error);
      toast.error("Không thể gửi lời mời kết bạn");
    } finally {
      setProcessingUsers(prev => ({ ...prev, [email]: false }));
    }
  };

  // Xử lý chấp nhận lời mời kết bạn
  const handleAcceptFriendRequest = async (email: string) => {
    if (processingUsers[email]) return;
    
    setProcessingUsers(prev => ({ ...prev, [email]: true }));
    try {
      await friendServices.acceptFriendRequest(email);
      toast.success("Đã chấp nhận lời mời kết bạn");
      fetchUsers("reset", 0, 10 * (page + 1), searchParams.get('q') || '');
    } catch (error) {
      console.error("Lỗi khi chấp nhận lời mời kết bạn:", error);
      toast.error("Không thể chấp nhận lời mời kết bạn");
    } finally {
      setProcessingUsers(prev => ({ ...prev, [email]: false }));
    }
  };

  // Xử lý từ chối lời mời kết bạn
  const handleRejectFriendRequest = async (email: string) => {
    if (processingUsers[email]) return;
    
    setProcessingUsers(prev => ({ ...prev, [email]: true }));
    try {
      await friendServices.rejectFriendRequest(email);
      toast.success("Đã từ chối lời mời kết bạn");
      fetchUsers("reset", 0, 10 * (page + 1), searchParams.get('q') || '');
    } catch (error) {
      console.error("Lỗi khi từ chối lời mời kết bạn:", error);
      toast.error("Không thể từ chối lời mời kết bạn");
    } finally {
      setProcessingUsers(prev => ({ ...prev, [email]: false }));
    }
  };
  
  // Xử lý hủy kết bạn (khi đã là bạn bè)
  const handleUnfriend = async (email: string) => {
    if (processingUsers[email]) return;
    
    setProcessingUsers(prev => ({ ...prev, [email]: true }));
    try {
      await friendServices.rejectFriendRequest(email);
      toast.success("Đã hủy kết bạn");
      fetchUsers("reset", 0, 10 * (page + 1), searchParams.get('q') || '');
    } catch (error) {
      console.error("Lỗi khi hủy kết bạn:", error);
      toast.error("Không thể hủy kết bạn");
    } finally {
      setProcessingUsers(prev => ({ ...prev, [email]: false }));
    }
  };

  // Xử lý tải thêm người dùng khi cuộn xuống
  const loadMoreUsers = useCallback(() => {
    if (loading || !hasMore) return;
    fetchUsers("infinite", page + 1, 10, searchParams.get('q') || '');
  }, [hasMore, page , loading]);

  // Xử lý khi URL search params thay đổi
  useEffect(() => {
    fetchUsers("reset", 0, 10, searchParams.get('q') || '');
  }, [searchParams]);
  
  // Thiết lập Intersection Observer cho infinite scroll
  useEffect(() => {
    const observer = new IntersectionObserver(
      entries => {
        if (entries[0].isIntersecting) {
          loadMoreUsers();
        }
      },
      { threshold: 1.0 }
    );

    if (observerTarget.current) {
      observer.observe(observerTarget.current);
    }

    return () => {
      if (observerTarget.current) {
        observer.unobserve(observerTarget.current);
      }
    };
  }, [loadMoreUsers]);

  // Hàm hiển thị nút tương tác dựa trên trạng thái kết bạn
  const renderFriendActionButton = (user: User) => {
    const friendStatus = getFriendStatusDisplay(user.friendStatus);
    const isProcessing = processingUsers[user.email] || false;
    const IconComponent = friendStatus?.icon;
    
    switch (user.friendStatus) {
      case "ACCEPTED":
        return (
          <div className="flex flex-col items-start gap-2">
            <div className={`flex items-center gap-1 px-3 py-1 rounded-md ${friendStatus?.bgColor} ${friendStatus?.color} ${friendStatus?.borderColor} border`}>
              {IconComponent && <IconComponent className="mr-1" />}
              <span>{friendStatus?.text}</span>
            </div>
            <button 
              onClick={() => handleUnfriend(user.email)}
              disabled={isProcessing}
              className="relative flex items-center gap-1 px-3 py-1 text-sm text-red-600 hover:text-red-700 hover:underline transition-all"
            >
              <FaUserTimes className={`text-sm transition-transform duration-200 ${isProcessing ? 'animate-pulse' : 'group-hover:scale-110'}`} />
              <span>{isProcessing ? "Đang hủy..." : "Hủy kết bạn"}</span>
              
              {isProcessing && (
                <div className="absolute inset-0 flex items-center justify-center bg-red-600 bg-opacity-20 rounded-md">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                </div>
              )}
            </button>
          </div>
        );
      case "PENDING":
        return (
          <div className="flex flex-col gap-2">
            <div className="flex gap-2">
              <button 
                onClick={() => handleAcceptFriendRequest(user.email)}
                disabled={isProcessing}
                className="relative flex items-center px-3 py-1 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
              >
                <FaUserCheck className="mr-1" />
                <span>{isProcessing ? "Đang xử lý..." : "Chấp nhận"}</span>
                
                {isProcessing && (
                  <div className="absolute inset-0 flex items-center justify-center bg-green-600 bg-opacity-20 rounded-md">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  </div>
                )}
              </button>
              <button 
                onClick={() => handleRejectFriendRequest(user.email)}
                disabled={isProcessing}
                className="relative flex items-center px-3 py-1 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
              >
                <FaUserTimes className="mr-1" />
                <span>{isProcessing ? "Đang xử lý..." : "Từ chối"}</span>
                
                {isProcessing && (
                  <div className="absolute inset-0 flex items-center justify-center bg-red-600 bg-opacity-20 rounded-md">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  </div>
                )}
              </button>
            </div>
          </div>
        );
      case "REQUESTED":
        return (
          <div className={`w-fit flex items-center gap-1 px-3 py-1 rounded-md ${friendStatus?.bgColor} ${friendStatus?.color} ${friendStatus?.borderColor} border`}>
            {IconComponent && <IconComponent className="mr-1" />}
            <span>{friendStatus?.text}</span>
          </div>
        );
      case "NOT_FRIEND":
      default:
        return (
          <button 
            onClick={() => handleSendFriendRequest(user.email)}
            disabled={isProcessing}
        className={`flex items-center gap-1 px-3 py-1 rounded-md ${friendStatus?.bgColor} ${friendStatus?.color} ${friendStatus?.borderColor} border`}
          >
            <FaUserPlus className="mr-1" />
            <span>{isProcessing ? "Đang gửi..." : "Kết bạn"}</span>
            
            {isProcessing && (
              <div className="absolute inset-0 flex items-center justify-center bg-blue-600 bg-opacity-20 rounded-md">
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              </div>
            )}
          </button>
        );
    }
  };

  return (
    <div className="lg:w-2/4 order-3 lg:order-2">
      <div className="space-y-4">
        {users.length > 0 ? (
          users.map((user) => (
            <div key={user.email} className="bg-white rounded-lg shadow p-4">
              <div className="flex items-start gap-4">
                {/* Avatar */}
                <div className="w-16 h-16 rounded-full overflow-hidden bg-gray-200 flex-shrink-0">
                  {user.avatarUrl ? (
                    <Image
                      src={user.avatarUrl}
                      alt={`${user.firstName} ${user.lastName}`}
                      width={64}
                      height={64}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <DefaultAvatar
                      name={`${user.firstName} ${user.lastName}`}
                      size={64}
                    />
                  )}
                </div>
                
                {/* Thông tin người dùng */}
                <div className="flex-grow">
                  <h3 className="text-lg font-semibold cursor-pointer" onClick={() => router.push(`/social-network/profile/${user.email}`)}>{user.firstName} {user.lastName}</h3>

                  {user.bio && (
                    <div className="mt-2 text-sm text-gray-700">
                      {user.bio}
                    </div>
                  )}
                  
                  <div className="mt-1 text-sm text-gray-600 flex items-center">
                    <FaEnvelope className="mr-1" />
                    <span>{user.email}</span>
                  </div>
                  
                  {user.job && (
                    <div className="mt-1 text-sm text-gray-600 flex items-center">
                      <FaBriefcase className="mr-1" />
                      <span>{user.job}</span>
                    </div>
                  )}
                  
                  <div className="mt-1 text-sm text-gray-600 flex items-center">
                    <FaUserFriends className="mr-1" />
                    <span>{user.numberOfFriend} bạn bè</span>
                  </div>
                  
                  <div className="mt-1 text-sm text-gray-600 flex items-center">
                    <FaCalendarAlt className="mr-1" />
                    <span>Tham gia: {formatDateForDisplay(user.registerDate)}</span>
                  </div>
                  
                  {/* Nút tương tác */}
                  <div className="mt-3">
                    {renderFriendActionButton(user)}
                  </div>
                </div>
              </div>
            </div>
          ))
        ) : (
          <div className="bg-white rounded-lg shadow p-8 text-center">
            <div className="text-gray-500 mb-2">Không tìm thấy người dùng nào phù hợp</div>
          </div>
        )}
        
        {/* Loading indicator & observer target */}
        {hasMore && (
          <div ref={observerTarget} className="py-4 text-center">
            {loading ? (
              <div className="flex justify-center items-center space-x-2">
                <div className="w-3 h-3 rounded-full bg-blue-600 animate-bounce" style={{ animationDelay: '0ms' }}></div>
                <div className="w-3 h-3 rounded-full bg-blue-600 animate-bounce" style={{ animationDelay: '150ms' }}></div>
                <div className="w-3 h-3 rounded-full bg-blue-600 animate-bounce" style={{ animationDelay: '300ms' }}></div>
              </div>
            ) : (
              <div className="text-gray-500 text-sm">Kéo xuống để tải thêm</div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
