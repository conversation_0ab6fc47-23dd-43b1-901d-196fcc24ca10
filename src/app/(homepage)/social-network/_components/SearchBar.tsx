'use client';

import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useRouter, useSearchParams } from "next/navigation";
import { useState, useEffect } from "react";
import { FaSearch } from "react-icons/fa";

// Đ<PERSON>nh nghĩa kiểu dữ liệu cho props
export interface SearchParams {
  searchTerm: string;
  searchType: string;
  hashtag?: string;
}

export function SearchBar() {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  // Khởi tạo state từ URL params
  const [searchTerm, setSearchTerm] = useState(searchParams.get('q') || '');
  const [searchType, setSearchType] = useState(searchParams.get('type') || 'posts');

  // Đồng bộ state với URL khi URL thay đổi
  useEffect(() => {
    const q = searchParams.get('q');
    const type = searchParams.get('type');
    
    if (q) setSearchTerm(q);
    if (type) setSearchType(type);
  }, [searchParams]);
  
  // Xử lý tìm kiếm bằng cách cập nhật URL
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Tạo URL mới với các tham số tìm kiếm
    const params = new URLSearchParams();
    if (searchTerm) params.set('q', searchTerm);
    params.set('type', searchType);
    
    // Chuyển hướng đến URL mới
    router.push(`/social-network?${params.toString()}`);
  };

  return <div className="mb-6 bg-white rounded-lg shadow p-4">
    <form onSubmit={handleSearch} className="flex flex-col sm:flex-row gap-3">
      <div className="relative flex-grow">
        <input
          type="text"
          placeholder="Tìm kiếm bài viết, người dùng hoặc hashtag..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" />
        <FaSearch className="absolute left-3 top-3 text-gray-400" />
      </div>
      <div className="flex gap-2">
      <Select value={searchType} onValueChange={setSearchType}>
            <SelectTrigger className="w-fit">
              <SelectValue placeholder="Tất cả" />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                <SelectItem value="posts">Bài viết</SelectItem>
                <SelectItem value="hashtags">Hashtag</SelectItem>
                <SelectItem value="users">Người dùng</SelectItem>
              </SelectGroup>
            </SelectContent>
          </Select>
        {/* <select
          value={searchType}
          onChange={(e) => setSearchType(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="all">Tất cả</option>
          <option value="posts">Bài viết</option>
          <option value="users">Người dùng</option>
          <option value="hashtags">Hashtag</option>
        </select> */}
        <button
          type="submit"
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200"
        >
          Tìm kiếm
        </button>
      </div>
    </form>
  </div>;
}