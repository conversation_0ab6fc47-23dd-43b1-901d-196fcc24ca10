"use client";

import PostContent from "@/components/common/PostContent";
import useUploadFile from "@/hook/useUploadFile";
import socialNetworkServices from "@/services/social-network/socialNetworkServices";
import { useCallback, useEffect, useRef, useState } from "react";
import { toast } from "react-toastify";
import { type ExtendedPost } from "../../social-network/profile/components/PostsTab";

/**
 * FeedBoard - Component hiển thị danh sách bài đăng từ API feedPostSocialNetwork
 * Đặc điểm:
 * - Hiển thị danh sách bài đăng từ API feedPostSocialNetwork
 * - Không có phân trang, chỉ hiển thị tất cả bài đăng trả về từ API
 * - Khi API không trả về dữ liệu, hiển thị thông báo "Bạn đã xem hết bài viết, vui lòng tải lại trang"
 * - <PERSON><PERSON> tải lại trang hoặc lần đầu vào trang, gửi isReload = true
 */
export function FeedBoard() {
  const [posts, setPosts] = useState<ExtendedPost[]>([]);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true); // Theo dõi còn dữ liệu để tải không
  const { viewFile } = useUploadFile();
  
  // Ref để theo dõi phần tử sẽ trigger tải thêm bài viết
  const observerTarget = useRef<HTMLDivElement>(null);

  // Hàm gọi API lấy danh sách bài đăng feed
  const fetchPosts = useCallback(
    async (isReload: boolean = true) => {
      // Nếu đang tải dữ liệu, không gọi API thêm
      if (loading) return;

      setLoading(true);
      try {
        // Gọi API feedPostSocialNetwork với tham số isReload
        const response = await socialNetworkServices.feedPostSocialNetwork({
          isReload
        });

        // Kiểm tra dữ liệu trả về
        if (response.data?.data && response.data.data.length > 0) {
          const postsData = response.data.data;

          // Chuyển đổi dữ liệu trả về sang định dạng ExtendedPost
          const formattedPosts = await Promise.all(
            postsData.map(async (post: any) => {
              let avatarUrl = undefined;
              let fileUrls: string[] = [];
              let postReferWithUrls = null;

              // Nếu có thumbnail, gọi API viewFile để lấy URL
              if (post.thumbnail) {
                try {
                  avatarUrl = (await viewFile(
                    post.thumbnail,
                    "social-network"
                  )) as string;
                } catch (error) {
                  console.error("Lỗi khi lấy URL cho thumbnail:", error);
                }
              }

              // Nếu có files, gọi API viewFile để lấy URL cho từng file
              if (post.files && post.files.length > 0) {
                try {
                  // Gọi API viewFile cho từng fileId trong mảng files
                  const filePromises = post.files.map((fileId) =>
                    viewFile(fileId, "social-network")
                  );
                  // Ép kiểu kết quả trả về thành string[]
                  fileUrls = (await Promise.all(filePromises)) as string[];
                } catch (error) {
                  console.error("Lỗi khi lấy URL cho files:", error);
                }
              }

              // Xử lý postRefer nếu có
              if (post.postRefer && post.referPostAvailable) {
                try {
                  // Xử lý avatar của postRefer
                  let referAvatarUrl = undefined;
                  if (post.postRefer.thumbnail) {
                    referAvatarUrl = (await viewFile(
                      post.postRefer.thumbnail,
                      "social-network"
                    )) as string;
                  }

                  // Xử lý files của postRefer
                  let referFileUrls: string[] = [];
                  if (post.postRefer.files && post.postRefer.files.length > 0) {
                    const referFilePromises = post.postRefer.files.map(
                      (fileId) => viewFile(fileId, "social-network")
                    );
                    referFileUrls = (await Promise.all(
                      referFilePromises
                    )) as string[];
                  }

                  // Tạo postRefer với các URL đã xử lý
                  postReferWithUrls = {
                    ...post.postRefer,
                    avatarUrl: referAvatarUrl,
                    fileUrls: referFileUrls
                  };
                } catch (error) {
                  console.error("Lỗi khi xử lý postRefer:", error);
                }
              }

              // Trả về đối tượng ExtendedPost với các URL đã xử lý
              return {
                ...post,
                avatarUrl,
                fileUrls,
                postRefer: postReferWithUrls
              };
            })
          );

          // Cập nhật state với dữ liệu mới
          if (isReload) {
            // Nếu là tải lại, thay thế toàn bộ danh sách
            setPosts(formattedPosts);
            setHasMore(true); // Reset trạng thái hasMore
          } else {
            // Nếu là tải thêm, thêm vào danh sách hiện có
            // Lọc ra các bài viết mới để tránh trùng lặp
            const newPosts = formattedPosts.filter(
              (newPost) => !posts.some((post) => post.postSocialNetworkId === newPost.postSocialNetworkId)
            );
            
            if (newPosts.length === 0) {
              // Không có bài viết mới, đánh dấu là đã hết
              setHasMore(false);
            } else {
              // Thêm bài viết mới vào danh sách
              setPosts(prevPosts => [...prevPosts, ...newPosts]);
            }
          }
        } else {
          // API không trả về dữ liệu
          if (isReload) {
            // Nếu là tải lại và không có dữ liệu, đặt posts là mảng rỗng
            setPosts([]);
          }
          // Đánh dấu không còn dữ liệu để tải nữa
          setHasMore(false);
        }
      } catch (error) {
        console.error("Lỗi khi tải bài đăng:", error);
        toast.error("Không thể tải bài đăng");
        setPosts([]);
      } finally {
        setLoading(false);
      }
    },
    [loading, viewFile]
  );

  // Sử dụng IntersectionObserver để tải thêm dữ liệu khi scroll đến cuối
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        // Nếu phần tử target hiển thị và không đang tải và còn dữ liệu
        if (entries[0].isIntersecting && !loading && hasMore) {
          // Gọi API với isReload = false để tải thêm dữ liệu
          fetchPosts(false);
        }
      },
      { threshold: 0.1 } // Kích hoạt khi 10% của phần tử hiển thị trong viewport
    );

    // Khi component được mount hoặc ref thay đổi, attach observer
    if (observerTarget.current) {
      observer.observe(observerTarget.current);
    }

    // Cleanup function để remove observer khi component unmount
    return () => {
      if (observerTarget.current) {
        observer.unobserve(observerTarget.current);
      }
    };
  }, [loading, hasMore, fetchPosts]);
  
  // Tải bài đăng lần đầu khi component được mount
  useEffect(() => {
    // Lần đầu vào trang, gửi isReload = true
    fetchPosts(true);
  }, []);

  // Hàm xử lý tải lại trang
  const handleReload = () => {
    setPosts([]); // Xóa danh sách bài viết cũ
    setHasMore(true); // Đặt lại trạng thái hasMore
    fetchPosts(true);
  };

  return (
    <div className="order-3 lg:order-2 lg:w-2/4">
      <div className="space-y-6">
        {posts.length > 0 ? (
          <>
            {posts.map((post) => (
              <div key={post.postSocialNetworkId}>
                <PostContent post={post} resetList={() => {}} />
              </div>
            ))}
          </>
        ) : loading ? (
          <div className="rounded-lg bg-white p-8 text-center shadow">
            <div className="flex items-center justify-center space-x-2">
              <div
                className="h-3 w-3 animate-bounce rounded-full bg-blue-600"
                style={{ animationDelay: "0ms" }}
              ></div>
              <div
                className="h-3 w-3 animate-bounce rounded-full bg-blue-600"
                style={{ animationDelay: "150ms" }}
              ></div>
              <div
                className="h-3 w-3 animate-bounce rounded-full bg-blue-600"
                style={{ animationDelay: "300ms" }}
              ></div>
            </div>
            <div className="mt-2 text-gray-500">Đang tải bài viết...</div>
          </div>
        ) : (
          <div className="rounded-lg bg-white p-8 text-center shadow">
            <div className="mb-2 text-gray-500">Không có bài viết nào</div>
          </div>
        )}

        {/* Phần tử theo dõi để kích hoạt tải thêm - đặt ở cuối danh sách */}
        <div ref={observerTarget} className="h-4"></div>
        
        {/* Hiển thị loading khi đang tải thêm */}
        {loading && posts.length > 0 && (
          <div className="flex justify-center p-4">
            <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-blue-500"></div>
          </div>
        )}
        
        {/* Chỉ hiển thị thông báo khi không còn bài viết */}
        {!hasMore && posts.length > 0 && (
          <div className="mt-4 rounded-lg bg-white p-4 text-center shadow">
            <div className="mb-2 text-gray-500">
              Bạn đã xem hết bài viết, vui lòng tải lại trang
            </div>
            <button
              onClick={handleReload}
              className="rounded-md bg-blue-600 px-4 py-2 text-white transition-colors hover:bg-blue-700"
              disabled={loading}
            >
              {loading ? "Đang tải..." : "Tải lại"}
            </button>
          </div>
        )}
      </div>
    </div>
  );
}

export default FeedBoard;
