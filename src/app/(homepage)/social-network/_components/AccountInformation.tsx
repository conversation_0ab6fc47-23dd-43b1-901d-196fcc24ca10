"use client";

import DefaultAvatar from "@/components/ui/DefaultAvatar";
import Image from "next/image";
import { FaBriefcase, FaBuilding, FaCalendarAlt, FaEnvelope, FaGraduationCap, FaMapMarkerAlt, FaPhone, FaVenusMars } from "react-icons/fa";
import { useUserSocialNetwork } from "../_contexts/UserSocialNetworkContext";
import { formatDateForDisplay } from "../utils/dateUtils";

export function AccountInformation() {
  // Sử dụng context để lấy dữ liệu người dùng
  const { userData, isLoading, error, fetchUserData } = useUserSocialNetwork();

  // Hiển thị trạng thái loading
  if (isLoading || !userData) {
    return (
      <div className="order-1 lg:w-1/4">
        <div className="sticky top-24 rounded-lg bg-white p-5 shadow">
          <div className="flex h-64 items-center justify-center">
            <div className="h-10 w-10 animate-spin rounded-full border-4 border-blue-500 border-t-transparent"></div>
          </div>
        </div>
      </div>
    );
  }

  // Hiển thị trạng thái lỗi
  if (error) {
    return (
      <div className="order-1 lg:w-1/4">
        <div className="sticky top-24 rounded-lg bg-white p-5 shadow">
          <div className="flex flex-col items-center justify-center h-64">
            <p className="text-red-500">{error}</p>
            <button 
              onClick={fetchUserData}
              className="mt-4 px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
            >
              Thử lại
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Hiển thị thông tin người dùng
  return (
    <div className="order-1 lg:w-1/4">
      <div className="sticky top-24 rounded-lg bg-white p-5 shadow">
        <div className="mb-4 flex flex-col items-center text-center">
          {userData?.avatarUrl ? (
            <Image
              src={userData.avatarUrl}
              alt={userData.fullName || "Người dùng"}
              width={100}
              height={100}
              className="mb-3 rounded-full object-cover size-24"
            />
          ) : (
            <DefaultAvatar name={userData?.fullName || `${userData?.firstName || ''} ${userData?.lastName || ''}`} size={100} className="mb-3 size-24" />
          )}
          <h2 className="text-xl font-semibold">{userData?.fullName || `${userData?.firstName || ''} ${userData?.lastName || ''}`}</h2>
          {userData?.bio && <p className="mt-1 text-sm text-gray-600">{userData.bio}</p>}
        </div>

        {/* Thông tin chi tiết người dùng */}
        <div className="mt-4 flex flex-col gap-y-2 text-gray-600 text-sm">
          {userData?.address && (
            <div className="flex items-center gap-2">
              <FaMapMarkerAlt className="text-gray-500" />
              <span>{userData.address}</span>
            </div>
          )}
          {userData?.education && (
            <div className="flex items-center gap-2">
              <FaGraduationCap className="text-gray-500" />
              <span>{userData.education}</span>
            </div>
          )}
          {userData?.job && (
            <div className="flex items-center gap-2">
              <FaBriefcase className="text-gray-500" />
              <span>{userData.job}</span>
            </div>
          )}
          {userData?.phoneNumber && (
            <div className="flex items-center gap-2">
              <FaPhone className="text-gray-500" />
              <span>{userData.phoneNumber}</span>
            </div>
          )}
          {userData?.dateOfBirth && (
            <div className="flex items-center gap-2">
              <FaCalendarAlt className="text-gray-500" />
              <span>{formatDateForDisplay(userData.dateOfBirth)}</span>
            </div>
          )}
          {userData?.gender && (
            <div className="flex items-center gap-2">
              <FaVenusMars className="text-gray-500" />
              <span>{userData.gender}</span>
            </div>
          )}
          {userData?.orgName && (
            <div className="flex items-center gap-2">
              <FaBuilding className="text-gray-500" />
              <span>{userData.orgName}</span>
            </div>
          )}
          {userData?.email && (
            <div className="flex items-center gap-2">
              <FaEnvelope className="text-gray-500" />
              <span>{userData.email}</span>
            </div>
          )}
        </div>

        <div className="mt-4 border-t border-gray-200 pt-4">
          <div className="mb-3 flex justify-between">
            <div className="text-center">
              <div className="text-lg font-semibold">
                {userData?.numberOfPost || 0}
              </div>
              <div className="text-xs text-gray-500">Bài viết</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-semibold">
                {userData?.numberOfLike || 0}
              </div>
              <div className="text-xs text-gray-500">Lượt thích</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-semibold">
                {userData?.numberOfFriend || 0}
              </div>
              <div className="text-xs text-gray-500">Bạn bè</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}