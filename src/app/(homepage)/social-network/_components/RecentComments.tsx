"use client";

import PostDetailModal from "@/components/common/PostDetailModal";
import DefaultAvatar from "@/components/ui/DefaultAvatar";
import useUploadFile from "@/hook/useUploadFile";
import socialNetworkServices from "@/services/social-network/socialNetworkServices";
import { Post } from "@/services/social-network/types/types";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { FaComment } from "react-icons/fa";
import { formatDateForDisplay } from "../utils/dateUtils";

export type ExtendedPost = Post & {
  avatarUrl?: string;
  fileUrls?: string[];
  orgName?: string;
  postRefer?: ExtendedPost | null;
};

export function RecentComments() {
  const router = useRouter();
  const { viewFile } = useUploadFile();
  // State để lưu trữ dữ liệu bình luận gần đây
  const [recentComments, setRecentComments] = useState([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const [showPostModal, setShowPostModal] = useState(false);
  const [selectedPost, setSelectedPost] = useState<ExtendedPost | null>(null);

  // Hàm để lấy dữ liệu bình luận gần đây từ API
  useEffect(() => {
    const fetchRecentComments = async () => {
      try {
        setIsLoading(true);
        const response = await socialNetworkServices.getNearestComment({
          page: 0,
          size: 5,
          sort: "DESC"
        });

        // Kiểm tra và xử lý dữ liệu trả về
        if (response?.data?.data?.content) {
          const comments = response.data.data.content;
          const commentsWithAvatarUrl = await Promise.all(
            comments.map(async (comment) => {
              const avatarUrl = await viewFile(
                comment.thumbnail,
                "social-network"
              );
              return {
                ...comment,
                thumbnail: avatarUrl as string
              };
            })
          );
          setRecentComments(commentsWithAvatarUrl);
        } else {
          setRecentComments([]);
        }
        setError(null);
      } catch (err) {
        console.error("Lỗi khi lấy bình luận gần đây:", err);
        setError("Không thể tải bình luận gần đây. Vui lòng thử lại sau.");
        setRecentComments([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchRecentComments();
  }, []);

  // Xử lý điều hướng đến bài đăng
  const handleNavigateToPost = (postId: number) => {
    // Lưu ID bài đăng vào state để truyền cho PostDetailModal
    setSelectedPost({ postSocialNetworkId: postId } as ExtendedPost);
    setShowPostModal(true);
  };

  // Hàm để đóng modal và reset state
  const handleClosePostModal = () => {
    setShowPostModal(false);
    setSelectedPost(null);
  };

  return (
    <>
      <div className="order-2 lg:order-3 lg:w-1/4">
        <div className="sticky top-24 rounded-lg bg-white p-5 shadow">
          <h3 className="mb-4 flex items-center gap-2 text-lg font-semibold">
            <FaComment className="text-blue-600" />
            Bình luận gần đây
          </h3>

          <div className="space-y-4">
            {isLoading ? (
              // Hiển thị trạng thái đang tải
              <div className="flex justify-center py-4">
                <div className="h-6 w-6 animate-spin rounded-full border-b-2 border-blue-600"></div>
              </div>
            ) : error ? (
              // Hiển thị thông báo lỗi
              <div className="py-4 text-center text-red-500">{error}</div>
            ) : recentComments.length === 0 ? (
              // Hiển thị khi không có bình luận nào
              <div className="py-4 text-center text-gray-500">
                Chưa có bình luận nào
              </div>
            ) : (
              // Hiển thị danh sách bình luận
              recentComments.map((comment) => (
                <div
                  key={comment.id}
                  className="border-b border-gray-100 pb-3 last:border-0"
                >
                  <div className="flex items-start gap-2">
                    <div className="relative h-7 w-7 sm:h-8 sm:w-8 overflow-hidden rounded-full">
                      {comment.thumbnail ? (
                        <Image
                          src={comment.thumbnail}
                          alt={
                            comment.fullName ||
                            `${comment.firstName || ""} ${comment.lastName || ""}`
                          }
                          fill
                          className="object-cover"
                        />
                      ) : (
                        <DefaultAvatar
                          name={`${comment.fullName ||
                            `${comment.firstName || ""} ${comment.lastName || ""}`}`}
                          size="100%"
                        />
                      )}
                    </div>
                    <div className="flex-1">
                      <div className="text-sm font-medium cursor-pointer" onClick={()=> router.push(`/social-network/profile/${comment.username}`)}>
                        {comment.fullName ||
                          `${comment.firstName || ""} ${comment.lastName || ""}`}
                      </div>
                      <div className="text-sm">{comment.content}</div>
                      <div className="mt-1 flex items-center justify-between">
                        <span className="text-xs text-gray-500">
                          {formatDateForDisplay(comment.createdAt)}
                        </span>
                        <div
                          onClick={() =>
                            handleNavigateToPost(comment.postSocialNetworkId)
                          }
                          className="text-xs text-blue-600 hover:underline cursor-pointer"
                        >
                          Xem bài viết
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      </div>

      {/* Sử dụng component PostDetailModal để hiển thị chi tiết bài đăng */}
      <PostDetailModal
        isOpen={showPostModal}
        onClose={handleClosePostModal}
        postId={selectedPost?.postSocialNetworkId || 0}
      />
    </>
  );
}
