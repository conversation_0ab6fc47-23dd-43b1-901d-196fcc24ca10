'use client'

import useUploadFile from "@/hook/useUploadFile";
import socialNetworkServices from "@/services/social-network/socialNetworkServices";
import { useSearchParams } from "next/navigation";
import { useCallback, useEffect, useRef, useState } from "react";
import { toast } from "react-toastify";
import { type ExtendedPost } from "../../social-network/profile/components/PostsTab";
import { PostContent } from "../../social-network/profile/components/posts";

export function BulletinBoard() {
  const searchParams = useSearchParams();
  const [posts, setPosts] = useState<ExtendedPost[]>([]);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(false);
  const [page, setPage] = useState(0);
  const observerTarget = useRef<HTMLDivElement>(null);

  const { viewFile } = useUploadFile();

  // Hàm gọi API tìm kiếm bài đăng
  const fetchPosts = useCallback(async (
    type: "reset" | "infinite",
    pageNumber: number = 0,
    size: number = 10,
    postSearch: string = '',
    hashtag: string = '',
  ) => {
    // Nếu đang tải dữ liệu, không gọi API thêm
    if (loading) return;

    setLoading(true);
    try {
      // Gọi API searchPostSocialNetwork với các tham số tìm kiếm
      const response = await socialNetworkServices.searchPostSocialNetwork({
        page: pageNumber,
        size: size,
        postSearch: postSearch,
        hashtag: hashtag,
      });

      // Kiểm tra dữ liệu trả về
      if (response.data?.data?.content) {
        const postsData = response.data.data.content;

        // Chuyển đổi dữ liệu trả về sang định dạng ExtendedPost
        const formattedPosts = await Promise.all(postsData.map(async (post: any) => {
          let avatarUrl = undefined;
          let fileUrls: string[] = [];
          let postReferWithUrls = null;

          // Nếu có thumbnail, gọi API viewFile để lấy URL
          if (post.thumbnail) {
            try {
              avatarUrl = (await viewFile(
                post.thumbnail,
                "social-network"
              )) as string;
            } catch (error) {
              console.error("Lỗi khi lấy URL cho thumbnail:", error);
            }
          }

          // Nếu có files, gọi API viewFile để lấy URL cho từng file
          if (post.files && post.files.length > 0) {
            try {
              // Gọi API viewFile cho từng fileId trong mảng files
              const filePromises = post.files.map((fileId) =>
                viewFile(fileId, "social-network")
              );
              // Ép kiểu kết quả trả về thành string[]
              fileUrls = (await Promise.all(filePromises)) as string[];
            } catch (error) {
              console.error("Lỗi khi lấy URL cho files:", error);
            }
          }

          // Xử lý postRefer nếu có
          if (post.postRefer && post.referPostAvailable) {
            try {
              // Xử lý avatar của postRefer
              let referAvatarUrl = undefined;
              if (post.postRefer.thumbnail) {
                referAvatarUrl = (await viewFile(
                  post.postRefer.thumbnail,
                  "social-network"
                )) as string;
              }

              // Xử lý files của postRefer
              let referFileUrls: string[] = [];
              if (post.postRefer.files && post.postRefer.files.length > 0) {
                const referFilePromises = post.postRefer.files.map((fileId) =>
                  viewFile(fileId, "social-network")
                );
                referFileUrls = (await Promise.all(
                  referFilePromises
                )) as string[];
              }

              // Tạo postRefer với các URL đã xử lý
              postReferWithUrls = {
                ...post.postRefer,
                avatarUrl: referAvatarUrl,
                fileUrls: referFileUrls
              };
            } catch (error) {
              console.error("Lỗi khi xử lý postRefer:", error);
            }
          }

          // Trả về post với thêm trường avatarUrl, fileUrls và postRefer đã xử lý
          return {
            ...post,
            avatarUrl,
            fileUrls,
            postRefer: postReferWithUrls
          };
        }));
        
        // Cập nhật danh sách bài đăng
        if (type === "reset") {
          setPosts(formattedPosts);
        } else {
          setPosts(prev => [...prev, ...formattedPosts]);
        }
        
        // Cập nhật số trang và trạng thái có thêm dữ liệu hay không
        setPage(pageNumber);
        setHasMore(!response.data.data.last);
      } else {
        // Nếu không có dữ liệu hoặc có lỗi, hiển thị danh sách rỗng
        if (type === "reset") {
          setPosts([]);
        }
        setHasMore(false);
      }
    } catch (error) {
      console.error("Lỗi khi tải bài đăng:", error);
      // Trong trường hợp lỗi, hiển thị danh sách rỗng nếu đang reset list
      if (type === "reset") {
        setPosts([]);
      }
      toast.error("Không thể tải bài đăng");
    } finally {
      setLoading(false);
    }
  }, [loading]);

  // Xử lý tải thêm bài viết khi cuộn xuống
  const loadMorePosts = useCallback(() => {
    if (loading || !hasMore) return;
    
    // Lấy thông tin tìm kiếm từ URL
    const searchTerm = searchParams.get('q') || '';
    const searchType = searchParams.get('type') || '';
    const postSearch = searchType === 'posts' ? searchTerm : '';
    const hashtag = searchType === 'hashtags' ? searchTerm : '';
    
    fetchPosts("infinite", page + 1, 10, postSearch, hashtag);
  }, [hasMore, page, loading, fetchPosts, searchParams]);

  // Xử lý khi URL search params thay đổi
  useEffect(() => {
    // Lấy thông tin tìm kiếm từ URL
    const searchTerm = searchParams.get('q') || '';
    const searchType = searchParams.get('type') || '';
    const postSearch = searchType === 'posts' ? searchTerm : '';
    const hashtag = searchType === 'hashtags' ? searchTerm : '';
    
    // Gọi API tìm kiếm bài đăng với từ khóa từ URL
    fetchPosts("reset", 0, 10, postSearch, hashtag);
  }, [searchParams]);
  
  // Thiết lập Intersection Observer cho infinite scroll
  useEffect(() => {
    const observer = new IntersectionObserver(
      entries => {
        if (entries[0].isIntersecting) {
          loadMorePosts();
        }
      },
      { threshold: 1.0 }
    );

    if (observerTarget.current) {
      observer.observe(observerTarget.current);
    }

    return () => {
      if (observerTarget.current) {
        observer.unobserve(observerTarget.current);
      }
    };
  }, [loadMorePosts]);

    return (
      <div className="lg:w-2/4 order-3 lg:order-2">
      <div className="space-y-6">
        {posts.length > 0 ? (
          posts.map((post) => (
            <div key={post.postSocialNetworkId}>
              <PostContent
                post={post}
                resetList={() => {
                  // Lấy thông tin tìm kiếm từ URL
                  const searchTerm = searchParams.get('q') || '';
                  const searchType = searchParams.get('type') || '';
                  const postSearch = searchType === 'posts' ? searchTerm : '';
                  const hashtag = searchType === 'hashtags' ? searchTerm : '';
                  
                  // Tải lại danh sách bài đăng từ API với thông tin tìm kiếm
                  fetchPosts("reset", 0, 10 * (page + 1), postSearch, hashtag);
                }}
              />
            </div>
          ))
        ) : (
          <div className="bg-white rounded-lg shadow p-8 text-center">
            <div className="text-gray-500 mb-2">Không tìm thấy bài viết nào phù hợp</div>
          </div>
        )}
        
        {/* Loading indicator & observer target */}
        {hasMore && (
          <div ref={observerTarget} className="py-4 text-center">
            {loading ? (
              <div className="flex justify-center items-center space-x-2">
                <div className="w-3 h-3 rounded-full bg-blue-600 animate-bounce" style={{ animationDelay: '0ms' }}></div>
                <div className="w-3 h-3 rounded-full bg-blue-600 animate-bounce" style={{ animationDelay: '150ms' }}></div>
                <div className="w-3 h-3 rounded-full bg-blue-600 animate-bounce" style={{ animationDelay: '300ms' }}></div>
              </div>
            ) : (
              <div className="text-gray-500 text-sm">Kéo xuống để tải thêm</div>
            )}
          </div>
        )}
      </div>
    </div>
    )
}