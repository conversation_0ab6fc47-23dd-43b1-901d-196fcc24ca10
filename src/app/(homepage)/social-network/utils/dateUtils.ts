/**
 * <PERSON><PERSON><PERSON> hàm tiện ích xử lý định dạng ngày tháng
 */

/**
 * Kiểm tra xem chuỗi có phải định dạng ngày hợp lệ không (8 hoặc 14 ký tự)
 * @param dateString Chuỗi ngày tháng cần kiểm tra
 * @returns true nếu là chuỗi 8 hoặc 14 ký tự, false nếu không phải
 */
const isValidDateString = (dateString: string | null | undefined): boolean => {
  return !!dateString && (dateString.length === 8 || dateString.length === 14);
};

/**
 * Chuyển đổi định dạng ngày từ "YYYYMMDD" hoặc "YYYYMMDDHHmmss" sang "YYYY-MM-DD" cho input date
 * @param dateString Chuỗi ngày tháng định dạng "YYYYMMDD" hoặc "YYYYMMDDHHmmss"
 * @returns Chuỗi ngày tháng định dạng "YYYY-MM-DD" hoặc chuỗi rỗng nếu input không hợp lệ
 */
export const formatDateForInput = (dateString: string | null | undefined): string => {
  if (!isValidDateString(dateString)) return "";
  
  // Luôn lấy 8 ký tự đầu tiên cho dù là chuỗi 8 hay 14 ký tự
  const dateStr = dateString!.substring(0, 8);
  const year = dateStr.substring(0, 4);
  const month = dateStr.substring(4, 6);
  const day = dateStr.substring(6, 8);
  return `${year}-${month}-${day}`;
};

/**
 * Chuyển đổi định dạng ngày từ "YYYY-MM-DD" sang "YYYYMMDD" cho API
 * @param dateString Chuỗi ngày tháng định dạng "YYYY-MM-DD"
 * @returns Chuỗi ngày tháng định dạng "YYYYMMDD" hoặc null nếu input không hợp lệ
 */
export const formatDateForApi = (dateString: string | null | undefined): string | null => {
  if (!dateString) return null;
  return dateString.replace(/-/g, "");
};

/**
 * Định dạng ngày hiển thị cho người dùng
 * @param dateString Chuỗi ngày tháng định dạng "YYYYMMDD" hoặc "YYYYMMDDHHmmss"
 * @returns Chuỗi ngày tháng định dạng "DD/MM/YYYY" hoặc "DD/MM/YYYY HH:mm:ss" hoặc giữ nguyên nếu input không hợp lệ
 */
export const formatDateForDisplay = (dateString: string | null | undefined): string | null | undefined => {
  if (!isValidDateString(dateString)) return dateString;
  
  const dateStr = dateString!;
  const year = dateStr.substring(0, 4);
  const month = dateStr.substring(4, 6);
  const day = dateStr.substring(6, 8);
  
  // Nếu là chuỗi 14 ký tự, hiển thị cả giờ phút giây
  if (dateStr.length === 14) {
    const hour = dateStr.substring(8, 10);
    const minute = dateStr.substring(10, 12);
    const second = dateStr.substring(12, 14);
    return `${day}/${month}/${year} ${hour}:${minute}:${second}`;
  }
  
  // Nếu là chuỗi 8 ký tự, chỉ hiển thị ngày tháng năm
  return `${day}/${month}/${year}`;
};
