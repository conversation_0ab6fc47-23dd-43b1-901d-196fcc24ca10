'use client';

import React from "react";
import { redirect } from "next/navigation";
import { getCookie } from "cookies-next";

import { Role } from "@/constants/role";
import { <PERSON>ieName } from "@/constants/cookie";

export default function ManagePostsPage() {
  const token = getCookie(CookieName.TOKEN);
  const user = getCookie(CookieName.USER);

  if (!user || token !== Role.ROLE_ADMIN) {
    if (typeof window !== "undefined") {
      redirect("/social-network/feed");
    }
    return null;
  }

  return (
    <div className="container mx-auto py-8">
      <h1 className="mb-4 text-2xl font-bold">Quản lý bài đăng (Admin)</h1>
      {/* Danh sách bài đăng, chức năng xóa/sửa sẽ ở đây */}
      <p>Chỉ quản trị viên mới xem được trang này.</p>
    </div>
  );
}
