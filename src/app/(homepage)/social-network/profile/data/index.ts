// Dữ liệu mẫu cho hoạt động gần đây
export const recentActivities  = [
  {
    id: 1,
    type: 'post',
    content: 'Đã đăng một bài viết mới',
    time: '2 giờ trước'
  },
  {
    id: 2,
    type: 'like',
    content: 'Đã thích bài viết của Trần Thị B',
    time: '5 giờ trước'
  },
  {
    id: 3,
    type: 'comment',
    content: 'Đã bình luận về bài viết của Lê Văn C',
    time: '1 ngày trước'
  },
  {
    id: 4,
    type: 'follow',
    content: 'Đã theo dõi Phạm Thị D',
    time: '2 ngày trước'
  },
  {
    id: 5,
    type: 'share',
    content: 'Đã chia sẻ bài viết của Hoàng Văn E',
    time: '3 ngày trước'
  }
];

// Dữ liệu mẫu cho bạn bè đề xuất
export const suggestedFriends = [
  {
    id: 1,
    name: 'Tr<PERSON><PERSON>h<PERSON> B',
    role: '<PERSON>i<PERSON><PERSON> viên <PERSON>',
    avatar: 'https://i.pravatar.cc/300?img=2',
    mutualFriends: 5
  },
  {
    id: 2,
    name: 'Lê Văn C',
    role: 'Giáo viên Vật lý',
    avatar: 'https://i.pravatar.cc/300?img=3',
    mutualFriends: 3
  },
  {
    id: 3,
    name: 'Phạm Thị D',
    role: 'Giáo viên Hóa học',
    avatar: 'https://i.pravatar.cc/300?img=4',
    mutualFriends: 2
  }
];


export enum UserRole {
  USER = 'ROLE_USER',
  ADMIN = 'ROLE_ADMIN'
}

// Enum cho quyền riêng tư
export enum PrivacyLevel {
  PUBLIC = 'public',
  FRIENDS = 'friends',
  PRIVATE = 'private'
}
export interface User {
  id: number;
  name: string;
  role: string;
  userRole: UserRole;
  avatar: string;
  coverPhoto: string;
  bio: string;
  location: string;
  education: string;
  email: string;
  joinDate: string;
  stats: {
    posts: number;
    followers: number;
    following: number;
    likes: number;
  }
}
// Dữ liệu mẫu cho người dùng hiện tại
export const userData: User = {
  id: 1,
  name: 'Nguyễn Văn A',
  role: 'Giáo viên Tiếng Anh',
  userRole: UserRole.USER, // Người dùng thường
  avatar: 'https://i.pravatar.cc/300?img=1',
  coverPhoto: 'https://images.unsplash.com/photo-1503676260728-1c00da094a0b?q=80&w=1920&auto=format&fit=crop',
  bio: 'Giáo viên Tiếng Anh với hơn 10 năm kinh nghiệm | Đam mê chia sẻ kiến thức và kết nối cộng đồng giáo dục',
  location: 'Hà Nội, Việt Nam',
  education: 'Thạc sĩ Giáo dục, Đại học Sư phạm Hà Nội',
  email: '<EMAIL>',
  joinDate: 'Tháng 3, 2023',
  stats: {
    posts: 28,
    followers: 156,
    following: 89,
    likes: 245
  }
};

// Interface cho hashtag
export interface Hashtag {
  id: number;
  name: string;
}

// Interface cho bình luận
export interface Comment {
  id: number;
  postId: number;
  userId: number;
  user: {
    id: number;
    name: string;
    avatar: string;
    role: UserRole;
  };
  content: string;
  createdAt: string;
  likes: number;
  parentId: number | null;
  replies: Comment[];
}

// Interface cho bài đăng
export interface Post {
  id: number;
  userId: number;
  user: {
    id: number;
    name: string;
    avatar: string;
    role: UserRole;
  };
  content: string;
  hashtags: Hashtag[];
  image?: string;
  createdAt: string;
  likes: number;
  comments: number;
  shares: number;
  saved: boolean;
  privacy: PrivacyLevel;
  commentsList: Comment[];
  showComments: boolean;
}

// Dữ liệu mẫu cho hashtags
export const sampleHashtags: Hashtag[] = [
  { id: 1, name: 'đổimới' },
  { id: 2, name: 'phươngphápdạyhọc' },
  { id: 3, name: 'khoahọcdữliệu' },
  { id: 4, name: 'trítuệnhântạo' },
  { id: 5, name: 'giáodục' },
  { id: 6, name: 'họctậptrựctuyến' }
];

// Dữ liệu mẫu cho bình luận
export const sampleComments: Comment[] = [
  {
    id: 1,
    postId: 1,
    userId: 2,
    user: {
      id: 2,
      name: 'Trần Thị B',
      avatar: 'https://i.pravatar.cc/300?img=2',
      role: UserRole.USER
    },
    content: 'Phương pháp này rất hiệu quả, tôi cũng đã áp dụng!',
    createdAt: '1 giờ trước',
    likes: 3,
    parentId: null,
    replies: [
      {
        id: 4,
        postId: 1,
        userId: 1,
        user: {
          id: 1,
          name: 'Nguyễn Văn A',
          avatar: 'https://i.pravatar.cc/300?img=1',
          role: UserRole.USER
        },
        content: 'Cảm ơn bạn! Rất vui khi biết phương pháp này hữu ích.',
        createdAt: '30 phút trước',
        likes: 1,
        parentId: 1,
        replies: []
      }
    ]
  },
  {
    id: 2,
    postId: 1,
    userId: 3,
    user: {
      id: 3,
      name: 'Lê Văn C',
      avatar: 'https://i.pravatar.cc/300?img=3',
      role: UserRole.USER
    },
    content: 'Bạn có thể chia sẻ thêm chi tiết về phương pháp này không?',
    createdAt: '45 phút trước',
    likes: 2,
    parentId: null,
    replies: []
  },
  {
    id: 3,
    postId: 2,
    userId: 2,
    user: {
      id: 2,
      name: 'Trần Thị B',
      avatar: 'https://i.pravatar.cc/300?img=2',
      role: UserRole.USER
    },
    content: 'Tài liệu rất hữu ích, cảm ơn bạn đã chia sẻ!',
    createdAt: '2 giờ trước',
    likes: 7,
    parentId: null,
    replies: []
  }
];

// Dữ liệu mẫu cho bài đăng
export const userPosts: Post[] = [
  {
    id: 1,
    userId: 1,
    user: {
      id: 1,
      name: 'Nguyễn Văn A',
      avatar: 'https://i.pravatar.cc/300?img=1',
      role: UserRole.USER
    },
    content: 'Hôm nay tôi đã thử nghiệm phương pháp dạy học mới với học sinh và nhận được phản hồi tích cực!',
    hashtags: [sampleHashtags[0], sampleHashtags[1]],
    image: 'https://images.unsplash.com/photo-1427504494785-3a9ca7044f45?q=80&w=1000&auto=format&fit=crop',
    createdAt: '2 giờ trước',
    likes: 24,
    comments: 8,
    shares: 3,
    saved: true,
    privacy: PrivacyLevel.PUBLIC,
    commentsList: [sampleComments[0], sampleComments[1]],
    showComments: false
  },
  {
    id: 2,
    userId: 1,
    user: {
      id: 1,
      name: 'Nguyễn Văn A',
      avatar: 'https://i.pravatar.cc/300?img=1',
      role: UserRole.USER
    },
    content: 'Chia sẻ một số tài liệu học tập mới nhất về khoa học dữ liệu mà tôi vừa tổng hợp được. Hy vọng giúp ích cho mọi người.',
    hashtags: [sampleHashtags[2]],
    image: 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?q=80&w=1000&auto=format&fit=crop',
    createdAt: '1 ngày trước',
    likes: 42,
    comments: 15,
    shares: 7,
    saved: false,
    privacy: PrivacyLevel.FRIENDS,
    commentsList: [sampleComments[2]],
    showComments: false
  },
  {
    id: 3,
    userId: 1,
    user: {
      id: 1,
      name: 'Nguyễn Văn A',
      avatar: 'https://i.pravatar.cc/300?img=1',
      role: UserRole.USER
    },
    content: 'Vừa hoàn thành khóa học về trí tuệ nhân tạo trong giáo dục. Có ai quan tâm đến chủ đề này không?',
    hashtags: [sampleHashtags[3], sampleHashtags[4]],
    createdAt: '3 ngày trước',
    likes: 18,
    comments: 6,
    shares: 2,
    saved: false,
    privacy: PrivacyLevel.PRIVATE,
    commentsList: [],
    showComments: false
  },
  {
    id: 4,
    userId: 1,
    user: {
      id: 1,
      name: 'Nguyễn Văn A',
      avatar: 'https://i.pravatar.cc/300?img=1',
      role: UserRole.USER
    },
    content: 'Chia sẻ kinh nghiệm xây dựng cộng đồng học tập trực tuyến hiệu quả. Mọi người cùng thảo luận nhé!',
    hashtags: [sampleHashtags[5], sampleHashtags[4]],
    image: 'https://images.unsplash.com/photo-1531482615713-2afd69097998?q=80&w=1000&auto=format&fit=crop',
    createdAt: '4 ngày trước',
    likes: 35,
    comments: 12,
    shares: 5,
    saved: true,
    privacy: PrivacyLevel.PUBLIC,
    commentsList: [],
    showComments: false
  },
  {
    id: 5,
    userId: 1,
    user: {
      id: 1,
      name: 'Nguyễn Văn A',
      avatar: 'https://i.pravatar.cc/300?img=1',
      role: UserRole.USER
    },
    content: 'Đang nghiên cứu về phương pháp giảng dạy tích hợp công nghệ. Ai có kinh nghiệm về lĩnh vực này không?',
    hashtags: [sampleHashtags[0], sampleHashtags[4], sampleHashtags[3]],
    image: 'https://images.unsplash.com/photo-1509062522246-3755977927d7?q=80&w=1000&auto=format&fit=crop',
    createdAt: '5 ngày trước',
    likes: 29,
    comments: 10,
    shares: 4,
    saved: false,
    privacy: PrivacyLevel.FRIENDS,
    commentsList: [],
    showComments: false
  }
];
