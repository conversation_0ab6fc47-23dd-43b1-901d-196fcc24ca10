"use client";

import * as yup from "yup";

import {
  FaBriefcase,
  FaBuilding,
  FaCalendarAlt,
  FaCamera,
  FaEdit,
  FaEnvelope,
  FaGraduationCap,
  FaMapMarkerAlt,
  FaPhone,
  FaSpinner,
  FaVenusMars
} from "react-icons/fa";
import { MdVerified } from "react-icons/md";
import { RiBuilding2Line } from "react-icons/ri";

import { useRef, useState } from "react";
import {
  formatDateForApi,
  formatDateForDisplay,
  formatDateForInput
} from "../../utils/dateUtils";

import DefaultAvatar from "@/components/ui/DefaultAvatar";
import DefaultCover from "@/components/ui/DefaultCover";
import useUploadFile from "@/hook/useUploadFile";
import { yupResolver } from "@hookform/resolvers/yup";
import Image from "next/image";
import { useForm } from "react-hook-form";
import { toast } from "react-toastify";
import { useUserSocialNetwork } from "../../_contexts/UserSocialNetworkContext";

// Schema validation cho form chỉnh sửa thông tin
const schema = yup.object({
  firstName: yup.string().required("Họ là bắt buộc"),
  lastName: yup.string().required("Tên là bắt buộc"),
  job: yup.string().nullable().optional(),
  bio: yup.string().nullable().optional(),
  address: yup.string().nullable().optional(),
  education: yup.string().nullable().optional(),
  phoneNumber: yup
    .string()
    .nullable()
    .matches(/^[0-9]*$/, "Số điện thoại chỉ được chứa số")
    .optional(),
  dateOfBirth: yup.string().nullable().optional(),
  gender: yup.string().nullable().optional(),
  thumbnail: yup.string().nullable().optional(),
  background: yup.string().nullable().optional()
});

// Định nghĩa kiểu dữ liệu cho form
type FormData = yup.InferType<typeof schema> & {
  orgName?: string | null; // Thêm trường orgName vào kiểu dữ liệu form
};

export default function UserProfileHeader() {
  // Sử dụng context để lấy dữ liệu người dùng
  const { userData, isLoading: loadingUserData, setUserData, updateUserData } = useUserSocialNetwork();
  const [isEditingProfile, setIsEditingProfile] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const { uploadSocialNetworkFile } = useUploadFile();
  // Khởi tạo react-hook-form
  const {
    register,
    handleSubmit,
    reset,
    formState: { errors }
  } = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      firstName: "",
      lastName: "",
      job: null,
      bio: null,
      address: null,
      education: null,
      phoneNumber: null,
      dateOfBirth: null,
      gender: null,
      thumbnail: null,
      background: null,
      orgName: null
    }
  });

  // Hàm xử lý chỉnh sửa thông tin người dùng
  const handleEditProfile = () => {
    setIsEditingProfile(true);
    // Reset form với giá trị từ userData, xử lý trường hợp userData có thể là null
    if (userData) {
      reset({
        firstName: userData.firstName || '',
        lastName: userData.lastName || '',
        job: userData.job || null,
        bio: userData.bio || null,
        address: userData.address || null,
        education: userData.education || null,
        phoneNumber: userData.phoneNumber || null,
        dateOfBirth: userData.dateOfBirth ? formatDateForInput(userData.dateOfBirth) : null,
        gender: userData.gender || null,
        thumbnail: userData.thumbnail || null,
        background: userData.background || null,
        orgName: userData.orgName || null
      });
    }
  };

  // Hàm xử lý lưu thông tin người dùng
  const onSubmit = async (data: FormData) => {
    try {
      setIsSaving(true);
      const formattedData = {
        ...data,
        dateOfBirth: formatDateForApi(data.dateOfBirth),
        fullName: `${data.firstName} ${data.lastName}`
      };

      if (userData?.avatarFile) {
        try {
          const { data: url } = await uploadSocialNetworkFile(
            userData.avatarFile
          );
          formattedData.thumbnail = url;
        } catch (error) {
          console.error("Lỗi khi lấy URL cho avatar:", error);
        }
      }

      if (userData?.coverFile) {
        try {
          const { data: url } = await uploadSocialNetworkFile(
            userData.coverFile
          );
          formattedData.background = url;
        } catch (error) {
          console.error("Lỗi khi lấy URL cho cover photo:", error);
        }
      }

      // Gọi API cập nhật thông tin người dùng thông qua context
      await updateUserData({
        ...formattedData,
        avatarFile: undefined,
        coverFile: undefined
      });
      toast.success("Cập nhật thông tin thành công");
      setIsEditingProfile(false);
    } catch (error) {
      console.error("Lỗi khi cập nhật thông tin người dùng:", error);
      toast.error("Có lỗi xảy ra khi cập nhật thông tin");
    } finally {
      setIsSaving(false);
    }
  };

  // Hàm xử lý cập nhật cover photo
  const handleUpdateCoverPhoto = (url: string, file: File) => {
    setUserData({
      ...userData!,
      coverUrl: url,
      coverFile: file
    });
  };

  // Hàm xử lý cập nhật avatar
  const handleUpdateAvatar = (url: string, file: File) => {
    if (userData) {
      setUserData({
        ...userData,
        avatarUrl: url,
        avatarFile: file
      });
    }
  };

  // Tham chiếu đến input file ẩn
  const coverPhotoInputRef = useRef<HTMLInputElement>(null);
  const avatarInputRef = useRef<HTMLInputElement>(null);

  // Hàm xử lý thay đổi cover photo
  const handleCoverPhotoChange = () => {
    // Kích hoạt click vào input file ẩn
    if (coverPhotoInputRef.current) {
      coverPhotoInputRef.current.click();
    }
  };

  // Hàm xử lý khi file cover photo được chọn
  const handleCoverPhotoFileChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (event) => {
        const imageUrl = event.target?.result as string;
        if (imageUrl) {
          handleUpdateCoverPhoto(imageUrl, file);
        }
      };
      reader.readAsDataURL(file);
    }
  };

  // Hàm xử lý thay đổi avatar
  const handleAvatarChange = () => {
    // Kích hoạt click vào input file ẩn
    if (avatarInputRef.current) {
      avatarInputRef.current.click();
    }
  };

  // Hàm xử lý khi file avatar được chọn
  const handleAvatarFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (event) => {
        const imageUrl = event.target?.result as string;
        if (imageUrl) {
          handleUpdateAvatar(imageUrl, file);
        }
      };
      reader.readAsDataURL(file);
    }
  };

  if (loadingUserData || !userData)
    return (
      <>
        {/* Cover Photo Skeleton */}
        <div className="relative h-64 w-full animate-pulse overflow-hidden bg-gray-200 md:h-80"></div>

        {/* Profile Header Skeleton */}
        <div className="container relative mx-auto px-4">
          <div className="-mt-20 mb-6 rounded-lg bg-white p-6 shadow-md md:p-8">
            <div className="flex flex-col items-center gap-6 md:flex-row md:items-start">
              {/* Avatar Skeleton */}
              <div className="relative">
                <div className="relative h-32 w-32 animate-pulse overflow-hidden rounded-full border-4 border-white bg-gray-300 md:h-40 md:w-40"></div>
              </div>

              {/* User Info Skeleton */}
              <div className="flex-1">
                <div className="flex w-full flex-col-reverse justify-between gap-4 md:flex-row md:items-center">
                  <div>
                    {/* Name Skeleton */}
                    <div className="mb-2 h-8 w-48 animate-pulse rounded-md bg-gray-300"></div>
                    {/* Role Skeleton */}
                    <div className="h-4 w-32 animate-pulse rounded-md bg-gray-300"></div>
                  </div>
                  {/* Button Skeleton */}
                  <div className="h-10 w-40 animate-pulse rounded-md bg-gray-300"></div>
                </div>

                {/* Bio Skeleton */}
                <div className="mt-4">
                  <div className="h-16 w-full animate-pulse rounded-md bg-gray-300"></div>
                </div>

                {/* User Details Skeleton */}
                <div className="mt-4 flex flex-wrap gap-x-6 gap-y-2">
                  <div className="h-6 w-32 animate-pulse rounded-md bg-gray-300"></div>
                  <div className="h-6 w-32 animate-pulse rounded-md bg-gray-300"></div>
                  <div className="h-6 w-32 animate-pulse rounded-md bg-gray-300"></div>
                </div>

                {/* Stats Skeleton */}
                <div className="mt-6 flex flex-wrap gap-6">
                  <div className="text-center">
                    <div className="mx-auto mb-1 h-8 w-8 animate-pulse rounded-md bg-gray-300"></div>
                    <div className="h-4 w-16 animate-pulse rounded-md bg-gray-300"></div>
                  </div>
                  <div className="text-center">
                    <div className="mx-auto mb-1 h-8 w-8 animate-pulse rounded-md bg-gray-300"></div>
                    <div className="h-4 w-16 animate-pulse rounded-md bg-gray-300"></div>
                  </div>
                  <div className="text-center">
                    <div className="mx-auto mb-1 h-8 w-8 animate-pulse rounded-md bg-gray-300"></div>
                    <div className="h-4 w-16 animate-pulse rounded-md bg-gray-300"></div>
                  </div>
                  <div className="text-center">
                    <div className="mx-auto mb-1 h-8 w-8 animate-pulse rounded-md bg-gray-300"></div>
                    <div className="h-4 w-16 animate-pulse rounded-md bg-gray-300"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </>
    );

  return (
    <>
      {/* Input file ẩn cho cover photo */}
      <input
        type="file"
        ref={coverPhotoInputRef}
        onChange={handleCoverPhotoFileChange}
        accept="image/*"
        className="hidden"
      />

      {/* Input file ẩn cho avatar */}
      <input
        type="file"
        ref={avatarInputRef}
        onChange={handleAvatarFileChange}
        accept="image/*"
        className="hidden"
      />

      {/* Cover Image */}
      <div className="relative h-64 w-full overflow-hidden md:h-80">
        {userData?.coverUrl ? (
          <Image
            src={userData.coverUrl}
            alt="Cover"
            fill
            className="object-cover"
          />
        ) : (
          <DefaultCover height="100%" /> // Sử dụng DefaultCover khi không có coverUrl
        )}
        {isEditingProfile && (
          <button
            className="absolute bottom-4 right-4 rounded-md bg-white p-2 px-4 shadow transition hover:bg-gray-100"
            onClick={handleCoverPhotoChange}
          >
            <div className="flex items-center gap-2">
              <FaCamera className="text-gray-700" />
              <span className="text-sm font-medium text-gray-700">
                Thay đổi ảnh bìa
              </span>
            </div>
          </button>
        )}
      </div>

      {/* Profile Header */}
      <div className="container relative mx-auto px-4">
        <div className="-mt-20 mb-6 rounded-lg bg-white p-6 shadow-md md:p-8">
          <div className="flex flex-col items-center gap-6 md:flex-row md:items-start">
            {/* Avatar */}
            <div className="relative">
              <div className="relative h-32 w-32 overflow-hidden rounded-full border-4 border-white md:h-40 md:w-40">
                {userData?.avatarUrl ? (
                  <Image
                    src={userData.avatarUrl}
                    alt={
                      userData?.fullName ||
                      `${userData?.firstName} ${userData?.lastName}`
                    }
                    fill
                    className="object-cover"
                  />
                ) : (
                  <DefaultAvatar
                    name={
                      userData?.fullName ||
                      `${userData?.firstName} ${userData?.lastName}` ||
                      "User"
                    }
                    size={200}
                  />
                )}
              </div>
              {isEditingProfile && (
                <button
                  className="absolute bottom-2 right-2 rounded-full bg-white p-2 shadow transition hover:bg-gray-100"
                  onClick={handleAvatarChange}
                >
                  <FaCamera className="text-sm text-gray-700" />
                </button>
              )}
            </div>

            {/* User Info */}
            <div className="flex-1">
              <div className="flex w-full flex-col-reverse justify-between gap-4 md:flex-row md:items-center">
                <div>
                  {isEditingProfile ? (
                    <div className="space-y-2">
                      <div className="text-2xl font-bold md:text-3xl">
                        {userData?.fullName ||
                          `${userData?.firstName} ${userData?.lastName}`}
                      </div>
                      <div className="flex gap-2">
                        <div className="flex-1">
                          <label className="mb-1 text-xs text-gray-600">
                            Họ
                          </label>
                          <input
                            type="text"
                            {...register("firstName")}
                            className={`w-full rounded-md border ${errors.firstName ? "border-red-500" : "border-gray-300"} p-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500`}
                          />
                          {errors.firstName && (
                            <p className="mt-1 text-xs text-red-500">
                              {errors.firstName.message}
                            </p>
                          )}
                        </div>
                        <div className="flex-1">
                          <label className="mb-1 text-xs text-gray-600">
                            Tên
                          </label>
                          <input
                            type="text"
                            {...register("lastName")}
                            className={`w-full rounded-md border ${errors.lastName ? "border-red-500" : "border-gray-300"} p-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500`}
                          />
                          {errors.lastName && (
                            <p className="mt-1 text-xs text-red-500">
                              {errors.lastName.message}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  ) : (
                    <h1 className="flex text-2xl font-bold md:text-3xl">
                      {userData?.fullName ||
                        `${userData?.firstName} ${userData?.lastName}`}
                      {userData?.orgName && (
                        <div className="ml-2 flex items-center rounded text-xl text-blue-600">
                          <MdVerified />
                          <div className="ml-2 flex items-center rounded-full bg-green-500 px-3 py-1 text-sm font-medium text-white shadow-sm">
                            <RiBuilding2Line></RiBuilding2Line>
                            <span className="ml-1"> {userData?.orgName}</span>
                          </div>
                        </div>
                      )}
                    </h1>
                  )}
                </div>
                <div className="flex gap-3">
                  {isEditingProfile ? (
                    <button
                      type="button"
                      className="flex items-center gap-2 rounded-md bg-green-600 px-4 py-2 text-white transition hover:bg-green-700 disabled:opacity-70"
                      onClick={handleSubmit(onSubmit)}
                      disabled={isSaving}
                    >
                      {isSaving ? (
                        <>
                          <FaSpinner className="animate-spin" />
                          <span>Đang lưu...</span>
                        </>
                      ) : (
                        "Lưu thông tin"
                      )}
                    </button>
                  ) : (
                    <button
                      className="flex items-center gap-2 rounded-md bg-blue-600 px-4 py-2 text-white transition hover:bg-blue-700"
                      onClick={handleEditProfile}
                    >
                      <FaEdit className="text-sm" /> Chỉnh sửa trang cá nhân
                    </button>
                  )}
                </div>
              </div>

              {/* User Bio */}
              <div className="mt-4">
                {isEditingProfile ? (
                  <>
                    <textarea
                      {...register("bio")}
                      className={`w-full rounded-md border ${errors.bio ? "border-red-500" : "border-gray-300"} p-2 text-gray-700 focus:border-blue-500 focus:outline-none`}
                      rows={3}
                    />
                    {errors.bio && (
                      <p className="mt-1 text-sm text-red-500">
                        {errors.bio.message}
                      </p>
                    )}
                  </>
                ) : (
                  <p className="text-gray-700">{userData?.bio}</p>
                )}
              </div>

              {/* User Details */}
              {isEditingProfile ? (
                <div className="mt-4 rounded-lg border border-gray-200 bg-gray-50 p-4">
                  <h3 className="mb-3 font-semibold text-gray-700">
                    Thông tin cá nhân
                  </h3>
                  <div className="space-y-4">
                    <div className="flex flex-col">
                      <label className="mb-1 flex items-center gap-2 text-sm text-gray-600">
                        <FaMapMarkerAlt className="text-gray-500" /> Địa điểm
                      </label>
                      <input
                        type="text"
                        {...register("address")}
                        className={`w-full rounded-md border ${errors.address ? "border-red-500" : "border-gray-300"} p-2 focus:outline-none focus:ring-2 focus:ring-blue-500`}
                        placeholder="Ví dụ: Hà Nội, Việt Nam"
                      />
                      {errors.address && (
                        <p className="mt-1 text-sm text-red-500">
                          {errors.address.message}
                        </p>
                      )}
                    </div>

                    <div className="flex flex-col">
                      <label className="mb-1 flex items-center gap-2 text-sm text-gray-600">
                        <FaGraduationCap className="text-gray-500" /> Học vấn
                      </label>
                      <input
                        type="text"
                        {...register("education")}
                        className={`w-full rounded-md border ${errors.education ? "border-red-500" : "border-gray-300"} p-2 focus:outline-none focus:ring-2 focus:ring-blue-500`}
                        placeholder="Ví dụ: Thạc sĩ Giáo dục, Đại học Sư phạm Hà Nội"
                      />
                      {errors.education && (
                        <p className="mt-1 text-sm text-red-500">
                          {errors.education.message}
                        </p>
                      )}
                    </div>

                    <div className="flex flex-col">
                      <label className="mb-1 flex items-center gap-2 text-sm text-gray-600">
                        <FaBriefcase className="text-gray-500" /> Nghề nghiệp
                      </label>
                      <input
                        type="text"
                        {...register("job")}
                        className={`w-full rounded-md border ${errors.job ? "border-red-500" : "border-gray-300"} p-2 focus:outline-none focus:ring-2 focus:ring-blue-500`}
                        placeholder="Ví dụ: Giáo viên"
                      />
                      {errors.job && (
                        <p className="mt-1 text-sm text-red-500">
                          {errors.job.message}
                        </p>
                      )}
                    </div>

                    <div className="flex flex-col">
                      <label className="mb-1 flex items-center gap-2 text-sm text-gray-600">
                        <FaPhone className="text-gray-500" /> Số điện thoại
                      </label>
                      <input
                        type="text"
                        {...register("phoneNumber")}
                        className={`w-full rounded-md border ${errors.phoneNumber ? "border-red-500" : "border-gray-300"} p-2 focus:outline-none focus:ring-2 focus:ring-blue-500`}
                        placeholder="Ví dụ: 0912345678"
                      />
                      {errors.phoneNumber && (
                        <p className="mt-1 text-sm text-red-500">
                          {errors.phoneNumber.message}
                        </p>
                      )}
                    </div>

                    <div className="flex flex-col">
                      <label className="mb-1 flex items-center gap-2 text-sm text-gray-600">
                        <FaCalendarAlt className="text-gray-500" /> Ngày sinh
                      </label>
                      <input
                        type="date"
                        {...register("dateOfBirth")}
                        className={`w-full rounded-md border ${errors.dateOfBirth ? "border-red-500" : "border-gray-300"} p-2 focus:outline-none focus:ring-2 focus:ring-blue-500`}
                      />
                      {errors.dateOfBirth && (
                        <p className="mt-1 text-sm text-red-500">
                          {errors.dateOfBirth.message}
                        </p>
                      )}
                    </div>

                    <div className="flex flex-col">
                      <label className="mb-1 flex items-center gap-2 text-sm text-gray-600">
                        <FaVenusMars className="text-gray-500" /> Giới tính
                      </label>
                      <select
                        {...register("gender")}
                        className={`w-full rounded-md border ${errors.gender ? "border-red-500" : "border-gray-300"} p-2 focus:outline-none focus:ring-2 focus:ring-blue-500`}
                      >
                        <option value="">Chọn giới tính</option>
                        <option value="Nam">Nam</option>
                        <option value="Nữ">Nữ</option>
                        <option value="Khác">Khác</option>
                      </select>
                      {errors.gender && (
                        <p className="mt-1 text-sm text-red-500">
                          {errors.gender.message}
                        </p>
                      )}
                    </div>

                    <div className="flex flex-col">
                      <label className="mb-1 flex items-center gap-2 text-sm text-gray-600">
                        <FaEnvelope className="text-gray-500" /> Email
                      </label>
                      <div className="w-full rounded-md border border-gray-200 bg-gray-100 p-2 text-gray-600">
                        {userData.email}
                        <div className="text-xs text-gray-500">
                          (Không thể thay đổi email)
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="mt-4 flex flex-wrap gap-x-6 gap-y-2 text-gray-600">
                  {userData?.address && (
                    <div className="flex items-center gap-2">
                      <FaMapMarkerAlt className="text-gray-500" />
                      <span>{userData.address}</span>
                    </div>
                  )}
                  {userData?.education && (
                    <div className="flex items-center gap-2">
                      <FaGraduationCap className="text-gray-500" />
                      <span>{userData.education}</span>
                    </div>
                  )}
                  {userData?.job && (
                    <div className="flex items-center gap-2">
                      <FaBriefcase className="text-gray-500" />
                      <span>{userData.job}</span>
                    </div>
                  )}
                  {userData?.phoneNumber && (
                    <div className="flex items-center gap-2">
                      <FaPhone className="text-gray-500" />
                      <span>{userData.phoneNumber}</span>
                    </div>
                  )}
                  {userData?.dateOfBirth && (
                    <div className="flex items-center gap-2">
                      <FaCalendarAlt className="text-gray-500" />
                      <span>{formatDateForDisplay(userData.dateOfBirth)}</span>
                    </div>
                  )}
                  {userData?.gender && (
                    <div className="flex items-center gap-2">
                      <FaVenusMars className="text-gray-500" />
                      <span>{userData.gender}</span>
                    </div>
                  )}
                  {userData?.orgName && (
                    <div className="flex items-center gap-2">
                      <FaBuilding className="text-gray-500" />
                      <span>{userData.orgName}</span>
                    </div>
                  )}
                  {userData?.email && (
                    <div className="flex items-center gap-2">
                      <FaEnvelope className="text-gray-500" />
                      <span>{userData.email}</span>
                    </div>
                  )}
                </div>
              )}

              {/* Stats */}
              <div className="mt-6 flex flex-wrap gap-6">
                <div className="text-center">
                  <div className="text-xl font-bold">
                    {userData?.numberOfPost || 0}
                  </div>
                  <div className="text-sm text-gray-600">Bài viết</div>
                </div>
                <div className="text-center">
                  <div className="text-xl font-bold">
                    {userData?.numberOfFriend || 0}
                  </div>
                  <div className="text-sm text-gray-600">Bạn bè</div>
                </div>
                <div className="text-center">
                  <div className="text-xl font-bold">
                    {userData?.numberOfLike || 0}
                  </div>
                  <div className="text-sm text-gray-600">Lượt thích</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
