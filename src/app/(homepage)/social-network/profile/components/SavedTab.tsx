"use client";

import { useCallback, useEffect, useRef, useState } from "react";
import { toast } from "react-toastify";

import useUploadFile from "@/hook/useUploadFile";
import socialNetworkServices from "@/services/social-network/socialNetworkServices";
import { Post } from "@/services/social-network/types/types";
import { ExtendedPost } from "./PostsTab";
import { PostItem } from "./posts";

// Sử dụng ExtendedPost từ PostsTab

const DEFAULT_SIZE = 5;

const SavedTab = () => {
  // States cho component
  const [savedPosts, setSavedPosts] = useState<ExtendedPost[]>([]);
  const [page, setPage] = useState(0);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const observerTarget = useRef<HTMLDivElement>(null);
  const { viewFile } = useUploadFile();
  const [initUI, setInitUI] = useState(true);

  // Lấy danh sách bài đăng đã lưu
  const fetchSavedPosts = async (
    type: "reset" | "infinite",
    page: number = 0,
    size: number = DEFAULT_SIZE
  ) => {
    setLoading(true);
    try {
      const response = await socialNetworkServices.getSavedPostsSocialNetwork({
        page,
        size
      });

      if (response.data?.data?.content) {
        // Lấy danh sách bài đăng từ API
        const postsData = response.data.data.content;

        // Xử lý lấy URL cho thumbnail và files của mỗi bài đăng
        const postsWithUrls = await Promise.all(
          postsData.map(async (post: Post) => {
            let avatarUrl = undefined;
            let fileUrls: string[] = [];
            let postReferWithUrls = null;

            // Nếu có thumbnail, gọi API viewFile để lấy URL
            if (post.thumbnail) {
              try {
                avatarUrl = (await viewFile(
                  post.thumbnail,
                  "social-network"
                )) as string;
              } catch (error) {
                console.error("Lỗi khi lấy URL cho thumbnail:", error);
              }
            }

            // Nếu có files, gọi API viewFile để lấy URL cho từng file
            if (post.files && post.files.length > 0) {
              try {
                // Gọi API viewFile cho từng fileId trong mảng files
                const filePromises = post.files.map((fileId) =>
                  viewFile(fileId, "social-network")
                );
                // Ép kiểu kết quả trả về thành string[]
                fileUrls = (await Promise.all(filePromises)) as string[];
              } catch (error) {
                console.error("Lỗi khi lấy URL cho files:", error);
              }
            }

            // Xử lý postRefer nếu có
            if (post.postRefer && post.referPostAvailable) {
              try {
                // Xử lý avatar của postRefer
                let referAvatarUrl = undefined;
                if (post.postRefer.thumbnail) {
                  referAvatarUrl = (await viewFile(
                    post.postRefer.thumbnail,
                    "social-network"
                  )) as string;
                }

                // Xử lý files của postRefer
                let referFileUrls: string[] = [];
                if (post.postRefer.files && post.postRefer.files.length > 0) {
                  const referFilePromises = post.postRefer.files.map((fileId) =>
                    viewFile(fileId, "social-network")
                  );
                  referFileUrls = (await Promise.all(referFilePromises)) as string[];
                }

                // Tạo postRefer với các URL đã xử lý
                postReferWithUrls = {
                  ...post.postRefer,
                  avatarUrl: referAvatarUrl,
                  fileUrls: referFileUrls
                };
              } catch (error) {
                console.error("Lỗi khi xử lý postRefer:", error);
              }
            }

            // Trả về bài đăng với các URL đã xử lý
            return {
              ...post,
              avatarUrl,
              fileUrls,
              postRefer: postReferWithUrls
            };
          })
        );

        // Cập nhật state tùy thuộc vào loại fetch
        if (type === "reset") {
          setSavedPosts(postsWithUrls);
        } else {
          setSavedPosts((prevPosts) => [...prevPosts, ...postsWithUrls]);
        }

        // Kiểm tra xem còn bài đăng để tải không
        setHasMore(postsWithUrls.length === size);
      } else {
        // Nếu không có dữ liệu, đặt hasMore thành false
        setHasMore(false);
      }
      setInitUI(false);
    } catch (error) {
      console.error("Lỗi khi lấy danh sách bài đăng đã lưu:", error);
      toast.error("Không thể tải danh sách bài đăng đã lưu");
      setInitUI(false);
    } finally {
      setLoading(false);
    }
  };

  // Xử lý bỏ lưu bài đăng
  const handleUnsavePost = async (postId: number) => {
    try {
      await socialNetworkServices.unSavePostSocialNetwork(postId);
      // Cập nhật lại danh sách sau khi bỏ lưu
      fetchSavedPosts("reset", 0, DEFAULT_SIZE * (page + 1));
      toast.success("Đã bỏ lưu bài đăng thành công");
    } catch (error) {
      console.error("Lỗi khi bỏ lưu bài đăng:", error);
      toast.error("Không thể bỏ lưu bài đăng");
    }
  };

  // Xử lý infinite scroll
  const loadMorePosts = useCallback(() => {
    if (loading || !hasMore) return;
    setPage(page + 1);
    // fetchSavedPosts("infinite", page + 1);
  }, [hasMore, loading, page]);

  const finishInitUI = () => {
    setInitUI(false);
  };

  // Thiết lập Intersection Observer cho infinite scroll
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          loadMorePosts();
        }
      },
      { threshold: 1.0 }
    );

    if (observerTarget.current) {
      observer.observe(observerTarget.current);
    }

    return () => {
      if (observerTarget.current) {
        observer.unobserve(observerTarget.current);
      }
    };
  }, [loadMorePosts]);

  // Lấy danh sách bài đăng đã lưu khi component được tạo
  useEffect(() => {
    fetchSavedPosts("reset");
  }, []);

  return (
    <div className="space-y-6">
      {/* Danh sách bài đăng đã lưu */}
      {savedPosts.map((post, index) => (
        <div key={post.postSocialNetworkId || index}>
          <PostItem
            post={post}
            onDelete={() => handleUnsavePost(post.postSocialNetworkId)}
            resetList={() => {
              fetchSavedPosts("reset", 0, DEFAULT_SIZE * (page + 1));
            }}
            isSavedTab={true}
          />
        </div>
      ))}

      {/* Hiển thị khi không có bài đăng nào */}
      {savedPosts.length === 0 && !loading && !initUI && (
        <div className="rounded-lg bg-white p-8 text-center shadow-md">
          <div className="mb-2 text-gray-500">Chưa có bài đăng nào được lưu</div>
          <p className="text-gray-600">
            Hãy lưu các bài đăng yêu thích để xem lại sau!
          </p>
        </div>
      )}

      {/* Loading indicator & observer target */}
      {hasMore && (
        <div ref={observerTarget} className="py-4 text-center">
          {loading && (
            <div className="flex items-center justify-center space-x-2">
              <div
                className="h-3 w-3 animate-bounce rounded-full bg-blue-600"
                style={{ animationDelay: "0ms" }}
              ></div>
              <div
                className="h-3 w-3 animate-bounce rounded-full bg-blue-600"
                style={{ animationDelay: "150ms" }}
              ></div>
              <div
                className="h-3 w-3 animate-bounce rounded-full bg-blue-600"
                style={{ animationDelay: "300ms" }}
              ></div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default SavedTab;
