"use client";

import { Comment, UserRole, userData } from "../../data";

import { FaComment } from "react-icons/fa";
import Image from "next/image";
import ReplyItem from "./ReplyItem";
import { useState } from "react";

interface CommentItemProps {
  comment: Comment;
  currentUserId: number;
  userRole: UserRole;
  onAddReply: (content: string) => void;
  onDeleteComment: (commentId: number, parentId?: number) => void;
  onLikeComment: (commentId: number, parentId?: number) => void;
}

const CommentItem = ({
  comment,
  currentUserId,
  userRole,
  onAddReply,
  onDeleteComment,
  onLikeComment
}: CommentItemProps) => {
  const [isReplying, setIsReplying] = useState(false);
  const [replyContent, setReplyContent] = useState("");

  // Hàm kiểm tra quyền chỉnh sửa bình luận
  const canEditComment = () => {
    return comment.userId === currentUserId || userRole === UserRole.ADMIN;
  };

  const handleAddReply = () => {
    if (!replyContent.trim()) return;
    onAddReply(replyContent);
    setReplyContent("");
    setIsReplying(false);
  };

  return (
    <div className="border-b border-gray-100 pb-3 last:border-0">
      {/* Comment */}
      <div className="flex items-start gap-3">
        <Image
          src={comment.user.avatar}
          alt={comment.user.name}
          width={32}
          height={32}
          className="rounded-full object-cover"
        />
        <div className="flex-1">
          <div className="rounded-lg bg-gray-100 p-3">
            <div className="font-medium">
              {comment.user.name}
            </div>
            <div>{comment.content}</div>
          </div>

          <div className="mt-1 flex items-center gap-4 text-sm">
            <button
              onClick={() => onLikeComment(comment.id)}
              className="text-gray-500 hover:text-blue-600"
            >
              Thích ({comment.likes})
            </button>
            <button
              onClick={() => setIsReplying(true)}
              className="text-gray-500 hover:text-blue-600"
            >
              Trả lời
            </button>
            <span className="text-gray-500">
              {comment.createdAt}
            </span>

            {canEditComment() && (
              <button
                onClick={() => onDeleteComment(comment.id)}
                className="ml-auto text-red-500 hover:text-red-700"
              >
                Xóa
              </button>
            )}
          </div>

          {/* Reply Form */}
          {isReplying && (
            <div className="mt-2 flex items-start gap-2">
              <Image
                src={userData.avatar}
                alt={userData.name}
                width={24}
                height={24}
                className="rounded-full object-cover"
              />
              <div className="flex-1">
                <div className="flex items-center gap-2">
                  <input
                    type="text"
                    value={replyContent}
                    onChange={(e) => setReplyContent(e.target.value)}
                    placeholder="Viết trả lời..."
                    className="flex-1 rounded-full border border-gray-200 px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  <div className="flex gap-1">
                    <button
                      onClick={handleAddReply}
                      disabled={!replyContent.trim()}
                      className="rounded-full bg-blue-600 p-1 text-sm text-white hover:bg-blue-700 disabled:cursor-not-allowed disabled:opacity-50 w-6 h-6 flex justify-center items-center"
                    >
                      <FaComment className="text-xs" />
                    </button>
                    <button
                      onClick={() => setIsReplying(false)}
                      className="rounded-full bg-gray-200 p-1 text-sm text-gray-600 hover:bg-gray-300 w-6 h-6 flex justify-center items-center"
                    >
                      ✕
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Replies */}
          {comment.replies.length > 0 && (
            <div className="ml-6 mt-2 space-y-3">
              {comment.replies.map((reply) => (
                <ReplyItem
                  key={reply.id}
                  reply={reply}
                  parentId={comment.id}
                  currentUserId={currentUserId}
                  userRole={userRole}
                  onDeleteReply={(replyId) => onDeleteComment(replyId, comment.id)}
                  onLikeReply={(replyId) => onLikeComment(replyId, comment.id)}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CommentItem;
