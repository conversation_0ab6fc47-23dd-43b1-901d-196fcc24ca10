"use client";

import { useState } from "react";

import HashtagDisplay from "../../../../../../components/common/HashtagDisplay";

interface HashtagInputProps {
  hashtags: string[];
  onChange: (hashtags: string[]) => void;
  placeholder?: string;
  className?: string;
  inputClassName?: string;
  tagClassName?: string;
  buttonClassName?: string;
  showTagIcon?: boolean;
}

const HashtagInput = ({
  hashtags,
  onChange,
  placeholder = "Thêm hashtag (Ví dụ: giaoduc, hoctap)",
  className = "mb-4",
  inputClassName = "flex-1 rounded-lg border border-gray-300 p-2 focus:border-blue-500 focus:outline-none",
  tagClassName = "flex items-center gap-1 rounded-full bg-blue-100 px-3 py-1 text-sm text-blue-700",
  buttonClassName = "rounded-lg bg-blue-500 px-4 py-2 text-white hover:bg-blue-600 disabled:bg-gray-300",
  showTagIcon = true
}: HashtagInputProps) => {
  const [hashtagInput, setHashtagInput] = useState("");

  // Xử lý thêm hashtag
  const handleAddHashtag = () => {
    if (hashtagInput.trim() && !hashtags.includes(hashtagInput.trim())) {
      onChange([...hashtags, hashtagInput.trim()]);
      setHashtagInput("");
    }
  };

  // Xử lý xóa hashtag
  const handleRemoveHashtag = (tag: string) => {
    onChange(hashtags.filter((t) => t !== tag));
  };

  // Xử lý nhấn Enter để thêm hashtag
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault();
      handleAddHashtag();
    }
  };

  // Xử lý paste nhiều hashtag
  const handlePaste = (e: React.ClipboardEvent) => {
    e.preventDefault();
    const pastedText = e.clipboardData.getData("text");

    // Tách các từ bằng khoảng trắng và xử lý từng từ
    const words = pastedText.split(/\s+/);

    // Thêm từng từ vào danh sách hashtag
    words.forEach((word) => {
      const cleanWord = word.trim();
      // Loại bỏ ký tự # nếu có
      const tag = cleanWord.startsWith("#")
        ? cleanWord.substring(1)
        : cleanWord;

      if (tag && !hashtags.includes(tag)) {
        onChange([...hashtags, tag]);
      }
    });

    setHashtagInput("");
  };

  return (
    <div className={className}>
      <HashtagDisplay 
        hashtags={hashtags}
        onRemove={handleRemoveHashtag}
        tagClassName={tagClassName}
        showTagIcon={showTagIcon}
      />
      <div className="flex gap-2">
        <input
          type="text"
          value={hashtagInput}
          onChange={(e) => setHashtagInput(e.target.value)}
          onKeyDown={handleKeyDown}
          onPaste={handlePaste}
          placeholder={placeholder}
          className={inputClassName}
        />
        <button
          onClick={handleAddHashtag}
          disabled={!hashtagInput.trim()}
          className={buttonClassName}
          type="button"
        >
          Thêm
        </button>
      </div>
    </div>
  );
};

export default HashtagInput;
