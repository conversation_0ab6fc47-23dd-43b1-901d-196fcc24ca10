"use client";

import { useRef, useState } from "react";
import { FaCamera, FaTrash } from "react-icons/fa";

import Image from "next/image";

interface ImageUploaderProps {
  images: string[];
  onImagesChange: (images: string[]) => void;
  imageFiles: File[];
  onImageFilesChange: (files: File[]) => void;
  className?: string;
  readOnly?: boolean; // Thêm tùy chọn chỉ đọc để ẩn nút xóa
}

const ImageUploader = ({
  images,
  onImagesChange,
  imageFiles,
  onImageFilesChange,
  className = "",
  readOnly = false
}: ImageUploaderProps) => {
  const [showImageManager, setShowImageManager] = useState(false);
  const [showFullImage, setShowFullImage] = useState(false);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Xử lý xóa ảnh
  const handleRemoveImage = (index: number) => {
    const newImages = [...images];
    newImages.splice(index, 1);
    onImagesChange(newImages);

    // Nếu là ảnh mới tải lên, cũng xóa khỏi imageFiles
    if (index < imageFiles.length) {
      const newImageFiles = [...imageFiles];
      newImageFiles.splice(index, 1);
      onImageFilesChange(newImageFiles);
    }
  };

  // Xử lý khi chọn file ảnh mới
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      // Lưu trữ các file để gửi lên server
      const newFiles = Array.from(files);
      onImageFilesChange([...imageFiles, ...newFiles]);

      // Tạo URL cho tất cả ảnh đã chọn để hiển thị
      const newImageUrls = newFiles.map((file) => URL.createObjectURL(file));
      onImagesChange([...images, ...newImageUrls]);
      
      // Reset giá trị của input file để có thể chọn lại cùng một file
      if (e.target) {
        e.target.value = '';
      }
    }
  };

  // Modal xem ảnh đầy đủ
  const FullImageModal = () => {
    if (!showFullImage) return null;
    
    const handlePrevImage = () => {
      setSelectedImageIndex((prev) => (prev === 0 ? images.length - 1 : prev - 1));
    };
    
    const handleNextImage = () => {
      setSelectedImageIndex((prev) => (prev === images.length - 1 ? 0 : prev + 1));
    };
    
    return (
      <div className="fixed inset-0 z-[60] flex items-center justify-center bg-black bg-opacity-80">
        <div className="relative h-full w-full flex items-center justify-center p-4">
          {/* Nút đóng */}
          <button
            onClick={() => setShowFullImage(false)}
            className="absolute right-4 top-4 z-10 rounded-full bg-black/30 p-2 text-white transition-all hover:bg-black/50 size-10 flex items-center justify-center"
          >
            <span className="text-2xl">&times;</span>
          </button>
          
          {/* Nút điều hướng trái */}
          <button
            onClick={handlePrevImage}
            className="absolute left-4 z-10 rounded-full bg-black/30 p-3 text-white transition-all hover:bg-black/50"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          
          {/* Ảnh đang xem */}
          <div className="relative h-[80vh] w-[80vw] max-w-5xl">
            <Image
              src={images[selectedImageIndex]}
              alt={`Ảnh ${selectedImageIndex + 1}`}
              fill
              sizes="80vw"
              className="object-contain"
            />
          </div>
          
          {/* Nút điều hướng phải */}
          <button
            onClick={handleNextImage}
            className="absolute right-4 z-10 rounded-full bg-black/30 p-3 text-white transition-all hover:bg-black/50"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>
          
          {/* Chỉ số ảnh */}
          <div className="absolute bottom-4 left-1/2 -translate-x-1/2 rounded-full bg-black/50 px-4 py-2 text-white">
            {selectedImageIndex + 1} / {images.length}
          </div>
        </div>
      </div>
    );
  };

  // Modal quản lý ảnh
  const ImageManagerModal = () => {
    if (!showImageManager) return null;

    const handleImageClick = (index: number) => {
      setSelectedImageIndex(index);
      setShowFullImage(true);
    };

    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
        <div className="relative max-h-[90vh] w-[90vw] max-w-4xl overflow-y-auto rounded-xl bg-white p-6 shadow-xl">
          <div className="mb-4 flex items-center justify-between border-b border-gray-200 ">
            <h3 className="text-xl font-medium text-gray-800">
              Tất cả ảnh
            </h3>
            <button
              onClick={() => setShowImageManager(false)}
              className="rounded-full p-2 text-gray-500 hover:bg-gray-100 hover:text-gray-700"
            >
              <span className="text-2xl">&times;</span>
            </button>
          </div>

          <div className="grid grid-cols-2 gap-4 sm:grid-cols-3 md:grid-cols-4">
            {images.map((src, index) => (
              <div
                key={index}
                className="relative aspect-square overflow-hidden rounded-lg border border-gray-200 cursor-pointer"
              >
                <Image
                  src={src}
                  alt={`Ảnh ${index + 1}`}
                  fill
                  sizes="(max-width: 768px) 100vw, 33vw"
                  className="object-cover"
                  onClick={() => handleImageClick(index)}
                />
               {!readOnly && <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleRemoveImage(index);
                  }}
                  className="absolute right-2 top-2 rounded-full bg-red-500 p-1.5 text-white shadow-md transition-all hover:bg-red-600"
                >
                  <FaTrash className="h-3 w-3" />
                </button>}
              </div>
            ))}
          </div>

          <div className="mt-6 flex justify-end gap-2">
            {!readOnly && <button
              onClick={() => fileInputRef.current?.click()}
              className="flex items-center gap-2 rounded-lg bg-blue-500 px-4 py-2 font-medium text-white transition-all hover:bg-blue-600"
            >
              <FaCamera />
              <span>Thêm ảnh</span>
            </button>}
            {!readOnly && <button
              onClick={() => setShowImageManager(false)}
              className="rounded-lg bg-gray-100 px-4 py-2 font-medium text-gray-700 transition-all hover:bg-gray-200"
            >
              Đóng
            </button>}
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className={className}>
      <input
        type="file"
        accept="image/*"
        multiple
        className="hidden"
        ref={fileInputRef}
        onChange={handleFileChange}
      />

      {/* Hiển thị ảnh đã chọn */}
      {images.length > 0 && (
        <div className="mb-6">
          {/* Layout hiển thị ảnh - chiều cao cố định cho tất cả các trường hợp */}
          <div className="h-[250px] md:h-[350px] rounded-xl">
            {images.length === 1 && (
              <div className="h-full w-full rounded-xl shadow-sm transition-all duration-300 hover:shadow-md cursor-pointer border"
              >
                <div className="relative h-full w-full">
                  <Image
                    src={images[0]}
                    alt="Ảnh đã chọn"
                    fill
                    className="object-cover"
                    onClick={() => {
                      setSelectedImageIndex(0);
                      setShowImageManager(true);
                    }}
                  />
                  {!readOnly && (
                    <button
                      onClick={() => handleRemoveImage(0)}
                      className="absolute right-2 top-2 rounded-full bg-black/30 p-1.5 text-white opacity-100 transition-all duration-200 hover:scale-105 hover:bg-black/50"
                    >
                      <FaTrash className="h-3 w-3" />
                    </button>
                  )}
                </div>
              </div>
            )}

            {images.length === 2 && (
              <div className="grid h-full grid-cols-2 gap-1.5 rounded-xl cursor-pointer">
                {images.map((image, index) => (
                  <div
                    key={index}
                    className="group relative h-full rounded-lg shadow-sm transition-all duration-300 hover:shadow-md border"
                  >
                    <div className="relative h-full w-full">
                      <Image
                        src={image}
                        alt={`Ảnh ${index + 1}`}
                        fill
                        className="object-cover"
                        onClick={() => setShowImageManager(true)}
                      />
                      {!readOnly && (
                        <button
                          onClick={() => handleRemoveImage(index)}
                          className="absolute right-2 top-2 rounded-full bg-black/30 p-1.5 text-white opacity-100 transition-all duration-200 hover:scale-105 hover:bg-black/50"
                        >
                          <FaTrash className="h-3 w-3" />
                        </button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}

            {images.length >= 3 && (
              <div className="grid h-full grid-cols-6 grid-rows-2 gap-1.5 cursor-pointer"

              >
                {/* Ảnh lớn ở bên trái */}
                <div className="group relative col-span-3 row-span-2 rounded-lg shadow-sm transition-all duration-300 hover:shadow-md border">
                  <Image
                    src={images[0]}
                    alt="Ảnh chính"
                    fill
                    className="object-cover"
                    onClick={() => setShowImageManager(true)}
                  />
                  {!readOnly && (
                    <button
                      onClick={() => handleRemoveImage(0)}
                      className="absolute right-2 top-2 rounded-full bg-black/30 p-1.5 text-white opacity-100 transition-all duration-200 hover:scale-105 hover:bg-black/50"
                    >
                      <FaTrash className="h-3 w-3" />
                    </button>
                  )}
                </div>

                {/* Các ảnh nhỏ ở bên phải */}
                {images.slice(1, 3).map((image, index) => (
                  <div
                    key={index + 1}
                    className="sm:col-span-1.5 group relative col-span-3 rounded-lg shadow-sm transition-all duration-300 hover:shadow-md border"
                  >
                    <Image
                      src={image}
                      alt={`Ảnh ${index + 2}`}
                      fill
                      className="object-cover"
                      onClick={() => setShowImageManager(true)}
                    />
                    {!readOnly && (
                      <button
                        onClick={() => handleRemoveImage(index + 1)}
                        className="absolute right-2 top-2 rounded-full bg-black/30 p-1.5 text-white opacity-100 transition-all duration-200 hover:scale-105 hover:bg-black/50"
                      >
                        <FaTrash className="h-3 w-3" />
                      </button>
                    )}

                    {/* Hiển thị lớp phủ với số lượng ảnh cộng thêm */}
                    {((index === 2 && images.length > 3) ||
                      (index === images.slice(1, 3).length - 1 &&
                        images.length > 3)) && (
                      <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-40 transition-all duration-300 hover:bg-opacity-50"
                           onClick={() => setShowImageManager(true)}
                      >
                        <span className="text-lg font-medium text-white">
                          +{images.length - (index + 2)}
                        </span>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Nút thêm ảnh - chỉ hiển thị khi không ở chế độ chỉ đọc */}
      {!readOnly && (
        <button
          onClick={() => fileInputRef.current?.click()}
          className="flex items-center gap-2 rounded-full bg-gray-100 px-4 py-2 text-gray-700 transition-all hover:bg-gray-200"
          type="button"
        >
          <FaCamera className="text-blue-500" />
          <span className="font-medium">Thêm ảnh</span>
        </button>
      )}

      {/* Modal quản lý ảnh */}
      <ImageManagerModal />
      
      {/* Modal xem ảnh đầy đủ */}
      <FullImageModal />
    </div>
  );
};

export default ImageUploader;
