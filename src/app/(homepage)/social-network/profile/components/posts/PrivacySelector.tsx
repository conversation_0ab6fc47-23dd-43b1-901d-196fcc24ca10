"use client";

import { useEffect, useRef, useState } from "react";
import { FaGlobe, FaLock, FaUserFriends as FaFriends } from "react-icons/fa";
import { PrivacyLevel } from "@/services/social-network/types/types";

interface PrivacySelectorProps {
  privacy: PrivacyLevel;
  onChange: (privacy: PrivacyLevel) => void;
  buttonClassName?: string;
  dropdownClassName?: string;
  optionClassName?: string;
}

const PrivacySelector = ({
  privacy,
  onChange,
  buttonClassName = "flex items-center gap-2 rounded-md bg-gray-100 px-3 py-1 text-sm font-medium text-gray-700 transition-colors hover:bg-gray-200",
  dropdownClassName = "absolute left-0 top-full z-10 mt-1 w-40 overflow-hidden rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5",
  optionClassName = "flex w-full items-center gap-2 px-4 py-2.5 text-left hover:bg-gray-50"
}: PrivacySelectorProps) => {
  const [showPrivacyDropdown, setShowPrivacyDropdown] = useState(false);
  const privacyDropdownRef = useRef<HTMLDivElement>(null);

  // Xử lý click bên ngoài dropdown để đóng
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        privacyDropdownRef.current &&
        !privacyDropdownRef.current.contains(event.target as Node)
      ) {
        setShowPrivacyDropdown(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Render biểu tượng quyền riêng tư
  const renderPrivacyIcon = (privacyLevel: PrivacyLevel) => {
    switch (privacyLevel) {
      case PrivacyLevel.PUBLIC:
        return <FaGlobe className="text-blue-500" />;
      case PrivacyLevel.FRIEND:
        return <FaFriends className="text-green-500" />;
      case PrivacyLevel.ONLY_ME:
        return <FaLock className="text-red-500" />;
      default:
        return <FaGlobe className="text-blue-500" />;
    }
  };

  // Render tên quyền riêng tư
  const renderPrivacyName = (privacyLevel: PrivacyLevel) => {
    switch (privacyLevel) {
      case PrivacyLevel.PUBLIC:
        return "Công khai";
      case PrivacyLevel.FRIEND:
        return "Bạn bè";
      case PrivacyLevel.ONLY_ME:
        return "Chỉ mình tôi";
      default:
        return "Công khai";
    }
  };

  return (
    <div className="relative">
      <button
        onClick={() => setShowPrivacyDropdown(!showPrivacyDropdown)}
        className={buttonClassName}
        type="button"
      >
        {renderPrivacyIcon(privacy)}
        <span>{renderPrivacyName(privacy)}</span>
      </button>

      {showPrivacyDropdown && (
        <div
          ref={privacyDropdownRef}
          className={dropdownClassName}
        >
          <button
            onClick={() => {
              onChange(PrivacyLevel.PUBLIC);
              setShowPrivacyDropdown(false);
            }}
            className={optionClassName}
          >
            <FaGlobe className="text-blue-500" />
            <span>Công khai</span>
          </button>
          <button
            onClick={() => {
              onChange(PrivacyLevel.FRIEND);
              setShowPrivacyDropdown(false);
            }}
            className={optionClassName}
          >
            <FaFriends className="text-green-500" />
            <span>Bạn bè</span>
          </button>
          <button
            onClick={() => {
              onChange(PrivacyLevel.ONLY_ME);
              setShowPrivacyDropdown(false);
            }}
            className={optionClassName}
          >
            <FaLock className="text-red-500" />
            <span>Chỉ mình tôi</span>
          </button>
        </div>
      )}
    </div>
  );
};

export default PrivacySelector;
