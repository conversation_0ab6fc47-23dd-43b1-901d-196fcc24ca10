"use client";

import { Comment, UserRole } from "../../data";

import Image from "next/image";

interface ReplyItemProps {
  reply: Comment;
  parentId: number;
  currentUserId: number;
  userRole: UserRole;
  onDeleteReply: (replyId: number) => void;
  onLikeReply: (replyId: number) => void;
}

const ReplyItem = ({
  reply,
  parentId,
  currentUserId,
  userRole,
  onDeleteReply,
  onLikeReply
}: ReplyItemProps) => {
  // Hàm kiểm tra quyền chỉnh sửa bình luận
  const canEditReply = () => {
    return reply.userId === currentUserId || userRole === UserRole.ADMIN;
  };

  return (
    <div className="flex items-start gap-2">
      <Image
        src={reply.user.avatar}
        alt={reply.user.name}
        width={24}
        height={24}
        className="rounded-full object-cover"
      />
      <div className="flex-1">
        <div className="rounded-lg bg-gray-100 p-2">
          <div className="text-sm font-medium">
            {reply.user.name}
          </div>
          <div className="text-sm">
            {reply.content}
          </div>
        </div>

        <div className="mt-1 flex items-center gap-4 text-xs">
          <button
            onClick={() => onLikeReply(reply.id)}
            className="text-gray-500 hover:text-blue-600"
          >
            Thích ({reply.likes})
          </button>
          <span className="text-gray-500">
            {reply.createdAt}
          </span>

          {canEditReply() && (
            <button
              onClick={() => onDeleteReply(reply.id)}
              className="ml-auto text-red-500 hover:text-red-700"
            >
              Xóa
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default ReplyItem;
