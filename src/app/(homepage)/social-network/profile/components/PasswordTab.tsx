"use client";

import {useEffect, useState} from "react";
import {FaEye, FaEyeSlash, FaLock} from "react-icons/fa";
import userServices from "@/services/user/userServices";

const PasswordTab = () => {
    const [currentPassword, setCurrentPassword] = useState("");
    const [newPassword, setNewPassword] = useState("");
    const [confirmPassword, setConfirmPassword] = useState("");
    const [showCurrentPassword, setShowCurrentPassword] = useState(false);
    const [showNewPassword, setShowNewPassword] = useState(false);
    const [showConfirmPassword, setShowConfirmPassword] = useState(false);
    const [error, setError] = useState("");
    const [success, setSuccess] = useState("");
    const [isLoading, setIsLoading] = useState(false);
    const [isHasPassword, setIsHasPassword] = useState(false);
    const [initUI, setInitUI] = useState(true);

    const validatePassword = (password: string) => {
        // <PERSON>ểm tra mật khẩu có ít nhất 8 ký tự, bao gồm chữ hoa, chữ thường, số và ký tự đặc biệt
        // const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
        const passwordRegex = /^.{6,}$/;
        return passwordRegex.test(password);
    };

    const handleSubmit = async (e: React.FormEvent) => {
        try {
            e.preventDefault();

            // Reset thông báo
            setError("");
            setSuccess("");

            // Kiểm tra mật khẩu hiện tại
            if (!currentPassword && isHasPassword) {
                setError("Vui lòng nhập mật khẩu hiện tại");
                return;
            }

            // Kiểm tra mật khẩu mới
            if (!validatePassword(newPassword)) {
                setError("Mật khẩu mới phải có ít nhất 6 ký tự");
                return;
            }

            // Kiểm tra xác nhận mật khẩu
            if (newPassword !== confirmPassword) {
                setError("Xác nhận mật khẩu không khớp");
                return;
            }

            // Mô phỏng gọi API để đổi mật khẩu
            setIsLoading(true);

            await userServices.changePassword({
                oldPassword: currentPassword,
                newPassword: newPassword
            });
            setSuccess("Đổi mật khẩu thành công!");
            setIsHasPassword(true);
            setCurrentPassword("");
            setNewPassword("");
            setConfirmPassword("");
        } catch (e) {
            console.log(e)
            // @ts-ignore
            if(e?.response.data.responseCode === 'PASSWORD-ERR-501'){
                setError("Mật khẩu cũ không đúng")
            }
            else{
                setError("Đã có lỗi xảy ra")
            }
        } finally {
            setIsLoading(false);
        }
    };

    const checkPassword = async () => {
        const res = await userServices.isHasPassword();
        setIsHasPassword(res?.data?.data);
        console.log(res);
    }

    useEffect(() => {
        checkPassword().then(r => setInitUI(false));
    }, []);

    return (
        <div>
            <div className="rounded-lg bg-white p-6 shadow-md">
                <h2 className="mb-6 text-xl font-semibold text-gray-800">Đổi mật khẩu</h2>

                {error && (
                    <div className="mb-4 rounded-md bg-red-50 p-3 text-red-700">
                        {error}
                    </div>
                )}

                {success && (
                    <div className="mb-4 rounded-md bg-green-50 p-3 text-green-700">
                        {success}
                    </div>
                )}

                {!initUI && (
                    <form onSubmit={handleSubmit} className="space-y-5">
                        {/* Mật khẩu hiện tại */}
                        {isHasPassword && (
                            <div>
                                <label htmlFor="current-password"
                                       className="mb-1 block text-sm font-medium text-gray-700">
                                    Mật khẩu hiện tại
                                </label>
                                <div className="relative">
                                    <input
                                        id="current-password"
                                        type={showCurrentPassword ? "text" : "password"}
                                        value={currentPassword}
                                        onChange={(e) => setCurrentPassword(e.target.value)}
                                        className="w-full rounded-md border border-gray-300 px-3 py-2 pr-10 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                                        placeholder="Nhập mật khẩu hiện tại"
                                    />
                                    <button
                                        type="button"
                                        onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                                        className="absolute inset-y-0 right-0 flex items-center px-3 text-gray-500"
                                    >
                                        {showCurrentPassword ? <FaEyeSlash/> : <FaEye/>}
                                    </button>
                                </div>
                            </div>
                        )}

                        {/* Mật khẩu mới */}
                        <div>
                            <label htmlFor="new-password" className="mb-1 block text-sm font-medium text-gray-700">
                                Mật khẩu mới
                            </label>
                            <div className="relative">
                                <input
                                    id="new-password"
                                    type={showNewPassword ? "text" : "password"}
                                    value={newPassword}
                                    onChange={(e) => setNewPassword(e.target.value)}
                                    className="w-full rounded-md border border-gray-300 px-3 py-2 pr-10 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                                    placeholder="Nhập mật khẩu mới"
                                />
                                <button
                                    type="button"
                                    onClick={() => setShowNewPassword(!showNewPassword)}
                                    className="absolute inset-y-0 right-0 flex items-center px-3 text-gray-500"
                                >
                                    {showNewPassword ? <FaEyeSlash/> : <FaEye/>}
                                </button>
                            </div>
                            <p className="mt-1 text-xs text-gray-500">
                                Mật khẩu phải có ít nhất 8 ký tự, bao gồm chữ hoa, chữ thường, số và ký tự đặc biệt
                            </p>
                        </div>

                        {/* Xác nhận mật khẩu mới */}
                        <div>
                            <label htmlFor="confirm-password" className="mb-1 block text-sm font-medium text-gray-700">
                                Xác nhận mật khẩu mới
                            </label>
                            <div className="relative">
                                <input
                                    id="confirm-password"
                                    type={showConfirmPassword ? "text" : "password"}
                                    value={confirmPassword}
                                    onChange={(e) => setConfirmPassword(e.target.value)}
                                    className="w-full rounded-md border border-gray-300 px-3 py-2 pr-10 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                                    placeholder="Xác nhận mật khẩu mới"
                                />
                                <button
                                    type="button"
                                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                                    className="absolute inset-y-0 right-0 flex items-center px-3 text-gray-500"
                                >
                                    {showConfirmPassword ? <FaEyeSlash/> : <FaEye/>}
                                </button>
                            </div>
                        </div>

                        {/* Nút đổi mật khẩu */}
                        <div className="pt-2">
                            <button
                                type="submit"
                                disabled={isLoading}
                                className="flex w-full items-center justify-center rounded-md bg-blue-600 px-4 py-2 text-white transition hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-70"
                            >
                                {isLoading ? (
                                    <>
                                <span
                                    className="mr-2 inline-block h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></span>
                                        Đang xử lý...
                                    </>
                                ) : (
                                    <>
                                        <FaLock className="mr-2"/>
                                        Đổi mật khẩu
                                    </>
                                )}
                            </button>
                        </div>
                    </form>
                )}
            </div>


            {initUI && (
                <div className="py-8 text-center">
                    <div className="flex items-center justify-center space-x-2">
                        <div
                            className="h-3 w-3 animate-bounce rounded-full bg-blue-600"
                            style={{animationDelay: "0ms"}}
                        ></div>
                        <div
                            className="h-3 w-3 animate-bounce rounded-full bg-blue-600"
                            style={{animationDelay: "150ms"}}
                        ></div>
                        <div
                            className="h-3 w-3 animate-bounce rounded-full bg-blue-600"
                            style={{animationDelay: "300ms"}}
                        ></div>
                    </div>
                </div>
            )}

        </div>
    );
};

export default PasswordTab;
