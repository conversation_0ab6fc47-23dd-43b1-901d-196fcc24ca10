"use client";

import MediaModal from '@/components/common/MediaModal';
import Image from 'next/image';
import { useEffect, useState } from 'react';
import { FaImage, FaPlay, FaVideo } from 'react-icons/fa';
import { userData, userPosts } from '../data';

type MediaType = 'photos' | 'videos';
type MediaItem = {
  id: number;
  type: 'photo' | 'video';
  url: string;
  postId: number;
  thumbnail?: string;
};

const PhotosTab = () => {
  const [activeTab, setActiveTab] = useState<MediaType>('photos');
  const [selectedMedia, setSelectedMedia] = useState<MediaItem | null>(null);
  const [relatedPost, setRelatedPost] = useState<any | null>(null);

  // Giả lập dữ liệu ảnh và video từ userPosts
  const photos: MediaItem[] = userPosts
    .filter(post => post.image)
    .map(post => ({
      id: post.id * 10 + 1, // Tạo ID duy nhất cho ảnh
      type: 'photo',
      url: post.image || '',
      postId: post.id
    }));

  // Giả lập dữ liệu video (sử dụng một số ảnh làm video để demo)
  const videos: MediaItem[] = userPosts
    .filter(post => post.image)
    .slice(0, 3) // Chỉ lấy 3 ảnh đầu tiên để làm video
    .map(post => ({
      id: post.id * 10 + 2, // Tạo ID duy nhất cho video
      type: 'video',
      url: post.image || '', // Trong thực tế, đây sẽ là URL video
      thumbnail: post.image, // Thumbnail của video
      postId: post.id
    }));

  // Lấy bài viết liên quan khi chọn media
  useEffect(() => {
    if (selectedMedia) {
      // Tìm bài viết liên quan dựa trên postId
      const post = userPosts.find(p => p.id === selectedMedia.postId);
      setRelatedPost(post || null);
    } else {
      setRelatedPost(null);
    }
  }, [selectedMedia]);

  // Xử lý click vào media
  const handleMediaClick = (media: MediaItem) => {
    setSelectedMedia(media);
  };

  // Đóng modal
  const handleCloseModal = () => {
    setSelectedMedia(null);
    setRelatedPost(null);
  };

  // Lấy danh sách media hiện tại dựa trên tab
  const currentMedia = activeTab === 'photos' ? photos : videos;

  return (
    <div>
      {/* Tab điều hướng */}
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <div className="flex border-b">
          <button
            className={`flex-1 py-3 font-medium ${activeTab === 'photos' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-600'}`}
            onClick={() => setActiveTab('photos')}
          >
            <div className="flex items-center justify-center gap-2">
              <FaImage />
              <span>Quản lý ảnh</span>
            </div>
          </button>
          <button
            className={`flex-1 py-3 font-medium ${activeTab === 'videos' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-600'}`}
            onClick={() => setActiveTab('videos')}
          >
            <div className="flex items-center justify-center gap-2">
              <FaVideo />
              <span>Quản lý video</span>
            </div>
          </button>
        </div>

        {/* Nội dung tab */}
        <div className="p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-xl font-semibold">{activeTab === 'photos' ? 'Ảnh' : 'Video'}</h3>
            <button className="text-blue-600 hover:underline">Xem tất cả</button>
          </div>
          
          {currentMedia.length > 0 ? (
            <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
              {currentMedia.map((media) => (
                <div 
                  key={media.id} 
                  className="relative aspect-square rounded-lg overflow-hidden cursor-pointer group"
                  onClick={() => handleMediaClick(media)}
                >
                  <Image 
                    src={media.type === 'photo' ? media.url : (media.thumbnail || media.url)} 
                    alt={media.type === 'photo' ? "Ảnh" : "Video"} 
                    fill 
                    className="object-cover group-hover:opacity-90 transition" 
                  />
                  {media.type === 'video' && (
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="bg-black bg-opacity-50 rounded-full p-3">
                        <FaPlay className="text-white" />
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              {activeTab === 'photos' ? 'Không có ảnh nào' : 'Không có video nào'}
            </div>
          )}
        </div>
      </div>

      {/* Modal hiển thị chi tiết ảnh/video */}
      {selectedMedia && relatedPost && (
        <MediaModal
          isOpen={!!selectedMedia}
          onClose={handleCloseModal}
          selectedMedia={selectedMedia}
          relatedPost={relatedPost}
          currentUser={userData}
          onPostUpdate={(updatedPost) => setRelatedPost(updatedPost)}
        />
      )}
    </div>
  );
};

export default PhotosTab;
