import DefaultAvatar from "@/components/ui/DefaultAvatar";
import useUploadFile from "@/hook/useUploadFile";
import friendServices from "@/services/social-network/friendServices";
import { FriendList } from "@/services/social-network/types/friend";
import Image from 'next/image';
import { useRouter } from "next/navigation";
import { useCallback, useEffect, useRef, useState } from "react";
import { FaUserFriends } from 'react-icons/fa';

const DEFAULT_SIZE = 10;

const FriendsTab = () => {
    const router = useRouter();
    const [friends, setFriends] = useState<FriendList[]>([]);
    const [page, setPage] = useState(-1);
    const [numberOfFriend, setNumberOfFriend] = useState(0);
    const [hasMore, setHasMore] = useState(true);
    const [loading, setLoading] = useState(false);
    const observerTarget = useRef<HTMLDivElement>(null);
    const { viewFile } = useUploadFile();
    const [initUI, setInitUI] = useState(true);

    const fetchFriends = async (
        type: "reset" | "infinite",
        page: number = 0,
        size: number = DEFAULT_SIZE
    ) => {
        setLoading(true);
        try{

            const response = await friendServices.getListFriendByCurrentSession({
                page,
                size
            });
            if(numberOfFriend == 0)
                setNumberOfFriend(response.data?.data?.totalElements)
            const list: FriendList[] = response.data?.data?.content ?? [];

            const listWithThumbnails: FriendList[] = await Promise.all(
                list.map(async (friend) => ({
                    ...friend,
                    thumbnail: await viewFile(friend.thumbnail, 'social-network')
                }))
            );

            type === "infinite"
                ? setFriends([...friends, ...listWithThumbnails])
                : setFriends([...listWithThumbnails]);
            setHasMore(!response.data.data.last);
        }
        finally {
            setLoading(false)
        }

    }

    useEffect(() => {
        // fetchFriends("reset");
    }, []);

    // Xử lý infinite scroll
    const loadMoreItems = useCallback(() => {
        if (loading || !hasMore) return;
        setPage(page + 1);
        fetchFriends("infinite", page + 1);
    }, [hasMore, loading]);

    // Thiết lập Intersection Observer cho infinite scroll
    useEffect(() => {
        const observer = new IntersectionObserver(
            (entries) => {
                if (entries[0].isIntersecting) {
                    loadMoreItems();
                }
            },
            { threshold: 1.0 }
        );

        if (observerTarget.current) {
            observer.observe(observerTarget.current);
        }

        return () => {
            if (observerTarget.current) {
                observer.unobserve(observerTarget.current);
            }
        };
    }, [loadMoreItems]);

  // @ts-ignore
    return (
    <div className="space-y-6">
        <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex justify-between items-center mb-4">
                <h3 className="text-xl font-semibold">Bạn bè</h3>
                <span className="text-gray-600">{numberOfFriend} người</span>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {friends.map((friend, index) => (
                    <div key={`${friend.username}-${index}`} className="flex items-center gap-3 p-3 border border-gray-100 rounded-lg hover:bg-gray-50 transition cursor-pointer"      onClick={()=>{router.push(`/social-network/profile/${friend.username}`)}}>
                        {friend.thumbnail ? (
                            <Image
                                // @ts-ignore
                                src={friend.thumbnail}
                                alt={friend.firstName + " " + friend.lastName}
                                width={50}
                                height={50}
                                className="rounded-full object-cover w-[50px] h-[50px]"
                            />
                        ) : (
                            <DefaultAvatar
                                name={
                                    `${friend.firstName} ${friend.lastName}` ||
                                    "User"
                                }
                                width={50}
                                height={50}
                                size={50}
                            />
                        )}
                        <div className="flex-1">
                            <div className="font-medium">{friend.firstName + " " + friend.lastName}</div>
                            <div className="text-sm text-gray-600">{friend.job}</div>
                            {friend.mutualFriendCount > 0 ? (
                                <div className="text-sm text-gray-600">{friend.mutualFriendCount} bạn chung</div>
                            )
                            :
                            (
                                <></>
                            )}
                        </div>
                        <button className="text-blue-600 hover:text-blue-800 transition">
                            <FaUserFriends />
                        </button>
                    </div>
                ))}
            </div>
        </div>

        {/* Loading indicator & observer target */}
        {hasMore && (
            <div ref={observerTarget} className="py-4 text-center">
                {loading ? (
                    <div className="flex items-center justify-center space-x-2">
                        <div
                            className="h-3 w-3 animate-bounce rounded-full bg-blue-600"
                            style={{ animationDelay: "0ms" }}
                        ></div>
                        <div
                            className="h-3 w-3 animate-bounce rounded-full bg-blue-600"
                            style={{ animationDelay: "150ms" }}
                        ></div>
                        <div
                            className="h-3 w-3 animate-bounce rounded-full bg-blue-600"
                            style={{ animationDelay: "300ms" }}
                        ></div>
                    </div>
                ) : (
                    <div className="text-sm text-gray-500">Kéo xuống để tải thêm</div>
                )}
            </div>
        )}
    </div>
  );
};

export default FriendsTab;
