'use client';

interface ProfileTabsProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
  tabs: {
    id: string;
    label: string;
  }[];
}

export default function ProfileTabs({ activeTab, onTabChange, tabs }: ProfileTabsProps) {
  return (
    <div className="bg-white rounded-lg shadow-md mb-6">
      <div className="flex border-b">
        {tabs.map((tab) => (
          <button 
            key={tab.id}
            className={`flex-1 py-4 text-center font-medium ${activeTab === tab.id ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-600 hover:text-gray-900'}`}
            onClick={() => onTabChange(tab.id)}
          >
            {tab.label}
          </button>
        ))}
      </div>
    </div>
  );
}
