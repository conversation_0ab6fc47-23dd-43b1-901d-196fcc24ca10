"use client";

import { useState } from "react";

import History from "@/components/History";
import FriendsTab from "./components/FriendsTab";
import PasswordTab from "./components/PasswordTab";
import PostsTab from "./components/PostsTab";
import ProfileTabs from "./components/ProfileTabs";
import SavedTab from "./components/SavedTab";
import UserProfileHeader from "./components/UserProfileHeader";

// Đã di chuyển kiểu UserData vào UserSocialNetworkContext

export default function ProfilePage() {
  const [activeTab, setActiveTab] = useState("posts");

  return (
    <div className="min-h-screen bg-gray-50 pb-10">
      <UserProfileHeader />
      <div className="container relative mx-auto px-4">
        <div className="flex flex-col gap-6 lg:flex-row lg:items-start">
          <History className="z-40 w-full self-start overflow-y-auto px-2 lg:sticky lg:top-20 lg:w-1/4" />
          <div className="w-full lg:w-3/4">
            <ProfileTabs
              activeTab={activeTab}
              onTabChange={setActiveTab}
              tabs={[
                { id: "posts", label: "Bài viết" },
                { id: "friends", label: "Bạn bè" },
                { id: "saved", label: "Đã lưu" },
                // { id: "photos", label: "Ảnh" },
                { id: "password", label: "Đổi mật khẩu" }
              ]}
            />
            {activeTab === "posts" && <PostsTab />}
            {activeTab === "friends" && <FriendsTab />}
            {activeTab === "saved" && <SavedTab />}
            {/* {activeTab === "photos" && <PhotosTab />} */}
            {activeTab === "password" && <PasswordTab />}
          </div>
        </div>
      </div>
    </div>
  );
}
