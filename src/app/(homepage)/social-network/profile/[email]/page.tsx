"use client";

import { useP<PERSON><PERSON>, useRouter } from "next/navigation";
import { useEffect, useState } from "react";

import useUploadFile from "@/hook/useUploadFile";
import socialNetworkServices from "@/services/social-network/socialNetworkServices";
import { UserSocialNetwork } from "@/services/social-network/types/types";
import ProfileTabs from "../components/ProfileTabs";
import FriendsTabReadOnly from "./components/FriendsTabReadOnly";
import PostsTabReadOnly from "./components/PostsTabReadOnly";
import UserProfileHeaderReadOnly from "./components/UserProfileHeaderReadOnly";

export type UserData = Partial<UserSocialNetwork> & {
  avatarUrl?: string;
  coverUrl?: string;
};

export default function EmailProfilePage() {
  const router = useRouter();
  // States cho trang profile
  const [activeTab, setActiveTab] = useState("posts");
  const [userData, setUserData] = useState<UserData>({});
  const [myProfile, setMyProfile] = useState<UserData>({});
  const [loadingUserData, setLoadingUserData] = useState(true);
  const { viewFile } = useUploadFile();
  const params = useParams();
  const email = params.email as string;

  // Gọi API để lấy dữ liệu người dùng theo email
  const fetchUserData = async () => {
    try {
      // Gọi API lấy thông tin user theo email (sử dụng getUserSocialNetworkByUserName)
      const response =
        await socialNetworkServices.getUserSocialNetworkByUserName(email);
      const userSocialNetwork: UserSocialNetwork = response.data.data;
      let avatarUrl = "";
      let coverUrl = "";

      // Lấy URL cho avatar và cover photo
      if (userSocialNetwork.thumbnail) {
        try {
          const thumbnailUrl = await viewFile(
            userSocialNetwork.thumbnail,
            "social-network"
          );
          avatarUrl = thumbnailUrl as string;
        } catch (error) {
          console.error("Lỗi khi lấy URL cho avatar:", error);
        }
      }

      if (userSocialNetwork.background) {
        try {
          const backgroundUrl = await viewFile(
            userSocialNetwork.background,
            "social-network"
          );
          coverUrl = backgroundUrl as string;
        } catch (error) {
          console.error("Lỗi khi lấy URL cho cover photo:", error);
        }
      }

      setUserData({
        ...userSocialNetwork,
        avatarUrl,
        coverUrl
      });
    } catch (error) {
      console.error("Lỗi khi lấy dữ liệu người dùng:", error);
    }
  };

  useEffect(() => {
    if (email && decodeURIComponent(email) !== myProfile.email) {
      try {
        setLoadingUserData(true);
        fetchUserData();
      } catch (error) {
        console.error("Lỗi khi lấy dữ liệu người dùng:", error);
      } finally {
        setLoadingUserData(false);
      }
    } else {
      router.push("/social-network/profile");
    }
  }, [email, myProfile.email]);

  useEffect(() => {
    const fetchUserData = async () => {
      try {
        setLoadingUserData(true);
        const response = await socialNetworkServices.getUserSocialNetwork();
        const userSocialNetwork: UserSocialNetwork = response.data.data;
        setMyProfile({
          ...userSocialNetwork
        });
      } catch (error) {
        console.error("Lỗi khi lấy dữ liệu người dùng:", error);
      } finally {
        setLoadingUserData(false);
      }
    };

    fetchUserData();
  }, []);

  const handleOpenProfile = (emailData: string) => {
    router.push(`/social-network/profile/${emailData}`);
  };
  return (
    <div className="min-h-screen bg-gray-50 pb-10">
      <UserProfileHeaderReadOnly
        userData={userData}
        loadingUserData={loadingUserData}
        resetUserData={fetchUserData}
      />
      <div className="container relative mx-auto px-4">
        <div className="flex flex-col gap-6 lg:flex-row lg:items-start">
          <div className="w-full">
            <ProfileTabs
              activeTab={activeTab}
              onTabChange={setActiveTab}
              tabs={[
                { id: "posts", label: "Bài viết" },
                { id: "friends", label: "Bạn bè" }
              ]}
            />
            {activeTab === "posts" && (
              <PostsTabReadOnly
                userData={userData}
                handleOpenProfile={handleOpenProfile}
              />
            )}
            {activeTab === "friends" && (
              <FriendsTabReadOnly
                userData={userData}
                handleOpenProfile={handleOpenProfile}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
