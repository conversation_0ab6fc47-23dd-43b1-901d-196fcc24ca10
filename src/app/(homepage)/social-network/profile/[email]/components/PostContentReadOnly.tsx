"use client";

import { useEffect, useState } from "react";
import {
    FaB<PERSON><PERSON>,
    <PERSON>aComment,
    FaUserFriends as FaFriends,
    FaGlobe,
    FaLock,
    FaShare,
    FaThumbsUp
} from "react-icons/fa";

import CommentSection from "@/components/common/CommentSection";
import HashtagDisplay from "@/components/common/HashtagDisplay";
import SharePostForm from "@/components/common/SharePostForm";
import DefaultAvatar from "@/components/ui/DefaultAvatar";
import useUploadFile from "@/hook/useUploadFile";
import { cn } from "@/lib/utils";
import socialNetworkServices from "@/services/social-network/socialNetworkServices";
import { PrivacyLevel } from "@/services/social-network/types/types";
import Image from "next/image";
import { MdVerified } from "react-icons/md";
import { RiBuilding2Line } from "react-icons/ri";
import { toast } from "react-toastify";
import { formatDateForDisplay } from "../../../utils/dateUtils";
import ImageUploader from "../../components/posts/ImageUploader";
import { ExtendedPost } from "./PostsTabReadOnly";

interface PostContentReadOnlyProps {
    post: ExtendedPost;
    hasBorder?: boolean;
    handleOpenProfile: (email:string) =>void;
}

const PostContentReadOnly = ({post, hasBorder = true ,handleOpenProfile}: PostContentReadOnlyProps) => {
    const [postData, setPostData] = useState<ExtendedPost>(post);
    const [isLoading, setIsLoading] = useState(false);
    const [showShareForm, setShowShareForm] = useState(false);
    const {viewFile} = useUploadFile();

    // Hàm lấy dữ liệu post từ API
    const fetchPostData = async () => {
        try {
            const response = await socialNetworkServices.getPostSocialNetworkById(
                post.postSocialNetworkId
            );
            if (response && response.data.data) {
                const postData = response.data.data;
                let avatarUrl = undefined;
                let fileUrls: string[] = [];

                // Nếu có thumbnail, gọi API viewFile để lấy URL
                if (postData.thumbnail) {
                    try {
                        avatarUrl = (await viewFile(
                            postData.thumbnail,
                            "social-network"
                        )) as string;
                    } catch (error) {
                        console.error("Lỗi khi lấy URL cho thumbnail:", error);
                    }
                }

                // Nếu có files, gọi API viewFile để lấy URL cho từng file
                if (postData.files && postData.files.length > 0) {
                    try {
                        // Gọi API viewFile cho từng fileId trong mảng files
                        const filePromises = postData.files.map((fileId: string) =>
                            viewFile(fileId, "social-network")
                        );
                        // Ép kiểu kết quả trả về thành string[]
                        fileUrls = (await Promise.all(filePromises)) as string[];
                    } catch (error) {
                        console.error("Lỗi khi lấy URL cho files:", error);
                    }
                }

                // Cập nhật state với post đã có avatarUrl và fileUrls
                setPostData({
                    ...postData,
                    avatarUrl,
                    fileUrls
                });
            }
        } catch (error) {
            console.error("Lỗi khi lấy dữ liệu bài đăng:", error);
        }
    };

    // Effect để cập nhật state postData khi prop post thay đổi
    useEffect(() => {
        setPostData(post);
    }, [post]);

    // Hàm xử lý thích bài đăng
    const handleLikePost = async (post: ExtendedPost) => {
        if (isLoading) return;
        setIsLoading(true);
        try {
            // Nếu đã thích, gọi API unlike, ngược lại gọi API like
            if (post.isLiked) {
                await socialNetworkServices.unlikePostSocialNetwork(
                    post.postSocialNetworkId
                );
            } else {
                await socialNetworkServices.likePostSocialNetwork(
                    post.postSocialNetworkId
                );
            }
            // Cập nhật lại dữ liệu post sau khi thích/bỏ thích
            await fetchPostData();
        } catch (error) {
            console.error("Lỗi khi thích/bỏ thích bài đăng:", error);
            toast.error("Không thể thích/bỏ thích bài đăng");
        } finally {
            setIsLoading(false);
        }
    };

    // State quản lý hiển thị/ẩn bình luận
    const [showComments, setShowComments] = useState(false);

    // Hàm xử lý hiển thị/ẩn bình luận
    const handleToggleComments = () => {
        setShowComments(!showComments);
    };

    // Hàm xử lý mở form chia sẻ bài đăng
    const handleOpenShareForm = () => {
        setShowShareForm(true);
    };

    // Hàm xử lý đóng form chia sẻ bài đăng
    const handleCloseShareForm = () => {
        setShowShareForm(false);
    };

    // Hàm xử lý lưu bài đăng
    const handleSavePost = async (post: ExtendedPost) => {
        if (isLoading) return;
        setIsLoading(true);

        try {
            if (post.isSaved) {
                await socialNetworkServices.unSavePostSocialNetwork(
                    post.postSocialNetworkId
                );
            } else {
                await socialNetworkServices.savePostSocialNetwork(
                    post.postSocialNetworkId
                );
            }
            await fetchPostData();
        } catch (error) {
            console.error("Lỗi khi lưu/bỏ lưu bài đăng:", error);
            toast.error("Không thể lưu/bỏ lưu bài đăng");
        } finally {
            setIsLoading(false);
        }
    };

    // Hàm render biểu tượng quyền riêng tư
    const renderPrivacyIcon = (privacy: PrivacyLevel) => {
        switch (privacy) {
            case PrivacyLevel.PUBLIC:
                return <FaGlobe className="text-gray-600"/>;
            case PrivacyLevel.FRIEND:
                return <FaFriends className="text-gray-600"/>;
            case PrivacyLevel.ONLY_ME:
                return <FaLock className="text-gray-600"/>;
            default:
                return <FaGlobe className="text-gray-600"/>;
        }
    };

    // Hàm render tên quyền riêng tư
    const renderPrivacyName = (privacy: PrivacyLevel) => {
        switch (privacy) {
            case PrivacyLevel.PUBLIC:
                return "Công khai";
            case PrivacyLevel.FRIEND:
                return "Bạn bè";
            case PrivacyLevel.ONLY_ME:
                return "Chỉ mình tôi";
            default:
                return "Công khai";
        }
    };

    // Hiển thị thông báo lỗi nếu không có bài viết
    if (!post) {
        return (
            <div className={`rounded-lg ${hasBorder ? 'border border-gray-200' : ''} bg-white p-4`}>
                <div className="flex flex-col items-center justify-center py-8">
                    <svg className="h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <h3 className="mt-2 text-sm font-medium text-gray-900">Bài viết không khả dụng</h3>
                    <p className="mt-1 text-sm text-gray-500">Bài viết này có thể đã bị xóa hoặc không tồn tại.</p>
                </div>
            </div>
        );
    }

    return (
        <div className={cn("overflow-hidden bg-white", hasBorder ? "rounded-lg shadow-lg" : "")}>
            {/* Post Header */}
            <div className="flex items-center justify-between p-4">
                <div className="flex items-center gap-3">
                    <div className="relative h-10 w-10 overflow-hidden rounded-full">
                        {postData.avatarUrl ? (
                            <Image
                                src={postData.avatarUrl}
                                alt={`${postData.firstName} ${postData.lastName}`}
                                fill
                                className="rounded-full object-cover"
                            />
                        ) : (
                            <DefaultAvatar
                                name={`${postData.firstName} ${postData.lastName}` || "User"}
                                size="100%"
                            />
                        )}
                    </div>
                    <div>
                        <div className="font-medium flex cursor-pointer"  onClick={()=>{handleOpenProfile(postData.email)}}>
                            {`${postData.firstName} ${postData.lastName}`}
                            {postData.orgName && (
                                <div className="flex items-center ml-2 text-blue-600 rounded text-base">
                                    <MdVerified/>
                                    <div
                                        className="ml-2 flex items-center bg-green-500 text-white px-3 py-1 rounded-full text-xs font-medium shadow-sm">
                                        <RiBuilding2Line></RiBuilding2Line>
                                        <span className="ml-1"> {postData.orgName}</span>
                                    </div>
                                </div>
                            )}
                        </div>
                        <div className="flex flex-col-reverse sm:flex-row items-start sm:items-center sm:gap-2 text-sm text-gray-500">
                            <span>
                                {formatDateForDisplay(postData.createdAt)}
                            </span>
                            <span className="flex items-center gap-1">
                                {renderPrivacyIcon(postData.privacyPolicy as PrivacyLevel)}
                                <span>
                                    {renderPrivacyName(postData.privacyPolicy as PrivacyLevel)}
                                </span>
                            </span>
                        </div>
                    </div>
                </div>
                {/* Không hiển thị menu options cho read-only */}
            </div>

            {/* Post Content */}
            <div className="px-4 pb-3">
                <p className="mb-2 whitespace-pre-wrap">{postData.content}</p>

                {/* Hashtags */}
                {postData.hashTags && postData.hashTags.length > 0 && (
                    <div className="mb-3">
                        <HashtagDisplay
                            hashtags={postData.hashTags}
                            readOnly={true}
                            showTagIcon={true}
                        />
                    </div>
                )}

                {/* Hiển thị ảnh của bài đăng */}
                {postData.fileUrls && postData.fileUrls.length > 0 ? (
                    <div className="mb-2 mt-4">
                        <ImageUploader
                            images={postData.fileUrls}
                            onImagesChange={() => {}}
                            imageFiles={[]}
                            onImageFilesChange={() => {}}
                            className="mb-3"
                            readOnly={true}
                        />
                    </div>
                ) : null}

                {/* Hiển thị bài đăng được chia sẻ (postRefer) */}
                {postData.postRefer && post.referPostAvailable && (
                    <div className="mt-4 mb-2 rounded-lg border border-gray-200 p-4">
                        <div className="mb-3 flex items-center">
                            <div className="relative h-8 w-8 overflow-hidden rounded-full">
                                {postData.postRefer.avatarUrl ? (
                                    <Image
                                        src={postData.postRefer.avatarUrl}
                                        alt={`${postData.postRefer.firstName} ${postData.postRefer.lastName}`}
                                        fill
                                        className="object-cover"
                                    />
                                ) : (
                                    <DefaultAvatar
                                        name={`${postData.postRefer.firstName} ${postData.postRefer.lastName}`}
                                        size="100%"
                                    />
                                )}
                            </div>
                            <div className="ml-2">
                                <div className="font-medium">
                                    {postData.postRefer.firstName} {postData.postRefer.lastName}
                                </div>
                                <div className="text-xs text-gray-500">
                                    {formatDateForDisplay(postData.postRefer.createdAt)}
                                </div>
                            </div>
                        </div>

                        {/* Nội dung bài đăng được chia sẻ */}
                        <div className="mb-3 whitespace-pre-wrap">{postData.postRefer.content}</div>

                        {/* Hashtags của bài đăng được chia sẻ */}
                        {postData.postRefer.hashTags && postData.postRefer.hashTags.length > 0 && (
                            <div className="mb-3">
                                <HashtagDisplay
                                    hashtags={postData.postRefer.hashTags}
                                    readOnly={true}
                                    showTagIcon={true}
                                />
                            </div>
                        )}

                        {/* Ảnh của bài đăng được chia sẻ */}
                        {postData.postRefer.fileUrls && postData.postRefer.fileUrls.length > 0 && (
                            <ImageUploader
                                images={postData.postRefer.fileUrls}
                                onImagesChange={() => {}}
                                imageFiles={[]}
                                onImageFilesChange={() => {}}
                                readOnly={true}
                            />
                        )}
                    </div>
                )}
                {postData.postReferId && !post.referPostAvailable && (
                    <div className="mt-4 mb-2 rounded-lg border border-gray-200 p-4 min-h-[10rem] flex justify-center text-center items-center">
                        <div className="mb-3 whitespace-pre-wrap text-gray-700 opacity-50">Bài viết không khả dụng</div>
                    </div>
                )}
            </div>

            {/* Post Stats */}
            <div className="flex justify-between border-b border-t border-gray-100 px-4 py-2 text-sm text-gray-500">
                <div>{postData.likeCount} lượt thích</div>
                <div>
                    {postData.commentCount} bình luận • {postData.shareCount} chia sẻ
                </div>
            </div>

            {/* Post Actions */}
            <div className="flex justify-between px-2 py-2">
                <button
                    onClick={() => handleLikePost(postData)}
                    disabled={isLoading}
                    className={`flex flex-1 items-center justify-center gap-2 rounded-sm py-2 ${postData.isLiked ? "font-medium text-blue-600" : "text-gray-600"} transition hover:bg-gray-100 text-xs sm:text-base`}
                >
                    <FaThumbsUp
                        className={postData.isLiked ? "text-blue-600" : "text-gray-600"}
                    />
                    <span>Thích</span>
                </button>
                <button
                    onClick={handleToggleComments}
                    className={`flex flex-1 items-center justify-center gap-2 rounded-sm py-2 ${showComments ? "font-medium text-blue-600" : "text-gray-600"} transition hover:bg-gray-100 text-xs sm:text-base whitespace-nowrap`}
                >
                    <FaComment
                        className={showComments ? "text-blue-600" : "text-gray-600"}
                    />
                    <span>Bình luận</span>
                </button>
                <button
                    onClick={handleOpenShareForm}
                    disabled={isLoading}
                    className="flex flex-1 items-center justify-center gap-2 rounded-sm py-2 text-gray-600 transition hover:bg-gray-100 text-xs sm:text-base"
                >
                    <FaShare/>
                    <span>Chia sẻ</span>
                </button>
                <button
                    onClick={() => handleSavePost(postData)}
                    disabled={isLoading}
                    className={`flex flex-1 items-center justify-center gap-2 py-2 ${postData.isSaved ? "font-medium text-blue-600" : "text-gray-600"} rounded-sm transition hover:bg-gray-100 text-xs sm:text-base`}
                >
                    <FaBookmark
                        className={postData.isSaved ? "text-blue-600" : "text-gray-600"}
                    />
                    <span>Lưu</span>
                </button>
            </div>

            {/* Comments Section */}
            {showComments && <CommentSection post={postData} resetList={() => {}} handleOpenProfile={handleOpenProfile}/>}

            {/* Share Post Form */}
            <SharePostForm
                postId={postData.postReferId || postData.postSocialNetworkId}
                isOpen={showShareForm}
                onClose={handleCloseShareForm}
                onSuccess={() => {}}
                userAvatar={postData.avatarUrl}
                userName={`${postData.firstName} ${postData.lastName}`}
            />
        </div>
    );
};

export default PostContentReadOnly;
