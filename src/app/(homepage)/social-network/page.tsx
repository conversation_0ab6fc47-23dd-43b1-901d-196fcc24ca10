"use client";

import { useSearchParams } from "next/navigation";
import { AccountInformation } from "./_components/AccountInformation";
import { BulletinBoard } from "./_components/BulletinBoard";
import { FeedBoard } from "./_components/FeedBoard";
import { RecentComments } from "./_components/RecentComments";
import { SearchBar } from "./_components/SearchBar";
import { UserBoard } from "./_components/UserBoard";

export default function FeedPage() {
  // Sử dụng URL search params thay vì state
  const searchParams = useSearchParams();
  const searchType = searchParams.get("type");

  return (
    <div className="container mx-auto px-4 py-6">
      <SearchBar />

      <div className="flex flex-col gap-6 lg:flex-row">
        <AccountInformation />
        {!searchType ? (
          <FeedBoard />
        ) : searchType === "users" ? (
          <UserBoard />
        ) : (
          <BulletinBoard />
        )}
        <RecentComments />
      </div>
    </div>
  );
}
