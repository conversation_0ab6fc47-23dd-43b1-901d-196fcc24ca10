"use client";

import { usePara<PERSON>, useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { toast } from "react-toastify";

import ForumBreadcrumb from "@/components/forums/ForumBreadcrumb";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import useUploadFile from "@/hook/useUploadFile";
import anonymousServices from "@/services/anonymousServices";
import forumsServices from "@/services/forums/forumsServices";
import { Post } from "@/services/forums/types";
import Image from "next/image";
import Link from "next/link";

export default function SearchResultsPage() {
  const router = useRouter();
  const { eduEcosystemId } = useParams();
  const searchParams = useSearchParams();
  const pageSize = 6;
  const { viewFile } = useUploadFile();

  // <PERSON><PERSON><PERSON> các tham số từ URL
  const keyword = searchParams.get("keyword");
  const hashtag = searchParams.get("hashtag");
  const topicIdsParam = searchParams.get("topicIds");
  const timeParam = searchParams.get("time");
  const viewCommentTypeParam = searchParams.get("viewCommentType");
  const sortParam = searchParams.get("sort") || "NEW";
  const pageParam = searchParams.get("page") || "0";

  const [state, setState] = useState({
    // Các bộ lọc cố định
    topicFilters: [] as { id: string; name: string }[],
    timeFilters: [
      { id: "TODAY", name: "Hôm nay" },
      { id: "THIS_WEEK", name: "Tuần này" },
      { id: "THIS_MONTH", name: "Tháng này" },
      { id: "LAST_3_MONTH", name: "3 tháng gần đây" },
      { id: "YEAR", name: "Năm nay" }
    ],
    viewCommentFilters: [
      { id: "MOST_VIEWED", name: "Trên 1000 lượt xem" },
      { id: "MOST_COMMENTED", name: "Trên 100 bình luận" },
      { id: "MOST_LIKED", name: "Trên 100 lượt thích" }
    ],
    sortOptions: [
      { id: "NEW", name: "Mới nhất" },
      { id: "OLD", name: "Cũ nhất" },
      { id: "COMMENT", name: "Nhiều bình luận" },
      { id: "LIKE", name: "Nhiều lượt thích" },
      { id: "VIEW", name: "Nhiều lượt xem" }
    ],

    // Dữ liệu và trạng thái
    selectedLevel: eduEcosystemId?.toString() || "all",
    searchResults: [] as Post[],
    isLoading: true,
    currentPage: parseInt(pageParam) || 0,
    totalPages: 0,
    totalElements: 0,
    postImages: {} as Record<string, string>,
    authorAvatars: {} as Record<string, string>,
    ecosystems: [] as { eduEcosystemId: number; name: string }[],

    // Giá trị tìm kiếm và bộ lọc từ URL
    searchInputValue: keyword || "",
    hashtag: hashtag || "",
    selectedTopics: topicIdsParam ? topicIdsParam.split(",") : [],
    selectedTimeFilters: timeParam ? [timeParam] : [],
    selectedViewFilters: viewCommentTypeParam ? [viewCommentTypeParam] : [],
    selectedSortOption: sortParam
  });

  useEffect(() => {
    const fetchEcosystems = async () => {
      try {
        const response = await anonymousServices.getListEduEcosystem();
        if (response.data?.data) {
          setState((prev) => ({
            ...prev,
            ecosystems: response.data.data
          }));
        }
      } catch (error) {
        console.error("Error fetching ecosystems:", error);
        toast.error(
          "Không thể tải danh sách hệ sinh thái. Vui lòng thử lại sau!"
        );
      }
    };

    fetchEcosystems();
  }, []);

  useEffect(() => {
    const fetchForums = async () => {
      try {
        const response = await forumsServices.getForums(
          0,
          9999,
          Number(eduEcosystemId)
        );
        if (response.data?.data?.content) {
          // Chuyển đổi dữ liệu từ API thành định dạng cần thiết cho topicFilters
          const topics = response.data.data.content.map((forum: any) => ({
            id: forum.forumId.toString(),
            name: forum.name
          }));
          setState((prev) => ({
            ...prev,
            topicFilters: topics
          }));
        }
      } catch (error) {
        console.error("Error fetching forums:", error);
        toast.error("Không thể tải danh sách chủ đề. Vui lòng thử lại sau!");
      }
    };

    fetchForums();
  }, [eduEcosystemId]);

  const updateState = (newState: Partial<typeof state>) => {
    setState((prevState) => ({
      ...prevState,
      ...newState
    }));
  };

  const handleSearch = () => {
    if (state.isLoading) return;
    if (state.isLoading) return;
    // Tạo đối tượng URLSearchParams để xây dựng query string
    const queryParams = new URLSearchParams();
    queryParams.set("keyword", state.searchInputValue.trim());

    // Thêm hashtag nếu có
    if (state.hashtag) {
      queryParams.set("hashtag", state.hashtag);
    }

    // Thêm các bộ lọc hiện tại vào URL
    if (state.selectedTopics.length > 0) {
      queryParams.set("topicIds", state.selectedTopics.join(","));
    }

    if (state.selectedTimeFilters.length > 0) {
      queryParams.set("time", state.selectedTimeFilters[0]);
    }

    if (state.selectedViewFilters.length > 0) {
      queryParams.set("viewCommentType", state.selectedViewFilters[0]);
    }

    // Thêm tùy chọn sắp xếp
    queryParams.set("sort", state.selectedSortOption);

    // Reset về trang đầu tiên khi tìm kiếm mới
    queryParams.set("page", "0");

    router.push(`/forums/${eduEcosystemId}/search?${queryParams.toString()}`);
  };

  // Xử lý checkbox chủ đề
  const handleTopicChange = (topicId: string) => {
    const newSelectedTopics = state.selectedTopics.includes(topicId)
      ? state.selectedTopics.filter((id) => id !== topicId)
      : [...state.selectedTopics, topicId];

    updateState({ selectedTopics: newSelectedTopics });
  };

  // Xử lý checkbox thời gian
  const handleTimeFilterChange = (timeId: string) => {
    // Chỉ cho phép chọn một bộ lọc thời gian
    const newSelectedTimeFilters = state.selectedTimeFilters.includes(timeId)
      ? []
      : [timeId];

    updateState({ selectedTimeFilters: newSelectedTimeFilters });
  };

  // Xử lý checkbox lượt xem/bình luận
  const handleViewFilterChange = (viewId: string) => {
    // Chỉ cho phép chọn một bộ lọc lượt xem/bình luận
    const newSelectedViewFilters = state.selectedViewFilters.includes(viewId)
      ? []
      : [viewId];

    updateState({ selectedViewFilters: newSelectedViewFilters });
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      handleSearch();
    }
  };

  // Hàm cập nhật URL với tất cả các bộ lọc
  const updateUrlWithFilters = (currentState = state) => {
    const queryParams = new URLSearchParams();

    // Thêm từ khóa tìm kiếm
    if (keyword) {
      queryParams.set("keyword", keyword);
    }

    // Thêm hashtag nếu có
    if (currentState.hashtag) {
      queryParams.set("hashtag", currentState.hashtag);
    }

    // Thêm các bộ lọc chủ đề
    if (currentState.selectedTopics.length > 0) {
      queryParams.set("topicIds", currentState.selectedTopics.join(","));
    }

    // Thêm bộ lọc thời gian
    if (currentState.selectedTimeFilters.length > 0) {
      queryParams.set("time", currentState.selectedTimeFilters[0]);
    }

    // Thêm bộ lọc lượt xem/bình luận
    if (currentState.selectedViewFilters.length > 0) {
      queryParams.set("viewCommentType", currentState.selectedViewFilters[0]);
    }

    // Thêm tùy chọn sắp xếp
    queryParams.set("sort", currentState.selectedSortOption);

    // Thêm trang hiện tại
    queryParams.set("page", currentState.currentPage.toString());

    // Cập nhật URL
    router.push(`/forums/${eduEcosystemId}/search?${queryParams.toString()}`);
  };

  // Áp dụng tất cả các bộ lọc
  const applyFilters = () => {
    if (state.isLoading) return;
    // Reset về trang đầu tiên khi áp dụng bộ lọc mới
    updateState({ currentPage: 0 });
    updateUrlWithFilters({
      ...state,
      currentPage: 0
    });
  };

  const clearFilters = () => {
    updateState({
      selectedTopics: [],
      selectedTimeFilters: [],
      selectedViewFilters: [],
      selectedSortOption: "NEW",
      currentPage: 0
    });
    updateUrlWithFilters({
      ...state,
      selectedTopics: [],
      selectedTimeFilters: [],
      selectedViewFilters: [],
      selectedSortOption: "NEW",
      currentPage: 0
    });
  };

  const loadPostImage = async (fileId: string) => {
    try {
      const imageData = await viewFile(fileId, "forums");
      updateState({
        postImages: {
          ...state.postImages,
          [fileId]: imageData as string
        }
      });
    } catch (error) {
      console.error("Error loading post image:", error);
    }
  };

  const loadAuthorAvatar = async (fileId: string) => {
    try {
      const imageData = await viewFile(fileId, "forums");
      updateState({
        authorAvatars: {
          ...state.authorAvatars,
          [fileId]: imageData as string
        }
      });
    } catch (error) {
      console.error("Error loading author avatar:", error);
    }
  };

  useEffect(() => {
    const fetchSearchResults = async () => {
      try {
        updateState({ isLoading: true, searchInputValue: keyword as string });
        // Xây dựng tham số tìm kiếm từ URL
        const apiParams: any = {
          page: state.currentPage,
          size: pageSize,
          eduEcosystemId: Number(eduEcosystemId) || null,
          sort: state.selectedSortOption
        };

        // Thêm keyword hoặc hashtag
        if (keyword) {
          apiParams.keyword = keyword;
        }

        if (hashtag) {
          apiParams.hashtag = hashtag;
        }

        // Thêm các tham số bộ lọc từ state
        if (state.selectedTopics.length > 0) {
          apiParams.topicIds = state.selectedTopics.join(",");
        }

        if (state.selectedTimeFilters.length > 0) {
          apiParams.time = state.selectedTimeFilters[0];
        }

        if (state.selectedViewFilters.length > 0) {
          apiParams.viewCommentType = state.selectedViewFilters[0];
        }

        const response = await forumsServices.searchPosts(apiParams);

        if (response.data.data) {
          updateState({
            searchResults: response.data.data.content,
            totalPages: response.data.data.totalPages,
            totalElements: response.data.data.totalElements
          });

          // Tải ảnh cho mỗi bài viết
          response.data.data.content.forEach((post: Post) => {
            if (post.userCreatedThumbNail) {
              loadPostImage(post.userCreatedThumbNail);
            }
            if (post.authorAvatar) {
              loadAuthorAvatar(post.authorAvatar);
            }
          });
        }
      } catch (error) {
        console.error("Error fetching search results:", error);
        toast.error("Không thể tải kết quả tìm kiếm. Vui lòng thử lại sau!");
      } finally {
        updateState({ isLoading: false });
      }
    };
    // Theo dõi thay đổi từ URL và cập nhật kết quả tìm kiếm
    fetchSearchResults();
  }, [keyword, hashtag, state.currentPage, searchParams]);

  const handlePageChange = (newPage: number) => {
    updateState({ currentPage: newPage });
    updateUrlWithFilters({
      ...state,
      currentPage: newPage
    });
  };

  // Định dạng thời gian tương đối
  const getRelativeTime = (dateString?: string): string => {
    if (!dateString) return "Không có dữ liệu";

    if (dateString.length >= 14) {
      const year = dateString.slice(0, 4);
      const month = dateString.slice(4, 6);
      const day = dateString.slice(6, 8);
      const hour = dateString.slice(8, 10);
      const minute = dateString.slice(10, 12);
      const second = dateString.slice(12, 14);

      const formattedDate = `${year}-${month}-${day}T${hour}:${minute}:${second}`;
      const date = new Date(formattedDate);
      const now = new Date();
      const diffSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

      if (diffSeconds < 60) return "Vừa xong";
      if (diffSeconds < 3600)
        return `${Math.floor(diffSeconds / 60)} phút trước`;
      if (diffSeconds < 86400)
        return `${Math.floor(diffSeconds / 3600)} giờ trước`;
      if (diffSeconds < 2592000)
        return `${Math.floor(diffSeconds / 86400)} ngày trước`;
      if (diffSeconds < 31536000)
        return `${Math.floor(diffSeconds / 2592000)} tháng trước`;

      return `${Math.floor(diffSeconds / 31536000)} năm trước`;
    }

    return "Không có dữ liệu";
  };

  return (
    <section>
      <div className="container mx-auto px-4">
        {/* Breadcrumb */}
        <ForumBreadcrumb
          items={[
            {
              type: "link",
              label: "Trang chủ",
              href: "/"
            },
            {
              type: "select",
              label: "Chọn cấp",
              value: eduEcosystemId?.toString() || "",
              href: "/forums/:value",
              options: [
                { key: 1, value: "1", label: "Mầm non" },
                { key: 2, value: "2", label: "Tiểu học" },
                { key: 3, value: "3", label: "Trung học cơ sở" },
                { key: 4, value: "4", label: "Trung học phổ thông" }
              ]
            },
            {
              type: "text",
              label: keyword
                ? `Kết quả tìm kiếm: "${keyword}"`
                : "Kết quả tìm kiếm"
            }
          ]}
        />

        <div className="container mx-auto px-4 py-6">
          <div className="">
            {/* Search Bar */}
            <div className="mb-6 rounded-lg bg-white p-4 shadow-sm">
              <div className="relative">
                <input
                  type="text"
                  value={state.searchInputValue}
                  onChange={(e) =>
                    updateState({ searchInputValue: e.target.value })
                  }
                  onKeyDown={handleKeyDown}
                  className="w-full rounded-lg border border-gray-200 py-2 pl-10 pr-4 text-sm focus:border-transparent focus:outline-none focus:ring-2 focus:ring-primary"
                />
                <div className="absolute left-3 top-1/2 flex h-5 w-5 -translate-y-1/2 transform items-center justify-center text-gray-400">
                  <i className="ri-search-line"></i>
                </div>
                <button
                  onClick={handleSearch}
                  className="absolute right-3 top-1/2 -translate-y-1/2 transform whitespace-nowrap rounded-lg bg-primary px-4 py-1 text-sm text-white"
                >
                  Tìm kiếm
                </button>
              </div>
            </div>
            {/* Filter Categories */}
            {/* <div className="mb-6 overflow-x-auto rounded-lg bg-white p-4 shadow-sm">
              <div className="flex min-w-max items-center space-x-3">
                <span className="text-sm font-medium text-gray-500">
                  Diễn đàn:
                </span>
                <div className="flex items-center space-x-2">
                  {state.ecosystems.map((eco) => (
                    <button
                      key={eco.eduEcosystemId}
                      className={`whitespace-nowrap rounded-full px-3 py-1.5 text-sm ${
                        eco.eduEcosystemId === Number(eduEcosystemId)
                          ? "bg-primary text-white"
                          : "cursor-not-allowed bg-white text-gray-700 hover:bg-gray-200"
                      }`}
                      disabled={eco.eduEcosystemId !== Number(eduEcosystemId)}
                    >
                      {eco.name}
                    </button>
                  ))}
                </div>
              </div>
            </div> */}

            {/* Filter Hastags */}
            {state.hashtag && (
              <div className="mb-6 overflow-x-auto rounded-lg bg-white p-4 shadow-sm">
                <div className="flex min-w-max items-center space-x-3">
                  <span className="text-sm font-medium text-gray-500">
                    Hashtags:
                  </span>
                  <div className="flex items-center space-x-2">
                    <button
                      className={`whitespace-nowrap rounded-full px-3 py-1.5 text-sm hover:bg-gray-200 ${
                        state.hashtag === hashtag
                          ? "bg-primary text-white"
                          : "bg-white text-gray-700"
                      }`}
                    >
                      {state.hashtag}
                    </button>
                  </div>
                </div>
              </div>
            )}

            <div className="flex flex-col gap-6 lg:flex-row">
              {/* Left Sidebar - Filters */}
              <div className="w-full lg:w-1/4">
                <div className="mb-6 rounded-lg bg-white p-4 shadow-sm">
                  <h3 className="mb-4 font-medium">Bộ lọc tìm kiếm</h3>

                  <div className="mb-6 flex flex-wrap justify-between gap-6 lg:flex-col">
                    {/* Filter by Topic */}
                    <div>
                      <h4 className="mb-3 text-sm font-medium text-gray-700">
                        Diễn đàn
                      </h4>
                      <div className="space-y-2">
                        {state.topicFilters.map((topic) => (
                          <label
                            key={topic.id}
                            className="flex items-center text-sm text-gray-600"
                          >
                            <input
                              type="checkbox"
                              className="mr-2"
                              checked={state.selectedTopics.includes(topic.id)}
                              onChange={() => handleTopicChange(topic.id)}
                            />
                            {topic.name}
                          </label>
                        ))}
                      </div>
                    </div>
                    {/* Filter by Time */}
                    <div>
                      <h4 className="mb-3 text-sm font-medium text-gray-700">
                        Thời gian đăng
                      </h4>
                      <div className="space-y-2">
                        {state.timeFilters.map((time) => (
                          <label
                            key={time.id}
                            className="flex items-center text-sm text-gray-600"
                          >
                            <input
                              type="checkbox"
                              className="mr-2"
                              checked={state.selectedTimeFilters.includes(
                                time.id
                              )}
                              onChange={() => handleTimeFilterChange(time.id)}
                            />
                            {time.name}
                          </label>
                        ))}
                      </div>
                    </div>
                    {/* Filter by Views/Comments */}
                    <div>
                      <h4 className="mb-3 text-sm font-medium text-gray-700">
                        Lượt xem/bình luận
                      </h4>
                      <div className="space-y-2">
                        {state.viewCommentFilters.map((filter) => (
                          <label
                            key={filter.id}
                            className="flex items-center text-sm text-gray-600"
                          >
                            <input
                              type="checkbox"
                              className="mr-2"
                              checked={state.selectedViewFilters.includes(
                                filter.id
                              )}
                              onChange={() => handleViewFilterChange(filter.id)}
                            />
                            {filter.name}
                          </label>
                        ))}
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-between border-t pt-4">
                    <button
                      onClick={clearFilters}
                      className="text-sm text-gray-500 hover:text-gray-700"
                    >
                      Xóa bộ lọc
                    </button>
                    <button
                      onClick={applyFilters}
                      className="text-sm text-primary hover:text-primary/80"
                    >
                      Áp dụng
                    </button>
                  </div>
                </div>
              </div>

              {/* Right Content - Search Results */}
              <div className="w-full lg:w-3/4">
                {state.isLoading ? (
                  <div className="flex h-40 w-full items-center justify-center rounded-lg bg-white p-4 shadow-sm">
                    <div className="flex flex-col items-center">
                      <div className="mb-2 h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
                      <p className="text-gray-500">Đang tải dữ liệu...</p>
                    </div>
                  </div>
                ) : (
                  <>
                    {/* View Toggle and Sort */}
                    <div className="mb-6 flex flex-col justify-between gap-2 rounded-lg bg-white p-4 shadow-sm sm:flex-row sm:items-center">
                      <div className="text-sm text-gray-500">
                        Hiển thị{" "}
                        <span className="font-medium text-gray-700">
                          {state.totalElements}
                        </span>{" "}
                        kết quả 
                        {keyword && " từ khóa "}
                        {keyword && (
                          <span className="font-medium text-gray-700">
                            "{keyword}"
                          </span>
                        )}
                        {state.hashtag && " hashtags "}
                        {state.hashtag && (
                          <span className="font-medium text-gray-700">
                            "{state.hashtag}"
                          </span>
                        )}
                      </div>
                      <div className="flex items-center space-x-4">
                        <div className="flex items-center space-x-2">
                          <span className="text-sm text-gray-500">
                            Sắp xếp:
                          </span>
                          <Select
                            value={state.selectedSortOption}
                            onValueChange={(value) => {
                              updateState({ selectedSortOption: value });
                              updateUrlWithFilters({
                                ...state,
                                selectedSortOption: value
                              });
                            }}
                          >
                            <SelectTrigger className="h-8 w-auto whitespace-nowrap px-2 py-1 text-sm">
                              <SelectValue placeholder="Sắp xếp">
                                {state.sortOptions.find(
                                  (option) =>
                                    option.id === state.selectedSortOption
                                )?.name || "Mới nhất"}
                              </SelectValue>
                            </SelectTrigger>
                            <SelectContent>
                              {state.sortOptions.map((option) => (
                                <SelectItem key={option.id} value={option.id}>
                                  {option.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                    </div>

                    {/* Loading Skeletons or Grid View Results */}
                    {state.searchResults.length > 0 ? (
                      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
                        {state.searchResults.map((post) => (
                          <div
                            key={post.postId}
                            className="overflow-hidden rounded-lg bg-white shadow-sm"
                          >
                            <div className="p-4">
                              <div className="mb-2 flex items-center text-xs text-gray-500">
                                <i className="ri-folder-line mr-1"></i>
                                <span>{post.forumName}</span>
                              </div>
                              <h3 className="mb-2 line-clamp-1 font-medium">
                                <p>{post.title}</p>
                              </h3>
                              <p className="mb-3 line-clamp-3 min-h-[60px] text-sm text-gray-600">
                                {post.content?.replace(/<[^>]*>/g, "")}
                              </p>
                              <div className="flex items-center justify-between text-xs text-gray-500">
                                <div className="flex items-center">
                                  <div className="mr-1 flex h-6 w-6 items-center justify-center rounded-full bg-gray-200">
                                    {post.authorAvatar ? (
                                      state.authorAvatars[post.authorAvatar] ? (
                                        <Image
                                          src={
                                            state.authorAvatars[
                                              post.authorAvatar
                                            ]
                                          }
                                          alt={post.authorName || ""}
                                          width={24}
                                          height={24}
                                          className="rounded-full object-cover"
                                        />
                                      ) : (
                                        <i className="ri-user-line"></i>
                                      )
                                    ) : (
                                      <div className="flex h-full w-full items-center justify-center rounded-full bg-indigo-100 text-xs font-bold text-indigo-600">
                                        {post.authorName?.charAt(0) || "A"}
                                      </div>
                                    )}
                                  </div>
                                  <span>{post.authorName}</span>
                                </div>
                                <div>{getRelativeTime(post.createdAt)}</div>
                              </div>
                            </div>
                            <div className="flex items-center justify-between bg-gray-50 px-4 py-2">
                              <div className="flex items-center space-x-3 text-xs text-gray-500">
                                <div className="flex items-center">
                                  <i className="ri-eye-line mr-1"></i>
                                  <span>{post.views || 0}</span>
                                </div>
                                <div className="flex items-center">
                                  <i className="ri-message-2-line mr-1"></i>
                                  <span>{post.comments || 0}</span>
                                </div>
                              </div>
                              <Link
                                href={`/forums/${eduEcosystemId}/topics/${post.forumId}/posts/${post.postId}`}
                                className="text-xs text-primary hover:underline"
                              >
                                Xem chi tiết
                              </Link>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="flex h-40 w-full items-center justify-center rounded-lg bg-white p-4 shadow-sm">
                        <p className="text-center text-gray-500">
                          Không tìm thấy kết quả nào phù hợp.
                        </p>
                      </div>
                    )}
                    {/* Pagination */}
                    <div className="mt-6 flex flex-col justify-between gap-4 sm:flex-row sm:items-center">
                      <div className="text-sm text-gray-500">
                        Hiển thị {state.currentPage * pageSize + 1}-
                        {Math.min(
                          (state.currentPage + 1) * pageSize,
                          state.totalElements
                        )}{" "}
                        trong tổng số {state.totalElements} kết quả
                      </div>
                      <div className="flex items-center space-x-1">
                        <button
                          onClick={() => handlePageChange(0)}
                          disabled={state.currentPage === 0}
                          className="flex h-8 w-8 items-center justify-center rounded border text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                        >
                          <i className="ri-arrow-left-s-line"></i>
                        </button>
                        {/* Các nút số trang */}
                        {Array.from(
                          { length: Math.min(5, state.totalPages) },
                          (_, i) => {
                            const pageNum = i;
                            const isVisible =
                              pageNum >= state.currentPage - 2 &&
                              pageNum <= state.currentPage + 2;
                            if (!isVisible) return null;

                            return (
                              <button
                                key={i}
                                onClick={() => handlePageChange(pageNum)}
                                className={`flex h-8 w-8 items-center justify-center rounded border ${
                                  state.currentPage === pageNum
                                    ? "bg-primary text-white"
                                    : "text-gray-500 hover:bg-gray-50"
                                }`}
                              >
                                {pageNum + 1}
                              </button>
                            );
                          }
                        )}
                        <button
                          onClick={() => handlePageChange(state.totalPages - 1)}
                          disabled={state.currentPage >= state.totalPages - 1}
                          className="flex h-8 w-8 items-center justify-center rounded border text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                        >
                          <i className="ri-arrow-right-s-line"></i>
                        </button>
                      </div>
                    </div>
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
