"use client";

import EditUserInfo from "@/components/forums/EditUserInfo";
import ForumBreadcrumb from "@/components/forums/ForumBreadcrumb";
import ChangePasswordModal from "@/components/modals/ChangePasswordModal";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { useAuth } from "@/context/AuthContext";
import useUploadFile from "@/hook/useUploadFile";
import anonymousServices from "@/services/anonymousServices";
import userServices from "@/services/user/userServices";
import { FileText, Heart, MessageSquare } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useParams, useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";
import { BiLike, BiSolidLike } from "react-icons/bi";

type TabType = "activities" | "posts" | "about" | "change-password";

interface UserHistory {
  userHistoryUserForumId: number;
  userForumId: number;
  content: string;
  historyUserForumsType: string;
  postTitle: string;
  objectId: number;
  eduEcosystemId: number;
  forumId: number;
  topicId: number;
  date: any;
}

interface UserHistoryResponse {
  content: UserHistory[];
  pageable: {
    pageNumber: number;
    pageSize: number;
    totalPages: number;
    totalElements: number;
  };
}

interface Post {
  textColor: string;
  backgroundColor: any;
  postId: number;
  title: string;
  content: string;
  createdAt: string;
  updatedAt: string;
  topicName: string;
  likeCount: number;
  commentCount: number;
  viewCount: number;
  status: string;
  userForumId: number;
  username: string;
  thumbnail: string;
  eduEcosystemId: number;
  forumId: number;
  color: string;
  isLike?: boolean;
}

interface UserInfo {
  userForumId: number;
  userId: number;
  username: string;
  thumbnail: string;
  topicUserList: any;
  background: string | null;
  phoneNumber: string;
  bio: string;
  fullName: string;
  firstName: string;
  lastName: string;
  gender: string;
  dateOfBirth: string;
  numberOfPost: number;
  userType: string;
  orgName: string;
  countPosts: number;
  countComments: number;
  countLikes: number;
  address: string;
  hometown: string;
  position: string;
  hobbies: string;
  careerGoal: string;
}

export default function UserProfilePage() {
  const { user } = useAuth();
  const { eduEcosystemId } = useParams();
  const router = useRouter();

  const [userInfo, setUserInfo] = useState<UserInfo>();
  const [activeTab, setActiveTab] = useState<TabType>("activities");
  const [userHistory, setUserHistory] = useState<UserHistory[]>([]);
  const [posts, setPosts] = useState<Post[]>([]);
  const [myPosts, setMyPosts] = useState<Post[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isChangePasswordModalOpen, setIsChangePasswordModalOpen] =
    useState(false);
  const [isEditUserInfoModalOpen, setIsEditUserInfoModalOpen] = useState(true);
  const [pagination, setPagination] = useState({
    page: 0,
    size: 10,
    totalPages: 0,
    totalElements: 0
  });
  const [selectedLevel, setSelectedLevel] = useState<string>(
    eduEcosystemId?.toString() || ""
  );
  const [inputSearch, setInputSearch] = useState("");
  const [debouncedSearch, setDebouncedSearch] = useState("");
  const [sort, setSort] = useState<"ASC" | "DESC">("DESC");
  const { viewFile } = useUploadFile();
  const tabKey: TabType[] = ["activities", "posts", "about", "change-password"];

  // Format date from "20250418165842" to readable format
  const formatDate = (dateStr: string) => {
    if (dateStr?.length >= 14) {
      const year = dateStr.substring(0, 4);
      const month = dateStr.substring(4, 6);
      const day = dateStr.substring(6, 8);
      const hour = dateStr.substring(8, 10);
      const minute = dateStr.substring(10, 12);
      const second = dateStr.substring(12, 14);

      const date = new Date(
        `${year}-${month}-${day}T${hour}:${minute}:${second}`
      );
      return new Intl.DateTimeFormat("vi-VN", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit"
      }).format(date);
    }
    return "";
  };

  // Mock data cho các hoạt động

  // Lấy chữ cái đầu tiên của username làm avatar mặc định
  const getInitialAvatar = () => {
    const username = userInfo?.username || "";
    return username.charAt(0)?.toUpperCase() || "U";
  };

  const getUserHistory = async (page = 0, size = 10) => {
    try {
      const response = await userServices.getUserHistory({ page, size });
      if (response.data.data) {
        setUserHistory(response.data.data.content);
        // setPagination({
        //   page: response.data.data.pageable.pageNumber,
        //   size: response.data.data.pageable.pageSize,
        //   totalPages: response.data.data.totalPages,
        //   totalElements: response.data.data.totalElements
        // });
      }
    } catch (error) {
      console.log(error);
    }
  };

  const getPosts = async (page = 0, size = 10) => {
    try {
      const response = await anonymousServices.getMyPostUserHighlight({
        page,
        size,
        textSearch: debouncedSearch,
        userForumId: user?.forumInfo?.userForumId as number
      });
      setPosts(response.data.data?.content);
      setPagination({
        page: response.data.data?.pageable?.pageNumber,
        size: response.data.data?.pageable?.pageSize,
        totalPages: response.data.data?.totalPages,
        totalElements: response.data.data?.totalElements
      });
    } catch (error) {
      console.log(error);
    }
  };

  const getMyPosts = async (page = 0, size = 10) => {
    try {
      const response = await userServices.getPostsUser({
        page,
        size,
        textSearch: debouncedSearch,
        sort: sort
      });
      if (response.data.data) {
        setMyPosts(response.data.data.content);
        setPagination({
          page: response.data.data.pageable.pageNumber,
          size: response.data.data.pageable.pageSize,
          totalPages: response.data.data.totalPages,
          totalElements: response.data.data.totalElements
        });
      }
    } catch (error) {
      console.log(error);
    }
  };

  const getUserInfo = async (status = true) => {
    if (userInfo && status) return;

    try {
      setIsLoading(true);
      const response = await userServices.getUserInfo();
      const rawUser = response?.data?.data;
      if (rawUser) {
        setUserInfo({
          ...rawUser,
          thumbnail: null
        });
        const thumbnail = await viewFile(rawUser.thumbnail, "forums");
        // Set userInfo với thumbnail (nếu có) hoặc null
        setUserInfo({
          ...rawUser,
          thumbnail: thumbnail
        });
      }
    } catch (error) {
      // Nếu có lỗi, set thumbnail = null, các trường khác giữ nguyên (nếu có)
      console.error(error);
    } finally {
      console.log("User info loaded", userInfo);
      setIsLoading(false);
    }
  };

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedSearch(inputSearch);
      setPagination({
        page: 0,
        size: 10,
        totalPages: 0,
        totalElements: 0
      });
    }, 400);
    return () => clearTimeout(handler);
  }, [inputSearch]);

  useEffect(() => {
    if (activeTab === "activities") {
      if (user?.forumInfo?.userForumId) {
        getPosts(pagination.page, pagination.size);
      }
      getUserInfo();
    } else if (activeTab === "posts") {
      getMyPosts(pagination.page, pagination.size);
    } else if (activeTab === "about") {
      getUserInfo();
    }
  }, [
    activeTab,
    pagination.page,
    debouncedSearch,
    sort,
    user?.forumInfo?.userForumId
  ]);

  // useEffect(() => {
  //   if (activeTab === "posts") {
  //     getMyPosts();
  //   }
  // }, [activeTab, pagination.page, debouncedSearch, sort]);

  const handleTabChange = (tab: TabType) => {
    setActiveTab(tab);
    setPagination({
      page: 0,
      size: 10,
      totalPages: 0,
      totalElements: 0
    });
  };

  useEffect(() => {
    setPagination({
      page: 0,
      size: 10,
      totalPages: 0,
      totalElements: 0
    });
    setInputSearch("");
  }, []);

  const handlePageChange = (newPage: number) => {
    setPagination((prev) => ({ ...prev, page: newPage }));
  };

  const renderPaginationButtons = () => {
    const buttons = [];
    const currentPage = pagination.page;
    const totalPages = pagination.totalPages;
    const maxVisiblePages = 5; // Số nút số hiển thị tối đa
    const delta = 1; // Số trang trước/sau currentPage hiển thị

    // Mũi tên trái
    buttons.push(
      <Button
        key="prev"
        variant="outline"
        size="sm"
        onClick={() => handlePageChange(currentPage - 1)}
        disabled={currentPage === 0}
        className="flex h-[42px] w-[42px] items-center justify-center rounded-lg p-0"
        aria-label="Trang trước"
      >
        <svg width="20" height="20" fill="none" viewBox="0 0 24 24">
          <path
            d="M15 19l-7-7 7-7"
            stroke="#2E228B"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      </Button>
    );

    const pageNumbers: (number | string)[] = [];

    if (totalPages <= maxVisiblePages) {
      // Hiển thị tất cả nếu ít trang
      for (let i = 0; i < totalPages; i++) pageNumbers.push(i);
    } else {
      const left = Math.max(currentPage - delta, 1);
      const right = Math.min(currentPage + delta, totalPages - 2);

      pageNumbers.push(0); // Trang đầu tiên luôn hiển thị

      if (left > 1) pageNumbers.push("left-ellipsis");

      for (let i = left; i <= right; i++) pageNumbers.push(i);

      if (right < totalPages - 2) pageNumbers.push("right-ellipsis");

      pageNumbers.push(totalPages - 1); // Trang cuối
    }

    for (const page of pageNumbers) {
      if (typeof page === "string") {
        buttons.push(
          <span key={page} className="px-2 text-gray-500">
            ...
          </span>
        );
      } else {
        buttons.push(
          <Button
            key={page}
            variant={page === currentPage ? "default" : "outline"}
            size="sm"
            onClick={() => handlePageChange(page)}
            className={`h-[42px] w-[35px] rounded-lg ${
              page === currentPage
                ? "bg-[#2E228B] text-white hover:bg-[#2E228B]"
                : "border-gray-200 text-[#2E228B] hover:bg-indigo-50"
            }`}
            aria-current={page === currentPage ? "page" : undefined}
          >
            {page + 1}
          </Button>
        );
      }
    }

    // Mũi tên phải
    buttons.push(
      <Button
        key="next"
        variant="outline"
        size="sm"
        onClick={() => handlePageChange(currentPage + 1)}
        disabled={currentPage === totalPages - 1}
        className="flex h-[42px] w-[42px] items-center justify-center rounded-lg p-0"
        aria-label="Trang sau"
      >
        <svg width="20" height="20" fill="none" viewBox="0 0 24 24">
          <path
            d="M9 5l7 7-7 7"
            stroke="#2E228B"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      </Button>
    );

    return buttons;
  };

  const handleEditUserInfoSuccess = async () => {
    await getUserInfo(false);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-r from-indigo-50 to-purple-50">
        <div className="container mx-auto px-4">
          <div className="rounded-[8px] bg-white/80 p-8 shadow-lg backdrop-blur-sm">
            <div className="flex flex-col items-center gap-6 md:flex-row md:items-start">
              {/* Avatar Skeleton */}
              <Skeleton className="h-40 w-40 rounded-full" />

              {/* User Info Skeleton */}
              <div className="flex-1 space-y-4">
                <div className="space-y-2">
                  <Skeleton className="h-8 w-64" />
                  <div className="flex gap-2">
                    <Skeleton className="h-6 w-24 rounded-full" />
                    <Skeleton className="h-6 w-32 rounded-full" />
                  </div>
                  <div className="grid grid-cols-1 gap-2 sm:grid-cols-2">
                    <Skeleton className="h-6 w-full" />
                    <Skeleton className="h-6 w-full" />
                    <Skeleton className="h-6 w-full" />
                  </div>
                </div>

                {/* Stats Skeleton */}
                <div className="mt-6 grid grid-cols-3 gap-4 rounded-[8px] bg-gradient-to-r from-indigo-50 to-purple-50 p-6">
                  <div className="text-center">
                    <Skeleton className="mx-auto h-6 w-20" />
                    <Skeleton className="mx-auto mt-2 h-8 w-12" />
                  </div>
                  <div className="text-center">
                    <Skeleton className="mx-auto h-6 w-20" />
                    <Skeleton className="mx-auto mt-2 h-8 w-12" />
                  </div>
                  <div className="text-center">
                    <Skeleton className="mx-auto h-6 w-20" />
                    <Skeleton className="mx-auto mt-2 h-8 w-12" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Tabs Skeleton */}
        <div className="container mx-auto mt-4 px-4">
          <div className="flex rounded-t-lg border border-indigo-100 bg-white">
            <Skeleton className="h-12 flex-1" />
            <Skeleton className="h-12 flex-1" />
            <Skeleton className="h-12 flex-1" />
          </div>
          <div className="rounded-b-lg border border-t-0 border-indigo-100 bg-white/80 p-6">
            <div className="space-y-4">
              {[...Array(3)].map((_, index) => (
                <Skeleton key={index} className="h-24 w-full rounded-[8px]" />
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100 pb-[24px]">
      <ForumBreadcrumb
        items={[
          {
            type: "link",
            label: "Trang chủ",
            href: "/"
          },
          {
            type: "select",
            label: "Chọn cấp",
            value: eduEcosystemId?.toString() || "",
            href: "/forums/:value",
            options: [
              { key: 1, value: "1", label: "Mầm non" },
              { key: 2, value: "2", label: "Tiểu học" },
              { key: 3, value: "3", label: "Trung học cơ sở" },
              { key: 4, value: "4", label: "Trung học phổ thông" }
            ]
          },
          {
            type: "text",
            label: "Trang cá nhân"
          }
        ]}
      />
      {/* Profile Header */}
      <div className="w-full">
        <div className="container mx-auto flex items-center gap-6 px-4 py-6">
          <div className="flex-shrink-0">
            {userInfo?.thumbnail ? (
                <div className="relative h-[128px] w-[128px]">
                  <Image
                      src={userInfo.thumbnail}
                      alt={String(userInfo.username)}
                      width={128}
                      height={128}
                      className="h-full w-full rounded-full border-4 border-white object-cover shadow-lg"
                      priority
                  />
                </div>
            ) : (
                <div className="flex h-[128px] w-[128px] items-center justify-center rounded-full bg-gray-200 text-4xl font-bold text-gray-500 shadow-lg">
                  {getInitialAvatar()}
                </div>
            )}
          </div>

          <div className="flex flex-col justify-center">
            <div className="text-[#1F2937 mb-1 text-2xl font-bold flex ">
              {userInfo?.fullName}

              {userInfo?.orgName && (
                  <div className="ml-2 flex items-center gap-0.5 bg-primary/5 text-primary px-1.5 py-0.5 rounded-full">
                    <i className="ri-verified-badge-fill text-xs"></i>
                    <span className="text-xs font-normal">{userInfo?.orgName}</span>
                  </div>
              )}
            </div>
            <div className="mb-1 text-base font-[500] text-[#2E228B]">
              {userInfo?.job}
            </div>
            <div className="max-w-4xl text-base text-[#4B5563]">
              {userInfo?.bio || ""}
            </div>
          </div>
        </div>
      </div>
      <div className="container mx-auto mb-[24px]">
        {/* Tabs ngang có underline */}
        <div className="flex min-h-[58px] gap-[32px] border-y border-gray-200 pt-[24px]">
          {["Tổng quan", "Bài viết", "Giới thiệu", "Đổi mật khẩu"].map(
            (tab, idx) => (
              <button
                key={tab}
                className={`h-full border-b-2 pb-[19px] text-base font-semibold transition-colors focus:outline-none ${
                  activeTab === tabKey[idx]
                    ? "border-[#2E228B] text-[#2E228B]"
                    : "border-transparent text-gray-500 hover:text-indigo-600"
                }`}
                onClick={() => handleTabChange(tabKey[idx])}
              >
                {tab}
              </button>
            )
          )}
        </div>
      </div>
      <section className="container mx-auto flex flex-col gap-6 lg:flex-row lg:gap-[33.33px]">
        {/* Layout 2 cột: trái là nội dung, phải là sidebar */}
        <div className="flex-1">
          {/* Cột trái: Nội dung chính */}
          <div
            className={`flex flex-col ${
              activeTab === "activities" ? "lg:col-span-2" : "lg:col-span-3"
            }`}
          >
            {/* Số liệu thống kê */}
            {activeTab === "activities" && (
              <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                <div
                  className="flex h-[132px] flex-col items-center justify-center rounded-[8px] bg-white px-6"
                  style={{
                    boxShadow:
                      "0px 1px 3px 0px #0000001A, 0px 1px 2px -1px #0000001A"
                  }}
                >
                  <svg
                    className="mb-[16px]"
                    width="26"
                    height="24"
                    viewBox="0 0 26 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M20.5547 22H4.55469C4.27469 22 4.03802 21.9033 3.84469 21.71C3.65135 21.5167 3.55469 21.28 3.55469 21V3C3.55469 2.72 3.65135 2.48333 3.84469 2.29C4.03802 2.09667 4.27469 2 4.55469 2H20.5547C20.8347 2 21.0714 2.09667 21.2647 2.29C21.458 2.48333 21.5547 2.72 21.5547 3V21C21.5547 21.28 21.458 21.5167 21.2647 21.71C21.0714 21.9033 20.8347 22 20.5547 22ZM19.5547 20V4H5.55469V20H19.5547ZM8.55469 7H16.5547V9H8.55469V7ZM8.55469 11H16.5547V13H8.55469V11ZM8.55469 15H16.5547V17H8.55469V15Z"
                      fill="#2E228B"
                    />
                  </svg>
                  <div className="mb-1 text-[14px] text-[#4B5563]">
                    Bài viết
                  </div>
                  <div className="text-2xl font-bold text-black">
                    {userInfo?.countPosts}
                  </div>
                </div>
                <div
                  className="flex h-[132px] flex-col items-center justify-center rounded-[8px] bg-white px-6"
                  style={{
                    boxShadow:
                      "0px 1px 3px 0px #0000001A, 0px 1px 2px -1px #0000001A"
                  }}
                >
                  <svg
                    className="mb-[16px]"
                    width="26"
                    height="24"
                    viewBox="0 0 26 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M15.2641 9.17999H21.6641C22.0241 9.17999 22.3574 9.26999 22.6641 9.44999C22.9707 9.62999 23.2141 9.87333 23.3941 10.18C23.5741 10.4867 23.6641 10.82 23.6641 11.18V13.28C23.6641 13.5467 23.6107 13.8 23.5041 14.04L20.4241 21.56C20.3441 21.7467 20.2207 21.8967 20.0541 22.01C19.8874 22.1233 19.7041 22.18 19.5041 22.18H2.66406C2.38406 22.18 2.1474 22.0833 1.95406 21.89C1.76073 21.6967 1.66406 21.46 1.66406 21.18V11.18C1.66406 10.9 1.76073 10.6633 1.95406 10.47C2.1474 10.2767 2.38406 10.18 2.66406 10.18H6.14406C6.30406 10.18 6.4574 10.1433 6.60406 10.07C6.75073 9.99666 6.87073 9.89332 6.96406 9.75999L12.4241 2.03999C12.4907 1.93332 12.5841 1.86333 12.7041 1.82999C12.8241 1.79666 12.9374 1.81332 13.0441 1.87999L14.8641 2.77999C15.3841 3.04666 15.7674 3.44666 16.0141 3.97999C16.2607 4.51332 16.3107 5.06666 16.1641 5.63999L15.2641 9.17999ZM7.66406 11.76V20.18H18.8241L21.6641 13.28V11.18H15.2641C14.8374 11.18 14.4541 11.06 14.1141 10.82C13.7741 10.58 13.5307 10.2667 13.3841 9.87999C13.2374 9.49333 13.2174 9.09332 13.3241 8.67999L14.2241 5.13999C14.2507 5.01999 14.2407 4.90666 14.1941 4.79999C14.1474 4.69333 14.0707 4.61333 13.9641 4.55999L13.3041 4.23999L8.60406 10.92C8.35073 11.2667 8.0374 11.5467 7.66406 11.76ZM5.66406 12.18H3.66406V20.18H5.66406V12.18Z"
                      fill="#22C55E"
                    />
                  </svg>
                  <div className="mb-1 text-[14px] text-[#4B5563]">
                    Lượt thích
                  </div>
                  <div className="text-2xl font-bold text-black">
                    {userInfo?.countLikes}
                  </div>
                </div>
                <div
                  className="flex h-[132px] flex-col items-center justify-center rounded-[8px] bg-white px-6"
                  style={{
                    boxShadow:
                      "0px 1px 3px 0px #0000001A, 0px 1px 2px -1px #0000001A"
                  }}
                >
                  <svg
                    className="mb-[16px]"
                    width="26"
                    height="24"
                    viewBox="0 0 26 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M7.23344 18.24L2.77344 21.74V3.23999C2.77344 2.97332 2.8701 2.73999 3.06344 2.53999C3.25677 2.33999 3.49344 2.23999 3.77344 2.23999H21.7734C22.0534 2.23999 22.2901 2.33999 22.4834 2.53999C22.6768 2.73999 22.7734 2.97332 22.7734 3.23999V17.24C22.7734 17.52 22.6768 17.76 22.4834 17.96C22.2901 18.16 22.0534 18.2533 21.7734 18.24H7.23344ZM6.53344 16.24H20.7734V4.23999H4.77344V17.64L6.53344 16.24ZM11.7734 9.23999H13.7734V11.24H11.7734V9.23999ZM7.77344 9.23999H9.77344V11.24H7.77344V9.23999ZM15.7734 9.23999H17.7734V11.24H15.7734V9.23999Z"
                      fill="#3B82F6"
                    />
                  </svg>
                  <div className="mb-1 text-[14px] text-[#4B5563]">
                    Bình luận
                  </div>
                  <div className="text-2xl font-bold text-black">
                    {userInfo?.countComments}
                  </div>
                </div>
              </div>
            )}

            {/* Nội dung từng tab */}
            {activeTab === "activities" && (
              <div className="mt-[32px]">
                <div className="mb-4 flex items-center justify-between text-[20px] font-bold text-[#1F2937]">
                  Bài viết nổi bật
                </div>
                <div className="flex flex-col gap-4">
                  {posts.map((post) => (
                    <Link
                      key={post.postId}
                      href={`/forums/${post?.eduEcosystemId}/topics/${post?.forumId}/posts/${post?.postId}`}
                      className="block h-[200px] overflow-auto rounded-[8px] bg-white p-6 shadow-combined transition-all hover:-translate-y-1 hover:shadow-lg"
                    >
                      <div className="mb-2 flex items-center gap-2">
                        <span
                          className="rounded-full px-3 py-1 text-xs font-medium text-white"
                          style={{
                            backgroundColor: post?.backgroundColor
                              ? post.backgroundColor
                              : "#E0E7FF",
                            color: post?.textColor || "blue"
                          }}
                        >
                          {post.topicName}
                        </span>
                        <span className="text-sm text-[#6B7280]">
                          {formatDate(post.createdAt)}
                        </span>
                      </div>
                      <div className="mb-1 line-clamp-2 text-[20px] font-bold text-black">
                        {post.title}
                      </div>
                      <div className="mb-3 line-clamp-2 text-base text-[#6B7280]">
                        {post.content}
                      </div>
                      <div className="mt-2 flex gap-6 text-sm text-[#6B7280]">
                        <span className="flex items-center gap-1">
                          {/* Eye icon */}
                          <svg
                            width="15"
                            height="20"
                            viewBox="0 0 15 20"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M7.2889 4.75C8.3152 4.75 9.28708 4.98333 10.2045 5.45C11.0831 5.90111 11.8237 6.52528 12.4262 7.3225C13.0288 8.11972 13.4195 9.01222 13.5983 10C13.4195 10.9878 13.0288 11.8803 12.4262 12.6775C11.8237 13.4747 11.0831 14.0989 10.2045 14.55C9.28708 15.0167 8.3152 15.25 7.2889 15.25C6.2626 15.25 5.29073 15.0167 4.37328 14.55C3.4947 14.0989 2.75414 13.4747 2.15157 12.6775C1.54901 11.8803 1.15832 10.9878 0.979492 10C1.15832 9.01222 1.54901 8.11972 2.15157 7.3225C2.75414 6.52528 3.4947 5.90111 4.37328 5.45C5.29073 4.98333 6.2626 4.75 7.2889 4.75ZM7.2889 14.0833C8.0975 14.0833 8.86723 13.9044 9.59808 13.5467C10.3056 13.2044 10.9062 12.7222 11.3999 12.1C11.8936 11.4778 12.2299 10.7778 12.4087 10C12.2299 9.22222 11.8936 8.52222 11.3999 7.9C10.9062 7.27778 10.3056 6.79556 9.59808 6.45333C8.86723 6.09556 8.0975 5.91667 7.2889 5.91667C6.4803 5.91667 5.71058 6.09556 4.97973 6.45333C4.2722 6.79556 3.67159 7.27778 3.17787 7.9C2.68416 8.52222 2.34789 9.22222 2.16907 10C2.34789 10.7778 2.68416 11.4778 3.17787 12.1C3.67159 12.7222 4.2722 13.2044 4.97973 13.5467C5.71058 13.9044 6.4803 14.0833 7.2889 14.0833ZM7.2889 12.625C6.81463 12.625 6.37729 12.5064 5.97687 12.2692C5.57646 12.0319 5.25769 11.7131 5.02055 11.3125C4.78341 10.9119 4.66484 10.4744 4.66484 10C4.66484 9.52556 4.78341 9.08806 5.02055 8.6875C5.25769 8.28694 5.57646 7.96806 5.97687 7.73083C6.37729 7.49361 6.81463 7.375 7.2889 7.375C7.76318 7.375 8.20052 7.49361 8.60094 7.73083C9.00135 7.96806 9.32012 8.28694 9.55726 8.6875C9.7944 9.08806 9.91297 9.52556 9.91297 10C9.91297 10.4744 9.7944 10.9119 9.55726 11.3125C9.32012 11.7131 9.00135 12.0319 8.60094 12.2692C8.20052 12.5064 7.76318 12.625 7.2889 12.625ZM7.2889 11.4583C7.55325 11.4583 7.79622 11.3922 8.01781 11.26C8.2394 11.1278 8.41628 10.9508 8.54845 10.7292C8.68063 10.5075 8.74672 10.2644 8.74672 10C8.74672 9.73556 8.68063 9.4925 8.54845 9.27083C8.41628 9.04917 8.2394 8.87222 8.01781 8.74C7.79622 8.60778 7.55325 8.54167 7.2889 8.54167C7.02455 8.54167 6.78159 8.60778 6.56 8.74C6.33841 8.87222 6.16153 9.04917 6.02935 9.27083C5.89718 9.4925 5.83109 9.73556 5.83109 10C5.83109 10.2644 5.89718 10.5075 6.02935 10.7292C6.16153 10.9508 6.33841 11.1278 6.56 11.26C6.78159 11.3922 7.02455 11.4583 7.2889 11.4583Z"
                              fill="#6B7280"
                            />
                          </svg>

                          {post.viewCount ?? 0}
                        </span>
                        <span className="flex items-center gap-1">
                          {/* Like icon */}
                          {post.isLike ? (
                            <BiSolidLike className="text-primary" />
                          ) : (
                            <BiLike />
                          )}

                          {post.likeCount ?? 0}
                        </span>
                        <span className="flex items-center gap-1">
                          {/* Comment icon */}
                          <svg
                            width="20"
                            height="20"
                            viewBox="0 0 20 20"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M5.04312 13.64L2.44238 15.6816V4.88997C2.44238 4.73442 2.49875 4.59831 2.61149 4.48164C2.72423 4.36497 2.86223 4.30664 3.02551 4.30664H13.5218C13.685 4.30664 13.823 4.36497 13.9358 4.48164C14.0485 4.59831 14.1049 4.73442 14.1049 4.88997V13.0566C14.1049 13.22 14.0485 13.36 13.9358 13.4766C13.823 13.5933 13.685 13.6478 13.5218 13.64H5.04312ZM4.63493 12.4733H12.9386V5.47331H3.60863V13.29L4.63493 12.4733ZM7.69051 8.38997H8.85676V9.55664H7.69051V8.38997ZM5.35801 8.38997H6.52426V9.55664H5.35801V8.38997ZM10.023 8.38997H11.1893V9.55664H10.023V8.38997Z"
                              fill="#6B7280"
                            />
                          </svg>

                          {post.commentCount ?? 0}
                        </span>
                      </div>
                    </Link>
                  ))}
                </div>
                {/* Pagination */}
                {pagination?.totalElements > 0 && (
                  <div className="mt-8 flex justify-center gap-2">
                    {renderPaginationButtons()}
                  </div>
                )}
                {!posts?.length && (
                  <div className=" mb-2 flex min-h-[10rem] items-center justify-center text-center">
                    <div className="mb-3 whitespace-pre-wrap text-gray-700 opacity-50">
                      Không có bài viết
                    </div>
                  </div>
                )}
              </div>
            )}
            {activeTab === "posts" && (
              <div>
                {/* Header filter và search */}
                {pagination.totalElements > 0 && (
                  <div className="shadow-shadow-sm mb-6 flex flex-col gap-3 rounded-[8px] bg-white p-4 shadow md:flex-row md:items-center md:justify-end">
                    <select
                      className="h-[38px] w-full rounded-lg border border-gray-200 bg-white px-3 text-sm text-gray-700 focus:outline-none md:w-[168px]"
                      value={sort}
                      onChange={(e) => {
                        setPagination((prev) => ({ ...prev, page: 0 }));
                        setSort(e.target.value as "ASC" | "DESC");
                      }}
                    >
                      <option value="DESC">Mới nhất</option>
                      <option value="ASC">Cũ nhất</option>
                    </select>
                    <div className="flex h-[38px] w-full items-center justify-end gap-2 md:w-[260px]">
                      <div className="relative h-full w-full">
                        <svg
                          className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400"
                          width="17"
                          height="16"
                          viewBox="0 0 17 16"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M12.2452 10.9733L15.0968 13.8266L14.1507 14.7733L11.2992 11.92C10.7751 12.3378 10.2021 12.6578 9.58024 12.88C8.92288 13.1111 8.24774 13.2266 7.55484 13.2266C6.47108 13.2266 5.46282 12.9555 4.53007 12.4133C3.62397 11.88 2.90886 11.16 2.38474 10.2533C1.83398 9.31998 1.55859 8.31109 1.55859 7.22665C1.55859 6.1422 1.83398 5.13332 2.38474 4.19998C2.90886 3.29331 3.62397 2.57776 4.53007 2.05332C5.46282 1.5022 6.47108 1.22665 7.55484 1.22665C8.63861 1.22665 9.64687 1.5022 10.5796 2.05332C11.4857 2.57776 12.2053 3.29331 12.7383 4.19998C13.2802 5.13332 13.5511 6.1422 13.5511 7.22665C13.5511 7.91998 13.4356 8.59554 13.2046 9.25332C12.9826 9.87554 12.6628 10.4489 12.2452 10.9733ZM10.8994 10.48C11.3169 10.0533 11.6412 9.56443 11.8721 9.01332C12.1031 8.44443 12.2186 7.84887 12.2186 7.22665C12.2186 6.3822 12.0054 5.59554 11.579 4.86665C11.1704 4.16443 10.6152 3.60887 9.91337 3.19998C9.18494 2.77332 8.39876 2.55998 7.55484 2.55998C6.71093 2.55998 5.92475 2.77332 5.19632 3.19998C4.49454 3.60887 3.93933 4.16443 3.53069 4.86665C3.10429 5.59554 2.89109 6.3822 2.89109 7.22665C2.89109 8.07109 3.10429 8.85776 3.53069 9.58665C3.93933 10.2889 4.49454 10.8444 5.19632 11.2533C5.92475 11.68 6.71093 11.8933 7.55484 11.8933C8.17668 11.8933 8.77186 11.7778 9.34039 11.5466C9.89116 11.3155 10.3797 10.9911 10.8061 10.5733L10.8994 10.48Z"
                            fill="#6B7280"
                          />
                        </svg>
                        <input
                          type="text"
                          placeholder="Tìm kiếm bài viết"
                          className="h-full w-full rounded-[8px] border border-gray-200 bg-white px-3 pl-8 text-sm text-gray-700 focus:outline-none"
                          value={inputSearch}
                          onChange={(e) => setInputSearch(e.target.value)}
                        />
                      </div>
                    </div>
                  </div>
                )}

                {pagination.totalElements < 1 && (
                  <div className="mb-2 mt-4  flex min-h-[10rem] items-center justify-center p-4 text-center">
                    <div className="mb-3 whitespace-pre-wrap text-gray-700 opacity-50">
                      {debouncedSearch
                        ? "Bài viết không khả dụng"
                        : "Không có bài viết"}
                    </div>
                  </div>
                )}
                {pagination.totalElements > 0 && (
                  <div className="mb-[16px] flex items-center justify-center">
                    <div className="text-center text-[20px] font-bold text-[#1F2937] md:text-left">
                      Tất cả bài viết ({pagination.totalElements})
                    </div>
                  </div>
                )}

                {/* Danh sách bài viết dạng lưới */}
                <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                  {myPosts.map((post) => (
                    <Link
                      className="block"
                      href={`/forums/${post?.eduEcosystemId}/topics/${post?.forumId}/posts/${post?.postId}`}
                      key={post.postId}
                    >
                      <div
                        className="h-[228px] rounded-[8px] bg-white p-6 transition-all hover:-translate-y-1 hover:shadow-lg"
                        style={{
                          boxShadow:
                            "0px 1px 3px 0px #0000001A, 0px 1px 2px -1px #0000001A"
                        }}
                      >
                        <div className="mb-2 flex items-center gap-2">
                          <span
                            className="rounded-full px-3 py-1 text-xs font-medium text-white"
                            style={{
                              backgroundColor: post?.backgroundColor
                                ? post.backgroundColor
                                : "#E0E7FF",
                              color: post?.textColor || "blue"
                            }}
                          >
                            {post.topicName}
                          </span>
                          <span className="text-sm text-[#6B7280]">
                            {formatDate(post.createdAt)}
                          </span>
                        </div>
                        <div className="mb-1 line-clamp-2 text-[20px] font-bold text-black">
                          {post.title}
                        </div>
                        <div className="mb-3 line-clamp-2 text-base text-[#6B7280]">
                          {post.content}
                        </div>
                        <div className="mt-2 flex gap-6 text-sm text-[#6B7280]">
                          <span className="flex items-center gap-1">
                            {/* Eye icon */}
                            <svg
                              width="15"
                              height="14"
                              viewBox="0 0 15 14"
                              fill="none"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                d="M7.2889 1.75C8.3152 1.75 9.28708 1.98333 10.2045 2.45C11.0831 2.90111 11.8237 3.52528 12.4262 4.3225C13.0288 5.11972 13.4195 6.01222 13.5983 7C13.4195 7.98778 13.0288 8.88028 12.4262 9.6775C11.8237 10.4747 11.0831 11.0989 10.2045 11.55C9.28708 12.0167 8.3152 12.25 7.2889 12.25C6.2626 12.25 5.29073 12.0167 4.37328 11.55C3.4947 11.0989 2.75414 10.4747 2.15157 9.6775C1.54901 8.88028 1.15832 7.98778 0.979492 7C1.15832 6.01222 1.54901 5.11972 2.15157 4.3225C2.75414 3.52528 3.4947 2.90111 4.37328 2.45C5.29073 1.98333 6.2626 1.75 7.2889 1.75ZM7.2889 11.0833C8.0975 11.0833 8.86723 10.9044 9.59808 10.5467C10.3056 10.2044 10.9062 9.72222 11.3999 9.1C11.8936 8.47778 12.2299 7.77778 12.4087 7C12.2299 6.22222 11.8936 5.52222 11.3999 4.9C10.9062 4.27778 10.3056 3.79556 9.59808 3.45333C8.86723 3.09556 8.0975 2.91667 7.2889 2.91667C6.4803 2.91667 5.71058 3.09556 4.97973 3.45333C4.2722 3.79556 3.67159 4.27778 3.17787 4.9C2.68416 5.52222 2.34789 6.22222 2.16907 7C2.34789 7.77778 2.68416 8.47778 3.17787 9.1C3.67159 9.72222 4.2722 10.2044 4.97973 10.5467C5.71058 10.9044 6.4803 11.0833 7.2889 11.0833ZM7.2889 9.625C6.81463 9.625 6.37729 9.50639 5.97687 9.26917C5.57646 9.03194 5.25769 8.71306 5.02055 8.3125C4.78341 7.91194 4.66484 7.47444 4.66484 7C4.66484 6.52556 4.78341 6.08806 5.02055 5.6875C5.25769 5.28694 5.57646 4.96806 5.97687 4.73083C6.37729 4.49361 6.81463 4.375 7.2889 4.375C7.76318 4.375 8.20052 4.49361 8.60094 4.73083C9.00135 4.96806 9.32012 5.28694 9.55726 5.6875C9.7944 6.08806 9.91297 6.52556 9.91297 7C9.91297 7.47444 9.7944 7.91194 9.55726 8.3125C9.32012 8.71306 9.00135 9.03194 8.60094 9.26917C8.20052 9.50639 7.76318 9.625 7.2889 9.625ZM7.2889 8.45833C7.55325 8.45833 7.79622 8.39222 8.01781 8.26C8.2394 8.12778 8.41628 7.95083 8.54845 7.72917C8.68063 7.5075 8.74672 7.26444 8.74672 7C8.74672 6.73556 8.68063 6.4925 8.54845 6.27083C8.41628 6.04917 8.2394 5.87222 8.01781 5.74C7.79622 5.60778 7.55325 5.54167 7.2889 5.54167C7.02455 5.54167 6.78159 5.60778 6.56 5.74C6.33841 5.87222 6.16153 6.04917 6.02935 6.27083C5.89718 6.4925 5.83109 6.73556 5.83109 7C5.83109 7.26444 5.89718 7.5075 6.02935 7.72917C6.16153 7.95083 6.33841 8.12778 6.56 8.26C6.78159 8.39222 7.02455 8.45833 7.2889 8.45833Z"
                                fill="#6B7280"
                              />
                            </svg>
                            {post.viewCount ?? 0}
                          </span>
                          <span className="flex items-center gap-1">
                            {/* ThumbsUp icon */}
                            {post.isLike ? (
                              <BiSolidLike className="text-primary" />
                            ) : (
                              <BiLike />
                            )}
                            {post.likeCount ?? 0}
                          </span>
                          <span className="flex items-center gap-1">
                            {/* MessageSquare icon */}
                            <span className="flex items-center gap-1">
                              {/* ThumbsUp icon */}
                              <svg
                                width="16"
                                height="20"
                                viewBox="0 0 16 20"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <path
                                  d="M5.04312 13.64L2.44238 15.6816V4.88997C2.44238 4.73442 2.49875 4.59831 2.61149 4.48164C2.72423 4.36497 2.86223 4.30664 3.02551 4.30664H13.5218C13.685 4.30664 13.823 4.36497 13.9358 4.48164C14.0485 4.59831 14.1049 4.73442 14.1049 4.88997V13.0566C14.1049 13.22 14.0485 13.36 13.9358 13.4766C13.823 13.5933 13.685 13.6478 13.5218 13.64H5.04312ZM4.63493 12.4733H12.9386V5.47331H3.60863V13.29L4.63493 12.4733ZM7.69051 8.38997H8.85676V9.55664H7.69051V8.38997ZM5.35801 8.38997H6.52426V9.55664H5.35801V8.38997ZM10.023 8.38997H11.1893V9.55664H10.023V8.38997Z"
                                  fill="#6B7280"
                                />
                              </svg>
                              {post.commentCount ?? 0}
                            </span>
                          </span>
                        </div>
                      </div>
                    </Link>
                  ))}
                </div>
                {/* Pagination */}
                {pagination?.totalElements > 0 && (
                  <div className="mt-8 flex justify-center gap-2">
                    {renderPaginationButtons()}
                  </div>
                )}
              </div>
            )}
            {activeTab === "about" && (
              <EditUserInfo
                isOpen={true}
                onClose={() => setIsEditUserInfoModalOpen(false)}
                userInfo={userInfo}
                onSuccess={handleEditUserInfoSuccess}
              />
            )}
            {activeTab === "change-password" && (
              <div>
                <ChangePasswordModal
                  isOpen={true}
                  onClose={() => setIsChangePasswordModalOpen(false)}
                />
              </div>
            )}
          </div>
          {/* Cột phải: Sidebar */}
        </div>
        {activeTab === "activities" && (
          <div className="flex max-w-[394px] flex-col gap-6">
            {/* Chuyên môn (tags nhiều màu) */}
            <div
              className="mb-6 flex min-h-[192px] flex-col rounded-[8px] bg-white p-6"
              style={{
                boxShadow:
                  "0px 1px 3px 0px #0000001A, 0px 1px 2px -1px #0000001A"
              }}
            >
              <div className="mb-3 font-semibold text-gray-700">Chủ đề</div>
              {userInfo?.topicUserList?.length === 0 && (
                <div className="flex flex-1 items-center justify-center">
                  <div className="text-gray-700 opacity-50">Chưa có chủ đề</div>
                </div>
              )}
              <div className="flex flex-wrap gap-2">
                {userInfo?.topicUserList?.map(
                  (item: {
                    topicId: string;
                    backgroundColor: string;
                    textColor: string;
                    topicName:
                      | string
                      | number
                      | bigint
                      | boolean
                      | React.ReactElement<
                          any,
                          string | React.JSXElementConstructor<any>
                        >
                      | Iterable<React.ReactNode>
                      | React.ReactPortal
                      | Promise<React.AwaitedReactNode>
                      | null
                      | undefined;
                  }) => (
                    <span
                      key={item?.topicId}
                      className={"rounded-full px-3 py-1 text-xs font-medium"}
                      style={{
                        color: item?.textColor || "blue",
                        backgroundColor: item?.backgroundColor || "#E0E7FF"
                      }}
                    >
                      {item.topicName}
                    </span>
                  )
                )}
              </div>
            </div>
            {/* Hoạt động gần đây (timeline, icon, link) */}
            <div
              className="rounded-[8px] bg-white p-6"
              style={{
                boxShadow:
                  "0px 1px 3px 0px #0000001A, 0px 1px 2px -1px #0000001A"
              }}
            >
              <div className="mb-3 font-semibold text-gray-700">
                Hoạt động gần đây
              </div>
              {/* Nếu không có hoạt động nào */}
              {userHistory.length === 0 && (
                <div className="flex flex-1 items-center justify-center">
                  <div className="text-gray-700 opacity-50">
                    Chưa có hoạt động nào
                  </div>
                </div>
              )}
              <div className="flex max-h-[324px] flex-col gap-4 overflow-auto">
                {userHistory.map((activity, idx) => {
                  // Xác định màu theo loại hoạt động
                  let color = "";
                  let bg = "";
                  let icon = null;
                  let border = "";
                  if (activity.historyUserForumsType === "CREATE_POST") {
                    color = "#5B4FFF";
                    bg = "bg-[#EEF0FF]";
                    border = "border-l-2 border-[#5B4FFF]";
                    icon = <FileText className="h-5 w-5 text-[#5B4FFF]" />;
                  } else if (
                    activity.historyUserForumsType === "CREATE_COMMENT"
                  ) {
                    color = "#3B82F6";
                    bg = "bg-[#E6F0FF]";
                    border = "border-l-2 border-[#3B82F6]";
                    icon = <MessageSquare className="h-5 w-5 text-[#3B82F6]" />;
                  } else if (activity.historyUserForumsType === "LIKE_POST") {
                    color = "#22C55E";
                    bg = "bg-[#E6F9ED]";
                    border = "border-l-2 border-[#22C55E]";
                    icon = <Heart className="h-5 w-5 text-[#22C55E]" />;
                  } else {
                    color = "#FACC15";
                    bg = "bg-[#FFF9E6]";
                    border = "border-l-2 border-[#FACC15]";
                    icon = <FileText className="h-5 w-5 text-[#FACC15]" />;
                  }
                  // Lấy ngày từ content hoặc trường ngày nếu có
                  let date = activity?.date;
                  const match = activity.content.match(/\d{2}\/\d{2}\/\d{4}/);
                  if (match) date = match[0];
                  return (
                    <div
                      key={activity.userHistoryUserForumId}
                      className={`flex items-start gap-3 bg-white pl-[18px] ${border}`}
                    >
                      {/* Icon */}
                      <div
                        className={`flex items-center justify-center rounded-full ${bg} mt-1`}
                        style={{ minWidth: 36, minHeight: 36 }}
                      >
                        {icon}
                      </div>
                      {/* Nội dung hoạt động */}
                      <div className="flex-1">
                        <div className="text-[15px] text-gray-700">
                          <span
                            dangerouslySetInnerHTML={{
                              __html: activity.content
                            }}
                          />
                        </div>
                        <div className="mt-1 text-xs font-medium text-gray-400">
                          {date}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        )}
      </section>
    </div>
  );
}
