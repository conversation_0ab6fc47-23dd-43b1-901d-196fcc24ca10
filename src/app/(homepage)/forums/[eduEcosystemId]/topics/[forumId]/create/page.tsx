"use client";

import ForumBreadcrumb from "@/components/forums/ForumBreadcrumb";
import PostForm from "@/components/forums/PostForm";
import { useAuth } from "@/context/AuthContext";
import useUploadFile from "@/hook/useUploadFile";
import forumsServices from "@/services/forums/forumsServices";
import Link from "next/link";
import { useParams, useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import "react-quill-new/dist/quill.snow.css";
import { toast } from "react-toastify";


export default function CreatePostPage() {
  const router = useRouter();
  const { eduEcosystemId, forumId } = useParams();
  const { isAuthenticated, user } = useAuth();
  const { uploadForumFile } = useUploadFile();

  const [isLoading, setIsLoading] = useState(false);
  const [forum, setForum] = useState<any>(null);

  useEffect(() => {
    const fetchForum = async () => {
      try {
        const response = await forumsServices.getForumById(Number(forumId));
        if (response.data.data) {
          setForum(response.data.data);
        }
      } catch (error) {
        console.error("Error fetching forum:", error);
      }
    };
    fetchForum();
  }, [eduEcosystemId, forumId]);


  const handleSubmit = async (data: {
    title: string;
    content: string;
    selectedTopicId: string | null;
    imageFiles: File[];
    hashTags: string[];
  }) => {
    if (!isAuthenticated) {
      toast.info("Vui lòng đăng nhập để thực hiện chức năng này!");
      router.push("/login");
      return;
    }

    if (!data.selectedTopicId) {
      toast.warning("Vui lòng chọn chủ đề!");
      return;
    }

    if (!data.title.trim()) {
      toast.warning("Vui lòng nhập tiêu đề bài viết!");
      return;
    }

    if (data.title.trim().length < 5) {
      toast.warning("Tiêu đề bài viết phải có ít nhất 5 ký tự!");
      return;
    }

    if (data.title.trim().length > 100) {
      toast.warning("Tiêu đề bài viết không được vượt quá 100 ký tự!");
      return;
    }

    if (!data.content.trim()) {
      toast.warning("Vui lòng nhập nội dung bài viết!");
      return;
    }

    try {
      setIsLoading(true);

      // Upload tất cả các ảnh
      const fileIds: string[] = [];
      for (const file of data.imageFiles) {
        const response = await uploadForumFile(file);
        if (response?.data) {
          fileIds.push(response.data);
        }
      }

      // Tạo bài viết mới
      await forumsServices.createPost({
        content: data.content,
        title: data.title,
        topicId: Number(data.selectedTopicId),
        forumId: Number(forumId),
        eduEcosystemId: forum?.eduEcosystemId,
        hashTags: data.hashTags?.length > 0 ? data.hashTags : null,
        files: fileIds?.length > 0 ? fileIds : null
      });

      toast.success("Bài viết của bạn đã được tạo thành công!");
      router.push(`/forums/${eduEcosystemId}/topics/${forumId}`);
    } catch (error) {
      console.error("Lỗi khi tạo bài viết:", error);
      toast.error("Không thể tạo bài viết. Vui lòng thử lại sau!");
    } finally {
      setIsLoading(false);
    }
  };

  if (!isAuthenticated) {
    return (
      <div className="container mx-auto py-20 text-center">
        <h2 className="text-2xl font-bold text-gray-700">
          Vui lòng đăng nhập để tạo bài viết
        </h2>
        <p className="mt-4 text-gray-600">
          Bạn cần đăng nhập để có thể tạo bài viết mới.
        </p>
        <Link
          href="/login"
          className="mt-8 inline-block rounded-lg bg-indigo-600 px-6 py-2.5 text-white transition-colors hover:bg-indigo-700"
        >
          Đăng nhập ngay
        </Link>
      </div>
    );
  }

  return (
    <div className="mb-8">
      {/* Breadcrumb */}
      <ForumBreadcrumb
        items={[
          {
            type: "link",
            label: "Trang chủ",
            href: "/"
          },
          {
            type: "select",
            label: "Chọn cấp",
            value: eduEcosystemId?.toString() || "",
            href: "/forums/:value",
            options: [
              { key: 1, value: "1", label: "Mầm non" },
              { key: 2, value: "2", label: "Tiểu học" },
              { key: 3, value: "3", label: "Trung học cơ sở" },
              { key: 4, value: "4", label: "Trung học phổ thông" }
            ]
          },
          {
            type: "link",
            label: forum?.name || "Chủ đề",
            href: `/forums/${eduEcosystemId}/topics/${forumId}`
          },
          {
            type: "text",
            label: "Tạo bài viết"
          }
        ]}
      />

      <PostForm
        isLoading={isLoading}
        handleSubmit={handleSubmit}
      />
    </div>
  );
}
