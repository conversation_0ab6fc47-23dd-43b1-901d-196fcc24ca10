"use client";

import Image from "next/image";
import Link from "next/link";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { toast } from "react-toastify";

// Import Swiper styles
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";

import ForumBreadcrumb from "@/components/forums/ForumBreadcrumb";
import Loading from "@/components/ui/Loading";
import { useAuth } from "@/context/AuthContext";
import useUploadFile from "@/hook/useUploadFile";
import forumsServices from "@/services/forums/forumsServices";
import { Forum, Post, Topic } from "@/services/forums/types";
import {
  RiAddLine,
  RiArrowDownSLine,
  RiArrowLeftSLine,
  RiArrowRightSLine,
  RiCloseLine,
  RiEyeLine,
  RiFileList3Line,
  RiFireLine,
  RiHeartFill,
  RiLoginBox<PERSON>ine,
  Ri<PERSON>essage2<PERSON>ine,
  RiSearchLine,
  RiTimeL<PERSON>,
  Ri<PERSON>serL<PERSON>,
  RiVerifiedBadgeFill
} from "react-icons/ri";

export default function TopicDetailPage() {
  const { forumId, topicId, eduEcosystemId } = useParams();
  const [forum, setForum] = useState<Forum | null>(null);
  const [topic, setTopic] = useState<Topic | null>(null);
  const [topics, setTopics] = useState<Topic[]>([]);
  const [posts, setPosts] = useState<Post[]>([]);
  const [totalPosts, setTotalPosts] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  const [showReplyModal, setShowReplyModal] = useState(false);
  const [replyContent, setReplyContent] = useState("");
  const [replyTitle, setReplyTitle] = useState("");
  const [hashTags, setHashTags] = useState<string[]>([]);
  const [newTag, setNewTag] = useState("");
  const [loadingLikeIds, setLoadingLikeIds] = useState<number[]>([]);
  const [loadingDeleteIds, setLoadingDeleteIds] = useState<number[]>([]);
  const [topicImages, setTopicImages] = useState<Record<string, string>>({});
  const [forumImage, setForumImage] = useState<string>("");
  const [lastUpdated, setLastUpdated] = useState<string>("");
  const { isAuthenticated, user } = useAuth();
  const { viewFile } = useUploadFile();
  const router = useRouter();
  const [sort, setSort] = useState<string>("NEW");
  const [searchValue, setSearchValue] = useState<string>("");

  const [topicIdState, setTopicIdState] = useState<number | null>(null);

  // Hàm tải ảnh cho topic
  const loadTopicImage = async (fileId: string) => {
    try {
      const imageData = await viewFile(fileId, "forums");
      setTopicImages((prev) => ({
        ...prev,
        [fileId]: imageData as string
      }));
    } catch (error) {
      console.error("Error loading topic image:", error);
    }
  };

  const loadPostsImage = async (fileId: string, type: "topic" | "forum") => {
    try {
      const imageData = await viewFile(fileId, "forums");
      return imageData as string;
    } catch (error) {
      console.error("Error loading post image:", error);
    }
  };

  const fetchData = async () => {
    try {
      setIsLoading(true);

      // // Lấy thông tin về forum
      const forumResponse = await forumsServices.getForumById(Number(forumId));
      if (forumResponse?.data?.data) {
        setForum(forumResponse?.data?.data);
      }

      // Lấy danh sách topics của forum
      const topicsResponse = await forumsServices.getTopicsByForumId(
        Number(forumId)
      );
      if (topicsResponse.data.data?.content) {
        setTopics(topicsResponse.data.data.content);
        // // Tải ảnh cho mỗi topic
        // topicsResponse.data.data.content.forEach((topicItem: Topic) => {
        //   if (topicItem.fileId) {
        //     loadTopicImage(topicItem.fileId);
        //   }
        // });
      }

      // Lấy danh sách posts trong topic
      const postsResponse = await forumsServices.getPostsByTopicId({
        topicId: topicIdState ? Number(topicIdState) : null,
        page: currentPage,
        size: 10,
        ecosystemId: eduEcosystemId ? Number(eduEcosystemId) : null,
        forumId: Number(forumId),
        sort: sort,
        textSearch: searchValue
      });
      if (postsResponse.data.data?.content) {
        // Tải ảnh cho mỗi bài post
        for (const post of postsResponse.data.data.content) {
          // if (post.topicThumbNail) {
          //   post.topicThumbNail = await loadPostsImage(
          //     post.topicThumbNail,
          //     "topic"
          //   );
          // }
          if (post.userCreatedThumbNail) {
            post.userCreatedThumbNail = await loadPostsImage(
              post.userCreatedThumbNail,
              "forum"
            );
          }
        }
        setPosts(postsResponse.data.data.content);
        setTotalPages(postsResponse.data.data.totalPages);
        setTotalPosts(postsResponse.data.data.totalElements);

        // Lấy ngày cập nhật mới nhất
        // if (postsResponse.data.data.content.length > 0) {
        //   const mostRecentPost = postsResponse.data.data.content
        //     .filter((post: Post) => post.createdAt)
        //     .sort((a: Post, b: Post) => {
        //       if (!a.createdAt) return 1;
        //       if (!b.createdAt) return -1;
        //       return b.createdAt.localeCompare(a.createdAt);
        //     })[0];

        //   if (mostRecentPost?.createdAt) {
        //     const dateStr = mostRecentPost.createdAt;
        //     // Format: "20250418164323" -> "2025-04-18T16:43:23"
        //     if (dateStr.length >= 14) {
        //       const year = dateStr.substring(0, 4);
        //       const month = dateStr.substring(4, 6);
        //       const day = dateStr.substring(6, 8);
        //       const hour = dateStr.substring(8, 10);
        //       const minute = dateStr.substring(10, 12);
        //       const second = dateStr.substring(12, 14);
        //
        //       const formattedDate = `${year}-${month}-${day}`;
        //       const latestDate = new Date(formattedDate);
        //       setLastUpdated(latestDate.toLocaleDateString("vi-VN"));
        //     }
        //   }
        // }
      }
      if (forumResponse?.data?.data?.fileId) {
        const thumbnail = (await viewFile(
          forumResponse?.data?.data?.fileId,
          "forums"
        )) as string;
        setForumImage(thumbnail || "");
      }
    } catch (error) {
      console.error("Error fetching topic data:", error);
      toast.error("Không thể tải thông tin chủ đề. Vui lòng thử lại sau!");
    } finally {
      setIsLoading(false);
    }
  };
  useEffect(() => {
    fetchData();
  }, [topicIdState, currentPage, sort]);

  // Hàm xử lý like/unlike bài viết
  const handleLikePost = async (postId: number, isLiked: boolean) => {
    if (!isAuthenticated) {
      toast.info("Vui lòng đăng nhập để thực hiện chức năng này!");
      router.push("/login");
      return;
    }

    try {
      // Thêm postId vào danh sách đang loading
      setLoadingLikeIds((prev) => [...prev, postId]);

      if (isLiked) {
        await forumsServices.unlikePost(postId);
      } else {
        await forumsServices.likePost(postId);
      }

      // Cập nhật lại danh sách posts sau khi like/unlike
      const postsResponse = await forumsServices.getPostsByTopicId({
        topicId: topicIdState ? Number(topicIdState) : null,
        page: currentPage,
        size: 10,
        ecosystemId: eduEcosystemId ? Number(eduEcosystemId) : null,
        forumId: Number(forumId),
        sort: sort
      });
      if (postsResponse.data.data?.content) {
        const formatData = postsResponse.data.data.content.map(
          (post: Post, index: number) => ({
            ...post,
            topicThumbNail: posts[index].topicThumbNail,
            forumThumbNail: posts[index].forumThumbNail,
            userCreatedThumbNail: posts[index].userCreatedThumbNail
          })
        );
        setPosts(formatData);
        setTotalPosts(postsResponse.data.data.totalElements);
      }
    } catch (error) {
      console.error("Error liking/unliking post:", error);
    } finally {
      // Xóa postId khỏi danh sách đang loading
      setLoadingLikeIds((prev) => prev.filter((id) => id !== postId));
    }
  };

  // Hàm xử lý gửi bài viết mới
  const handleSubmitReply = async () => {
    if (!isAuthenticated) {
      toast.info("Vui lòng đăng nhập để thực hiện chức năng này!");
      router.push("/login");
      return;
    }

    if (!replyTitle.trim()) {
      toast.warning("Vui lòng nhập tiêu đề bài viết!");
      return;
    }

    if (replyTitle.trim().length < 5) {
      toast.warning("Tiêu đề bài viết phải có ít nhất 5 ký tự!");
      return;
    }

    if (replyTitle.trim().length > 100) {
      toast.warning("Tiêu đề bài viết không được vượt quá 100 ký tự!");
      return;
    }

    if (!replyContent.trim()) {
      toast.warning("Vui lòng nhập nội dung bài viết!");
      return;
    }

    try {
      setIsLoading(true);

      await forumsServices.createPost({
        content: replyContent,
        title: replyTitle || `Phản hồi: ${topic?.title || ""}`,
        topicId: Number(topicIdState || topicId),
        forumId: Number(forumId),
        eduEcosystemId: forum?.eduEcosystemId || 1,
        hashTags: hashTags
      });

      // Xóa nội dung reply sau khi gửi thành công
      setReplyContent("");
      setReplyTitle("");
      setHashTags([]);

      // Tải lại danh sách bài viết
      const postsResponse = await forumsServices.getPostsByTopicId({
        topicId: topicId ? Number(topicId) : null,
        page: currentPage,
        size: 10,
        ecosystemId: eduEcosystemId ? Number(eduEcosystemId) : null,
        forumId: Number(forumId),
        sort: sort
      });
      if (postsResponse.data.data?.content) {
        const formatData = postsResponse.data.data.content.map(
          (post: Post, index: number) => ({
            ...post,
            topicThumbNail: posts[index].topicThumbNail,
            forumThumbNail: posts[index].forumThumbNail,
            userCreatedThumbNail: posts[index].userCreatedThumbNail
          })
        );
        setPosts(formatData);
        setTotalPages(postsResponse.data.data.totalPages);
      }
      setShowReplyModal(false);
      toast.success("Bài viết của bạn đã được gửi thành công!");
    } catch (error) {
      console.error("Lỗi khi gửi bài viết:", error);
      toast.error("Không thể gửi bài viết. Vui lòng thử lại sau!");
    } finally {
      setIsLoading(false);
    }
  };

  // Định dạng thời gian tương đối
  const getRelativeTime = (dateString?: string) => {
    if (!dateString) return "Chưa có bài viết";

    // Format: "20250418164323" -> "2025-04-18T16:43:23"
    if (dateString.length >= 14) {
      const year = dateString.substring(0, 4);
      const month = dateString.substring(4, 6);
      const day = dateString.substring(6, 8);
      const hour = dateString.substring(8, 10);
      const minute = dateString.substring(10, 12);
      const second = dateString.substring(12, 14);

      const formattedDate = `${year}-${month}-${day}T${hour}:${minute}:${second}`;
      const date = new Date(formattedDate);

      const now = new Date();
      const diffMs = now.getTime() - date.getTime();
      const diffMins = Math.floor(diffMs / 60000);
      const diffHours = Math.floor(diffMins / 60);
      const diffDays = Math.floor(diffHours / 24);
      const diffWeeks = Math.floor(diffDays / 7);

      if (diffMins < 60) return `${diffMins} phút trước`;
      if (diffHours < 24) return `${diffHours} giờ trước`;
      if (diffDays < 7) return `${diffDays} ngày trước`;
      return `${diffWeeks} tuần trước`;
    }

    return "Ngày không hợp lệ";
  };

  // Hàm xử lý xóa bài viết
  const handleDeletePost = async (postId: number, post: Post) => {
    if (!isAuthenticated) {
      toast.info("Vui lòng đăng nhập để thực hiện chức năng này!");
      router.push("/login");
      return;
    }

    // Kiểm tra quyền admin hoặc người tạo bài viết
    if (
      Number(user?.forumInfo?.userForumId) !== post.userCreatedId &&
      user?.forumInfo?.userType !== "ROLE_ADMIN"
    ) {
      toast.error("Bạn không có quyền xóa bài viết này!");
      return;
    }

    try {
      setLoadingDeleteIds((prev) => [...prev, postId]);
      await forumsServices.deleteForum(postId);

      // Cập nhật lại danh sách bài viết
      const postsResponse = await forumsServices.getPostsByTopicId({
        topicId: topicId ? Number(topicId) : null,
        page: currentPage,
        size: 10,
        ecosystemId: eduEcosystemId ? Number(eduEcosystemId) : null,
        forumId: Number(forumId),
        sort: sort
      });
      if (postsResponse.data.data?.content) {
        const formatData = postsResponse.data.data.content.map(
          (post: Post, index: number) => ({
            ...post,
            topicThumbNail: posts[index].topicThumbNail,
            forumThumbNail: posts[index].forumThumbNail,
            userCreatedThumbNail: posts[index].userCreatedThumbNail
          })
        );
        setPosts(formatData);
      }
      toast.success("Đã xóa bài viết thành công!");
    } catch (error) {
      console.error("Lỗi khi xóa bài viết:", error);
      toast.error("Không thể xóa bài viết. Vui lòng thử lại sau!");
    } finally {
      setLoadingDeleteIds((prev) => prev.filter((id) => id !== postId));
    }
  };

  const handleTopicClick = async (clickedTopicId: number) => {
    try {
      setIsLoading(true);

      setTopicIdState(clickedTopicId);
      setCurrentPage(0);

      // Cập nhật URL mà không reload trang
      router.replace(`/forums/${eduEcosystemId}/topics/${forumId}`, {
        scroll: false
      });
    } catch (error) {
      console.error("Error handling topic click:", error);
      toast.error("Không thể chuyển chủ đề. Vui lòng thử lại sau!");
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto flex h-screen items-center justify-center py-20">
        <Loading
          color="primary"
          size="lg"
          variant="spinner"
          text="Đang tải chủ đề..."
        />
      </div>
    );
  }

  if (!forum) {
    return (
      <div className="container mx-auto py-20 text-center">
        <h2 className="text-2xl font-bold text-gray-700">
          Không tìm thấy chủ đề
        </h2>
        <p className="mt-4 text-gray-600">
          Chủ đề bạn đang tìm kiếm không tồn tại hoặc đã bị xóa.
        </p>
        <Link
          href={`/forums/${eduEcosystemId}`}
          className="mt-8 inline-block rounded-md bg-indigo-600 px-6 py-2.5 text-white transition-colors hover:bg-indigo-700"
        >
          Quay lại diễn đàn
        </Link>
      </div>
    );
  }

  return (
    <section className="mb-6">
      <ForumBreadcrumb
        items={[
          {
            type: "link",
            label: "Trang chủ",
            href: "/"
          },
          {
            type: "select",
            label: "Chọn cấp",
            value: eduEcosystemId?.toString() || "",
            href: "/forums/:value",
            options: [
              { key: 1, value: "1", label: "Mầm non" },
              { key: 2, value: "2", label: "Tiểu học" },
              { key: 3, value: "3", label: "Trung học cơ sở" },
              { key: 4, value: "4", label: "Trung học phổ thông" }
            ]
          },
          {
            type: "text",
            label: forum?.name || "Chủ đề"
          }
        ]}
        lastUpdated={lastUpdated || "Đang cập nhật..."}
      />

      {/* Forum Header Banner */}
      <div className="container mx-auto mt-4 px-3 sm:mt-6 sm:px-4">
        <div className="mb-4 rounded-[12px] bg-white p-4 shadow sm:mb-6 sm:p-6">
          <div className="flex flex-col items-center justify-between sm:flex-row">
            <div className="mb-4 flex items-center md:mb-0">
              <div className="mr-3 flex h-12 w-12 items-center justify-center rounded-full bg-[#DBEAFE] text-primary sm:mr-4 sm:h-16 sm:w-16">
                <Image
                  src={forumImage || ""}
                  alt="user"
                  width={48}
                  height={48}
                  className="h-8 w-8 rounded-full object-cover sm:h-10 sm:w-10 md:h-12 md:w-12"
                />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-800 sm:text-2xl">
                  {forum?.name}
                </h1>
                <p className="mt-1 text-sm text-gray-600 sm:text-base">
                  {forum?.description ||
                    "Nơi chia sẻ và thảo luận về các chủ đề"}
                </p>
              </div>
            </div>
            <div className="flex flex-col items-center md:items-end">
              <div className="mb-3 flex space-x-6">
                <div className="text-center">
                  <div className="text-lg font-semibold text-gray-800 sm:text-xl">
                    {totalPosts}
                  </div>
                  <div className="text-xs text-[#6B7280] sm:text-sm">
                    Bài viết
                  </div>
                </div>
              </div>
              {isAuthenticated && (
                <Link
                  href={`/forums/${eduEcosystemId}/topics/${forumId}/create`}
                  className="flex items-center whitespace-nowrap rounded-[8px] bg-primary px-3 py-1.5 text-sm text-white hover:bg-primary/90 sm:px-4 sm:py-2 sm:text-base"
                >
                  <div className="mr-1.5 flex h-4 w-4 items-center justify-center sm:mr-2 sm:h-5 sm:w-5">
                    <RiAddLine />
                  </div>
                  Tạo bài viết mới
                </Link>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Filter and Search */}
      <div className="container mx-auto px-3 sm:px-4">
        <div className="mb-4 rounded-lg bg-white p-3 shadow sm:mb-6 sm:p-4">
          <div className="flex flex-col items-center justify-between gap-3 sm:gap-4 md:flex-row">
            <div className="flex w-full flex-col gap-3 sm:flex-row sm:flex-wrap sm:gap-4 md:w-auto">
              <div className="relative w-full sm:w-auto sm:flex-1 md:w-48">
                <select
                  className="w-full appearance-none rounded-lg border border-gray-200 bg-gray-50 px-2 py-1.5 pr-8 text-xs focus:border-transparent focus:outline-none focus:ring-2 focus:ring-primary sm:px-3 sm:py-2 sm:text-sm"
                  value={topicIdState?.toString() || ""}
                  onChange={(e) => {
                    const value = e.target.value;
                    if (value) {
                      handleTopicClick(Number(value));
                    } else {
                      setTopicIdState(null);
                    }
                  }}
                >
                  <option value="">Tất cả chủ đề con</option>
                  {topics.map((topic) => (
                    <option key={topic.topicId} value={topic.topicId}>
                      {topic.topicName}
                    </option>
                  ))}
                </select>
                <div className="pointer-events-none absolute right-3 top-1/2 flex h-3 w-3 -translate-y-1/2 transform items-center justify-center sm:h-4 sm:w-4">
                  <RiArrowDownSLine />
                </div>
              </div>
              <div className="relative w-full md:w-48">
                <select
                  onChange={(e) => setSort(e.target.value)}
                  value={sort}
                  className="w-full appearance-none rounded-lg border border-gray-200 bg-gray-50 px-3 py-2 pr-8 text-sm focus:border-transparent focus:outline-none focus:ring-2 focus:ring-primary"
                >
                  <option value="NEW">Mới nhất</option>
                  <option value="COMMENT">Nhiều bình luận nhất</option>
                  <option value="VIEW">Nhiều lượt xem nhất</option>
                </select>
                <div className="pointer-events-none absolute right-3 top-1/2 flex h-3 w-3 -translate-y-1/2 transform items-center justify-center sm:h-4 sm:w-4">
                  <RiArrowDownSLine />
                </div>
              </div>
            </div>
            <div className="relative mt-3 w-full sm:mt-0 sm:flex-1 md:w-64">
              <input
                type="text"
                placeholder="Tìm kiếm bài viết"
                className="w-full rounded-lg border border-gray-200 bg-gray-50 px-2 py-1.5 pl-8 text-xs focus:border-transparent focus:outline-none focus:ring-2 focus:ring-primary sm:px-3 sm:py-2 sm:pl-9 sm:text-sm"
                value={searchValue}
                onChange={(e) => setSearchValue(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    fetchData();
                  }
                }}
              />
              <div className="absolute left-2 top-1/2 flex h-3 w-3 -translate-y-1/2 transform items-center justify-center sm:left-3 sm:h-4 sm:w-4 cursor-pointer" onClick={() => fetchData()}>
                <RiSearchLine />
              </div>
            </div>
          </div>
        </div>

        <div className="flex flex-col gap-4 sm:gap-6 lg:flex-row lg:gap-8">
          {/* Main Content */}
          <div className="w-full">
            {/* Posts */}
            <div className="mb-4 space-y-3 sm:mb-6 sm:space-y-4">
              {posts.length === 0 ? (
                <div className="rounded-lg bg-white p-4 text-center shadow-md sm:p-6 md:p-8">
                  <div className="mx-auto mb-3 flex h-12 w-12 items-center justify-center rounded-full bg-gray-100 sm:mb-4 sm:h-16 sm:w-16">
                    <RiFileList3Line
                      size={20}
                      className="text-gray-400 sm:hidden"
                    />
                    <RiFileList3Line
                      size={24}
                      className="hidden text-gray-400 sm:block"
                    />
                  </div>
                  <p className="mb-4 text-gray-600">
                    Chưa có bài viết nào cho chủ đề này.
                  </p>
                  {isAuthenticated ? (
                    // <Link
                    //   href="#"
                    //   onClick={() => setShowReplyModal(true)}
                    //   className="inline-flex items-center rounded-lg bg-primary px-4 py-2 text-white transition-colors hover:bg-primary/90"
                    // >
                    //   <RiAddLine className="mr-2" />
                    //   Thêm bài viết đầu tiên
                    // </Link>
                      <div></div>
                  ) : (
                    <Link
                      href="/login"
                      className="inline-flex items-center rounded-lg bg-primary px-4 py-2 text-white transition-colors hover:bg-primary/90"
                    >
                      <RiLoginBoxLine className="mr-2" />
                      Đăng nhập để thêm bài viết
                    </Link>
                  )}
                </div>
              ) : (
                posts.map((post) => (
                  <div
                    key={post.postId}
                    className="rounded-lg border border-gray-200 bg-white p-3 shadow transition-all duration-300 hover:shadow-md sm:p-4"
                  >
                    <div className="flex items-start gap-2 sm:gap-4">
                      <div className="flex-shrink-0">
                        {post?.userCreatedThumbNail ? (
                          <Image
                            src={post?.userCreatedThumbNail || ""}
                            alt="user"
                            width={48}
                            height={48}
                            className="h-8 w-8 rounded-full object-cover sm:h-10 sm:w-10 md:h-12 md:w-12"
                          />
                        ) : (
                          <div className="flex h-8 w-8 items-center justify-center rounded-full bg-gradient-to-br from-primary/10 to-primary/20 text-xs font-bold text-primary sm:h-10 sm:w-10 sm:text-sm md:h-12 md:w-12 md:text-base">
                            {post?.userCreatedFullName?.charAt(0) || "A"}
                          </div>
                        )}
                      </div>
                      <div className="flex-grow">
                        <div className="mb-1 flex flex-wrap items-center gap-1 sm:gap-2">
                          <span
                            className="rounded-full px-1.5 py-0.5 text-[10px] font-normal sm:px-2 sm:text-xs"
                            style={{
                              backgroundColor: post?.backgroundColor,
                              color: post?.textColor
                            }}
                          >
                            {post.topicName}
                          </span>
                          {post.viewCount > 1000 && (
                            <span className="rounded-full bg-green-50 px-1.5 py-0.5 text-[10px] font-normal text-green-700 sm:px-2 sm:text-xs">
                              <RiFireLine className="mr-1" />
                              Hot
                            </span>
                          )}
                          {/* {post.hashTags && post.hashTags.length > 0 && (
                            <div className="mt-2 flex flex-wrap gap-2">
                              {post.hashTags.map((tag, idx) => (
                                <Link
                                  key={idx}
                                  href={`/forums/${eduEcosystemId}/search?keyword=${encodeURIComponent(tag)}`}
                                  className="rounded-full bg-gray-100 px-3 py-1 text-xs text-gray-800 hover:bg-gray-200"
                                >
                                  #{tag}
                                </Link>
                              ))}
                            </div>
                          )} */}
                        </div>
                        <h3 className="mb-1 text-base font-medium text-gray-800 sm:text-lg">
                          <Link
                            href={`/forums/${eduEcosystemId}/topics/${forumId}/posts/${post.postId}`}
                            className="transition-colors hover:text-primary"
                          >
                            {post.title}
                          </Link>
                        </h3>
                        <p
                          className="mb-2 line-clamp-2 text-xs text-gray-600 sm:mb-3 sm:text-sm"
                          dangerouslySetInnerHTML={{ __html: post.content }}
                        />
                        <div className="flex flex-wrap items-center justify-between gap-2 sm:gap-4">
                          <div className="flex items-center text-xs text-[#6B7280] sm:text-sm">
                            <div className="mr-2 flex items-center">
                              <RiUserLine className="mr-1" />
                              <span>{post.userCreatedFullName}</span>
                            </div>
                            {post.orgName && (
                                <div className="mr-2 flex items-center gap-0.5 bg-primary/5 text-primary px-1.5 py-0.5 rounded-full"> <i className="ri-verified-badge-fill text-xs"></i> <span className="text-xs font-normal">{post.orgName}</span> </div>
                            )}
                            <div className="flex items-center">
                              <RiTimeLine className="mr-1" />
                              <span>{getRelativeTime(post.createdAt)}</span>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2 text-xs text-[#6B7280] sm:space-x-4 sm:text-sm">
                            <div className="flex items-center">
                              <RiEyeLine className="mr-0.5" />
                              <span>{post.viewCount}</span>
                            </div>
                            <div className="flex items-center">
                              <RiMessage2Line className="mr-0.5" />
                              <span>{post.commentCount || 0}</span>
                            </div>
                            <button
                              onClick={() =>
                                handleLikePost(
                                  post.postId,
                                  post.isLike ?? false
                                )
                              }
                              disabled={
                                !isAuthenticated ||
                                loadingLikeIds.includes(post.postId)
                              }
                              className={`flex items-center transition-colors ${
                                isAuthenticated
                                  ? post.isLike
                                    ? "text-primary"
                                    : "text-[#6B7280] hover:text-primary"
                                  : "text-gray-400"
                              }`}
                            >
                              <RiHeartFill
                                className={`mr-1.5 ${post.isLike ? "text-primary" : ""}`}
                              />
                              <span>{post.likeCount || 0}</span>
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="mt-4 flex flex-col items-center justify-between gap-3 sm:mt-6 sm:flex-row">
                <div className="text-sm text-[#6B7280]">
                  Hiển thị {currentPage * 10 + 1}-
                  {Math.min((currentPage + 1) * 10, totalPosts)} trong tổng số{" "}
                  {totalPosts} bài viết
                </div>
                <div className="flex max-w-full items-center space-x-1 overflow-x-auto pb-1 sm:max-w-none sm:pb-0">
                  <button
                    disabled={currentPage === 0}
                    onClick={() => setCurrentPage(currentPage - 1)}
                    className="flex h-8 w-8 items-center justify-center rounded-lg border border-gray-200 text-[#6B7280] hover:bg-gray-50 disabled:opacity-50"
                  >
                    <RiArrowLeftSLine />
                  </button>
                  {Array.from({ length: totalPages }, (_, i) => (
                    <button
                      key={i}
                      onClick={() => setCurrentPage(i)}
                      className={`flex h-7 w-7 flex-shrink-0 items-center justify-center rounded-lg border border-gray-200 text-xs sm:h-8 sm:w-8 sm:text-sm ${
                        currentPage === i
                          ? "bg-primary text-white"
                          : "text-gray-700 hover:bg-gray-50"
                      }`}
                    >
                      {i + 1}
                    </button>
                  ))}
                  <button
                    disabled={currentPage === totalPages - 1}
                    onClick={() => setCurrentPage(currentPage + 1)}
                    className="flex h-8 w-8 items-center justify-center rounded-lg border border-gray-200 text-[#6B7280] hover:bg-gray-50 disabled:opacity-50"
                  >
                    <RiArrowRightSLine />
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Fixed Create Post Button (Mobile) */}
      <div className="fixed bottom-4 right-4 sm:bottom-6 sm:right-6 md:hidden">
        {isAuthenticated && (
          <Link
            href={`/forums/${eduEcosystemId}/topics/${forumId}/create`}
            className="flex items-center justify-center whitespace-nowrap rounded-full bg-primary p-2.5 text-white shadow-lg hover:bg-primary/90 sm:p-3"
          >
            <div className="flex h-6 w-6 items-center justify-center">
              <RiAddLine />
            </div>
          </Link>
        )}
      </div>

      {/* Reply Form Modal */}
      {showReplyModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4">
          <div className="w-full max-w-sm rounded-lg bg-white shadow-xl sm:max-w-md md:max-w-lg lg:max-w-2xl">
            <div className="p-4 sm:p-6">
              <div className="mb-4 flex items-center justify-between">
                <h3 className="text-base font-semibold text-gray-900 sm:text-lg">
                  Thêm bài viết mới
                </h3>
                <button
                  onClick={() => setShowReplyModal(false)}
                  className="text-[#6B7280] hover:text-gray-700"
                >
                  <RiCloseLine />
                </button>
              </div>

              {!isAuthenticated ? (
                <div className="py-4 text-center sm:py-6">
                  <p className="mb-4 text-gray-600">
                    Bạn cần đăng nhập để gửi bài viết
                  </p>
                  <Link
                    href="/login"
                    className="inline-block rounded-md bg-primary px-4 py-2 text-sm text-white transition-colors hover:bg-primary/90 sm:px-6 sm:py-2.5 sm:text-base"
                  >
                    Đăng nhập ngay
                  </Link>
                </div>
              ) : (
                <div className="flex flex-col gap-3 sm:gap-4">
                  <div>
                    <label
                      htmlFor="reply-title"
                      className="mb-1 block text-sm font-medium text-gray-700"
                    >
                      Tiêu đề <span className="text-red-500">*</span>
                    </label>
                    <input
                      id="reply-title"
                      type="text"
                      className="w-full rounded-md border border-gray-300 p-2 text-sm sm:p-3 sm:text-base"
                      placeholder="Tiêu đề bài viết của bạn..."
                      value={replyTitle}
                      onChange={(e) => setReplyTitle(e.target.value)}
                      maxLength={100}
                      disabled={!isAuthenticated}
                    />
                    <p className="mt-1 text-xs text-[#6B7280] sm:text-sm">
                      {replyTitle.length}/100 ký tự (tối thiểu 5 ký tự)
                    </p>
                  </div>
                  <div>
                    <label
                      htmlFor="reply-content"
                      className="mb-1 block text-sm font-medium text-gray-700"
                    >
                      Nội dung <span className="text-red-500">*</span>
                    </label>
                    <textarea
                      id="reply-content"
                      className="min-h-[120px] w-full rounded-md border border-gray-300 p-2 text-sm sm:min-h-[150px] sm:p-3 sm:text-base"
                      placeholder="Nhập nội dung bài viết của bạn..."
                      value={replyContent}
                      onChange={(e) => setReplyContent(e.target.value)}
                      disabled={!isAuthenticated}
                    />
                  </div>
                  <div className="flex justify-end gap-2 sm:gap-3">
                    <button
                      onClick={() => setShowReplyModal(false)}
                      className="rounded border border-gray-300 px-3 py-1.5 text-sm text-gray-600 transition-colors hover:bg-gray-50 sm:px-4 sm:py-2 sm:text-base"
                    >
                      Hủy
                    </button>
                    <button
                      className="rounded bg-primary px-3 py-1.5 text-sm text-white transition-colors hover:bg-primary/90 disabled:cursor-not-allowed disabled:bg-gray-400 sm:px-4 sm:py-2 sm:text-base"
                      disabled={
                        !isAuthenticated ||
                        !replyContent.trim() ||
                        !replyTitle.trim()
                      }
                      onClick={handleSubmitReply}
                    >
                      Gửi bài viết
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </section>
  );
}
