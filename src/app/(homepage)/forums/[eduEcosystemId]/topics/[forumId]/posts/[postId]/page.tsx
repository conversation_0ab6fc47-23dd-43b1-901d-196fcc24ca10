"use client";

import ForumBreadcrumb from "@/components/forums/ForumBreadcrumb";
import Loading from "@/components/ui/Loading";
import { useAuth } from "@/context/AuthContext";
import useUploadFile from "@/hook/useUploadFile";
import anonymousServices from "@/services/anonymousServices";
import forumsServices from "@/services/forums/forumsServices";
import { Comment, Forum, Post } from "@/services/forums/types";
import dynamic from "next/dynamic";
import Image from "next/image";
import Link from "next/link";
import { useParams, useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import "react-quill-new/dist/quill.snow.css";
import { toast } from "react-toastify";

// Import react-quill-new
const ReactQuill = dynamic(() => import("react-quill-new"), {
  ssr: false,
  loading: () => <Loading color="primary" size="sm" variant="spinner" />
});

export default function PostPage() {
  const { eduEcosystemId, postId, forumId } = useParams();
  const [post, setPost] = useState<Post | null>(null);
  const [forum, setForum] = useState<Forum | null>(null);
  const [comments, setComments] = useState<Comment[]>([]);
  const [commentContent, setCommentContent] = useState("");
  const [replyContent, setReplyContent] = useState("");
  const [commentPage, setCommentPage] = useState(0);
  const [commentTotalPages, setCommentTotalPages] = useState(1);
  const [loadingComments, setLoadingComments] = useState(false);
  const [author, setAuthor] = useState<any>(null);
  const [loadingLikeCommentIds, setLoadingLikeCommentIds] = useState<number[]>(
    []
  );
  const [replyToComment, setReplyToComment] = useState<number | null>(null);
  const [loadingReplies, setLoadingReplies] = useState<number[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isDeleting, setIsDeleting] = useState(false);
  const { isAuthenticated, user } = useAuth();
  const router = useRouter();
  const [loadingLikeIds, setLoadingLikeIds] = useState<number[]>([]);
  const [loadingDeleteCommentIds, setLoadingDeleteCommentIds] = useState<
    number[]
  >([]);
  const [isSubmittingComment, setIsSubmittingComment] = useState(false);
  const [currentPage, setCurrentPage] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  const [postFiles, setPostFiles] = useState<Record<string, string>>({});
  const [selectedFile, setSelectedFile] = useState<string | null>(null);
  const [editingCommentId, setEditingCommentId] = useState<number | null>(null);
  const [editingCommentContent, setEditingCommentContent] = useState("");
  const [loadingEditCommentIds, setLoadingEditCommentIds] = useState<number[]>(
    []
  );
  const { viewFile, downloadFile } = useUploadFile();
  const [expandedCommentIds, setExpandedCommentIds] = useState<number[]>([]);
  const [showReportModal, setShowReportModal] = useState(false);
  const [reportReason, setReportReason] = useState("");
  const [isReporting, setIsReporting] = useState(false);
  const [relatedPosts, setRelatedPosts] = useState<Post[]>([]);

  // Hàm tải bài viết liên quan
  const loadRelatedPosts = async (topicId: number, postId: number) => {
    try {
      // Lấy các bài viết cùng topic, ngoại trừ bài viết hiện tại
      const response = await forumsServices.getPostsByTopicId({
        topicId: topicId,
        forumId: Number(forumId),
        ecosystemId: Number(eduEcosystemId),
        page: 0,
        size: 4
      });

      if (response.data.data) {
        // Tải ảnh thumbnail cho các bài viết liên quan nếu có
        for (const relatedPost of response.data.data.content) {
          if (relatedPost.userCreatedThumbNail) {
            try {
              relatedPost.userCreatedThumbNail = await viewFile(
                relatedPost.userCreatedThumbNail,
                "forums"
              );
            } catch (error) {
              console.error("Error loading thumbnail:", error);
            }
          }
        }

        setRelatedPosts(response.data.data.content);
      }
    } catch (error) {
      console.error("Error loading related posts:", error);
    }
  };
  const loadAuthor = async () => {
    console.log("post?.userCreatedId", post?.userCreatedId);
    try {
      const response = await anonymousServices.getAuthor(
        Number(post?.userCreatedId)
      );
      setAuthor(response?.data?.data);
    } catch (error) {
      console.error("Error loading author:", error);
    }
  };

  useEffect(() => {
    const loadPost = async (postId: number, page = 0) => {
      try {
        setIsLoading(true);
        const response = await forumsServices.getPostById(Number(postId));
        setPost(response.data.data);
        if (response.data.data) {
          if (response.data.data.userCreatedThumbNail) {
            const thumbnail = await viewFile(
              response.data.data.userCreatedThumbNail,
              "forums"
            );
            setPost((prev) =>
              prev
                ? { ...prev, userCreatedThumbNail: thumbnail as string }
                : prev
            );
          }

          // Load files if exist
          if (response.data.data.files && response.data.data.files.length > 0) {
            const files: Record<string, string> = {};
            for (const file of response.data.data.files) {
              try {
                const fileData = await viewFile(file.fileId, "forums");
                files[file.fileId] = fileData as string;
              } catch (error) {
                console.error("Error loading file:", error);
              }
            }
            setPostFiles(files);
          }
          await loadComments(Number(postId));
          // Tải bài viết liên quan
          await loadRelatedPosts(
            Number(response.data.data.topicId),
            Number(postId)
          );
        }
      } catch (error: any) {
        if (error.status && error.status != 400) {
          toast.error("Không thể tải chi tiết bài viết. Vui lòng thử lại sau!");
        }
      } finally {
        setIsLoading(false);
      }
    };

    if (postId) {
      loadPost(Number(postId), currentPage);
    }
  }, [postId, currentPage]);
  useEffect(() => {
    const loadForum = async () => {
      const response = await forumsServices.getForumById(Number(forumId));
      setForum(response.data.data);
    };
    loadForum();
  }, [forumId]);

  // Hàm tải bình luận
  const loadComments = async (postId: number, page = 0) => {
    try {
      // setLoadingComments(true);
      const response = await forumsServices.getCommentsByPostId(postId, page);
      if (response.data.data?.content) {
        // Tải lại replies cho các comment đã mở
        const commentsWithReplies = await Promise.all(
          response.data.data.content.map(async (comment: Comment) => {
            if (expandedCommentIds.includes(comment.commentId)) {
              try {
                const repliesResponse = await forumsServices.getCommentReplies(
                  comment.commentId
                );
                return {
                  ...comment,
                  replies: repliesResponse.data.data
                };
              } catch (error) {
                console.error("Lỗi khi tải phản hồi:", error);
                return comment;
              }
            }
            return comment;
          })
        );

        setComments(commentsWithReplies);
        setCommentTotalPages(response.data.data.totalPages);
        setCommentPage(page);
      }
    } catch (error) {
      console.error("Lỗi khi tải bình luận:", error);
      toast.error("Không thể tải bình luận. Vui lòng thử lại sau!");
    } finally {
      // setLoadingComments(false);
    }
  };

  // Hàm gửi bình luận mới
  const handleSubmitComment = async () => {
    if (!isAuthenticated) {
      router.push("/login");
      return;
    }

    if (!commentContent.trim()) {
      return;
    }

    try {
      setIsSubmittingComment(true);
      const response = await forumsServices.createComment({
        content: commentContent,
        postId: post?.postId || null
      });

      // Reset form
      setCommentContent("");

      // Gọi lại API để tải lại danh sách bình luận
      await loadComments(post?.postId || 0, commentPage);

      if (post) {
        setPost((prev) =>
          prev
            ? {
                ...prev,
                commentCount: (prev.commentCount || 0) + 1
              }
            : null
        );
      }
    } catch (error) {
      console.error("Lỗi khi gửi bình luận:", error);
      toast.error("Không thể gửi bình luận. Vui lòng thử lại sau!");
    } finally {
      setIsSubmittingComment(false);
    }
  };

  // Hàm gửi trả lời cho bình luận
  const handleSubmitReplyComment = async () => {
    if (!isAuthenticated) {
      router.push("/login");
      return;
    }

    if (!replyContent.trim() || !post || replyToComment === null) return;

    try {
      const response = await forumsServices.createComment({
        content: replyContent,
        postId: post.postId,
        parentId: replyToComment
      });

      // Reset form
      setReplyContent("");
      setReplyToComment(null);

      // Gọi lại API để tải lại danh sách bình luận
      await loadComments(post.postId, commentPage);

      if (!expandedCommentIds.includes(replyToComment)) {
        setExpandedCommentIds(prev => [...prev, replyToComment]);
        await handleLoadReplies(replyToComment);
      }

      if (post) {
        setPost((prev) =>
          prev
            ? {
                ...prev,
                commentCount: (prev.commentCount || 0) + 1
              }
            : null
        );
      }
    } catch (error) {
      console.error("Lỗi khi gửi trả lời:", error);
      toast.error("Không thể gửi trả lời. Vui lòng thử lại sau!");
    }
  };

  // Hàm xử lý xóa bình luận
  const handleDeleteComment = async (commentId: number) => {
    try {
      setLoadingDeleteCommentIds((prev) => [...prev, commentId]);
      await forumsServices.deleteComment(commentId);

      // Gọi lại API để tải lại danh sách bình luận
      await loadComments(post?.postId || 0, commentPage);

      if (post) {
        setPost((prev) =>
          prev
            ? {
                ...prev,
                commentCount: (prev.commentCount || 0) - 1
              }
            : null
        );
      }
    } catch (error) {
      console.error("Lỗi khi xóa bình luận:", error);
      toast.error("Không thể xóa bình luận. Vui lòng thử lại sau!");
    } finally {
      setLoadingDeleteCommentIds((prev) =>
        prev.filter((id) => id !== commentId)
      );
    }
  };

  // Hàm xử lý like/unlike bình luận
  const handleLikeComment = async (commentId: number, isLiked: boolean) => {
    if (!isAuthenticated) {
      toast.info("Vui lòng đăng nhập để thực hiện chức năng này!");
      router.push("/login");
      return;
    }

    if (!post) return;

    try {
      // Thêm commentId vào danh sách đang loading
      setLoadingLikeCommentIds((prev) => [...prev, commentId]);

      if (isLiked) {
        await forumsServices.unlikeComment(commentId);
      } else {
        await forumsServices.likeComment(commentId);
      }

      // Tải lại bình luận
      await loadComments(post.postId, commentPage);
    } catch (error) {
      console.error("Lỗi khi thích/bỏ thích bình luận:", error);
      toast.error("Không thể thực hiện thao tác. Vui lòng thử lại sau!");
    } finally {
      // Xóa commentId khỏi danh sách đang loading
      setLoadingLikeCommentIds((prev) => prev.filter((id) => id !== commentId));
    }
  };

  // Hàm tải các phản hồi của một bình luận
  const handleLoadReplies = async (commentId: number) => {
    if (loadingReplies.includes(commentId)) return;

    try {
      setLoadingReplies((prev) => [...prev, commentId]);
      const response = await forumsServices.getCommentReplies(commentId);

      if (response.data.data) {
        // Cập nhật bình luận với danh sách phản hồi
        setComments((prevComments) =>
          prevComments.map((comment) =>
            comment.commentId === commentId
              ? { ...comment, replies: response.data.data }
              : comment
          )
        );
        // Thêm commentId vào danh sách đã mở
        setExpandedCommentIds((prev) => [...prev, commentId]);
      }
    } catch (error) {
      console.error("Lỗi khi tải phản hồi:", error);
      toast.error("Không thể tải phản hồi. Vui lòng thử lại sau!");
    } finally {
      setLoadingReplies((prev) => prev.filter((id) => id !== commentId));
    }
  };

  // Định dạng thời gian tương đối
  const getRelativeTime = (dateString?: string) => {
    if (!dateString) return "Chưa có bài viết";

    // Format: "20250418164323" -> "2025-04-18T16:43:23"
    if (dateString.length >= 14) {
      const year = dateString.substring(0, 4);
      const month = dateString.substring(4, 6);
      const day = dateString.substring(6, 8);
      const hour = dateString.substring(8, 10);
      const minute = dateString.substring(10, 12);
      const second = dateString.substring(12, 14);

      const formattedDate = `${year}-${month}-${day}T${hour}:${minute}:${second}`;
      const date = new Date(formattedDate);

      const now = new Date();
      const diffMs = now.getTime() - date.getTime();
      const diffMins = Math.floor(diffMs / 60000);
      const diffHours = Math.floor(diffMins / 60);
      const diffDays = Math.floor(diffHours / 24);
      const diffWeeks = Math.floor(diffDays / 7);

      if (diffMins < 60) return `${diffMins} phút trước`;
      if (diffHours < 24) return `${diffHours} giờ trước`;
      if (diffDays < 7) return `${diffDays} ngày trước`;
      return `${diffWeeks} tuần trước`;
    }

    return "Ngày không hợp lệ";
  };
  useEffect(() => {
    if (!isLoading && post) {
      loadAuthor();
    }
  }, [post, isLoading]);
  // Render bình luận
  const renderComment = (comment: Comment) => {
    return (
      <div key={comment.commentId} className="border-b pb-6">
        <div className="flex items-start">
          <div className="mr-3 h-10 w-10 flex-shrink-0 overflow-hidden rounded-full">
            {/* Avatar có thể là chữ cái đầu hoặc hình ảnh */}
            <Link
              href={`/forums/${eduEcosystemId}/user/${comment.userCreatedId}`}
              className="flex h-full w-full items-center justify-center bg-indigo-100 font-bold text-indigo-600"
            >
              {comment.userCreatedFullName?.charAt(0) || "A"}
            </Link>
          </div>
          <div className="flex-grow">
            <div className="mb-1 flex flex-wrap items-center justify-between">
              <div className="flex">
                <div className="font-medium text-gray-900">
                  <Link
                    href={`/forums/${eduEcosystemId}/user/${comment.userCreatedId}`}
                  >
                    {comment.userCreatedFullName || "Ẩn danh"}
                  </Link>
                </div>
                {comment.orgName && (
                  <div className="ml-2 flex items-center gap-0.5 rounded-full bg-primary/5 px-1.5 py-0.5 text-primary">
                    <i className="ri-verified-badge-fill text-xs"></i>{" "}
                    <span className="text-xs font-normal">
                      {comment.orgName}
                    </span>
                  </div>
                )}
              </div>
              <div className="ml-2 text-xs text-gray-500">
                {getRelativeTime(comment.createdAt)}
              </div>
            </div>
            {/* Nội dung bình luận */}
            <div className="mb-3 text-gray-700">
              {editingCommentId === comment.commentId ? (
                <div className="space-y-2">
                  <textarea
                    className="w-full rounded-md border border-gray-300 p-2 text-sm focus:border-indigo-500 focus:ring-1 focus:ring-indigo-500"
                    value={editingCommentContent}
                    onChange={(e) => setEditingCommentContent(e.target.value)}
                    rows={3}
                  />
                  <div className="flex justify-end gap-2">
                    <button
                      onClick={() => {
                        setEditingCommentId(null);
                        setEditingCommentContent("");
                      }}
                      className="rounded border border-gray-300 px-3 py-1 text-sm text-gray-600 hover:bg-gray-50"
                    >
                      Hủy
                    </button>
                    <button
                      onClick={() => handleEditComment(comment.commentId)}
                      disabled={
                        !editingCommentContent.trim() ||
                        loadingEditCommentIds.includes(comment.commentId)
                      }
                      className="rounded bg-indigo-600 px-3 py-1 text-sm text-white hover:bg-indigo-700 disabled:cursor-not-allowed disabled:bg-gray-400"
                    >
                      {loadingEditCommentIds.includes(comment.commentId) ? (
                        <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
                      ) : (
                        "Lưu"
                      )}
                    </button>
                  </div>
                </div>
              ) : (
                comment.content
              )}
            </div>

            {/* Các nút tương tác */}
            <div className="flex items-center text-sm">
              <button
                onClick={() =>
                  handleLikeComment(comment.commentId, comment.isLike ?? false)
                }
                className={`mr-4 flex items-center text-gray-500 hover:text-primary ${
                  comment.isLike ? "text-indigo-600" : ""
                }`}
                disabled={
                  !isAuthenticated ||
                  loadingLikeCommentIds.includes(comment.commentId)
                }
              >
                <div className="mr-1 flex h-4 w-4 items-center justify-center">
                  {loadingLikeCommentIds.includes(comment.commentId) ? (
                    <div className="h-3 w-3 animate-spin rounded-full border-2 border-indigo-600 border-t-transparent"></div>
                  ) : (
                    <i
                      className={
                        comment.isLike ? "ri-heart-fill" : "ri-heart-line"
                      }
                    ></i>
                  )}
                </div>
                <span>{comment.countLike || 0}</span>
              </button>

              <button
                onClick={() => setReplyToComment(comment.commentId)}
                className="flex items-center text-gray-500 hover:text-primary"
              >
                <div className="mr-1 flex h-4 w-4 items-center justify-center">
                  <i className="ri-reply-line"></i>
                </div>
                <span>Trả lời</span>
              </button>

              {/* Chỉ hiển thị nút sửa nếu người dùng là tác giả hoặc admin */}
              {user &&
                (Number(user.forumInfo?.userForumId) == comment.userCreatedId ||
                  user.forumInfo?.userType === "ROLE_ADMIN") && (
                  <button
                    onClick={() => {
                      setEditingCommentId(comment.commentId);
                      setEditingCommentContent(comment.content);
                    }}
                    className="ml-4 flex items-center text-gray-500 hover:text-primary"
                  >
                    <div className="mr-1 flex h-4 w-4 items-center justify-center">
                      <i className="ri-edit-line"></i>
                    </div>
                    <span>Sửa</span>
                  </button>
                )}

              {/* Chỉ hiển thị nút xóa nếu người dùng là tác giả hoặc admin */}
              {user &&
                (Number(user.forumInfo?.userForumId) == comment.userCreatedId ||
                  user.forumInfo?.userType === "ROLE_ADMIN") && (
                  <button
                    onClick={() => handleDeleteComment(comment.commentId)}
                    className="ml-4 flex items-center text-gray-500 hover:text-red-600"
                    disabled={loadingDeleteCommentIds.includes(
                      comment.commentId
                    )}
                  >
                    <div className="mr-1 flex h-4 w-4 items-center justify-center">
                      {loadingDeleteCommentIds.includes(comment.commentId) ? (
                        <div className="h-3 w-3 animate-spin rounded-full border-2 border-red-600 border-t-transparent"></div>
                      ) : (
                        <i className="ri-delete-bin-line"></i>
                      )}
                    </div>
                    <span>Xóa</span>
                  </button>
                )}
            </div>

            {/* Form trả lời */}
            {replyToComment === comment.commentId && (
              <div className="mt-4 rounded-lg bg-gray-50 p-3">
                <div className="flex gap-2">
                  <textarea
                    className="flex-1 rounded-md border border-gray-300 p-2 text-sm focus:border-indigo-500 focus:ring-1 focus:ring-indigo-500"
                    placeholder="Viết trả lời..."
                    value={replyContent}
                    onChange={(e) => setReplyContent(e.target.value)}
                    rows={2}
                  />
                  <div className="flex flex-col gap-1">
                    <button
                      onClick={handleSubmitReplyComment}
                      disabled={!replyContent.trim()}
                      className="rounded bg-indigo-600 px-3 py-1 text-sm text-white hover:bg-indigo-700 disabled:cursor-not-allowed disabled:bg-gray-400"
                    >
                      Gửi
                    </button>
                    <button
                      onClick={() => setReplyToComment(null)}
                      className="rounded border border-gray-300 px-3 py-1 text-sm text-gray-600 hover:bg-gray-50"
                    >
                      Hủy
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* Hiển thị số lượng trả lời nếu có */}
            {comment.hasChild && !comment.replies && (
              <div className="mt-2">
                <button
                  className="hover:text-primary-dark flex items-center font-medium text-primary"
                  onClick={() => handleLoadReplies(comment.commentId)}
                  disabled={loadingReplies.includes(comment.commentId)}
                >
                  <div className="mr-1 flex h-4 w-4 items-center justify-center">
                    {loadingReplies.includes(comment.commentId) ? (
                      <div className="h-3 w-3 animate-spin rounded-full border-2 border-indigo-600 border-t-transparent"></div>
                    ) : (
                      <i className="ri-chat-1-line"></i>
                    )}
                  </div>
                  <span>Xem trả lời</span>
                </button>
              </div>
            )}

            {/* Hiển thị các trả lời nếu đã được tải */}
            {comment.replies && comment.replies.length > 0 && (
              <div className="ml-6 mt-4 space-y-4">
                {comment.replies.map((reply) => (
                  <div key={reply.commentId} className="border-t pb-2 pt-4">
                    <div className="flex items-start">
                      <div className="mr-2 h-8 w-8 flex-shrink-0 overflow-hidden rounded-full">
                        <div className="flex h-full w-full items-center justify-center bg-indigo-100 text-xs font-bold text-indigo-600">
                          {reply.userCreatedFullName?.charAt(0) || "A"}
                        </div>
                      </div>
                      <div className="flex-grow">
                        <div className="mb-1 flex flex-wrap items-center justify-between">
                          <div className="flex">
                            <div className="text-base font-medium text-gray-900">
                              {reply.userCreatedFullName || "Ẩn danh"}
                            </div>
                            {reply.orgName && (
                              <div className="ml-2 flex items-center gap-0.5 rounded-full bg-primary/5 px-1.5 py-0.5 text-primary">
                                <i className="ri-verified-badge-fill text-xs"></i>{" "}
                                <span className="text-xs font-normal">
                                  {reply.orgName}
                                </span>
                              </div>
                            )}
                          </div>

                          <div className="ml-2 text-xs text-gray-500">
                            {getRelativeTime(reply.createdAt)}
                          </div>
                        </div>
                        <div className="mb-3 text-base text-gray-700">
                          {editingCommentId === reply.commentId ? (
                            <div className="space-y-2">
                              <textarea
                                className="w-full rounded-md border border-gray-300 p-2 text-base focus:border-indigo-500 focus:ring-1 focus:ring-indigo-500"
                                value={editingCommentContent}
                                onChange={(e) =>
                                  setEditingCommentContent(e.target.value)
                                }
                                rows={3}
                              />
                              <div className="flex justify-end gap-2">
                                <button
                                  onClick={() => {
                                    setEditingCommentId(null);
                                    setEditingCommentContent("");
                                  }}
                                  className="rounded border border-gray-300 px-3 py-1 text-sm text-gray-600 hover:bg-gray-50"
                                >
                                  Hủy
                                </button>
                                <button
                                  onClick={() =>
                                    handleEditComment(reply.commentId)
                                  }
                                  disabled={
                                    !editingCommentContent.trim() ||
                                    loadingEditCommentIds.includes(
                                      reply.commentId
                                    )
                                  }
                                  className="rounded bg-indigo-600 px-3 py-1 text-sm text-white hover:bg-indigo-700 disabled:cursor-not-allowed disabled:bg-gray-400"
                                >
                                  {loadingEditCommentIds.includes(
                                    reply.commentId
                                  ) ? (
                                    <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
                                  ) : (
                                    "Lưu"
                                  )}
                                </button>
                              </div>
                            </div>
                          ) : (
                            reply.content
                          )}
                        </div>
                        <div className="flex items-center text-sm">
                          <button
                            onClick={() =>
                              handleLikeComment(
                                reply.commentId,
                                reply.isLike ?? false
                              )
                            }
                            className={`mr-4 flex items-center text-gray-500 hover:text-primary ${
                              reply.isLike ? "text-indigo-600" : ""
                            }`}
                            disabled={
                              !isAuthenticated ||
                              loadingLikeCommentIds.includes(reply.commentId)
                            }
                          >
                            <div className="mr-1 flex h-4 w-4 items-center justify-center">
                              {loadingLikeCommentIds.includes(
                                reply.commentId
                              ) ? (
                                <div className="h-2 w-2 animate-spin rounded-full border-2 border-indigo-600 border-t-transparent"></div>
                              ) : (
                                <i
                                  className={
                                    reply.isLike
                                      ? "ri-heart-fill"
                                      : "ri-heart-line"
                                  }
                                ></i>
                              )}
                            </div>
                            <span>{reply.countLike || 0}</span>
                          </button>

                          {/* Chỉ hiển thị nút sửa nếu người dùng là tác giả hoặc admin */}
                          {user &&
                            (Number(user.forumInfo?.userForumId) ==
                              reply.userCreatedId ||
                              user.forumInfo?.userType === "ROLE_ADMIN") && (
                              <button
                                onClick={() => {
                                  setEditingCommentId(reply.commentId);
                                  setEditingCommentContent(reply.content);
                                }}
                                className="flex items-center text-gray-500 hover:text-primary"
                              >
                                <div className="mr-1 flex h-4 w-4 items-center justify-center">
                                  <i className="ri-edit-line"></i>
                                </div>
                                <span>Sửa</span>
                              </button>
                            )}

                          {/* Chỉ hiển thị nút xóa nếu người dùng là tác giả hoặc admin */}
                          {user &&
                            (Number(user.forumInfo?.userForumId) ==
                              reply.userCreatedId ||
                              user.forumInfo?.userType === "ROLE_ADMIN") && (
                              <button
                                onClick={() =>
                                  handleDeleteComment(reply.commentId)
                                }
                                className="ml-4 flex items-center text-gray-500 hover:text-red-600"
                                disabled={loadingDeleteCommentIds.includes(
                                  reply.commentId
                                )}
                              >
                                <div className="mr-1 flex h-4 w-4 items-center justify-center">
                                  {loadingDeleteCommentIds.includes(
                                    reply.commentId
                                  ) ? (
                                    <div className="h-2 w-2 animate-spin rounded-full border-2 border-red-600 border-t-transparent"></div>
                                  ) : (
                                    <i className="ri-delete-bin-line"></i>
                                  )}
                                </div>
                                <span>Xóa</span>
                              </button>
                            )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  // Hàm xử lý like/unlike bài viết
  const handleLikePost = async (postId: number, isLiked: boolean) => {
    if (!isAuthenticated) {
      toast.info("Vui lòng đăng nhập để thực hiện chức năng này!");
      router.push("/login");
      return;
    }

    try {
      // Thêm postId vào danh sách đang loading
      setLoadingLikeIds((prev) => [...prev, postId]);

      if (isLiked) {
        await forumsServices.unlikePost(postId);
      } else {
        await forumsServices.likePost(postId);
      }

      // Tải lại bài viết sau khi like/unlike
      const response = await forumsServices.getPostById(Number(postId));
      if (response.data.data) {
        setPost(response.data.data);
      }
    } catch (error) {
      console.error("Lỗi khi thích/bỏ thích bài viết:", error);
      toast.error("Không thể thực hiện thao tác. Vui lòng thử lại sau!");
    } finally {
      // Xóa postId khỏi danh sách đang loading
      setLoadingLikeIds((prev) => prev.filter((id) => id !== postId));
    }
  };

  // Hàm xử lý xóa bài viết
  const handleDeletePost = async () => {
    if (!post) return;

    try {
      setIsLoading(true);
      await forumsServices.deletePost(post.postId);
      router.push(`/forums/${eduEcosystemId}/topics/${forumId}`);
    } catch (error) {
      console.error("Lỗi khi xóa bài viết:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Hàm sửa bài viết
  const handleEditPost = async (editedContent: string) => {
    if (!post) return;

    try {
      setIsLoading(true);
      const response = await forumsServices.updatePost(post.postId, {
        title: post.title,
        content: editedContent,
        hashTags: post.hashTags,
        files:
          post.files?.map((file) => ({
            fileId: file,
            action: "ADD" as const
          })) || []
      });
      if (response.data.data) {
        setPost(response.data.data);
      }
    } catch (error) {
      console.error("Lỗi khi sửa bài viết:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Hàm xử lý sửa bình luận
  const handleEditComment = async (commentId: number) => {
    try {
      setLoadingEditCommentIds((prev) => [...prev, commentId]);
      const response = await forumsServices.updateComment(
        commentId,
        editingCommentContent
      );
      if (response.data.data) {
        // Gọi lại API để tải lại danh sách bình luận
        await loadComments(post?.postId || 0, commentPage);
        setEditingCommentId(null);
        setEditingCommentContent("");
      }
    } catch (error) {
      console.error("Lỗi khi sửa bình luận:", error);
      toast.error("Không thể sửa bình luận. Vui lòng thử lại sau!");
    } finally {
      setLoadingEditCommentIds((prev) => prev.filter((id) => id !== commentId));
    }
  };

  // Hàm xử lý báo cáo bài viết
  const handleReportPost = async () => {
    if (!post) return;
    setIsReporting(true);
    try {
      await forumsServices.reportPost(post.postId, reportReason);
      toast.success("Báo cáo của bạn đã được gửi!");
      setShowReportModal(false);
      setReportReason("");
    } catch (error) {
      console.error("Lỗi khi gửi báo cáo:", error);
      toast.error("Không thể gửi báo cáo. Vui lòng thử lại sau!");
    } finally {
      setIsReporting(false);
    }
  };

  const handleCloseReportModal = () => {
    setShowReportModal(false);
    setReportReason("");
    setIsReporting(false);
  };

  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <Loading
          color="primary"
          size="lg"
          variant="spinner"
          text="Đang tải bài viết..."
        />
      </div>
    );
  }

  if (!post) {
    return (
      <div className="container mx-auto py-20 text-center">
        <h2 className="text-2xl font-bold text-gray-700">
          Không tìm thấy bài viết
        </h2>
        <p className="mt-4 text-gray-600">
          Bài viết bạn đang tìm kiếm không tồn tại hoặc đã bị xóa.
        </p>
        <Link
          href={`/forums/${eduEcosystemId}/topics/${forumId}`}
          className="mt-8 inline-block rounded-md bg-indigo-600 px-6 py-2.5 text-white transition-colors hover:bg-indigo-700"
        >
          Quay lại diễn đàn
        </Link>
      </div>
    );
  }

  return (
    <section className="bg-gray-50">
      {/* Breadcrumb */}
      <ForumBreadcrumb
        items={[
          {
            type: "link",
            label: "Trang chủ",
            href: "/"
          },
          {
            type: "select",
            label: "Chọn cấp",
            value: eduEcosystemId?.toString() || "",
            href: "/forums/:value",
            options: [
              { key: 1, value: "1", label: "Mầm non" },
              { key: 2, value: "2", label: "Tiểu học" },
              { key: 3, value: "3", label: "Trung học cơ sở" },
              { key: 4, value: "4", label: "Trung học phổ thông" }
            ]
          },
          {
            type: "link",
            label: forum?.name || "Chủ đề",
            href: `/forums/${eduEcosystemId}/topics/${forumId}`
          },
          {
            type: "text",
            label: post?.title || "Bài viết"
          }
        ]}
        lastUpdated={getRelativeTime(post?.createdAt) || "Đang cập nhật..."}
      />
      {/* Main Content */}
      <div className="container mx-auto px-4 py-6 sm:px-6">
        <div className="flex flex-col gap-8 lg:flex-row">
          {/* Main Content */}
          <div className="w-full lg:w-2/3">
            <article className="mb-6 rounded-md bg-white p-6 shadow-sm">
              {/* Article Header */}
              <div className="mb-6">
                <div className="mb-3 flex flex-wrap items-center gap-2">
                  <span
                    style={{
                      backgroundColor: post?.backgroundColor,
                      color: post?.textColor
                    }}
                    className="rounded-full px-2 py-0.5 text-xs"
                  >
                    {post?.topicName}
                  </span>
                </div>
                <h1 className="mb-3 break-words text-lg font-bold text-gray-900 sm:mb-4 sm:text-xl md:text-2xl lg:text-3xl">
                  {post?.title}
                </h1>
                <div className="flex flex-col gap-2 border-b pb-3 sm:flex-row sm:items-center sm:justify-between sm:gap-4 sm:pb-4">
                  <div className="flex items-center">
                    <Link
                      href={`/forums/${eduEcosystemId}/user/${post?.userCreatedId}`}
                      className="mr-3 h-10 w-10 overflow-hidden rounded-full"
                    >
                      {post?.userCreatedThumbNail ? (
                        <img
                          src={post.userCreatedThumbNail}
                          alt="Avatar"
                          className="h-full w-full object-cover object-top"
                        />
                      ) : (
                        <div className="flex h-full w-full items-center justify-center bg-indigo-100 font-bold text-indigo-600">
                          {post?.userCreatedFullName?.charAt(0) || "A"}
                        </div>
                      )}
                    </Link>
                    <div>
                      <Link
                        href={`/forums/${eduEcosystemId}/user/${post?.userCreatedId}`}
                        className="flex items-center justify-start gap-1 font-medium text-gray-900"
                      >
                        <div>{post?.userCreatedFullName || "Ẩn danh"}</div>
                        {post.orgName && (
                          <div className="ml-2 flex items-center gap-0.5 rounded-full bg-primary/5 px-1.5 py-0.5 text-primary">
                            <i className="ri-verified-badge-fill text-xs"></i>
                            <span className="text-xs font-normal">
                              {post.orgName}
                            </span>
                          </div>
                        )}
                      </Link>
                      <div className="text-sm text-gray-500">
                        Đăng {getRelativeTime(post?.createdAt)}
                      </div>
                    </div>
                  </div>
                  <div className="flex flex-wrap items-center gap-y-1 space-x-1 sm:gap-y-2 sm:space-x-2">
                    <button
                      onClick={() =>
                        handleLikePost(post?.postId || 0, post?.isLike || false)
                      }
                      className={`like-button flex items-center ${post?.isLike ? "active" : ""}`}
                    >
                      <div className="flex h-5 w-5 items-center justify-center">
                        <i
                          className={
                            post?.isLike ? "ri-heart-fill" : "ri-heart-line"
                          }
                        ></i>
                      </div>
                      <span className="ml-1 text-sm">
                        {post?.likeCount || 0}
                      </span>
                    </button>
                    {/*<button className="bookmark-button flex items-center">*/}
                    {/*  <div className="flex h-5 w-5 items-center justify-center">*/}
                    {/*    <i className="ri-bookmark-line"></i>*/}
                    {/*  </div>*/}
                    {/*</button>*/}
                    {/*<button className="flex items-center text-gray-500 hover:text-primary">*/}
                    {/*  <div className="flex h-5 w-5 items-center justify-center">*/}
                    {/*    <i className="ri-share-line"></i>*/}
                    {/*  </div>*/}
                    {/*</button>*/}
                    {/* Thêm các nút xóa, sửa và báo cáo */}
                    {user &&
                      (Number(user.forumInfo?.userForumId) ===
                        post?.userCreatedId ||
                        user.forumInfo?.userType === "ROLE_ADMIN") && (
                        <>
                          <button
                            onClick={() => {
                              if (
                                window.confirm(
                                  "Bạn có chắc chắn muốn xóa bài viết này?"
                                )
                              ) {
                                handleDeletePost();
                              }
                            }}
                            className="flex items-center text-gray-500 hover:text-red-600"
                            title="Xóa bài viết"
                          >
                            <div className="flex h-5 w-5 items-center justify-center">
                              <i className="ri-delete-bin-line"></i>
                            </div>
                          </button>
                          <button
                            onClick={() =>
                              router.push(
                                `/forums/${eduEcosystemId}/topics/${forumId}/posts/${postId}/edit`
                              )
                            }
                            className="flex items-center text-gray-500 hover:text-indigo-600"
                            title="Sửa bài viết"
                          >
                            <div className="flex h-5 w-5 items-center justify-center">
                              <i className="ri-edit-line"></i>
                            </div>
                          </button>
                        </>
                      )}
                    {user &&
                      Number(user.forumInfo?.userForumId) !==
                        post?.userCreatedId && (
                        <button
                          onClick={() => setShowReportModal(true)}
                          className="flex items-center text-gray-500 hover:text-yellow-600"
                          title="Báo cáo bài viết"
                        >
                          <div className="flex h-5 w-5 items-center justify-center">
                            <i className="ri-flag-line"></i>
                          </div>
                        </button>
                      )}
                  </div>
                </div>
              </div>

              {/* Article Content */}
              <div className="prose prose-xs sm:prose-sm md:prose mx-auto mb-4 max-w-full overflow-hidden text-gray-800 sm:mb-6">
                <div
                  dangerouslySetInnerHTML={{ __html: post?.content || "" }}
                />
              </div>

              {/* Attached Files */}
              {post?.files && post.files.length > 0 && (
                <div className="mb-6 ">
                  <h3 className="mb-4 text-lg font-bold">Tài liệu đính kèm</h3>
                  <div className="space-y-3">
                    {post.files.map((file, index) => {
                      const formatFileSize = (size: number) => {
                        if (size < 1024) {
                          return `${size} bytes`;
                        } else if (size < 1024 * 1024) {
                          return `${(size / 1024).toFixed(2)} KB`;
                        } else {
                          return `${(size / (1024 * 1024)).toFixed(2)} MB`;
                        }
                      };
                      return (
                        <div
                          key={file.fileId}
                          className="flex items-center justify-between rounded-lg bg-[#F9FAFB] p-3"
                        >
                          <div className="flex items-center space-x-3">
                            <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-[#2E2E8B1A]/10 text-gray-500">
                              <Image
                                src="/icon/icon-file.svg"
                                alt="File icon"
                                width={20}
                                height={20}
                                className="text-[#2E2E8B]"
                              />
                            </div>
                            <div>
                              <div className="text-sm font-medium text-gray-700">
                                {file.fileOriginalName}
                              </div>
                              <div className="flex items-center space-x-1 text-xs text-gray-500">
                                <div>{file.fileType}</div>
                                <span className="inline-block h-1 w-1 rounded-full bg-gray-500" />
                                <div>{formatFileSize(file.fileSize)}</div>
                              </div>
                            </div>
                          </div>
                          <a
                            href={postFiles[file.fileId] || "#"}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="flex h-8 w-8 items-center justify-center rounded-full hover:bg-gray-200"
                            title="Mở tài liệu trong tab mới"
                            onClick={(e) => {
                              if (!postFiles[file.fileId]) {
                                e.preventDefault();
                                toast.info(
                                  "Đang tải tài liệu, vui lòng thử lại sau"
                                );
                              }
                            }}
                          >
                            <i className="ri-download-line text-xl"></i>
                          </a>
                        </div>
                      );
                    })}
                  </div>
                </div>
              )}

              {/* Article Tags */}
              {post?.hashTags && post.hashTags.length > 0 && (
                <div className="flex justify-start gap-2.5 border-t pt-6">
                  <div className="mb-2 text-sm font-medium text-gray-700">
                    Tags:
                  </div>
                  <div className="flex flex-wrap gap-1 sm:gap-2">
                    {post.hashTags.map((tag, index) => (
                      <Link
                        key={index}
                        href={`/forums/${eduEcosystemId}/search?hashtag=${encodeURIComponent(tag)}`}
                        className="rounded-full bg-gray-100 px-3 py-1 text-sm text-gray-800 hover:bg-gray-200"
                      >
                        {tag}
                      </Link>
                    ))}
                  </div>
                </div>
              )}

              {/* Article Stats */}
              <div className="mt-4 flex flex-col justify-between gap-3 border-t pt-4 sm:mt-6 sm:flex-row sm:items-center sm:gap-4 sm:pt-6">
                <div className="flex flex-wrap items-center gap-4 text-sm text-gray-500 sm:gap-6">
                  <div className="flex items-center">
                    <div className="mr-1 flex h-4 w-4 items-center justify-center">
                      <i className="ri-eye-line"></i>
                    </div>
                    <span>{post?.viewCount || 0} lượt xem</span>
                  </div>
                  <div className="flex items-center">
                    <div className="mr-1 flex h-4 w-4 items-center justify-center">
                      <i className="ri-message-2-line"></i>
                    </div>
                    <span>{post?.commentCount || 0} bình luận</span>
                  </div>
                  <div className="flex items-center">
                    <div className="mr-1 flex h-4 w-4 items-center justify-center">
                      <i className="ri-heart-line"></i>
                    </div>
                    <span>{post?.likeCount || 0} lượt thích</span>
                  </div>
                </div>
              </div>
            </article>

            {/* Comments Section */}
            <div className="mb-6 rounded-md bg-white p-6 shadow-sm">
              <h3 className="mb-6 text-xl font-bold">
                Bình luận ({post?.commentCount || 0})
              </h3>
              {/* Comment Form */}
              {isAuthenticated ? (
                <div className="mb-8">
                  <div className="mb-3 flex flex-row items-start sm:mb-4">
                    <div className="mx-auto mb-2 mr-1 h-10 w-10 flex-shrink-0 overflow-hidden rounded-full sm:mx-0 sm:mb-0 sm:mr-3">
                      {user?.avatar ? (
                        <img
                          src={user.avatar}
                          alt="User Avatar"
                          className="h-full w-full object-cover object-top"
                        />
                      ) : (
                        <div className="flex h-full w-full items-center justify-center bg-indigo-100 font-bold text-indigo-600">
                          {user?.firstName?.charAt(0) ||
                            user?.username?.charAt(0) ||
                            "U"}
                        </div>
                      )}
                    </div>
                    <div className="flex-grow">
                      <textarea
                        className="w-full rounded-md border border-gray-200 p-3 text-sm focus:border-transparent focus:outline-none focus:ring-2 focus:ring-primary"
                        rows={3}
                        placeholder="Viết bình luận của bạn..."
                        value={commentContent}
                        onChange={(e) => setCommentContent(e.target.value)}
                      />
                      <div className="mt-2 flex justify-end">
                        <button
                          onClick={handleSubmitComment}
                          disabled={
                            !commentContent.trim() || isSubmittingComment
                          }
                          className="whitespace-nowrap rounded-lg bg-primary px-4 py-2 text-sm font-medium text-white disabled:bg-gray-400"
                        >
                          {isSubmittingComment
                            ? "Đang gửi..."
                            : "Đăng bình luận"}
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="mb-8 rounded-lg bg-gray-50 p-6 text-center">
                  <p className="mb-3 text-gray-600">
                    Vui lòng đăng nhập để bình luận
                  </p>
                  <Link
                    href="/login"
                    className="hover:bg-primary-dark inline-block rounded-lg bg-primary px-4 py-2 text-sm text-white transition-colors"
                  >
                    Đăng nhập ngay
                  </Link>
                </div>
              )}

              {/* Comments List */}
              <div className="space-y-6">
                {comments.map((comment) => renderComment(comment))}
              </div>

              {/* Load More Comments */}
              {commentTotalPages > 1 && (
                <div className="mt-6 text-center">
                  <button
                    onClick={() =>
                      loadComments(post?.postId || 0, commentPage + 1)
                    }
                    className="hover:text-primary-dark mx-auto flex items-center justify-center font-medium text-primary"
                  >
                    <div className="mr-1 flex h-5 w-5 items-center justify-center">
                      <i className="ri-more-line"></i>
                    </div>
                    Xem thêm bình luận
                  </button>
                </div>
              )}
            </div>
          </div>

          {/* Sidebar */}
          <div className="order-first w-full space-y-4 sm:space-y-6 lg:order-last lg:w-1/3">
            {/* Author Card */}
            <div className="rounded-md bg-white p-6 shadow-sm">
              <h3 className="mb-4 text-lg font-bold">Về tác giả</h3>
              <div className="mb-3 flex items-center sm:mb-4">
                <div className="mr-3 h-12 w-12 flex-shrink-0 overflow-hidden rounded-full">
                  {post?.userCreatedThumbNail ? (
                    <img
                      src={post.userCreatedThumbNail}
                      alt="Avatar"
                      className="h-full w-full object-cover object-top"
                    />
                  ) : (
                    <div className="flex h-full w-full items-center justify-center bg-indigo-100 font-bold text-indigo-600">
                      {post?.userCreatedFullName?.charAt(0) || "A"}
                    </div>
                  )}
                </div>
                <div>
                  <div className="flex">
                    <div className="font-medium text-gray-900">
                      {post?.userCreatedFullName || "Ẩn danh"}
                    </div>
                    {post.orgName && (
                      <div className="ml-2 flex items-center gap-0.5 rounded-full bg-primary/5 px-1.5 py-0.5 text-primary">
                        <i className="ri-verified-badge-fill text-xs"></i>
                        <span className="text-xs font-normal">
                          {post.orgName}
                        </span>
                      </div>
                    )}
                  </div>
                  <div className="text-sm text-gray-500">
                    Đã đăng {author?.countPosts || 0} bài viết
                  </div>
                </div>
              </div>
              <Link
                href={`/forums/${eduEcosystemId}/user/${post?.userCreatedId}`}
                className="hover:text-primary-dark flex items-center text-sm font-medium text-primary"
              >
                <div className="mr-1 flex h-4 w-4 items-center justify-center">
                  <i className="ri-user-line"></i>
                </div>
                Xem trang cá nhân
              </Link>
            </div>

            {/* Related Posts */}
            <div className="mb-6 rounded-md bg-white p-6 shadow-sm">
              <h3 className="mb-4 text-lg font-bold">Bài viết liên quan</h3>
              <div className="space-y-3 sm:space-y-4">
                {relatedPosts.length > 0 ? (
                  relatedPosts.map((relatedPost) => (
                    <div
                      key={relatedPost.postId}
                      className="flex items-start gap-2 sm:gap-3"
                    >
                      <div className="h-12 w-12 flex-shrink-0 overflow-hidden rounded-lg sm:h-14 sm:w-14 md:h-16 md:w-16">
                        {relatedPost.userCreatedThumbNail ? (
                          <Image
                            src={relatedPost.userCreatedThumbNail}
                            alt={relatedPost.title}
                            width={64}
                            height={64}
                            className="h-full w-full object-cover object-top"
                          />
                        ) : (
                          <div className="flex h-full w-full items-center justify-center bg-indigo-100 font-bold text-indigo-600">
                            {relatedPost?.userCreatedFullName?.charAt(0) || "A"}
                          </div>
                        )}
                      </div>
                      <div>
                        <Link
                          href={`/forums/${eduEcosystemId}/topics/${forumId}/posts/${relatedPost.postId}`}
                          className="line-clamp-2 block text-sm font-medium leading-5 text-gray-900 hover:text-primary"
                        >
                          {relatedPost.title}
                        </Link>
                        <div className="text-xs text-gray-500">
                          {getRelativeTime(relatedPost.createdAt)}
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-sm text-gray-500">
                    Không có bài viết liên quan
                  </div>
                )}
              </div>
            </div>

            {/* Popular Tags */}
            {/*<div className="mb-6 rounded-md bg-white p-6 shadow-sm">*/}
            {/*  <h3 className="mb-4 text-lg font-bold">Hashtags</h3>*/}
            {/*  <div className="flex flex-wrap justify-center gap-1.5 sm:justify-start sm:gap-2">*/}
            {/*    {post?.hashTags?.map((tag, index) => (*/}
            {/*      <Link*/}
            {/*        key={index}*/}
            {/*        href={`/forums/${eduEcosystemId}/search?hashtag=${encodeURIComponent(tag)}`}*/}
            {/*        className="rounded-full bg-gray-100 px-3 py-1 text-sm text-gray-800 hover:bg-gray-200"*/}
            {/*      >*/}
            {/*        #{tag}*/}
            {/*      </Link>*/}
            {/*    ))}*/}
            {/*  </div>*/}
            {/*</div>*/}
          </div>
        </div>
      </div>

      {/* File Preview Modal */}
      {selectedFile && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-2 sm:p-4">
          <div className="relative w-full max-w-xs rounded-lg bg-white p-2 sm:max-w-md sm:p-4 md:max-w-2xl lg:max-w-4xl">
            <button
              onClick={() => setSelectedFile(null)}
              className="absolute right-7 top-5 bg-white text-gray-500 hover:bg-gray-100"
            >
              <i className="ri-close-line h-6 w-6 text-xl"></i>
            </button>
            {postFiles[selectedFile] ? (
              <Image
                src={postFiles[selectedFile]}
                alt={`File ${selectedFile}`}
                width={800}
                height={600}
                className="max-h-[80vh] w-full object-contain"
              />
            ) : (
              <div className="flex h-[60vh] items-center justify-center">
                <Loading color="primary" size="lg" variant="spinner" />
              </div>
            )}
          </div>
        </div>
      )}

      {/* Report Modal */}
      {showReportModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-2 sm:p-4">
          <div className="relative w-full max-w-md rounded-lg bg-white p-4 sm:p-6">
            <button
              onClick={handleCloseReportModal}
              className="absolute right-7 top-5 bg-white text-gray-500 hover:bg-gray-100"
            >
              <i className="ri-close-line h-6 w-6 text-xl"></i>
            </button>
            <h3 className="mb-4 text-lg font-semibold text-gray-900">
              Báo cáo bài viết
            </h3>
            <textarea
              className="w-full rounded border border-gray-300 px-4 py-2 text-sm"
              placeholder="Nhập lý do báo cáo..."
              value={reportReason}
              onChange={(e) => setReportReason(e.target.value)}
              rows={4}
            />
            <div className="mt-4 flex flex-col justify-end gap-2 sm:flex-row">
              <button
                onClick={handleCloseReportModal}
                className="mb-2 rounded-lg border border-gray-300 px-4 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 sm:mb-0"
              >
                Hủy bỏ
              </button>
              <button
                onClick={handleReportPost}
                disabled={!reportReason.trim() || isReporting}
                className="rounded-lg bg-primary px-4 py-2 text-sm font-medium text-white hover:bg-primary/80 disabled:bg-gray-400"
              >
                {isReporting ? "Đang gửi..." : "Gửi báo cáo"}
              </button>
            </div>
          </div>
        </div>
      )}
    </section>
  );
}
