"use client";

import ForumBreadcrumb from "@/components/forums/ForumBreadcrumb";
import PostForm from "@/components/forums/PostForm";
import Loading from "@/components/ui/Loading";
import { useAuth } from "@/context/AuthContext";
import useUploadFile from "@/hook/useUploadFile";
import forumsServices from "@/services/forums/forumsServices";
import { Forum, Post } from "@/services/forums/types";
import Link from "next/link";
import { useParams, useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { toast } from "react-toastify";

export default function EditPostPage() {
  const { eduEcosystemId, forumId, postId } = useParams();
  const router = useRouter();
  const { isAuthenticated, user } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [post, setPost] = useState<Post | null>(null);
  const [forum, setForum] = useState<Forum | null>(null);

  const { uploadForumFile, viewFile } = useUploadFile();

  useEffect(() => {
    const loadPost = async () => {
      try {
        setIsLoading(true);
        const response = await forumsServices.getPostById(Number(postId));
        if (response.data.data) {
          const postData = response.data.data;
          setPost(postData);
        }
      } catch (error) {
        console.error("Lỗi khi tải bài viết:", error);
        toast.error("Không thể tải bài viết. Vui lòng thử lại sau!");
      } finally {
        setIsLoading(false);
      }
    };

    if (postId) {
      loadPost();
    }
  }, [postId]);

  useEffect(() => {
    const loadForum = async () => {
      const response = await forumsServices.getForumById(Number(forumId));
      setForum(response.data.data);
    };
    loadForum();
  }, [forumId]);

  const handleSubmit = async (data: {
    title: string;
    content: string;
    selectedTopicId: string | null;
    imageFiles: File[];
    hashTags: string[];
    removedExistingFiles?: {
      fileId: string;
      fileOriginalName: string;
      fileStoredName: string;
      fileType: string;
      fileSize: number;
    }[];
  }) => {

    if (!isAuthenticated) {
      toast.info("Vui lòng đăng nhập để thực hiện chức năng này!");
      router.push("/login");
      return;
    }

    if (!data.selectedTopicId) {
      toast.warning("Vui lòng chọn chủ đề!");
      return;
    }

    if (!data.title.trim()) {
      toast.warning("Vui lòng nhập tiêu đề bài viết!");
      return;
    }

    if (data.title.trim().length < 5) {
      toast.warning("Tiêu đề bài viết phải có ít nhất 5 ký tự!");
      return;
    }

    if (data.title.trim().length > 100) {
      toast.warning("Tiêu đề bài viết không được vượt quá 100 ký tự!");
      return;
    }

    if (!data.content.trim()) {
      toast.warning("Vui lòng nhập nội dung bài viết!");
      return;
    }

    try {
      setIsLoading(true);

      // Upload tất cả các ảnh mới
      const files: string[] = [];
      for (const file of data.imageFiles) {
        const response = await uploadForumFile(file);
        if (response?.data) {
          files.push(response.data);
        }
      }

      // Tạo mảng files với các action phù hợp
      let fileActions = [];

      // Thêm tất cả các file mới với action ADD
      for (const fileId of files) {
        fileActions.push({ fileId, action: "ADD" as const });
      }

      // Thêm các file đã bị xóa với action DELETE
      if (data.removedExistingFiles && data.removedExistingFiles.length > 0) {
        for (const file of data.removedExistingFiles) {
          fileActions.push({ fileId: file.fileId, action: "DELETE" as const });
        }
      }

      // Cập nhật bài viết
      await forumsServices.updatePost(Number(postId), {
        title: data.title,
        content: data.content,
        topicId: Number(data.selectedTopicId),
        hashTags: data.hashTags,
        files: fileActions.length > 0 ? fileActions : undefined
      });


      toast.success("Cập nhật bài viết thành công");
      router.push(
        `/forums/${eduEcosystemId}/topics/${forumId}/posts/${postId}`
      );
    } catch (error) {
      console.error("Lỗi khi cập nhật bài viết:", error);
      toast.error("Có lỗi xảy ra khi cập nhật bài viết");
    }
  };

  if (!isAuthenticated) {
    return (
      <div className="container mx-auto py-20 text-center">
        <h2 className="text-2xl font-bold text-gray-700">
          Vui lòng đăng nhập để sửa bài viết
        </h2>
        <p className="mt-4 text-gray-600">
          Bạn cần đăng nhập để có thể sửa bài viết.
        </p>
        <Link
          href="/login"
          className="mt-8 inline-block rounded-md bg-indigo-600 px-6 py-2.5 text-white transition-colors hover:bg-indigo-700"
        >
          Đăng nhập ngay
        </Link>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="container flex min-h-screen items-center justify-center">
        <Loading
          color="primary"
          size="lg"
          variant="spinner"
          text="Đang tải bài viết..."
        />
      </div>
    );
  }

  if (!post) {
    return (
      <div className="container mx-auto py-20 text-center">
        <h2 className="text-2xl font-bold text-gray-700">
          Không tìm thấy bài viết
        </h2>
        <p className="mt-4 text-gray-600">
          Bài viết bạn đang tìm kiếm không tồn tại hoặc đã bị xóa.
        </p>
        <Link
          href={`/forums/${eduEcosystemId}/topics/${forumId}`}
          className="mt-8 inline-block rounded-md bg-indigo-600 px-6 py-2.5 text-white transition-colors hover:bg-indigo-700"
        >
          Quay lại
        </Link>
      </div>
    );
  }

  // if (
  //   user?.forumInfo?.userForumId !== post.userCreatedId &&
  //   user?.forumInfo?.userType !== "ROLE_ADMIN"
  // ) {
  //   return (
  //     <div className="container mx-auto py-20 text-center">
  //       <h2 className="text-2xl font-bold text-gray-700">
  //         Bạn không có quyền sửa bài viết này
  //       </h2>
  //       <p className="mt-4 text-gray-600">
  //         Chỉ tác giả của bài viết mới có thể sửa bài viết.
  //       </p>
  //       <Link
  //         href={`/forums/${eduEcosystemId}/topics/${forumId}/posts/${postId}`}
  //         className="mt-8 inline-block rounded-md bg-indigo-600 px-6 py-2.5 text-white transition-colors hover:bg-indigo-700"
  //       >
  //         Quay lại
  //       </Link>
  //     </div>
  //   );
  // }

  return (
    <div className="mb-6">
      {/* Breadcrumb */}
      <ForumBreadcrumb
        items={[
          {
            type: "link",
            label: "Trang chủ",
            href: "/"
          },
          {
            type: "select",
            label: "Chọn cấp",
            value: eduEcosystemId?.toString() || "",
            href: "/forums/:value",
            options: [
              { key: 1, value: "1", label: "Mầm non" },
              { key: 2, value: "2", label: "Tiểu học" },
              { key: 3, value: "3", label: "Trung học cơ sở" },
              { key: 4, value: "4", label: "Trung học phổ thông" }
            ]
          },
          {
            type: "link",
            label: forum?.name || "Chủ đề",
            href: `/forums/${eduEcosystemId}/topics/${forumId}`
          },
          {
            type: "link",
            label: post?.title || "Bài viết",
            href: `/forums/${eduEcosystemId}/topics/${forumId}/posts/${postId}`
          },
          {
            type: "text",
            label: "Sửa bài viết"
          }
        ]}
      />
      
        <PostForm
          isLoading={isLoading}
          handleSubmit={handleSubmit}
          initialData={{
            title: post?.title || "",
            content: post?.content || "",
            selectedTopicId: post?.topicId?.toString() || null,
            hashTags: post?.hashTags || [],
            existingFiles: post?.files || [],
          }}
          isEditing={true}
        />
    </div>
  );
}
