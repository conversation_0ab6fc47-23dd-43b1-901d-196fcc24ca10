"use client";

import React, { useEffect, useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { useAuth } from "@/context/AuthContext";
import { Button } from "@/components/ui/button";
import userServices from "@/services/user/userServices";
import {
  User,
  Heart,
  MessageSquare,
  FileText,
  Clock,
  Phone,
  Share2,
  Star
} from "lucide-react";
import useUploadFile from "@/hook/useUploadFile";
import { Skeleton } from "@/components/ui/skeleton";
import ChangePasswordModal from "@/components/modals/ChangePasswordModal";
import EditUserInfo from "@/components/forums/EditUserInfo";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { useParams, useRouter } from "next/navigation";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import notificationsServices from "@/services/notifications/notificationsServices";
import Loading from "@/components/ui/Loading";
import { boolean } from "yup";

type TabType = "activities" | "posts" | "about" | "change-password";

interface UserHistory {
  userHistoryUserForumId: number;
  userForumId: number;
  content: string;
  historyUserForumsType: string;
  postTitle: string;
  objectId: number;
  eduEcosystemId: number;
  forumId: number;
  topicId: number;
  date: any;
}

interface UserHistoryResponse {
  content: UserHistory[];
  pageable: {
    pageNumber: number;
    pageSize: number;
    totalPages: number;
    totalElements: number;
  };
}

interface Post {
  postId: number;
  title: string;
  content: string;
  createdAt: string;
  updatedAt: string;
  color: string;
  topicName: string;
  likeCount: number;
  commentCount: number;
  viewCount: number;
  status: string;
  userForumId: number;
  username: string;
  thumbnail: string;
  eduEcosystemId: number;
  forumId: number;
}

interface UserInfo {
  userForumId: number;
  userId: number;
  username: string;
  thumbnail: string;
  topicUserList: any;
  background: string | null;
  phoneNumber: string;
  bio: string;
  fullName: string;
  firstName: string;
  lastName: string;
  gender: string;
  dateOfBirth: string;
  numberOfPost: number;
  userType: string;
  orgName: string;
  countPosts: number;
  countComments: number;
  countLikes: number;
  address: string;
  hometown: string;
  position: string;
  hobbies: string;
  careerGoal: string;
}

interface ForumNotification {
  notificationsId: number;
  recipientId: number;
  senderId: number;
  content: string;
  type: "REPLY_COMMENT" | "LIKE_COMMENT" | "COMMENT" | "LIKE_POST";
  objectId: number;
  createdDate: string;
  read: boolean;
  eduEcosystemId: number;
  forumId: number;
  topicId: number;
}

interface NotificationResponse {
  content: ForumNotification[];
  pageable: {
    pageNumber: number;
    pageSize: number;
  };
  totalPages: number;
  totalElements: number;
  last: boolean;
}

const notificationIcons = {
  COMMENT: <MessageSquare className="text-blue-500" />,
  LIKE: <Heart className="text-rose-500" />,
  FOLLOW: <User className="text-green-500" />,
  SHARE: <Share2 className="text-purple-500" />,
  FEATURED: <Star className="text-yellow-500" />
};

const notificationBg = {
  COMMENT: "bg-blue-50",
  LIKE: "bg-rose-50",
  FOLLOW: "bg-green-50",
  SHARE: "bg-purple-50",
  FEATURED: "bg-yellow-50"
};

export default function UserProfilePage() {
  const { user } = useAuth();
  const { eduEcosystemId } = useParams();
  const router = useRouter();

  const [userInfo, setUserInfo] = useState<UserInfo>();
  const [activeTab, setActiveTab] = useState<TabType>("activities");
  const [userHistory, setUserHistory] = useState<UserHistory[]>([]);
  const [posts, setPosts] = useState<Post[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isReading, setIsReading] = useState<string>("");
  const [isChangePasswordModalOpen, setIsChangePasswordModalOpen] =
    useState(false);
  const [isEditUserInfoModalOpen, setIsEditUserInfoModalOpen] = useState(true);
  const [pagination, setPagination] = useState({
    page: 0,
    size: 10,
    totalPages: 0,
    totalElements: 0
  });
  const [selectedLevel, setSelectedLevel] = useState<string>(
    eduEcosystemId?.toString() || ""
  );
  const [inputSearch, setInputSearch] = useState("");
  const [debouncedSearch, setDebouncedSearch] = useState("");
  const [sort, setSort] = useState<"ASC" | "DESC">("DESC");
  const [tab, setTab] = useState<"all" | "unread" | "read">("all");
  const [notifications, setNotifications] = useState<ForumNotification[]>([]);
  const [page, setPage] = useState<number>(0);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [unreadCount, setUnreadCount] = useState<number>(0);
  const [pageSize, setPageSize] = useState<number>(8);
  const [totalPages, setTotalPages] = useState<number>(1);

  useEffect(() => {
    setPagination({
      page: 0,
      size: 10,
      totalPages: 0,
      totalElements: 0
    });
    setInputSearch("");
  }, []);

  const handlePageChange = (newPage: number) => {
    setPagination((prev) => ({ ...prev, page: newPage }));
  };

  // Hàm lấy thông báo
  async function fetchNotifications(
    pageNumber: number = 0,
    size: number = pageSize,
    read?: string
  ) {
    try {
      setIsLoading(true);
      const response = await notificationsServices.getForumNotifications(
        pageNumber,
        size,
        isReading
      );
      const data = response.data.data as NotificationResponse;
      setNotifications(data.content);
      setHasMore(!data.last);
      setPagination({
        page: pageNumber,
        size: size,
        totalPages: data.totalPages,
        totalElements: data.totalElements
      });
      setTotalPages(data.totalPages);
    } catch (error) {
      console.error("Error fetching notifications:", error);
    } finally {
      setIsLoading(false);
    }
  }

  const handleGoBack = () => {
    router.back();
  };

  const markAsRead = async (id: number) => {
    try {
      await notificationsServices.markNotificationAsRead(id);
    } catch (error) {
      console.error("Error marking notification as read:", error);
    }
  };

  const clearNotifications = async () => {
    try {
      setIsLoading(true);
      // Đánh dấu tất cả thông báo chưa đọc là đã đọc
      await notificationsServices.updateAllRead();
    } catch (error) {
      console.error("Error clearing notifications:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const getNotificationBg = (type: ForumNotification["type"]) => {
    switch (type) {
      case "LIKE_POST":
      case "LIKE_COMMENT":
        return "#FFD700";
      case "COMMENT":
      case "REPLY_COMMENT":
        return "#007BFF";
      default:
        return "#FF0000";
    }
  };

  const getNotificationIcon = (type: ForumNotification["type"]) => {
    switch (type) {
      case "LIKE_POST":
      case "LIKE_COMMENT":
        return (
          <svg
            width="40"
            height="40"
            viewBox="0 0 40 40"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M0 20C0 8.95431 8.95431 0 20 0C31.0457 0 40 8.95431 40 20C40 31.0457 31.0457 40 20 40C8.95431 40 0 31.0457 0 20Z"
              fill="#FEE2E2"
            />
            <path
              d="M19.992 14.8535C20.5073 14.3913 21.098 14.0868 21.7643 13.9402C22.4305 13.7935 23.0879 13.809 23.7364 13.9868C24.4115 14.1735 24.9956 14.5135 25.4886 15.0068C25.9816 15.5002 26.3214 16.0846 26.508 16.7602C26.6856 17.409 26.7012 18.0646 26.5546 18.7268C26.408 19.389 26.1038 19.9824 25.6418 20.5068L19.992 26.1602L14.3422 20.5068C13.8803 19.9824 13.576 19.389 13.4295 18.7268C13.2829 18.0646 13.2984 17.409 13.4761 16.7602C13.6627 16.0846 14.0047 15.5002 14.5021 15.0068C14.9996 14.5135 15.5815 14.1735 16.2477 13.9868C16.8962 13.809 17.5536 13.7935 18.2198 13.9402C18.8861 14.0868 19.4768 14.3913 19.992 14.8535ZM24.5359 15.9468C24.2072 15.6179 23.8252 15.3935 23.3899 15.2735C22.9546 15.1535 22.5149 15.1424 22.0707 15.2402C21.6266 15.3379 21.2313 15.5424 20.8848 15.8535L19.992 16.6535L19.0993 15.8535C18.7617 15.5424 18.3686 15.3379 17.92 15.2402C17.4714 15.1424 17.0294 15.1535 16.5942 15.2735C16.1589 15.3935 15.7769 15.6179 15.4482 15.9468C15.1195 16.2757 14.893 16.6579 14.7686 17.0935C14.6443 17.529 14.6287 17.9668 14.722 18.4068C14.8153 18.8468 15.0129 19.2402 15.315 19.5868L19.992 24.2802L24.6691 19.5868C24.9711 19.2402 25.1688 18.8468 25.2621 18.4068C25.3553 17.9668 25.3398 17.529 25.2154 17.0935C25.0911 16.6579 24.8645 16.2757 24.5359 15.9468Z"
              fill="#EF4444"
            />
          </svg>
        );
      case "COMMENT":
      case "REPLY_COMMENT":
        return (
          <svg
            width="32"
            height="33"
            viewBox="0 0 32 33"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M0 16.0469C0 7.21032 7.16344 0.046875 16 0.046875C24.8366 0.046875 32 7.21032 32 16.0469C32 24.8834 24.8366 32.0469 16 32.0469C7.16344 32.0469 0 24.8834 0 16.0469Z"
              fill="#DBEAFE"
            />
            <path
              d="M12.3016 20.2067L9.33008 22.54V10.2067C9.33008 10.0289 9.39448 9.87337 9.52329 9.74004C9.6521 9.60671 9.80978 9.54004 9.99633 9.54004H21.9888C22.1754 9.54004 22.3331 9.60671 22.4619 9.74004C22.5907 9.87337 22.6551 10.0289 22.6551 10.2067V19.54C22.6551 19.7267 22.5907 19.8867 22.4619 20.02C22.3331 20.1534 22.1754 20.2156 21.9888 20.2067H12.3016ZM11.8352 18.8734H21.3226V10.8734H10.6626V19.8067L11.8352 18.8734ZM15.3263 14.2067H16.6588V15.54H15.3263V14.2067ZM12.6613 14.2067H13.9938V15.54H12.6613V14.2067ZM17.9913 14.2067H19.3238V15.54H17.9913V14.2067Z"
              fill="#3B82F6"
            />
          </svg>
        );
      default:
        return "📢";
    }
  };

  const formatDate = (dateStr: string) => {
    if (dateStr?.length >= 14) {
      const year = dateStr.substring(0, 4);
      const month = dateStr.substring(4, 6);
      const day = dateStr.substring(6, 8);
      const hour = dateStr.substring(8, 10);
      const minute = dateStr.substring(10, 12);
      const second = dateStr.substring(12, 14);
      const date = new Date(
        `${year}-${month}-${day}T${hour}:${minute}:${second}`
      );
      return new Intl.DateTimeFormat("vi-VN", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
        hour12: false
      }).format(date);
    }
    return "Không xác định";
  };

  useEffect(() => {
    fetchNotifications(page, pageSize);
  }, [page, isReading, pageSize]);
  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <Loading
          color="primary"
          size="lg"
          variant="spinner"
          text="Đang tải thông báo..."
        />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100 pb-6 sm:pb-8">
      <div className="container mx-auto px-2 py-4 sm:px-4 sm:py-8">
        <div className="mb-4 flex flex-col gap-2 sm:mb-6 sm:gap-4 md:flex-row md:items-center md:justify-between">
          <h1 className="text-xl font-bold text-[#1F2937] sm:text-2xl">
            Thông báo của bạn
          </h1>
        </div>
        <div className="mb-4 sm:mb-6">
          <div className="flex flex-col gap-2 rounded-xl border border-gray-100 bg-white px-2 py-2 sm:flex-row sm:items-center sm:justify-between sm:gap-4 sm:px-4 sm:py-3">
            {/* Tabs */}
            <div className="flex flex-wrap gap-2">
              <Button
                variant="ghost"
                onClick={() => {
                  setIsReading("");
                  setTab("all");
                }}
                className={`rounded-[8px] border px-3 py-1.5 text-sm font-medium shadow-none transition-none sm:px-6 sm:py-2 sm:text-base ${
                  tab === "all"
                    ? "border-[#2E228B] bg-[#2E228B] text-white"
                    : "border-gray-200 bg-white text-[#374151]"
                }`}
              >
                Tất cả
              </Button>
              <Button
                variant="ghost"
                onClick={() => {
                  setIsReading("false");
                  setTab("unread");
                }}
                className={`rounded-[8px] border px-3 py-1.5 text-sm font-medium shadow-none transition-none sm:px-6 sm:py-2 sm:text-base ${
                  tab === "unread"
                    ? "border-[#2E228B] bg-[#2E228B] text-white"
                    : "border-gray-200 bg-white text-[#374151]"
                }`}
              >
                Chưa đọc
              </Button>
              <Button
                variant="ghost"
                onClick={() => {
                  setIsReading("true");
                  setTab("read");
                }}
                className={`rounded-[8px] border px-3 py-1.5 text-sm font-medium shadow-none transition-none sm:px-6 sm:py-2 sm:text-base ${
                  tab === "read"
                    ? "border-[#2E228B] bg-[#2E228B] text-white"
                    : "border-gray-200 bg-white text-[#374151]"
                }`}
              >
                Đã đọc
              </Button>
            </div>
            {/* Nút đánh dấu đã đọc */}
            <div className="flex flex-col items-end gap-2 sm:flex-row sm:gap-0">
              <button
                className="flex w-full items-center justify-center gap-1 border-none bg-transparent px-0 py-0 text-sm font-medium text-[#2E228B] shadow-none outline-none focus:ring-0 sm:w-auto sm:text-base"
                style={{ boxShadow: "none" }}
                onClick={() => {
                  setNotifications((prev) =>
                    prev.map((n) => ({ ...n, read: true }))
                  );
                  clearNotifications();
                }}
              >
                <svg
                  width="18"
                  height="18"
                  viewBox="0 0 15 14"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g clipPath="url(#clip0_689_14946)">
                    <path
                      d="M7.11472 8.03839L7.93109 8.85505L12.876 3.92005L13.6924 4.74839L7.93109 10.5117L4.22242 6.79005L5.05046 5.97339L7.11472 8.03839ZM7.11472 6.38172L9.99536 3.48839L10.8234 4.31672L7.93109 7.21005L7.11472 6.38172ZM5.47031 9.68339L4.64227 10.5117L0.933594 6.79005L1.74997 5.97339L2.57801 6.79005L5.47031 9.68339Z"
                      fill="#2E228B"
                    />
                  </g>
                  <defs>
                    <clipPath id="clip0_689_14946">
                      <rect
                        width="14.5781"
                        height="14"
                        fill="white"
                        transform="translate(0.0234375)"
                      />
                    </clipPath>
                  </defs>
                </svg>
                Đánh dấu tất cả đã đọc
              </button>
              <span className="block text-center text-sm text-gray-400 sm:ml-4">
                {pagination.totalElements} thông báo
              </span>
            </div>
          </div>
        </div>
        <div className="space-y-3 sm:space-y-4">
          {notifications.map((notification) => (
            <div
              key={notification.notificationsId}
              onClick={() => {
                markAsRead(notification.notificationsId);
              }}
              className={`relative flex flex-col gap-2 rounded-2xl border border-transparent bg-opacity-60 p-3 shadow-sm transition-all sm:gap-3 sm:p-5 md:flex-row ${!notification.read ? "border-y-0 !border-l-4 border-r-0 !border-primary" : ""} bg-white hover:shadow-lg`}
            >
              {/* Icon + nội dung */}
              <div className="flex flex-1 items-start gap-4">
                <div
                  className="flex h-10 w-10 items-center justify-center rounded-full sm:h-12 sm:w-12"
                  style={{
                    background: getNotificationBg(notification.type) + "22"
                  }}
                >
                  {getNotificationIcon(notification.type)}
                </div>
                <div className="min-w-0 flex-1">
                  <div className="flex items-center gap-2">
                    <span
                      className="text-sm font-semibold text-[#1F2937] sm:text-base"
                      dangerouslySetInnerHTML={{
                        __html: notification.content
                      }}
                    />
                  </div>
                  {/* Link dưới nội dung */}
                  {notification.type === "COMMENT" ||
                  notification.type === "REPLY_COMMENT" ||
                  notification.type === "LIKE_POST" ||
                  notification.type === "LIKE_COMMENT" ? (
                    <Link
                      href={`/forums/${notification.eduEcosystemId}/topics/${notification.topicId}/posts/${notification.forumId}`}
                      className="mt-1 inline-block text-xs text-primary underline sm:text-sm"
                    >
                      Xem bài viết
                    </Link>
                  ) : notification.type === "FOLLOW" ? (
                    <Link
                      href={`/forums/${notification.eduEcosystemId}/user/${notification.senderId}`}
                      className="mt-1 inline-block text-xs text-primary underline sm:text-sm"
                    >
                      Xem hồ sơ
                    </Link>
                  ) : null}
                </div>
              </div>
              {/* Thời gian */}
              <div className="flex min-w-[120px] flex-col items-end justify-between">
                <span className="mb-2 text-xs text-gray-400 md:absolute md:right-8 md:top-5 md:mb-0">
                  {formatDate(notification.createdDate)}
                </span>
              </div>
            </div>
          ))}
          {notifications.length === 0 && (
            <div className="flex h-full w-full items-center justify-center">
              <p className="text-gray-500">
                {isReading == "true"
                  ? "Chưa có thông báo"
                  : "Chưa có thông báo mới"}{" "}
              </p>
            </div>
          )}
        </div>
        {totalPages > 0 && (
          <div className="mt-6 flex flex-col items-center justify-between gap-2 rounded-[8px] border border-gray-100 bg-white px-2 py-2 sm:mt-8 sm:flex-row sm:gap-0 sm:px-4 sm:py-3">
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-700">Hiển thị</span>
              <select
                className="h-9 w-16 rounded-md border border-gray-200 bg-white px-2 text-sm text-gray-700 focus:outline-none"
                value={pageSize}
                onChange={(e) => setPageSize(Number(e.target.value))}
              >
                {[8, 16, 32, 64].map((size) => (
                  <option key={size} value={size}>
                    {size}
                  </option>
                ))}
              </select>
              <span className="text-sm text-gray-700">thông báo mỗi trang</span>
            </div>
            <div className="flex items-center gap-4">
              <div className="text-sm font-medium text-gray-700">
                Trang {totalPages === 0 ? 0 : page + 1} / {totalPages}
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  className={`rounded-lg border ${
                    page === 0
                      ? "border-gray-200 text-gray-400"
                      : "border-gray-200 text-gray-700"
                  } px-4 py-2`}
                  onClick={() => setPage((p) => Math.max(0, p - 1))}
                  disabled={page === 0}
                >
                  &lt; Trước
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className={`rounded-lg border ${
                    page === totalPages - 1
                      ? "border-gray-200 text-gray-400"
                      : "border-gray-200 text-gray-700"
                  } px-4 py-2`}
                  onClick={() =>
                    setPage((p) => Math.min(totalPages - 1, p + 1))
                  }
                  disabled={page >= totalPages - 1}
                >
                  Sau &gt;
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
