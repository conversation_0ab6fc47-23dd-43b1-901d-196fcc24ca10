"use client";

import { useParams, useRouter } from "next/navigation";
import { useEffect, useState } from "react";

import ForumBreadcrumb from "@/components/forums/ForumBreadcrumb";
import ForumSidebar from "@/components/forums/ForumSidebar";
import Loading from "@/components/ui/Loading";
import useUploadFile from "@/hook/useUploadFile";
import anonymousServices from "@/services/anonymousServices";
import forumsServices from "@/services/forums/forumsServices";
import { Forum } from "@/services/forums/types";
import Image from "next/image";
import Link from "next/link";
import { toast } from "react-toastify";
// Thêm skeleton loading component
const ImageSkeleton = () => (
  <div className="relative h-12 w-12 flex-shrink-0 animate-pulse rounded-full bg-gray-200" />
);

export default function ForumPage() {
  const router = useRouter();
  const { eduEcosystemId } = useParams();
  const [selectedLevel, setSelectedLevel] = useState<string>(
    eduEcosystemId?.toString() || "all"
  );
  const [ecosystems, setEcosystems] = useState<
    { eduEcosystemId: number; name: string }[]
  >([]);

  const [forums, setForums] = useState<Forum[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [lastUpdated, setLastUpdated] = useState<string>("");
  const [currentPage, setCurrentPage] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [totalElements, setTotalElements] = useState(0);
  const pageSize = 10;
  const { viewFile } = useUploadFile();
  const [topicImages, setTopicImages] = useState<Record<string, string>>({});

  const loadTopicImage = async (fileId: string) => {
    try {
      const imageData = await viewFile(fileId, "forums");
      setTopicImages((prev) => ({
        ...prev,
        [fileId]: imageData as string
      }));
    } catch (error) {
      console.error("Error loading topic image:", error);
    }
  };

  useEffect(() => {
    const fetchEcosystems = async () => {
      try {
        const response = await anonymousServices.getListEduEcosystem();
        if (response.data?.data) {
          setEcosystems(response.data.data);
        }
      } catch (error) {
        console.error("Error fetching ecosystems:", error);
        toast.error(
          "Không thể tải danh sách hệ sinh thái. Vui lòng thử lại sau!"
        );
      }
    };

    fetchEcosystems();
  }, []);

  useEffect(() => {
    const fetchForums = async () => {
      try {
        setIsLoading(true);
        const response = await forumsServices.getForums(
          currentPage,
          pageSize,
          Number(eduEcosystemId)
        );
        if (response.data.data) {
          setForums(response.data.data.content);
          setTotalPages(response.data.data.totalPages);
          setTotalElements(response.data.data.totalElements);

          // Tải ảnh cho mỗi diễn đàn
          response.data.data.content.forEach((forum: Forum) => {
            if (forum.fileId) {
              loadTopicImage(forum.fileId);
            }
          });

          // Lấy ngày cập nhật mới nhất
          if (response.data.data.content.length > 0) {
            const mostRecentForum = response.data.data.content
              .filter((forum: Forum) => forum.newPostCreatedAt)
              .sort((a: Forum, b: Forum) => {
                if (!a.newPostCreatedAt) return 1;
                if (!b.newPostCreatedAt) return -1;
                return b.newPostCreatedAt.localeCompare(a.newPostCreatedAt);
              })[0];

            if (mostRecentForum?.newPostCreatedAt) {
              const dateStr = mostRecentForum.newPostCreatedAt;
              // Format: "20250418164323" -> "2025-04-18T16:43:23"
              if (dateStr.length >= 14) {
                const year = dateStr.substring(0, 4);
                const month = dateStr.substring(4, 6);
                const day = dateStr.substring(6, 8);
                const hour = dateStr.substring(8, 10);
                const minute = dateStr.substring(10, 12);
                const second = dateStr.substring(12, 14);

                const formattedDate = `${year}-${month}-${day}T${hour}:${minute}:${second}`;
                const latestDate = new Date(formattedDate);
                setLastUpdated(latestDate.toLocaleDateString("vi-VN"));
              }
            }
          }
        }
      } catch (error) {
        console.error("Error fetching forums:", error);
        toast.error("Không thể tải dữ liệu diễn đàn. Vui lòng thử lại sau!");
      } finally {
        setIsLoading(false);
      }
    };

    fetchForums();
  }, [currentPage, selectedLevel]);

  const handlePageChange = (newPage: number) => {
    if (newPage >= 0 && newPage < totalPages) {
      setCurrentPage(newPage);
    }
  };

  // Xử lý thumbnail cho forum
  const getForumIcon = (forum: Forum, index: number) => {
    if (forum.fileId && topicImages[forum.fileId]) {
      return topicImages[forum.fileId];
    }
    return null;
  };

  // Định dạng thời gian tương đối
  const getRelativeTime = (dateString?: string) => {
    if (!dateString) return "Chưa có bài viết";

    // Format: "20250418164323" -> "2025-04-18T16:43:23"
    if (dateString.length >= 14) {
      const year = dateString.substring(0, 4);
      const month = dateString.substring(4, 6);
      const day = dateString.substring(6, 8);
      const hour = dateString.substring(8, 10);
      const minute = dateString.substring(10, 12);
      const second = dateString.substring(12, 14);

      const formattedDate = `${year}-${month}-${day}T${hour}:${minute}:${second}`;
      const date = new Date(formattedDate);

      const now = new Date();
      const diffMs = now.getTime() - date.getTime();
      const diffMins = Math.floor(diffMs / 60000);
      const diffHours = Math.floor(diffMins / 60);
      const diffDays = Math.floor(diffHours / 24);
      const diffWeeks = Math.floor(diffDays / 7);

      if (diffMins < 1) return "1 phút trước";
      if (diffMins < 60) return `${diffMins} phút trước`;
      if (diffHours < 24) return `${diffHours} giờ trước`;
      if (diffDays < 7) return `${diffDays} ngày trước`;
      return `${diffWeeks} tuần trước`;
    }

    return "Ngày không hợp lệ";
  };

  return (
    <section>
      <ForumBreadcrumb
        items={[
          {
            type: 'link',
            label: 'Trang chủ',
            href: '/'
          },
          {
            type: 'select',
            label: 'Chọn cấp',
            value: selectedLevel,
            href: '/forums/:value',
            options: [
              { key: 1, value: "1", label: "Mầm non" },
              { key: 2, value: "2", label: "Tiểu học" },
              { key: 3, value: "3", label: "Trung học cơ sở" },
              { key: 4, value: "4", label: "Trung học phổ thông" }
            ]
          }
        ]}
        lastUpdated={lastUpdated}
      />

      {/* Main Content */}
      <div className="container mx-auto px-4 py-6">
        <div className="flex flex-col gap-6 lg:flex-row">
          {/* Left Column - Main Content */}
          <div className="w-full lg:w-2/3">
            {/* Help Box */}
            <div className="mb-6 flex flex-col md:flex-row items-center rounded-[12px] bg-gray-100 p-4 md:p-6">
              <div className="mb-4 md:mb-0 md:mr-6">
                <img
                  src="/homepage/forum_img.png"
                  alt="Hỗ trợ"
                  className="h-20 w-20 sm:h-24 sm:w-24 rounded object-cover mx-auto md:mx-0"
                />
              </div>
              <div className="text-center md:text-left">
                <h2 className="mb-2 text-lg font-semibold">
                  Bạn chưa tìm thấy giải pháp?
                </h2>
                <p className="text-gray-600">
                  Hãy tạo bài viết và cộng đồng sẽ giúp đỡ bạn
                </p>
              </div>
            </div>

            {/* Forum Categories */}
            <div className="overflow-hidden">
              {/* Forum Header */}
              <div className="grid grid-cols-12 border-b bg-gray-50 px-4 py-3 text-sm text-gray-500">
                <div className="col-span-6 md:col-span-6">Diễn đàn</div>
                <div className="col-span-2 text-center hidden md:block">Chủ đề</div>
                <div className="col-span-2 text-center hidden md:block">Bài viết</div>
                <div className="col-span-6 md:col-span-2 text-right">Cập nhật</div>
              </div>

              {isLoading ? (
                <div className="flex items-center justify-center py-10 sm:py-20">
                  <Loading color="primary" size="lg" variant="spinner" />
                </div>
              ) : forums.length === 0 ? (
                <div className="p-8 text-center text-gray-500">
                  Chưa có chủ đề
                </div>
              ) : (
                <div className="mb-6 bg-white shadow-sm overflow-hidden rounded-b-[12px] overflow-x-auto">
                  {forums.map((forum, index) => (
                    <div
                      key={forum.forumId + index + forum.name}
                      className="border-b"
                    >
                      <div className="grid grid-cols-12 items-center px-4 py-4">
                        <div className="col-span-6 md:col-span-6 flex">
                          <div className="relative mr-3 h-10 w-10 flex-shrink-0">
                            {isLoading || !getForumIcon(forum, index) ? (
                              <div className="flex h-10 w-10 items-center justify-center rounded-full bg-gray-100">
                                <i className="ri-computer-line ri-lg text-primary"></i>
                              </div>
                            ) : (
                              <Image
                                src={getForumIcon(forum, index) as string}
                                alt={forum.name}
                                fill
                                className="rounded-full object-cover"
                                sizes="(max-width: 768px) 40px, (max-width: 1440px) 40px, 40px"
                                loading="lazy"
                              />
                            )}
                          </div>
                          <div>
                            <Link
                              href={`/forums/${forum.eduEcosystemId}/topics/${forum.forumId}`}
                              className="font-medium text-primary hover:underline"
                            >
                              {forum.name}
                            </Link>
                            <p className="mt-1 text-sm text-gray-500 line-clamp-2 md:line-clamp-none">
                              {forum.description}
                            </p>
                          </div>
                        </div>
                        <div className="col-span-2 text-center hidden md:block">
                          <span className="text-gray-700">
                            {forum.topicCount || 0}
                          </span>
                        </div>
                        <div className="col-span-2 text-center hidden md:block">
                          <span className="text-gray-700">
                            {forum.postCount || 0}
                          </span>
                        </div>
                        <div className="col-span-6 md:col-span-2 text-right text-sm text-gray-500">
                          <div>{getRelativeTime(forum.newPostCreatedAt)}</div>
                          <Link href={`/forums/${forum.eduEcosystemId}/user/${forum.userForumId}`} className="text-primary">
                            {forum.newPostCreatedBy}
                          </Link>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {/* Pagination */}
              <div className="flex flex-col md:flex-row items-center justify-between gap-4 md:gap-0 mt-4">
                <div className="text-sm text-gray-500 text-center md:text-left w-full md:w-auto">
                  Hiển thị {forums.length} trong tổng số {totalElements} kết quả
                </div>
                <div className="flex items-center space-x-1 w-full md:w-auto justify-center md:justify-end">
                  <button
                    onClick={() => handlePageChange(0)}
                    disabled={currentPage === 0}
                    className="flex h-7 w-7 sm:h-8 sm:w-8 items-center justify-center rounded border text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                  >
                    <i className="ri-arrow-left-s-line"></i>
                  </button>
                  <button
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 0}
                    className="flex h-7 w-7 sm:h-8 sm:w-8 items-center justify-center rounded border text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                  >
                    <i className="ri-arrow-left-line"></i>
                  </button>
                  <span className="px-2 text-xs sm:text-sm text-gray-500">
                    Trang {totalPages === 0 ? 0 : currentPage + 1} / {totalPages}
                  </span>
                  <button
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage >= totalPages - 1}
                    className="flex h-7 w-7 sm:h-8 sm:w-8 items-center justify-center rounded border text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                  >
                    <i className="ri-arrow-right-line"></i>
                  </button>
                  <button
                    onClick={() => handlePageChange(totalPages - 1)}
                    disabled={currentPage >= totalPages - 1}
                    className="flex h-7 w-7 sm:h-8 sm:w-8 items-center justify-center rounded border text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                  >
                    <i className="ri-arrow-right-s-line"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Right Column - Sidebar */}
          <ForumSidebar />
        </div>
      </div>
    </section>
  );
}
