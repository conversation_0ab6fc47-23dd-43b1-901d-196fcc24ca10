import type { Metadata } from "next";
import { Inter, Quicksand } from "next/font/google";
import "@/style/globals.css";
import ClientRootLayout from "@/components/ClientRootLayout";

const quicksand = Quicksand({
  subsets: ["latin"]
});

export const metadata: Metadata = {
  title: "<PERSON><PERSON> sinh thái học tập, sáng tạo",
  description: "<PERSON><PERSON> sinh thái học tập, sáng tạo"
};

export default function RootLayout({
  children
}: Readonly<{
  children: React.ReactNode;
}>) {
  const dataMenu = [
    { name: "<PERSON><PERSON><PERSON><PERSON> thiệ<PERSON>", href: "/about-us" },
    { name: "<PERSON> tức", href: "/user/news" },
    { name: "<PERSON>h<PERSON><PERSON> kê", href: "" },
    { name: "<PERSON><PERSON><PERSON> đàn", href: "" },
    { name: "<PERSON>ạng xã hội", href: "/social-network" },
    { name: "<PERSON><PERSON><PERSON>", href: "" },
    { name: "<PERSON><PERSON><PERSON> hạng", href: "" },
    { name: "<PERSON><PERSON><PERSON><PERSON> sát", href: "" },
    { name: "<PERSON><PERSON><PERSON> nhậ<PERSON>", href: "/login" }
  ];
  const dataOfMenuChild = [
    {
      title:
        "Hệ sinh thái học tập sáng tạo: kết nối kiến thức, phát triển tư duy, chia sẻ.",
      button: [],
      items: []
    },
    {
      title:
        "Tin tức mới nhất về hoạt động của hệ sinh thái học tập, sáng tạo.",
      button: [],
      items: []
    },
    {
      title:
        "Thống kê các trường các cấp của thuộc hệ sinh thái học tập, sáng tạo.",
      button: [],
      items: []
    },
    {
      title:
        "Nơi thảo luận các vấn đề về học tập, sáng tạo của học sinh, phụ huynh và giáo viên.",
      button: [],
      items: [
        {
          name: "Diễn đàn học tập sáng tạo cấp mầm non",
          href: "/forums/1",
          image: "/mamnon.png"
        },
        {
          name: "Diễn đàn học tập sáng tạo cấp tiểu học",
          href: "/forums/2",
          image: "/tieuhoc.png"
        },
        {
          name: "Diễn đàn học tập sáng tạo cấp THCS",
          href: "/forums/3",
          image: "/thcs.png"
        },
        {
          name: "Diễn đàn học tập sáng tạo cấp THPT",
          href: "/forums/4",
          image: "/thpt.png"
        },
      ]
    },
    {
      title:
        "",
      button: [],
      items: []
    },
    {
      title: "Tổng hợp các phần mềm hỗ trợ trong học tập, giảng dạy.",
      button: [],
      items: [
        {
          name: "Nghiên cứu",
          href: "#",
          image: "/tieuhoc.jpg"
        },
        {
          name: "Học liệu",
          href: "#",
          image: "/trunghoc.jpg"
        },
        {
          name: "Dự án",
          href: "#",
          image: "/trunghoc1.jpg"
        },
        {
          name: "Kỹ năng",
          href: "#",
          image: "/boicanh.jpg"
        },
        {
          name: "Thống kê",
          href: "#",
          image: "/thongke.jpg"
        },
        {
          name: "Hệ sinh thái số",
          href: "#",
          image: "/vanhoa.jpg"
        }
      ]
    },
    {
      title: "",
      button: [],
      items: []
    }
  ];
  return (
    <ClientRootLayout 
      dataMenu={dataMenu} 
      dataOfMenuChild={dataOfMenuChild}
      quicksandClassName={quicksand.className}
    >
      {children}
    </ClientRootLayout>
  );
}
