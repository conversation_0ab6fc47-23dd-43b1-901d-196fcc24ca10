import HeroSection from "@/components/ui/HeroSection";
import { BsArrowDownShort } from "react-icons/bs";
export default async function NewsPage() {
  const articles = [
    {
      id: 2,
      category: "Hội thảo khoa học về hệ sinh thái ...",
      title:
        '<PERSON><PERSON><PERSON> 17/9, Trường Đại học Thủ đô Hà Nội đã tổ chức Hội thảo khoa học với chủ đề "Hệ sinh thái học tập...',
      imageUrl: "/news/news1.jpg"
    },
    {
      id: 1,
      category: "Thông báo tuyển sinh cùng lúc hai ...",
      title:
        "Thông báo tuyển sinh cùng lúc hai chương trình trình độ đại học hệ chính quy năm 2024 tại Trường Đại học...",
      imageUrl: "/news/news2.jpg"
    },
    {
      id: 3,
      category: "Thông báo tuyển sinh trình độ Đại ...",
      title:
        "Thông báo tuyển sinh trình độ Đại học theo hình thức Vừa làm vừa học nhóm ngành đào tạo giáo viên đối t...",
      imageUrl: "/news/news3.jpg"
    },
    {
      id: 4,
      category: "Thông báo đăng ký hưởng chính ...",
      title:
        "Thông báo đăng ký hưởng chính sách theo Nghị định 116/2020/NĐ-CP đối với sinh viên đại học chính quy ...",
      imageUrl: "/news/news4.jpg"
    }
  ];
  return (
    <div>
      <HeroSection
        description="Hệ sinh thái học tập sáng tạo Hà Nội kết nối, thúc đẩy sự phát triển bền vững của mạng lưới giáo dục sáng tạo hướng tới xây dựng Hà Nội-Thành phố sáng tạo."
        title="HỆ SINH THÁI HỌC TẬP SÁNG TẠO HÀ NỘI"
        backgroundImageUrl="anhthudo.jpg"
      />
      <div className="mt-[2rem] text-[#414B5B]">
        <div className="page_breadcrumb">
          <div className="container mx-auto px-4">
            <h1 className="text-4xl font-bold">Tin tức</h1>
            <p className="mt-2 text-lg">Tin tức và sự kiện</p>

            {/* Phần dòng kẻ */}
            <div className="my-6 border-b border-gray-200"></div>

            {/* Phần tiêu đề Latest */}
            <h2 className="text-3xl font-bold">Hiển thị tin tức mới nhất</h2>

            {/* Phần tìm kiếm */}
            <div className="mt-4 flex items-center space-x-4">
              <input
                type="text"
                placeholder="Nhập tiêu đề ..."
                className="w-full max-w-[400px] rounded-full border border-gray-300 px-4 py-2 focus:outline-none"
              />
              <button className="rounded-full bg-blue-500 px-6 py-2 text-white">
                Tìm kiếm
              </button>
            </div>

            {/* Phần kết quả */}
            <div className="mt-4 text-gray-600">4 kết quả</div>
          </div>
        </div>
      </div>

      <div className="container mx-auto text-[#414B5B]">
        <div className="h-[1px] bg-gray-300"></div>
        <div className="mt-[20px] ">
          <h1 className="text-4xl font-bold">Tin tức</h1>
        </div>

        <div className="mt-[40px] grid grid-cols-1 gap-x-[30px] md:grid-cols-2 lg:grid-cols-4">
          {articles.map((article) => (
            <div
              key={article.id}
              className="border-t border-gray-300 pt-[20px]"
            >
              <img
                src={article.imageUrl}
                alt={article.title}
                className="mb-4 h-40 w-full object-cover"
              />
              <a
                href={`/user/news/${article.id}`}
                className=" font-medium text-[#414B5B] hover:text-blue-500"
              >
                {article.category}
              </a>
              <h3 className="mt-2  font-bold text-[#414B5B] transition-colors duration-300">
                {article.title}
              </h3>
            </div>
          ))}
        </div>

        <div className="mb-[4rem] mt-[50px] flex items-center justify-center">
          <button className="flex items-center space-x-2 rounded-full border border-gray-400 px-[20px] py-[7px] text-gray-500 transition-all duration-300 hover:border-gray-700 hover:text-gray-700">
            <span>Xem thêm</span>
            <span className="text-xl text-gray-400">
              <BsArrowDownShort />
            </span>
          </button>
        </div>
      </div>
    </div>
  );
}
