"use client";

import HeroSection from "@/components/ui/HeroSection";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import evaluationServices from "@/services/evaluationServices";
import { useEffect, useState } from "react";
import { toast } from "react-toastify";

export default function RatingPage() {
  // State for dropdown data
  const [educationLevels, setEducationLevels] = useState([]);
  const [schools, setSchools] = useState([]);
  const [periodRankings, setPeriodRankings] = useState([]);
  const [loading, setLoading] = useState(true);

  // State for selected values
  const [selectedEducationLevel, setSelectedEducationLevel] = useState("");
  const [selectedSchool, setSelectedSchool] = useState("");
  const [selectedPeriodRanking, setSelectedPeriodRanking] = useState("");

  // State for rating data
  const [ratingData, setRatingData] = useState<any[]>([]);
  const [ratingLoading, setRatingLoading] = useState(false);
  const [submittingItems, setSubmittingItems] = useState<Set<number>>(
    new Set()
  );

  // Helper function to create flattened data with topic in first column
  const createFlattenedData = (data: any[]) => {
    const flattened: any[] = [];

    data.forEach((topic: any) => {
      let topicItemCount = 0;

      topic.criteriaList?.forEach((criteria: any, criteriaIndex: number) => {
        // Calculate total rows for this criteria
        let criteriaRowCount = 0;
        criteria.criteriaIndexList?.forEach((criteriaIndexItem: any) => {
          const descriptions = criteriaIndexItem.periodIndexDescriptions || [];
          criteriaRowCount += descriptions.length;
        });

        let criteriaItemCount = 0;
        criteria.criteriaIndexList?.forEach((criteriaIndexItem: any, indexIndex: number) => {
          const descriptions = criteriaIndexItem.periodIndexDescriptions || [];
          descriptions.forEach((item: any, descIndex: number) => {
            flattened.push({
              ...item,
              topicName: topic.name,
              criteriaName: criteria.name,
              criteriaIndexName: criteriaIndexItem.name,
              globalIndex: 1,
              isFirstInCriteria: criteriaItemCount === 0,
              criteriaRowSpan: criteriaRowCount,
              isFirstInCriteriaIndex: descIndex === 0,
              criteriaIndexRowSpan: descriptions.length,
              isFirstInTopic: topicItemCount === 0,
              topicRowSpan: 0 // Will be calculated after
            });
            topicItemCount++;
            criteriaItemCount++;
          });
        });
      });

      // Update topicRowSpan for all items in this topic
      for (let i = flattened.length - topicItemCount; i < flattened.length; i++) {
        flattened[i].topicRowSpan = topicItemCount;
      }
    });

    return flattened;
  };

  // Fetch dropdown data on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // First, get education levels
        const eduResponse = await evaluationServices.getEducationLevels();
        const educationLevelsData = eduResponse.data.data || [];
        setEducationLevels(educationLevelsData);

        // Get the first education level's ID as ecosystemId for schools
        const firstEcosystemId =
          educationLevelsData.length > 0 ? educationLevelsData[0].id : null;

        setSelectedEducationLevel(firstEcosystemId?.toString() || "");
      } catch (error) {
        console.error("Error fetching dropdown data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Fetch rating data when school and period ranking are selected
  const fetchRatingData = async (schoolId: string, periodRankingId: string) => {
    try {
      setRatingLoading(true);
      const response = await evaluationServices.getGeneralEvaluationForms({
        periodRankingId: parseInt(periodRankingId),
        schoolId: parseInt(schoolId)
      });

      setRatingData(response.data.data || []);
    } catch (error) {
      console.error("Error fetching rating data:", error);
    } finally {
      setRatingLoading(false);
    }
  };

  useEffect(() => {
    if (selectedEducationLevel) {
      setRatingData([]);
      const fetchSchoolsAndPeriodRankings = async () => {
        try {
          // Fetch schools and period rankings
          const [schoolsResponse, periodResponse] = await Promise.all([
            evaluationServices.getSchools(selectedEducationLevel),
            evaluationServices.getPeriodRankings()
          ]);

          const schoolsData = schoolsResponse.data.data || [];
          const periodRankingsData = periodResponse.data.data || [];

          setSchools(schoolsData);
          setPeriodRankings(periodRankingsData);

          if (schoolsData.length > 0) {
            setSelectedSchool(schoolsData[0].id.toString());
          }
          if (periodRankingsData.length > 0) {
            setSelectedPeriodRanking(periodRankingsData[0].id.toString());
          }
        } catch (error) {
          console.error("Error fetching schools and period rankings:", error);
        }
      };

      fetchSchoolsAndPeriodRankings();
    }
  }, [selectedEducationLevel]);

  // Automatically fetch data when selections change
  useEffect(() => {
    if (selectedSchool && selectedPeriodRanking && !loading) {
      fetchRatingData(selectedSchool, selectedPeriodRanking);
    }
  }, [selectedSchool, selectedPeriodRanking, loading]);

  // Handle submit single evaluation
  const handleSubmitEvaluation = async (item: any, inputValue: number) => {
    if (isNaN(inputValue)) {
      return toast.error("Vui lòng nhập một giá trị hợp lệ!");
    }
    if (submittingItems.has(item.descriptionId)) return;

    try {
      setSubmittingItems((prev) => new Set(prev).add(item.descriptionId));

      const payload = {
        schoolId: Number(selectedSchool),
        periodRankingId: Number(selectedPeriodRanking),
        eduEcosystemId: Number(selectedEducationLevel),
        topicId: item.topicId,
        periodIndexDescriptions: [
          {
            id: item.id,
            description: item.description,
            topicId: item.topicId,
            criteriaId: item.criteriaId,
            criteriaIndexId: item.criteriaIndexId,
            descriptionId: item.descriptionId,
            periodRankingId: item.periodRankingId,
            schoolUnitsAssignedId: item.schoolUnitsAssignedId,
            mark: inputValue,
            minMark: item.minMark,
            maxMark: item.maxMark
          }
        ],
        fileRequests: []
      };

      await evaluationServices.submitGeneralEvaluationForms(payload);
      fetchRatingData(selectedSchool, selectedPeriodRanking);

      toast.success("Cập nhật điểm thành công!");
    } catch (error) {
      console.error("Error submitting evaluation:", error);
      toast.error("Có lỗi xảy ra khi cập nhật điểm. Vui lòng thử lại!");
    } finally {
      setSubmittingItems((prev) => {
        const newSet = new Set(prev);
        newSet.delete(item.descriptionId);
        return newSet;
      });
    }
  };

  return (
    <div>
      <HeroSection
        description="Hệ sinh thái học tập sáng tạo Hà Nội kết nối, thúc đẩy sự phát triển bền vững của mạng lưới giáo dục sáng tạo hướng tới xây dựng Hà Nội-Thành phố sáng tạo."
        title="HỆ SINH THÁI HỌC TẬP SÁNG TẠO HÀ NỘI"
        backgroundImageUrl="anhthudo.jpg"
      />

      {/* Content Section */}
      <div className="bg-white pt-8">
        <div className="container mx-auto px-4">
          {/* Header Section */}
          <div className="mb-8">
            <h1 className="text-4xl font-bold text-[#414B5B]">Xếp hạng</h1>
          </div>

          {/* Search Section */}
          <div className="mb-8">
            {/* Filter Dropdowns */}
            <div className="flex flex-col flex-wrap items-stretch gap-4 sm:flex-row sm:items-center">
              <Select
                value={selectedEducationLevel}
                onValueChange={setSelectedEducationLevel}
              >
                <SelectTrigger className="w-full sm:w-[260px]">
                  <SelectValue
                    placeholder={loading ? "Đang tải..." : "Tất cả cấp học"}
                  />
                </SelectTrigger>
                <SelectContent>
                  {educationLevels.map((level: any) => (
                    <SelectItem key={level.id} value={level.id.toString()}>
                      {level.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={selectedSchool} onValueChange={setSelectedSchool}>
                <SelectTrigger className="w-full sm:w-[260px]">
                  <SelectValue
                    placeholder={loading ? "Đang tải..." : "Tất cả trường học"}
                  />
                </SelectTrigger>
                <SelectContent>
                  {schools.map((school: any) => (
                    <SelectItem key={school.id} value={school.id.toString()}>
                      {school.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select
                value={selectedPeriodRanking}
                onValueChange={setSelectedPeriodRanking}
              >
                <SelectTrigger className="w-full sm:w-[260px]">
                  <SelectValue
                    placeholder={
                      loading ? "Đang tải..." : "Tất cả đợt xếp hạng"
                    }
                  />
                </SelectTrigger>
                <SelectContent>
                  {periodRankings.map((period: any) => (
                    <SelectItem key={period.id} value={period.id.toString()}>
                      {period.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      </div>

      {/* Rating Table Section - Excel Style */}
      <div className="bg-white pb-8">
        <div className="container mx-auto px-4">
          {/* Divider */}
          <div className="mb-8 h-[1px] bg-gray-300"></div>

          {/* Excel-style Rating Table */}
          <div className="overflow-hidden rounded-lg border">
            <div className="overflow-x-auto">
              <table className="w-full border-collapse border-2 border-black bg-white text-sm">
                <thead>
                  <tr className="bg-green-100">
                    <th className="w-1/6 border border-black px-2 py-2 text-center text-xs font-bold text-black sm:text-sm">
                      Thành tố
                    </th>
                    <th className="w-1/6 border border-black px-2 py-2 text-center text-xs font-bold text-black sm:text-sm">
                      Tiêu chí
                    </th>
                    <th className="w-1/6 border border-black px-2 py-2 text-center text-xs font-bold text-black sm:text-sm">
                      Chỉ số
                    </th>
                    <th className="w-1/12 border border-black bg-yellow-300 px-2 py-2 text-center text-xs font-bold text-black sm:text-sm">
                      211
                    </th>
                    <th className="border border-black px-2 py-2 text-center text-xs font-bold text-black sm:text-sm">
                      Mô tả
                      <br />
                      (Có thể tự chỉnh sửa hướng dẫn chi tiết mô tả)
                    </th>
                    <th className="w-1/12 border border-black px-2 py-2 text-center text-xs font-bold text-black sm:text-sm">
                      Thang đo
                    </th>
                    <th className="w-1/6 border border-black px-2 py-2 text-center text-xs font-bold text-black sm:text-sm">
                      Chọn điểm
                      <br />
                      (từ 1-5 với
                      <br />
                      từng chỉ báo)
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {ratingLoading ? (
                    <tr>
                      <td
                        colSpan={7}
                        className="border border-black px-4 py-8 text-center text-xs text-black sm:text-sm"
                      >
                        Đang tải dữ liệu...
                      </td>
                    </tr>
                  ) : ratingData.length === 0 ? (
                    <tr>
                      <td
                        colSpan={7}
                        className="border border-black px-4 py-8 text-center text-xs text-black sm:text-sm"
                      >
                        Không có dữ liệu đánh giá
                      </td>
                    </tr>
                  ) : (
                    createFlattenedData(ratingData).map(
                      (item: any, index: number) => {
                        return (
                          <tr key={index} className="hover:bg-gray-50">
                            {item.isFirstInTopic && (
                              <td
                                className="border border-black bg-blue-100 px-2 py-2 align-top text-xs font-medium text-black sm:text-sm"
                                rowSpan={item.topicRowSpan}
                              >
                                {item.topicName}
                              </td>
                            )}
                            {item.isFirstInCriteria && (
                              <td
                                className="border border-black bg-green-100 px-2 py-2 align-top text-left text-xs font-medium text-black sm:text-sm"
                                rowSpan={item.criteriaRowSpan}
                              >
                                {item.criteriaName}
                              </td>
                            )}
                            {item.isFirstInCriteriaIndex && (
                              <td
                                className="border border-black px-2 py-2 align-top text-left text-xs text-black sm:text-sm"
                                rowSpan={item.criteriaIndexRowSpan}
                              >
                                {item.criteriaIndexName}
                              </td>
                            )}
                            <td className="border border-black bg-yellow-100 px-2 py-2 align-top text-center text-xs text-black sm:text-sm">
                              {item.globalIndex}
                            </td>
                            <td className="border border-black px-2 py-2 align-top text-left text-xs text-black sm:text-sm">
                              {item.description}
                            </td>
                            <td className="border border-black px-2 py-2 align-top text-center text-xs text-black sm:text-sm">
                              {item.minMark} - {item.maxMark}
                            </td>
                            <td className="border border-black px-2 py-2 align-top text-center">
                              <div className="flex flex-col items-center justify-center gap-2 sm:flex-row">
                                <input
                                  id={`input-${item.descriptionId}`}
                                  type="number"
                                  min={item.minMark}
                                  max={item.maxMark}
                                  defaultValue={item.mark}
                                  className="w-12 rounded border border-gray-400 px-1 py-1 text-center text-xs sm:text-sm"
                                  onInput={(e) => {
                                    const target = e.target as HTMLInputElement;
                                    const value = parseInt(target.value);
                                    const min = item.minMark;
                                    const max = item.maxMark;

                                    if (value < min) {
                                      target.value = min.toString();
                                    } else if (value > max) {
                                      target.value = max.toString();
                                    }
                                  }}
                                  onBlur={(e) => {
                                    const target = e.target as HTMLInputElement;
                                    const value = parseInt(target.value);
                                    const min = item.minMark;
                                    const max = item.maxMark;

                                    if (isNaN(value) || value < min) {
                                      target.value = min.toString();
                                    } else if (value > max) {
                                      target.value = max.toString();
                                    }
                                  }}
                                />
                                <button
                                  onClick={() => {
                                    const inputElement =
                                      document.getElementById(
                                        `input-${item.descriptionId}`
                                      ) as HTMLInputElement;
                                    const value = parseInt(inputElement.value);
                                    handleSubmitEvaluation(item, value);
                                  }}
                                  disabled={submittingItems.has(
                                    item.descriptionId
                                  )}
                                  className="whitespace-nowrap rounded bg-blue-500 px-2 py-1 text-xs text-white hover:bg-blue-600 disabled:cursor-not-allowed disabled:bg-gray-400"
                                >
                                  {submittingItems.has(item.descriptionId)
                                    ? "..."
                                    : "Cập nhật"}
                                </button>
                              </div>
                            </td>
                          </tr>
                        );
                      }
                    )
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
