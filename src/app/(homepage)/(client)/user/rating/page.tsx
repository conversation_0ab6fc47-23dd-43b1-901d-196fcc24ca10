"use client";

import HeroSection from "@/components/ui/HeroSection";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import evaluationServices from "@/services/evaluationServices";
import { useEffect, useState } from "react";
import { toast } from "react-toastify";

export default function RatingPage() {
  // State for dropdown data
  const [educationLevels, setEducationLevels] = useState([]);
  const [schools, setSchools] = useState([]);
  const [periodRankings, setPeriodRankings] = useState([]);
  const [loading, setLoading] = useState(true);

  // State for selected values
  const [selectedEducationLevel, setSelectedEducationLevel] = useState("");
  const [selectedSchool, setSelectedSchool] = useState("");
  const [selectedPeriodRanking, setSelectedPeriodRanking] = useState("");

  // State for rating data
  const [ratingData, setRatingData] = useState<any[]>([]);
  const [ratingLoading, setRatingLoading] = useState(false);

  // Helper function to create flattened data with topic in first column
  const createFlattenedData = (data: any[]) => {
    const flattened: any[] = [];

    data.forEach((topic: any) => {
      let topicItemCount = 0;

      topic.criteriaList?.forEach((criteria: any, criteriaIndex: number) => {
        // Calculate total rows for this criteria
        let criteriaRowCount = 0;
        criteria.criteriaIndexList?.forEach((criteriaIndexItem: any) => {
          const descriptions = criteriaIndexItem.periodIndexDescriptions || [];
          criteriaRowCount += descriptions.length;
        });

        let criteriaItemCount = 0;
        criteria.criteriaIndexList?.forEach((criteriaIndexItem: any, indexIndex: number) => {
          const descriptions = criteriaIndexItem.periodIndexDescriptions || [];
          descriptions.forEach((item: any, descIndex: number) => {
            flattened.push({
              ...item,
              topicName: topic.name,
              criteriaName: criteria.name,
              criteriaIndexName: criteriaIndexItem.name,
              globalIndex: 1,
              isFirstInCriteria: criteriaItemCount === 0,
              criteriaRowSpan: criteriaRowCount,
              isFirstInCriteriaIndex: descIndex === 0,
              criteriaIndexRowSpan: descriptions.length,
              isFirstInTopic: topicItemCount === 0,
              topicRowSpan: 0, // Will be calculated after
              files: item.files || [] // Add files information
            });
            topicItemCount++;
            criteriaItemCount++;
          });
        });
      });

      // Update topicRowSpan for all items in this topic
      for (let i = flattened.length - topicItemCount; i < flattened.length; i++) {
        flattened[i].topicRowSpan = topicItemCount;
      }
    });

    return flattened;
  };

  // Fetch dropdown data on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // First, get education levels
        const eduResponse = await evaluationServices.getEducationLevels();
        const educationLevelsData = eduResponse.data.data || [];
        setEducationLevels(educationLevelsData);

        // Get the first education level's ID as ecosystemId for schools
        const firstEcosystemId =
          educationLevelsData.length > 0 ? educationLevelsData[0].id : null;

        setSelectedEducationLevel(firstEcosystemId?.toString() || "");
      } catch (error) {
        console.error("Error fetching dropdown data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Fetch rating data when school and period ranking are selected
  const fetchRatingData = async (schoolId: string, periodRankingId: string) => {
    try {
      setRatingLoading(true);
      const response = await evaluationServices.getGeneralEvaluationForms({
        periodRankingId: parseInt(periodRankingId),
        schoolId: parseInt(schoolId)
      });

      setRatingData(response.data.data || []);
    } catch (error) {
      console.error("Error fetching rating data:", error);
    } finally {
      setRatingLoading(false);
    }
  };

  useEffect(() => {
    if (selectedEducationLevel) {
      setRatingData([]);
      const fetchSchoolsAndPeriodRankings = async () => {
        try {
          // Fetch schools and period rankings
          const [schoolsResponse, periodResponse] = await Promise.all([
            evaluationServices.getSchools(selectedEducationLevel),
            evaluationServices.getPeriodRankings()
          ]);

          const schoolsData = schoolsResponse.data.data || [];
          const periodRankingsData = periodResponse.data.data || [];

          setSchools(schoolsData);
          setPeriodRankings(periodRankingsData);

          if (schoolsData.length > 0) {
            setSelectedSchool(schoolsData[0].id.toString());
          }
          if (periodRankingsData.length > 0) {
            setSelectedPeriodRanking(periodRankingsData[0].id.toString());
          }
        } catch (error) {
          console.error("Error fetching schools and period rankings:", error);
        }
      };

      fetchSchoolsAndPeriodRankings();
    }
  }, [selectedEducationLevel]);

  // Automatically fetch data when selections change
  useEffect(() => {
    if (selectedSchool && selectedPeriodRanking && !loading) {
      fetchRatingData(selectedSchool, selectedPeriodRanking);
    }
  }, [selectedSchool, selectedPeriodRanking, loading]);

  // Handle submit all evaluations
  const handleSubmitAllEvaluations = async () => {
    if (!selectedSchool || !selectedPeriodRanking) {
      return toast.error("Vui lòng chọn trường học và đợt xếp hạng!");
    }

    try {
      setRatingLoading(true);

      // Collect all input values
      const flattenedData = createFlattenedData(ratingData);
      const periodIndexDescriptions: any[] = [];

      flattenedData.forEach((item: any) => {
        const inputElement = document.getElementById(`input-${item.descriptionId}`) as HTMLInputElement;
        if (inputElement) {
          const value = parseInt(inputElement.value);
          if (!isNaN(value)) {
            periodIndexDescriptions.push({
              id: item.id,
              description: item.description,
              topicId: item.topicId,
              criteriaId: item.criteriaId,
              criteriaIndexId: item.criteriaIndexId,
              descriptionId: item.descriptionId,
              periodRankingId: item.periodRankingId,
              schoolUnitsReviewId: item.schoolUnitsReviewId,
              mark: value,
              minMark: item.minMark,
              maxMark: item.maxMark
            });
          }
        }
      });

      if (periodIndexDescriptions.length === 0) {
        return toast.error("Không có dữ liệu để cập nhật!");
      }

      // Submit all data in one API call
      const payload = {
        schoolId: Number(selectedSchool),
        periodRankingId: Number(selectedPeriodRanking),
        eduEcosystemId: Number(selectedEducationLevel),
        periodIndexDescriptions: periodIndexDescriptions,
        fileRequests: []
      };

      await evaluationServices.submitGeneralEvaluationForms(payload);

      // Refresh data
      await fetchRatingData(selectedSchool, selectedPeriodRanking);
      toast.success("Cập nhật tất cả điểm thành công!");
    } catch (error) {
      console.error("Error submitting all evaluations:", error);
      toast.error("Có lỗi xảy ra khi cập nhật điểm. Vui lòng thử lại!");
    } finally {
      setRatingLoading(false);
    }
  };

  return (
    <div>
      <HeroSection
        description="Hệ sinh thái học tập sáng tạo Hà Nội kết nối, thúc đẩy sự phát triển bền vững của mạng lưới giáo dục sáng tạo hướng tới xây dựng Hà Nội-Thành phố sáng tạo."
        title="HỆ SINH THÁI HỌC TẬP SÁNG TẠO HÀ NỘI"
        backgroundImageUrl="anhthudo.jpg"
      />

      {/* Content Section */}
      <div className="bg-white pt-8">
        <div className="container mx-auto px-4">
          {/* Header Section */}
          <div className="mb-8">
            <h1 className="text-4xl font-bold text-[#414B5B]">Xếp hạng</h1>
          </div>

          {/* Search Section */}
          <div className="mb-8">
            {/* Filter Dropdowns */}
            <div className="flex flex-col flex-wrap items-stretch gap-4 sm:flex-row sm:items-center">
              <Select
                value={selectedEducationLevel}
                onValueChange={setSelectedEducationLevel}
              >
                <SelectTrigger className="w-full sm:w-[260px]">
                  <SelectValue
                    placeholder={loading ? "Đang tải..." : "Cấp học"}
                  />
                </SelectTrigger>
                <SelectContent>
                  {educationLevels.map((level: any) => (
                    <SelectItem key={level.id} value={level.id.toString()}>
                      {level.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={selectedSchool} onValueChange={setSelectedSchool}>
                <SelectTrigger className="w-full sm:w-[260px]">
                  <SelectValue
                    placeholder={loading ? "Đang tải..." : "Trường học"}
                  />
                </SelectTrigger>
                <SelectContent>
                  {schools.map((school: any) => (
                    <SelectItem key={school.id} value={school.id.toString()}>
                      {school.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select
                value={selectedPeriodRanking}
                onValueChange={setSelectedPeriodRanking}
              >
                <SelectTrigger className="w-full sm:w-[260px]">
                  <SelectValue
                    placeholder={
                      loading ? "Đang tải..." : "Đợt xếp hạng"
                    }
                  />
                </SelectTrigger>
                <SelectContent>
                  {periodRankings.map((period: any) => (
                    <SelectItem key={period.id} value={period.id.toString()}>
                      {period.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              {ratingData.length > 0 && (
                <button
                  onClick={handleSubmitAllEvaluations}
                  disabled={ratingLoading}
                  className="w-full sm:w-auto rounded-full bg-green-500 px-6 py-2 text-white transition-colors hover:bg-green-600 disabled:bg-gray-400 disabled:cursor-not-allowed"
                >
                  {ratingLoading ? "Đang cập nhật..." : "Cập nhật tất cả"}
                </button>
              )}

            </div>
          </div>
        </div>
      </div>

      {/* Rating Table Section - Excel Style */}
      <div className="bg-white pb-8">
        <div className="container mx-auto px-4">
          {/* Divider */}
          <div className="mb-8 h-[1px] bg-gray-300"></div>

          {/* Excel-style Rating Table */}
          <div className="overflow-hidden rounded-lg border">
            <div className="overflow-x-auto">
              <table className="w-full border-collapse border-2 border-black bg-white text-sm">
                <thead>
                  <tr className="bg-green-100">
                    <th className="w-[150px] border border-black px-2 py-2 text-center text-xs font-bold text-black sm:text-sm">
                      Thành tố
                    </th>
                    <th className="w-[150px] border border-black px-2 py-2 text-center text-xs font-bold text-black sm:text-sm">
                      File đính kèm
                    </th>
                    <th className="border border-black px-2 py-2 text-center text-xs font-bold text-black sm:text-sm">
                      Tiêu chí
                    </th>
                    <th className="w-1/6 border border-black px-2 py-2 text-center text-xs font-bold text-black sm:text-sm">
                      Chỉ số
                    </th>
                    <th className="w-1/14 border border-black bg-yellow-300 px-2 py-2 text-center text-xs font-bold text-black sm:text-sm">
                      211
                    </th>
                    <th className="border border-black px-2 py-2 text-center text-xs font-bold text-black sm:text-sm">
                      Mô tả
                      <br />
                      <span className="hidden sm:block">(Có thể tự chỉnh sửa hướng dẫn chi tiết mô tả)</span>
                    </th>
                    <th className="w-[20px] border border-black px-2 py-2 text-center text-xs font-bold text-black sm:text-sm">
                      Thang đo
                    </th>
                    <th className="w-[20px] border border-black px-2 py-2 text-center text-xs font-bold text-black sm:text-sm">
                      Chọn điểm
                      <br />
                      <span >(từ 1-5 với từng chỉ báo)</span>
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {ratingLoading ? (
                    <tr>
                      <td
                        colSpan={8}
                        className="border border-black px-4 py-8 text-center text-xs text-black sm:text-sm"
                      >
                        Đang tải dữ liệu...
                      </td>
                    </tr>
                  ) : ratingData.length === 0 ? (
                    <tr>
                      <td
                        colSpan={8}
                        className="border border-black px-4 py-8 text-center text-xs text-black sm:text-sm"
                      >
                        Không có dữ liệu đánh giá
                      </td>
                    </tr>
                  ) : (
                    createFlattenedData(ratingData).map(
                      (item: any, index: number) => {
                        return (
                          <tr key={index} className="hover:bg-gray-50">
                            {item.isFirstInTopic && (
                              <td
                                className="border border-black bg-blue-100 px-2 py-2 align-top text-xs text-black sm:text-sm font-bold"
                                rowSpan={item.topicRowSpan}
                              >
                                {item.topicName}
                              </td>
                            )}
                            {item.isFirstInTopic && (
                              <td
                                className="border border-black bg-blue-50 px-2 py-2 align-top text-xs text-black sm:text-sm"
                                rowSpan={item.topicRowSpan}
                              >
                                {item.files && item.files.length > 0 ? (
                                  <div className="space-y-1">
                                    {item.files.map((file: any, index: number) => (
                                      <div key={index} className="text-xs">
                                        <a
                                          href={file.url || '#'}
                                          target="_blank"
                                          rel="noopener noreferrer"
                                          className="text-blue-600 hover:text-blue-800 underline break-all"
                                        >
                                          {file.name || `File ${index + 1}`}
                                        </a>
                                      </div>
                                    ))}
                                  </div>
                                ) : (
                                  <span className="text-gray-400 text-xs">Không có file</span>
                                )}
                              </td>
                            )}
                            {item.isFirstInCriteria && (
                              <td
                                className="border border-black bg-green-50 px-2 py-2 align-top text-left text-xs font-medium text-black sm:text-sm"
                                rowSpan={item.criteriaRowSpan}
                              >
                                {item.criteriaName}
                              </td>
                            )}
                            {item.isFirstInCriteriaIndex && (
                              <td
                                className="border border-black px-2 py-2 align-top text-left text-xs text-black sm:text-sm"
                                rowSpan={item.criteriaIndexRowSpan}
                              >
                                {item.criteriaIndexName}
                              </td>
                            )}
                            <td className="border border-black bg-yellow-100 px-2 py-2 align-top text-center text-xs text-black sm:text-sm">
                              {item.globalIndex}
                            </td>
                            <td className="border border-black px-2 py-2 align-top text-left text-xs text-black sm:text-sm">
                              {item.description}
                            </td>
                            <td className="border border-black px-2 py-2 align-top text-center text-xs text-black sm:text-sm">
                              {item.minMark} - {item.maxMark}
                            </td>
                            <td className="border border-black px-2 py-2 align-top text-center">
                              <input
                                id={`input-${item.descriptionId}`}
                                type="number"
                                min={item.minMark}
                                max={item.maxMark}
                                defaultValue={item.mark}
                                className="w-16 rounded border border-gray-400 px-1 py-1 text-center text-xs sm:text-sm"
                                onInput={(e) => {
                                  const target = e.target as HTMLInputElement;
                                  const value = parseInt(target.value);
                                  const min = item.minMark;
                                  const max = item.maxMark;

                                  if (value < min) {
                                    target.value = min.toString();
                                  } else if (value > max) {
                                    target.value = max.toString();
                                  }
                                }}
                                onBlur={(e) => {
                                  const target = e.target as HTMLInputElement;
                                  const value = parseInt(target.value);
                                  const min = item.minMark;
                                  const max = item.maxMark;

                                  if (isNaN(value) || value < min) {
                                    target.value = min.toString();
                                  } else if (value > max) {
                                    target.value = max.toString();
                                  }
                                }}
                              />
                            </td>
                          </tr>
                        );
                      }
                    )
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
