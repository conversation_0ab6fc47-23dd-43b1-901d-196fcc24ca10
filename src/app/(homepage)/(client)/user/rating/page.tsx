"use client";

import HeroSection from "@/components/ui/HeroSection";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import evaluationServices from "@/services/evaluationServices";
import { useEffect, useState } from "react";
import { toast } from "react-toastify";

export default function RatingPage() {
  // State for dropdown data
  const [educationLevels, setEducationLevels] = useState([]);
  const [schools, setSchools] = useState([]);
  const [periodRankings, setPeriodRankings] = useState([]);
  const [loading, setLoading] = useState(true);

  // State for selected values
  const [selectedEducationLevel, setSelectedEducationLevel] = useState("");
  const [selectedSchool, setSelectedSchool] = useState("");
  const [selectedPeriodRanking, setSelectedPeriodRanking] = useState("");


  // State for rating data
  const [ratingData, setRatingData] = useState<any[]>([]);
  const [ratingLoading, setRatingLoading] = useState(false);
  const [submittingItems, setSubmittingItems] = useState<Set<number>>(new Set());

  // Helper function to create flattened data with topic headers
  const createFlattenedData = (data: any[]) => {
    const flattened: any[] = [];

    data.forEach((topic: any) => {
      // Add topic header row
      flattened.push({
        isTopicHeader: true,
        topicName: topic.name,
        colSpan: 6
      });

      topic.criteriaList?.forEach((criteria: any) => {
        criteria.criteriaIndexList?.forEach((criteriaIndex: any) => {
          const descriptions = criteriaIndex.periodIndexDescriptions || [];
          descriptions.forEach((item: any, descIndex: number) => {
            flattened.push({
              ...item,
              topicName: topic.name,
              criteriaName: criteria.name,
              criteriaIndexName: criteriaIndex.name,
              globalIndex: 1,
              isFirstInCriteria: descIndex === 0,
              criteriaRowSpan: descriptions.length,
              isTopicHeader: false
            });
          });
        });
      });
    });

    return flattened;
  };

  // Fetch dropdown data on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // First, get education levels
        const eduResponse = await evaluationServices.getEducationLevels();
        const educationLevelsData = eduResponse.data.data || [];
        setEducationLevels(educationLevelsData);

        // Get the first education level's ID as ecosystemId for schools
        const firstEcosystemId = educationLevelsData.length > 0 ? educationLevelsData[0].id : null;

        // Then fetch schools and period rankings
        const [schoolsResponse, periodResponse] = await Promise.all([
          firstEcosystemId ? evaluationServices.getSchools(firstEcosystemId.toString()) : Promise.resolve({ data: { data: [] } }),
          evaluationServices.getPeriodRankings()
        ]);

        const schoolsData = schoolsResponse.data.data || [];
        const periodRankingsData = periodResponse.data.data || [];

        setSchools(schoolsData);
        setPeriodRankings(periodRankingsData);

        // Set default selected values
        if (educationLevelsData.length > 0) {
          setSelectedEducationLevel(educationLevelsData[0].id.toString());
        }
        if (schoolsData.length > 0) {
          setSelectedSchool(schoolsData[0].id.toString());
        }
        if (periodRankingsData.length > 0) {
          setSelectedPeriodRanking(periodRankingsData[0].id.toString());
        }

        // Automatically fetch rating data if we have both school and period ranking
        if (schoolsData.length > 0 && periodRankingsData.length > 0) {
          fetchRatingData(schoolsData[0].id.toString(), periodRankingsData[0].id.toString());
        }
      } catch (error) {
        console.error("Error fetching dropdown data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Fetch rating data when school and period ranking are selected
  const fetchRatingData  = async (schoolId: string, periodRankingId: string) => {
    try {
      setRatingLoading(true);
      const response = await evaluationServices.getGeneralEvaluationForms({
        periodRankingId: parseInt(periodRankingId),
        schoolId: parseInt(schoolId)
      });

      setRatingData(response.data.data || []);
    } catch (error) {
      console.error("Error fetching rating data:", error);
    } finally {
      setRatingLoading(false);
    }
  };

  // Handle search button click
  const handleSearch = () => {
    fetchRatingData(selectedSchool, selectedPeriodRanking);
  };

  // Handle submit single evaluation
  const handleSubmitEvaluation = async (item: any, inputValue: number) => {
    if (isNaN(inputValue)) {
     return toast.error("Vui lòng nhập một giá trị hợp lệ!");
    };
    if (submittingItems.has(item.descriptionId)) return;

    try {
      setSubmittingItems(prev => new Set(prev).add(item.descriptionId));

      const payload = {
        schoolId: Number(oldSelectedSchool),
        periodRankingId: Number(oldSelectedPeriodRanking),
        eduEcosystemId: Number(oldSelectedEducationLevel), // You might need to get this from user context
        topicId: item.topicId, // You might need to get this from user context
        periodIndexDescriptions: [{
          id: item.id,
          description: item.description,
          topicId: item.topicId,
          criteriaId: item.criteriaId,
          criteriaIndexId: item.criteriaIndexId,
          descriptionId: item.descriptionId,
          periodRankingId: item.periodRankingId,
          schoolUnitsAssignedId: item.schoolUnitsAssignedId,
          mark: inputValue,
          minMark: item.minMark,
          maxMark: item.maxMark
        }],
        fileRequests: []
      };

      await evaluationServices.submitGeneralEvaluationForms(payload);
      fetchRatingData(oldSelectedSchool, oldSelectedPeriodRanking);
      setSelectedEducationLevel(oldSelectedEducationLevel);
      setSelectedSchool(oldSelectedSchool);
      setSelectedPeriodRanking(oldSelectedPeriodRanking);

      toast.success("Cập nhật điểm thành công!");
    } catch (error) {
      console.error("Error submitting evaluation:", error);
      toast.error("Có lỗi xảy ra khi cập nhật điểm. Vui lòng thử lại!");
    } finally {
      setSubmittingItems(prev => {
        const newSet = new Set(prev);
        newSet.delete(item.descriptionId);
        return newSet;
      });
    }
  };


  return (
    <div>
      <HeroSection
        description="Hệ sinh thái học tập sáng tạo Hà Nội kết nối, thúc đẩy sự phát triển bền vững của mạng lưới giáo dục sáng tạo hướng tới xây dựng Hà Nội-Thành phố sáng tạo."
        title="HỆ SINH THÁI HỌC TẬP SÁNG TẠO HÀ NỘI"
        backgroundImageUrl="anhthudo.jpg"
      />

      {/* Content Section */}
      <div className="bg-white pt-8">
        <div className="container mx-auto px-4">
          {/* Header Section */}
          <div className="mb-8">
            <h1 className="text-4xl font-bold text-[#414B5B]">Xếp hạng</h1>
          </div>

          {/* Search Section */}
          <div className="mb-8">
            {/* Filter Dropdowns */}
            <div className="flex flex-col sm:flex-row flex-wrap items-stretch sm:items-center gap-4">
              <Select value={selectedEducationLevel} onValueChange={setSelectedEducationLevel}>
                <SelectTrigger className="w-full sm:w-[260px]">
                  <SelectValue placeholder={loading ? "Đang tải..." : "Tất cả cấp học"} />
                </SelectTrigger>
                <SelectContent>
                  {educationLevels.map((level: any) => (
                    <SelectItem key={level.id} value={level.id.toString()}>
                      {level.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={selectedSchool} onValueChange={setSelectedSchool}>
                <SelectTrigger className="w-full sm:w-[260px]">
                  <SelectValue placeholder={loading ? "Đang tải..." : "Tất cả trường học"} />
                </SelectTrigger>
                <SelectContent>
                  {schools.map((school: any) => (
                    <SelectItem key={school.id} value={school.id.toString()}>
                      {school.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={selectedPeriodRanking} onValueChange={setSelectedPeriodRanking}>
                <SelectTrigger className="w-full sm:w-[260px]">
                  <SelectValue placeholder={loading ? "Đang tải..." : "Tất cả đợt xếp hạng"} />
                </SelectTrigger>
                <SelectContent>
                  {periodRankings.map((period: any) => (
                    <SelectItem key={period.id} value={period.id.toString()}>
                      {period.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <button
                onClick={handleSearch}
                disabled={!selectedSchool || !selectedPeriodRanking || ratingLoading}
                className="w-full sm:w-auto rounded-full bg-blue-500 px-6 py-2 text-white transition-colors hover:bg-blue-600 disabled:bg-gray-400 disabled:cursor-not-allowed"
              >
                {ratingLoading ? "Đang tải..." : "Tìm kiếm"}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Rating Table Section - Excel Style */}
      <div className="bg-white pb-8">
        <div className="container mx-auto px-4">
          {/* Divider */}
          <div className="mb-8 h-[1px] bg-gray-300"></div>

          {/* Excel-style Rating Table */}
          <div className="overflow-hidden rounded-lg border">
            <div className="overflow-x-auto">
              <table className="w-full border-collapse border-2 border-black bg-white text-sm">
                <thead>
                  <tr className="bg-green-100">
                    <th className="border border-black px-2 py-2 text-center font-bold text-black text-xs sm:text-sm w-1/3 sm:w-1/6">
                      Tiêu chí
                    </th>
                    <th className="border border-black px-2 py-2 text-center font-bold text-black text-xs sm:text-sm w-1/3 sm:w-1/6">
                      Chỉ số
                    </th>
                    <th className="border border-black px-2 py-2 text-center font-bold text-black text-xs sm:text-sm bg-yellow-300 w-1/12">
                      211
                    </th>
                    <th className="border border-black px-2 py-2 text-center font-bold text-black text-xs sm:text-sm ">
                      Mô tả<br/>(Có thể tự chỉnh sửa hướng dẫn chi tiết mô tả)
                    </th>
                    <th className="border border-black px-2 py-2 text-center font-bold text-black text-xs sm:text-sm w-1/12">
                      Thang đo
                    </th>
                    <th className="border border-black px-2 py-2 text-center font-bold text-black text-xs sm:text-sm w-1/6 ">
                      Chọn điểm<br/>(từ 1-5 với<br/>từng chỉ báo)
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {ratingLoading ? (
                    <tr>
                      <td colSpan={6} className="border border-black px-4 py-8 text-center text-black text-xs sm:text-sm">
                        Đang tải dữ liệu...
                      </td>
                    </tr>
                  ) : ratingData.length === 0 ? (
                    <tr>
                      <td colSpan={6} className="border border-black px-4 py-8 text-center text-black text-xs sm:text-sm">
                        Vui lòng chọn trường học và đợt xếp hạng để xem dữ liệu đánh giá
                      </td>
                    </tr>
                  ) : (
                    createFlattenedData(ratingData).map((item: any, index: number) => {
                      if (item.isTopicHeader) {
                        return (
                          <tr key={`topic-${index}`} className="bg-blue-200">
                            <td
                              colSpan={6}
                              className="border border-black px-2 py-2 text-left text-black text-xs sm:text-sm font-bold"
                            >
                              {item.topicName}
                            </td>
                          </tr>
                        );
                      }

                      return (
                        <tr key={index} className="hover:bg-gray-50">
                          {item.isFirstInCriteria && (
                            <>
                              <td
                                className="border border-black px-2 py-2 text-left text-black text-xs sm:text-sm font-medium bg-green-100"
                                rowSpan={item.criteriaRowSpan}
                              >
                                {item.criteriaName}
                              </td>
                              <td
                                className="border border-black px-2 py-2 text-left text-black text-xs sm:text-sm"
                                rowSpan={item.criteriaRowSpan}
                              >
                                {item.criteriaIndexName}
                              </td>
                            </>
                          )}
                          <td className="border border-black px-2 py-2 text-center text-black text-xs sm:text-sm bg-yellow-100">
                            {item.globalIndex}
                          </td>
                          <td className="border border-black px-2 py-2 text-left text-black text-xs sm:text-sm">
                            {item.description}
                          </td>
                          <td className="border border-black px-2 py-2 text-center text-black text-xs sm:text-sm">
                            {item.minMark} - {item.maxMark}
                          </td>
                          <td className="border border-black px-2 py-2 text-center">
                            <div className="flex flex-col sm:flex-row items-center gap-2 justify-center">
                              <input
                                id={`input-${item.descriptionId}`}
                                type="number"
                                min={item.minMark}
                                max={item.maxMark}
                                defaultValue={item.mark}
                                className="w-12 px-1 py-1 border border-gray-400 rounded text-center text-xs sm:text-sm"
                                onInput={(e) => {
                                  const target = e.target as HTMLInputElement;
                                  const value = parseInt(target.value);
                                  const min = item.minMark;
                                  const max = item.maxMark;

                                  if (value < min) {
                                    target.value = min.toString();
                                  } else if (value > max) {
                                    target.value = max.toString();
                                  }
                                }}
                                onBlur={(e) => {
                                  const target = e.target as HTMLInputElement;
                                  const value = parseInt(target.value);
                                  const min = item.minMark;
                                  const max = item.maxMark;

                                  if (isNaN(value) || value < min) {
                                    target.value = min.toString();
                                  } else if (value > max) {
                                    target.value = max.toString();
                                  }
                                }}
                              />
                              <button
                                onClick={() => {
                                  const inputElement = document.getElementById(`input-${item.descriptionId}`) as HTMLInputElement;
                                  const value = parseInt(inputElement.value);
                                  handleSubmitEvaluation(item, value);
                                }}
                                disabled={submittingItems.has(item.descriptionId)}
                                className="px-2 py-1 bg-blue-500 text-white text-xs rounded hover:bg-blue-600 disabled:bg-gray-400 disabled:cursor-not-allowed whitespace-nowrap"
                              >
                                {submittingItems.has(item.descriptionId) ? "..." : "Cập nhật"}
                              </button>
                            </div>
                          </td>
                        </tr>
                      );
                    })
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
