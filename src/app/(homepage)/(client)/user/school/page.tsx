"use client";
import HeroSection from "@/components/ui/HeroSection";
import { useState } from "react";
import { BsArrowDownShort } from "react-icons/bs";
import { FaAward } from "react-icons/fa";
export default function Creactive() {
  const articles = [
    {
      id: 1,
      category: "Creative Field",
      title: "Trường THPT Cầu G<PERSON>ấ<PERSON>",
      date: "2024",
      imageUrl: "/rank/thpt1.jpg",
      icon: <FaAward className="text-2xl text-yellow-500" />,
      href: "https://truongthptcaugiay.edu.vn"
    },
    {
      id: 1,
      category: "Creative Field",
      title: "Trường THCS Hà Nội - Amsterdam",
      date: "2024",
      imageUrl: "/rank/thpt2.png",
      icon: <FaAward className="text-2xl text-yellow-500" />,
      href: "https://hn-ams.edu.vn"
    },
    {
      id: 1,
      category: "Creative Field",
      title: "Trường THCS dân lập <PERSON>",
      date: "2024",
      imageUrl: "/rank/thpt3.jpg",
      icon: <FaAward className="text-2xl text-yellow-500" />,
      href: "https://luongthevinh.com.vn/home/"
    },
    {
      id: 1,
      category: "Creative Field",
      title: "Trường THCS Đoàn Thị Điểm",
      date: "2024",
      imageUrl: "/rank/thpt4.jpg",
      icon: <FaAward className="text-2xl text-yellow-500" />,
      href: "https://thcs-doanthidiem.edu.vn/"
    },
    {
      id: 1,
      category: "Creative Field",
      title: "Trường THCS Chu Văn An",
      date: "2023",
      imageUrl: "/rank/thpt5.jpg",
      icon: <FaAward className="text-2xl text-yellow-500" />,
      href: "http://c2chuvanan.edu.vn/"
    },
    {
      id: 1,
      category: "Creative Field",
      title: "Trường THCS Cầu Giấy",
      date: "2023",
      imageUrl: "/rank/thpt6.jpg",
      icon: <FaAward className="text-2xl text-yellow-500" />,
      href: "https://thcscaugiay.edu.vn/"
    }
    // Add more articles here
  ];

  const [creativeField, setCreativeField] = useState("");
  const [date, setDate] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const handleRedirect = (href : any) => {
    window.open(href, "_blank");
  };
  const handleClearFilters = () => {
    setCreativeField("");
    setDate("");
    setSearchTerm("");
  };
  return (
    <div>
      <HeroSection
        description="Hệ sinh thái học tập sáng tạo Hà Nội kết nối, thúc đẩy sự phát triển bền vững của mạng lưới giáo dục sáng tạo hướng tới xây dựng Hà Nội-Thành phố sáng tạo."
        title="HỆ SINH THÁI HỌC TẬP SÁNG TẠO HÀ NỘI"
        backgroundImageUrl="anhthudo.jpg"
      />

      <div className="container mx-auto ">
        <div className="mx-auto mt-20 max-w-7xl bg-white p-6">
          {/* Title */}
          <h1 className="mb-4 text-3xl font-bold text-gray-900">
            Danh sách các trường trong hệ sinh thái
          </h1>

          {/* Description */}
          <p className="mb-6 text-sm text-gray-700">
            Hệ sinh thái học tập, sáng tạo là một mạng lưới các trường học từ
            mầm non đến trung học, nơi học sinh được phát triển toàn diện về tri
            thức, kỹ năng và tư duy sáng tạo. Mỗi cấp học trong hệ sinh thái đều
            có chương trình giáo dục hiện đại, phương pháp giảng dạy tiên tiến,
            tạo môi trường học tập linh hoạt và phát triển cá nhân. Hệ sinh thái
            chú trọng việc kết nối kiến thức, khám phá tiềm năng và nuôi dưỡng
            tinh thần sáng tạo, giúp học sinh chuẩn bị tốt cho tương lai.
          </p>

          {/* Filters Section */}
          <div className="mb-6 flex flex-wrap gap-4">
            {/* Creative Field Filter */}
            <div className="flex-1">
              <label
                htmlFor="creative-field"
                className="mb-2 block text-sm text-gray-600"
              >
                Tìm theo quận, huyện
              </label>
              <select
                id="creative-field"
                value={creativeField}
                onChange={(e) => setCreativeField(e.target.value)}
                className="w-full rounded-lg border border-gray-300 p-2 focus:ring focus:ring-blue-300"
              >
                <option value="">Xin mời chọn</option>
                <option value="bd">Ba Đình</option>
                <option value="cg">Cầu Giấy</option>
                <option value="dd">Đống Đa</option>
                <option value="hbt">Hai Bà Trưng</option>
                <option value="hk">Hoàn Kiếm</option>
                <option value="tx">Thanh Xuân</option>
                {/* Add options for Creative Field */}
              </select>
            </div>

            {/* Date Filter */}
            <div className="flex-1">
              <label
                htmlFor="date"
                className="mb-2 block text-sm text-gray-600"
              >
                Chọn cấp học
              </label>
              <select
                id="date"
                value={date}
                onChange={(e) => setDate(e.target.value)}
                className="w-full rounded-lg border border-gray-300 p-2 focus:ring focus:ring-blue-300"
              >
                <option value="">Xin mời chọn</option>
                <option value="1">Tiểu học</option>
                <option value="2">Trung học cơ sở</option>
                <option value="3">Trung học phổ thông</option>
                <option value="4">Đại học</option>
                {/* Add options for Date */}
              </select>
            </div>
          </div>

          {/* Search and Clear Filters */}
          <div className="mb-6 flex items-center justify-between">
            {/* Clear all filters button */}
            <button
              onClick={handleClearFilters}
              className="text-sm text-blue-600 hover:underline"
            >
              Xóa bộ lọc
            </button>

            {/* Search bar and button */}
            <div className="flex gap-2">
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="flex-1 rounded-lg border border-gray-300 p-2 focus:ring focus:ring-blue-300"
                placeholder="Nhập tên trường ..."
              />
              <button className="rounded-lg bg-blue-600 p-2 text-white hover:bg-blue-700">
                Tìm kiếm
              </button>
            </div>
          </div>

          {/* Results Info and Sorting */}
          <div className="mb-4 flex items-center justify-between text-sm text-gray-600">
            <p>̉6 kết quả</p>
            <div className="flex gap-4">
              <button className="hover:underline">Sắp xếp: Mới nhất</button>
              <div className="flex items-center gap-2">
                <button className="rounded-lg bg-gray-200 p-2">
                  Xem dưới dạng danh sách
                </button>
                <button className="rounded-lg bg-gray-200 p-2">
                  Xem dưới dạng lưới
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto">
        <div className="h-[1px] bg-gray-300"></div>

        <div className="mt-[40px] grid grid-cols-1 gap-[30px] md:grid-cols-2 lg:grid-cols-4">
          {articles.map((article) => (
            <div
              key={article.id}
              className="relative cursor-pointer overflow-hidden border-t border-gray-300  bg-white pt-4"
              onClick={() => handleRedirect(article.href)}
            >
              <div className="relative">
                {/* Icon positioned at the top-left */}
                <div className="absolute left-3 top-3 rounded-full bg-white p-2 shadow-lg">
                  {article.icon}
                </div>
                <img
                  src={article.imageUrl}
                  alt={article.title}
                  className="h-48 w-full object-cover"
                />
              </div>
              <div className="py-4">
                {/* Title */}
                <h3 className="text-lg font-bold text-gray-800">
                  {article.title}
                </h3>
                {/* Date */}
                <p className="text-sm text-gray-500">{article.date}</p>
              </div>
            </div>
          ))}
        </div>

        <div className="mb-[4rem] mt-[50px] flex items-center justify-center">
          <button className="flex items-center space-x-2 rounded-full border border-gray-400 px-[20px] py-[7px] text-gray-500 transition-all duration-300 hover:border-gray-700 hover:text-gray-700">
            <span>Xem thêm</span>
            <span className="text-xl text-gray-400">
              <BsArrowDownShort />
            </span>
          </button>
        </div>
      </div>
    </div>
  );
}
