import Image from "next/image";

import HeroSection from "@/components/ui/HeroSection";

export default function CreativeLearningContextPage() {

  return (
    <section className="space-y-10 pb-[3rem]">
      <HeroSection
        description="Hệ sinh thái học tập sáng tạo Hà Nội kết nối, thúc đẩy sự phát triển bền vững của mạng lưới giáo dục sáng tạo hướng tới xây dựng Hà Nội - Thành phố sáng tạo."
        title="HỆ SINH THÁI HỌC TẬP SÁNG TẠO HÀ NỘI "
        backgroundImageUrl="bannermain_1.jpg"
      />
      <div className="container mx-auto text-[#414B5B]">
        <h3 className="text-[36px] font-bold text-center"><PERSON><PERSON><PERSON> cả<PERSON> học tập sáng tạo</h3>
        <div className="space-y-[1rem]">
          <div className="">
            <h4 className="text-[20px] font-bold">1. <PERSON><PERSON><PERSON><PERSON> ni<PERSON></h4>
            <p className="text-pretty text-[20px] ">
              <PERSON><PERSON><PERSON> cảnh học tập có thể hiểu là tập hợp các hoàn cảnh, trường hợp, tình huống liên quan tới việc học tập gắn với nhu cầu [5 ; 128]. Học tập lí thuyết, học tập khái niệm, học tập kỹ năng, thực hành, bài tập tình huống thực tế, bài tập nhóm, se-mi-na, trình diễn, tiểu luận, phản biện, nhận xét, tra cứu tài liệu,....
              <br />
              Bối cảnh quan hệ với nội dung và người học chặt chẽ. Khái niệm bối cảnh có thể hiểu rộng hơn chính là các tương tác [5;128] hoặc như là khả năng tương tác/liên kết các sự kiện và điều kiện khác nhau đối với đối tượng học tập [3]. Việc thiết kế bối cảnh học tập có thể thực hiện theo các mô hình lí thuyết khác nhau, như lí thuyết mạng tác nhân (actor network) [6 ], hoặc theo ngôn ngữ mẫu tình huống (pattern language) (Alexander, 1977). Các hệ thống nói trên sẽ được kết nối với nhau một cách phức hợp thành hệ thống lớn hơn, ví dụ hệ thống bộ môn sẽ gồm: các nhóm giáo viên - các nhóm học sinh - các bài giảng bộ môn - các nội dung do học sinh tạo ra thông qua tiểu luận, bài tập nhóm - hệ e-learning 1.0 và 2.0 của bộ môn - tri thức mở trên Internet. Các hệ thống cũng có thể kết nối theo mạng lưới với tính tự do, linh hoạt và bình đẳng.
            </p>
          </div>
          <div className="">
            <h4 className="text-[20px] font-bold">2. Mô tả</h4>
            <p className="text-pretty text-[20px] ">
              Bối cảnh học tập có thể bao gồm: hoàn cảnh học tập, phương pháp học tập, tình huống học tập, dự án học tập…nhằm hướng tới kết quả học tập nhất định. Có thể mô tả cụ thể như sau:
            </p>
            <p className="text-[20px] font-bold">
              *Hoàn cảnh học tập sáng tạo
            </p>
            <div className="text-[20px] ">
              <div className="text-pretty text-[20px] ">
                <p >
                  Theo “Từ điển tiếng Việt”: “Hoàn cảnh là toàn thể nói chung những nhân tố khách quan bên ngoài có tác động đến sự sinh sống, sự hoạt động của con người, sự việc xảy ra hoặc diễn biến của sự việc nào đó. Hoàn cảnh có thể ảnh hưởng đến các quyết định và hành động của một cá nhân hoặc tổ chức. Hoàn cảnh học tập sáng tạo là đưa các nhân tố sáng tạo tác động đến quá trình học tập của học sinh nhằm phát triển tư duy và năng lực sáng tạo cho học sinh.
                </p>
                <p>Hoàn cảnh học tập sáng tạo có thể bao gồm bối cảnh văn hóa địa phương, di sản, làng nghề, ứng dụng công nghệ KHKT, kết nối doanh nghiệp, tổ chức chính trị xã hội, tổ chức xã hội, gia đình và các chủ thể khác của hệ sinh thái</p>
              </div>
            </div>
            <p className="text-[20px] font-bold">
              *Phương pháp học tập sáng tạo
            </p>
            <div className="text-[20px] ">
              <div className="text-pretty text-[20px] ">
                <p >
                  Là cách thức xây dựng một lộ trình học cụ thể, sáng tạo để giúp mỗi người hiểu và nắm rõ nội dung bài giảng hơn. Từ đó, hướng tới đạt được những mục tiêu và kết quả tốt trong học tập.
                </p>
                <p>Các phương pháp học tập sáng tạo tiêu biểu:</p>
                <ul className="list-disc">
                  <li>Phương pháp Brainstorming (Công não): Đây là một phương pháp được sử dụng để phát triển nhiều giải pháp sáng tạo cho một vấn đề. Phương pháp này hoạt động bằng cách tập trung vào một vấn đề và rút ra nhiều câu trả lời cơ bản cho vấn đề đó. Các quan điểm về vấn đề trước hết được thể hiện một cách rất phóng khoáng và ngẫu nhiên theo dòng suy nghĩ càng nhiều càng tốt. Chúng có thể rất rộng và sâu và không giới hạn ở những khía cạnh nhỏ nhất của vấn đề. Trong Brainstorming, vấn đề được đào bới từ nhiều góc độ và góc nhìn khác nhau. Cuối cùng, các ý kiến ​​sẽ được nhóm lại và đánh giá. Phương pháp này có thể được thực hiện bởi một đến nhiều người. Số lượng người tham gia càng nhiều thì lời giải sẽ càng nhanh chóng hoặc toàn diện hơn nhờ vào nhiều góc nhìn khác nhau theo trình độ và khả năng của mỗi cá nhân.</li>
                  <li>Phương pháp Mindmap (Bản đồ tư duy): Mindmap là phương pháp trình bày ý tưởng bằng hình ảnh, giúp não bộ phát huy tối đa khả năng ghi nhớ, giúp người tư duy tìm ra phương pháp hiệu quả nhất để giải quyết vấn đề một cách tối ưu. Tony Buzan được coi là “Cha Đẻ” Của Mindmap Hiện Đại. Đây là một cách để ghi nhớ chi tiết, tổng hợp hoặc chia nhỏ vấn đề thành một số loại sơ đồ phân nhánh. Không giống như máy tính, ngoài khả năng ghi nhớ các mẫu tuyến tính, não bộ con người còn có khả năng giao tiếp và liên hệ các dữ liệu với nhau. Phương pháp này khai thác cả hai khả năng này của não bộ.</li>
                  <li>Phương pháp sáu chiếc mũ tư duy: Sáu chiếc mũ tư duy là một phương pháp được phát minh bởi Tiến sĩ Edward de Bono vào năm 1980. Năm 1985, Tiến sĩ Edward de Bono đã mô tả chi tiết nó trong cuốn sách “Sáu chiếc mũ tư duy” của ông. Phương pháp Sáu Chiếc Nón Tư Duy là một phương pháp mạnh mẽ và độc đáo, hướng mọi người tập trung vào cùng một vấn đề từ cùng một góc độ, do đó loại bỏ hoàn toàn các tranh luận từ các góc độ khác nhau. Ngoài ra, nó giúp các cá nhân có được nhiều góc nhìn về một đối tượng sẽ khác nhiều so với những gì một người bình thường có thể nhìn thấy. Đây là một khuôn mẫu cho tư duy và nó có thể được kết hợp vào tư duy định hướng.</li>
                </ul>
              </div>
            </div>
            <p className="text-[20px] font-bold">
              *Tình huống học tập sáng tạo
            </p>
            <div className="text-[20px] ">
              <div className="text-pretty text-[20px]">
                <p >
                  Tình huống học tập là <span className="underline"> tổ hợp những mối quan hệ, sự tương tác giữa học sinh (chủ thể nhận thức) với đối tượng nhận thức (nội dung tri thức) được hình thành trong quá trình học tập cụ thể và được diễn ra trong một không gian, thời gian nhất định.</span> Tình huống học tập hay chính là <span className="font-bold">môi trường học tập</span>. Quá trình học tập là một quá trình giải quyết các tình huống. Tình huống học tập nhấn mạnh đến hoạt động học tập của người học, biến người học trở thành chủ thể của hoạt động học nhằm đạt được chất lượng và hiệu quả dạy học cao nhất, thỏa mãn mục tiêu giáo dục đặt ra.  Tình huống học tập sáng tạo là việc đặt ra yêu cầu, mục tiêu sáng tạo trong các tình huống qua đó rèn luyện tư duy sáng tạo, hình thành năng lực sáng tạo cho học sinh.
                </p>
              </div>
            </div>
            <p className="text-[20px] font-bold">
              *Dự án học tập sáng tạo
            </p>
            <div className="text-[20px] ">
              <div className="text-pretty text-[20px] ">
                <p >
                  Dự án học tập sáng tạo là những thiết kế có chủ đích để thu hút tư duy của học sinh xung quanh các kỳ vọng, nội dụng và các kĩ năng mà học sinh cần biết, từ đó phát triển tư duy sáng tạo, hình thành năng lực sáng tạo cho học sinh.
                  <br />
                  Có thể phân loại dự án học tập sáng tạo theo một số tiêu chí sau:
                </p>
              </div>
              <div className="">
                <span className="font-bold">a) Phân loại theo quỹ thời gian thực hiện dự án:</span>
                <ul>
                  <li>Dự án nhỏ: thực hiện trong một số giờ học, có thể từ 2 đến 6 giờ.</li>
                  <li>Dự án trung bình: thực hiện trong một số ngày (còn gọi là ngày dự án) nhưng giới hạn trong một tuần hoặc 40 giờ học.</li>
                  <li>Dự án lớn: được thực hiện với quỹ thời gian lớn, tối thiểu là một tuần, có thể kéo dài trong nhiều tuần.</li>
                </ul>
              </div>
              <div className="">
                <span className="font-bold">b) Phân loại theo nhiệm vụ:</span>
                <ul>
                  <li>Dự án tìm hiểu: là dự án khảo sát thực trạng đối tượng.</li>
                  <li>Dự án nghiên cứu: nhằm giải quyết các vấn đề, giải thích các hiện tượng, quá trình.</li>
                  <li>Dự án kiến tạo: tập trung vào việc tạo ra các sản phẩm vật chất hoặc thực hiện các hành động thực tiễn, nhằm thực hiện những nhiệm vụ như trang trí, trưng bài, biểu diễn, sáng tác.</li>
                </ul>
              </div>
              <div className="">
                <span className="font-bold">c) Phân loại theo mức độ phức hợp của nội dung học tập:</span>
                <ul>
                  <li>Dự án mang tính thực hành: là dự án có trong tâm là việc thực hiện một nhiệm vụ thực hành mang tính phức hợp trên cơ sở vận dụng kiến thức, kỹ năng cơ bản đã học nhằm tạo ra một sản phẩm vật chất</li>
                  <li>Dự án mang tính tích hợp: là dự án mang nội dung tích hợp nhiều nội dung hoạt động như tìm hiểu thực tiễn, nghiên cứu lí thuyết, giải quyết vấn đề, thực hiện các hoạt động thực hành, thực tiễn</li>
                  <li>Ngoài các cách phân loại trên, còn có thể phân loại theo chuyên môn (dự án môn học, dự án liên môn, dự án ngoài môn học); theo sự tham gia của người học (dự án cá nhân, dự án nhóm, dự án lớp…).</li>
                </ul>
              </div>
            </div>
            <p className="text-[20px] font-bold">
              *Kết quả học tập sáng tạo
            </p>
            <div className="text-[20px] ">
              <div className="text-pretty text-[20px] ">
                <p >
                  Kết quả học tập <span className="text-[#FF0000] font-[500] underline">phản ánh sự hiểu biết hoặc khả năng thực hành của người học sau một quá trình học tập.</span>Trong khi kết quả học tập thường
                  được thể hiện dưới dạng kiến thức, kỹ năng hoặc thái độ, thì hệ thống giáo dục ở Việt Nam chủ yếu được đo lường bằng khối lượng lý thuyết trẻ được học. Việt Nam đang chuyển đổi chương trình giảng dạy và sách giáo khoa phổ thông theo hướng chuyển từ lý thuyết sang thực hành nhiều hơn, nên cần một bộ sách tiêu chuẩn có đủ các tiêu chí mới và phù hợp với quốc tế để đánh giá kết quả học tập.
                </p>
                <p>
                  <span className="font-bold">Kết quả học tập</span> có sự tác động trở lại đối với chủ thể, nội dung học tập và các yếu tố thuộc bối cảnh học tập
                </p>
              </div>
            </div>
            <div className="flex items-center justify-center">
              <div className="h-[568px]">
                <Image src="/explore-creative-learning/img-4.svg" className="object-cover" width={627} height={568} alt="image" />
              </div>
            </div>
          </div>
          <div className="">
            <h4 className="text-[20px] font-bold">3. Yêu cầu tính sáng tạo trong thành tố</h4>
            <ul className="list-disc pl-[2rem] text-[20px]">
              <li>
                Hệ thống hoàn cảnh, phương pháp, ngữ cảnh, tình huống, dự án học tập phong phú, đa dạng được thiết kế sáng tạo hướng tới mục tiêu cụ thể đối với mỗi cấp học/đối tượng/môn học.
              </li>
              <li>
                Kết quả học tập hướng tới hình thành năng lực sáng tạo cho chủ thể học tập.
              </li>
            </ul>
          </div>
          <div className="">
            <h4 className="text-[20px] font-bold">4. Các cấp độ của tri thức học tập</h4>
            <div className="overflow-x-auto text-[16px] font-semibold">
              <table className="min-w-full table-auto border-collapse border border-black">
                <thead>
                  <tr>
                    <th className="border font-bold border-black bg-[#FFE498] px-4 py-2 w-[30%]">CN HTST</th>
                    <th className="border border-black bg-[#92D050] px-4 py-2">Cấp độ nhà trường</th>
                    <th className="border border-black bg-[#92D050] px-4 py-2">Cấp độ Quận/Huyện</th>
                    <th className="border border-black bg-[#92D050] px-4 py-2">Cấp độ Tỉnh/Thành phố</th>
                  </tr>
                </thead>
                <tbody>
                  <tr className="font-medium">
                    <td className="border border-black bg-[#FFE498] px-4 py-2 font-bold text-center">Hoàn cảnh học tập sáng tạo</td>
                    <td className="border border-black px-4 py-2">
                      <p>Bối cảnh văn hóa địa phương, di sản, làng nghề, ứng dụng công nghệ KHKT, kết nối doanh nghiệp, tổ chức chính trị xã hội, tổ chức xã hội, gia đình và các chủ thể khác của hệ sinh thái</p>
                    </td>
                    <td className="border border-black px-4 py-2">
                      <p>Bối cảnh địa phương</p>
                    </td>
                    <td className="border border-black px-4 py-2">
                      <p>Bối cảnh địa phương</p>
                    </td>
                  </tr>
                  <tr>
                    <td className="border font-bold border-black bg-[#FFE498] px-4 py-2 text-center ">Phương pháp học tập sáng tạo</td>
                    <td className="border border-black px-4 py-2">
                      <ol className="list-decimal font-[500] list-inside">
                        <li>Phương pháp Brainstorming (Công não)</li>
                        <li>Phương pháp Mindmap (Bản đồ tư duy)</li>
                        <li>Phương pháp sáu chiếc mũ tư duy</li>
                        <li>Các phương pháp khác:……</li>
                      </ol>
                    </td>
                    <td className="border font-[500] border-black px-4 py-2">
                      <p>Chuyên đề giáo dục</p>
                    </td>
                    <td className="border font-[500] border-black px-4 py-2">
                      <p>Chuyên đề giáo dục</p>
                    </td>
                  </tr>
                  <tr className="font-medium">
                    <td className="border border-black bg-[#FFE498] px-4 py-2 font-bold text-center">Tình huống học tập sáng tạo</td>
                    <td className="border  border-black px-4 py-2">
                      <p>Xây dựng các tình huống học tập qua các hoạt động: Học qua chơi; Học qua thực hành, trải nghiệm; Học qua tình huống; Học thực tế…</p>
                    </td>
                    <td className="border  border-black px-4 py-2">
                      <p>Bối cảnh đặc thù (Không gian,thời gian)</p>
                    </td>
                    <td className="border  border-black px-4 py-2">
                      <p>Bối cảnh đặc thù (Không gian,thời gian)</p>
                    </td>
                  </tr>
                  <tr>
                    <td className="border font-bold border-black bg-[#FFE498] px-4 py-2 text-center ">Dự án học tập sáng tạo</td>
                    <td className="border font-[500] border-black px-4 py-2">
                      <ol className="list-decimal list-inside">
                        <li>Theo quỹ thời gian thực hiện dự án: Dự án nhỏ, Dự án trung bình, Dự án lớn</li>
                        <li>Theo nhiệm vụ: Dự án tìm hiểu, dự án nghiên cứu, dự án kiến tạo</li>
                        <li>Theo mức độ phức hợp của nội dung học tập: dự án mang tính thực hành, dự án mang tính tích hợp</li>
                        <li>Theo chuyên môn: dự án môn học, dự án liên môn, dự án ngoài môn học</li>
                        <li>Theo sự tham gia của người học: dự án cá nhân, dự án nhóm, dự án lớp…</li>
                      </ol>
                    </td>
                    <td className="border font-[500] border-black px-4 py-2">
                      <p>Dự án giáo dục</p>
                    </td>
                    <td className="border font-[500] border-black px-4 py-2">
                      <p>Dự án giáo dục</p>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
