"use client";

import { formatDateForDisplay } from "@/app/(homepage)/social-network/utils/dateUtils";
import { useRouter } from "next/navigation";
import { useCallback, useEffect, useRef, useState } from "react";
import {
  FaBell,
  FaBookmark,
  FaComment,
  FaEllipsisH,
  FaHeart,
  FaShare,
  FaTrash,
  FaUserPlus
} from "react-icons/fa";
import { IoMdCheckmarkCircleOutline } from "react-icons/io";
import socialNetworkServices from "../services/social-network/socialNetworkServices";
import PostDetailModal from "./common/PostDetailModal";

// Cấu trúc dữ liệu thông báo đã được chuyển đổi để sử dụng trong component
interface NotificationDropdownProps {
  unreadCount: number;
  onUnreadCountChange: () => void;
}

// State cho modal chi tiết bài đăng
interface PostModalState {
  isOpen: boolean;
  postId: number | null;
}

interface Notification {
  notificationsId: number;
  recipientId: number;
  senderId: number;
  type: string;
  objectId: number;
  createdDate: string;
  content: string;
  email: string | null;
  read: boolean;
}

const NotificationDropdown = ({ unreadCount, onUnreadCountChange }: NotificationDropdownProps) => {
  const router = useRouter();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [loadingMore, setLoadingMore] = useState<boolean>(false);
  const [page, setPage] = useState<number>(0);
  const [pageSize] = useState<number>(5);
  const [lastPage, setLastPage] = useState<boolean>(true);
  const notificationListRef = useRef<HTMLDivElement>(null);

  // State cho modal chi tiết bài đăng
  const [showPostModal, setShowPostModal] = useState<boolean>(false);
  const [selectedPostId, setSelectedPostId] = useState<number | null>(null);

  // Hàm lấy danh sách thông báo
  const fetchNotifications = useCallback(
    async (pageNumber = 0, isLoadMore = false) => {
      try {
        setLoading(!isLoadMore);
        if (isLoadMore) {
          setLoadingMore(true);
        }

        const response = await socialNetworkServices.getNotification({
          page: pageNumber,
          size: pageSize,
          sort: "DESC",
          isRead: undefined,
        });

        const data = response?.data?.data?.content || [];
        
        if (isLoadMore) {
          setNotifications((prev) => [...prev, ...data]);
        } else {
          setNotifications(data);
        }

        // Kiểm tra xem còn thông báo để tải thêm không
        setLastPage(response?.data?.data?.last);
      } catch (error) {
        console.error("Lỗi khi lấy thông báo:", error);
      } finally {
        setLoading(false);
        setLoadingMore(false);
      }
    },
    [pageSize]
  );

  // Hàm lấy số lượng thông báo chưa đọc - đã được chuyển lên component cha

  // Hàm đánh dấu thông báo đã đọc
  const markAsRead = async (notificationId: number , notificationType: string) => {
    const type = notificationType.toUpperCase();
    try {
      if (
        type === "REPLY_COMMENT" ||
        type === "LIKE_COMMENT" ||
        type === "LIKE_POST" ||
        type === "COMMENT" ||
        type === "SAVED_POST" ||
        type === "SHARE_POST"
      ) {
        await socialNetworkServices.updatePostNotification(notificationId);
      } else if (type === 'ADD_FRIEND' || type === 'ACCEPT_FRIEND') {
        await socialNetworkServices.updateFriendNotification(notificationId);
      }

      // Cập nhật state
      setNotifications((prev) =>
        prev.map((item) =>
          item.notificationsId === notificationId ? { ...item, read: true } : item
        )
      );

      onUnreadCountChange();
    } catch (error) {
      console.error("Lỗi khi đánh dấu thông báo đã đọc:", error);
    }
  };

  // Hàm xóa thông báo
  const deleteNotification = async (notificationId: number) => {
    try {
      await socialNetworkServices.deleteNotification(notificationId);

      // Kiểm tra xem thông báo bị xóa có phải là chưa đọc không
      const deletedNotification = notifications.find(
        (item) => item.notificationsId === notificationId
      );

      // Cập nhật state
      setNotifications((prev) =>
        prev.filter((item) => item.notificationsId !== notificationId)
      );

      // Nếu thông báo bị xóa là chưa đọc, giảm số lượng thông báo chưa đọc và thông báo cho component cha
      if (!deletedNotification?.read) {
        onUnreadCountChange();
      }
    } catch (error) {
      console.error("Lỗi khi xóa thông báo:", error);
    }
  };

  // Hàm đánh dấu tất cả thông báo đã đọc
  const markAllAsRead = async () => {
    try {
      await socialNetworkServices.updateAllNotification();
      // Đánh dấu tất cả thông báo đã đọc trong state
      setNotifications((prev) =>
        prev.map((item) => ({ ...item, read: true }))
      );

      // Cập nhật số lượng thông báo chưa đọc và thông báo cho component cha
      onUnreadCountChange();
    } catch (error) {
      console.error("Lỗi khi đánh dấu tất cả thông báo đã đọc:", error);
    }
  };

  // Hàm tải thêm thông báo khi nhấn nút "Xem thêm thông báo"
  const loadMoreNotifications = () => {
    if (!loadingMore) {
      const nextPage = page + 1;
      setPage(nextPage);
      fetchNotifications(nextPage, true);
    }
  };

  // Xử lý khi click vào thông báo
  const handleNotificationClick = async (notification: Notification) => {

    // Xử lý chuyển hướng hoặc hiển thị nội dung dựa vào loại thông báo
    const type = notification.type.toUpperCase();

    // Các loại thông báo liên quan đến bài đăng -> mở PostDetailModal
    if (
      type === "REPLY_COMMENT" ||
      type === "LIKE_COMMENT" ||
      type === "LIKE_POST" ||
      type === "COMMENT" ||
      type === "SAVED_POST" ||
      type === "SHARE_POST"
    ) {
      // Đánh dấu thông báo đã đọc nếu chưa đọc
      if (!notification.read) {
        await markAsRead(notification.notificationsId , notification.type);
      }
      // Mở modal chi tiết bài đăng
      if (notification.objectId) {
        // Thêm state để quản lý modal
        setShowPostModal(true);
        setSelectedPostId(notification.objectId);
      }
    }
    // Các loại thông báo liên quan đến kết bạn -> điều hướng đến trang profile
    else if (type === "ADD_FRIEND" || type === "ACCEPT_FRIEND") {
      // Đánh dấu thông báo đã đọc nếu chưa đọc
      if (!notification.read) {
        await markAsRead(notification.notificationsId , notification.type);
      }
      // Điều hướng đến trang profile của người dùng
      if (notification.email) {
        router.push(`/social-network/profile/${notification.email}`);
      }
    }
  };

  // Khởi tạo khi component được mount
  useEffect(() => {
    // Lấy danh sách thông báo khi component mount
    fetchNotifications();
  }, []);

  const getNotificationIcon = (type: string) => {
    switch (type.toUpperCase()) {
      case "LIKE":
      case "LIKE_POST":
      case "LIKE_COMMENT":
        return <FaHeart className="text-red-500" />;
      case "COMMENT":
      case "REPLY_COMMENT":
        return <FaComment className="text-blue-500" />;
      case "SHARE":
      case "SHARE_POST":
        return <FaShare className="text-green-500" />;
      case "ADD_FRIEND":
      case "ACCEPT_FRIEND":
      case "FRIEND_REQUEST":
        return <FaUserPlus className="text-purple-500" />;
      case "SAVED_POST":
        return <FaBookmark className="text-yellow-500" />;
      case "SYSTEM":
        return <FaBell className="text-gray-500" />;
      default:
        return <FaBell className="text-gray-500" />;
    }
  };

  // Đóng modal chi tiết bài đăng
  const handleClosePostModal = () => {
    setShowPostModal(false);
    setSelectedPostId(null);
  };

  return (
    <>
      {/* Nút hiển thị thông báo với badge số lượng chưa đọc */}
      <div className="absolute -right-14 md:right-0 z-50 mt-2 w-[360px] md:w-[450px]">
        <div className="rounded-lg border border-gray-200 bg-white shadow-lg">
          {/* Header */}
          <div className="flex items-center justify-between border-b border-gray-200 p-3 md:p-4">
            <h3 className="text-base font-semibold text-gray-900 md:text-lg flex items-center">
              Thông báo
            </h3>
            {unreadCount > 0 && (
              <button
                onClick={markAllAsRead}
                className="flex items-center gap-1 text-xs text-blue-600 hover:text-blue-800 md:text-sm"
              >
                <IoMdCheckmarkCircleOutline className="text-sm md:text-base" />
                Đánh dấu tất cả đã đọc
              </button>
            )}
          </div>

          {/* Notifications List */}
          <div
            className="max-h-[60vh] overflow-y-auto md:max-h-96"
            ref={notificationListRef}
          >
            {loading ? (
              <div className="p-8 text-center">
                <div className="mx-auto mb-3 h-8 w-8 animate-spin rounded-full border-b-2 border-blue-500"></div>
                <p className="text-gray-500">Đang tải thông báo...</p>
              </div>
            ) : notifications.length === 0 ? (
              <div className="p-8 text-center">
                <FaBell className="mx-auto mb-3 text-4xl text-gray-300" />
                <p className="text-gray-500">Chưa có thông báo nào</p>
              </div>
            ) : (
              <div className="divide-y divide-gray-100">
                {notifications.map((notification) => (
                  <div
                    key={notification.notificationsId}
                    className={`cursor-pointer p-3 transition-colors hover:bg-gray-50 md:p-4 ${!notification.read ? "bg-blue-50" : ""}`}
                  >
                    <div className="flex items-start space-x-2 md:space-x-3">
                      {/* Avatar or Icon */}
                      <div
                        className="flex-shrink-0"
                        onClick={() => handleNotificationClick(notification)}
                      >
                        <div className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-100 md:h-10 md:w-10">
                          <div className="text-xs md:text-sm">
                            {getNotificationIcon(notification.type)}
                          </div>
                        </div>
                      </div>

                      {/* Content */}
                      <div
                        className="min-w-0 flex-1"
                        onClick={() => handleNotificationClick(notification)}
                      >
                        <div className="mb-1">
                          <div 
                            className="text-xs text-gray-600 md:text-sm"
                            dangerouslySetInnerHTML={{ __html: notification.content }}
                          />
                        </div>

                        <div className="flex items-center justify-between">
                          <span className="text-xs text-gray-400">
                            {formatDateForDisplay(notification.createdDate)}
                          </span>
                          {!notification.read && (
                            <span className="rounded-full bg-blue-100 px-1.5 py-0.5 text-xs text-blue-800">
                              Mới
                            </span>
                          )}
                        </div>
                      </div>

                      {/* Action menu */}
                      <div className="flex-shrink-0">
                        <div className="group relative">
                          <button className="p-1 text-gray-400 hover:text-gray-600">
                            <FaEllipsisH className="text-sm" />
                          </button>
                          <div className="absolute right-0 top-[60%] z-10 mt-1 hidden w-48 rounded-md border border-gray-200 bg-white shadow-lg group-hover:block">
                            {!notification.read && (
                              <button
                                onClick={() => markAsRead(notification.notificationsId,notification.type)}
                                className="flex w-full items-center gap-2 px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
                              >
                                <IoMdCheckmarkCircleOutline />
                                Đánh dấu đã đọc
                              </button>
                            )}
                            <button
                              onClick={() =>
                                deleteNotification(notification.notificationsId)
                              }
                              className="flex w-full items-center gap-2 px-3 py-2 text-left text-sm text-red-600 hover:bg-gray-100"
                            >
                              <FaTrash />
                              Xóa thông báo
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}

                {/* Loading more indicator */}
                {loadingMore && (
                  <div className="p-4 text-center">
                    <div className="mx-auto h-6 w-6 animate-spin rounded-full border-b-2 border-blue-500"></div>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Footer */}
          {!lastPage && (
            <div className="border-t border-gray-200 bg-gray-50 p-2 md:p-3">
              <button
                className="w-full py-1.5 text-center text-xs font-medium text-blue-600 hover:text-blue-800 md:py-2 md:text-sm"
                onClick={() => {
                  loadMoreNotifications();
                }}
              >
                Xem thêm
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Modal chi tiết bài đăng */}
      {showPostModal && selectedPostId && (
        <PostDetailModal
          isOpen={showPostModal}
          onClose={handleClosePostModal}
          postId={selectedPostId}
        />
      )}
    </>
  );
};

export default NotificationDropdown;
