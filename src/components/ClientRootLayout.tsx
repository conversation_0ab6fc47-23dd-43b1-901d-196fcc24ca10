'use client';

import { SWRProvider } from "@/context/SwrProvider";
import Footer from "@/components/ui/Footer";
import ClientHeader from "@/components/ClientHeader";

interface ClientRootLayoutProps {
  children: React.ReactNode;
  dataMenu: any[];
  dataOfMenuChild: any[];
  quicksandClassName: string;
}

export default function ClientRootLayout({
  children,
  dataMenu,
  dataOfMenuChild,
  quicksandClassName
}: ClientRootLayoutProps) {
  return (
    <>
      <ClientHeader dataMenu={dataMenu} dataOfMenuChild={dataOfMenuChild} />
      <main className="flex h-svh flex-col">
        <section className="flex-1">
          <SWRProvider>{children}</SWRProvider>
        </section>
        <Footer />
      </main>
    </>
  );
} 