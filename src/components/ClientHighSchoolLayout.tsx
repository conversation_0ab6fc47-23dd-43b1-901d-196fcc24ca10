'use client';

import { SWRProvider } from "@/context/SwrProvider";
import Footer from "@/components/ui/Footer";
import HeroSection from "@/components/ui/HeroSection";
import MenuSelected from "@/components/ui/MenuSelected";
import ClientHeader from "@/components/ClientHeader";

interface ClientHighSchoolLayoutProps {
  children: React.ReactNode;
  dataMenu: any[];
  dataOfMenuChild: any[];
  dataMenuSelected: any[];
}

export default function ClientHighSchoolLayout({
  children,
  dataMenu,
  dataOfMenuChild,
  dataMenuSelected
}: ClientHighSchoolLayoutProps) {
  return (
    <>
      <ClientHeader dataMenu={dataMenu} dataOfMenuChild={dataOfMenuChild} />
      <main className="flex h-svh flex-col">
        <section className="flex-1">
          <HeroSection
            description="<PERSON><PERSON> sinh thái học tập sáng tạo cấp THPT là môi trường gi<PERSON>o dục tích cực, kết nối công nghệ và cộng đồng, gi<PERSON><PERSON> học sinh phát triển tư duy sáng tạo, kỹ năng hợp tác, tự chủ và định hướng nghề nghiệp."
            title="HỆ SINH THÁI HỌC TẬP SÁNG TẠO CẤP THPT"
            backgroundImageUrl="high-school/banner1.jpg"
          />
          <MenuSelected dataMenu={dataMenuSelected} />
          <SWRProvider>{children}</SWRProvider>
        </section>
        <Footer />
      </main>
    </>
  );
} 