"use client";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { ChevronDown } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import React from "react";

interface SelectOption {
  key: number | string;
  value: string;
  label: string;
}

type BreadcrumbItemType = {
  type: "link" | "select" | "text";
  label?: string;
  href?: string;
  value?: string;
  options?: SelectOption[];
  onValueChange?: (value: string) => void;
};

interface ForumBreadcrumbProps {
  items: BreadcrumbItemType[];
  lastUpdated?: string;
}

export default function ForumBreadcrumb({
  items,
  lastUpdated
}: ForumBreadcrumbProps) {
  const router = useRouter();

  const renderBreadcrumbItem = (item: BreadcrumbItemType, index: number) => {
    switch (item.type) {
      case "link":
        return (
          <Link
            href={item.href || "/"}
            className="max-w-[150px] truncate text-xs hover:text-primary sm:max-w-none sm:text-sm"
            title={item.label}
          >
            {item.label}
          </Link>
        );
      case "select":
        return (
          <div className="relative">
            <DropdownMenu>
              <DropdownMenuTrigger className="flex h-auto items-center border-none bg-transparent p-0 text-xs shadow-none outline-none hover:text-primary sm:text-sm">
                <span className="max-w-[120px] truncate sm:max-w-none">
                  {item.options?.find((opt) => opt.value === item.value)
                    ?.label || item.label}
                </span>
                <ChevronDown className="ml-1 h-3 w-3" />
              </DropdownMenuTrigger>
              <DropdownMenuContent className="z-50 mt-1 w-36 rounded border border-gray-100 bg-white py-1 shadow-lg sm:w-48">
                {item.options?.map((option) => (
                  <DropdownMenuItem
                    key={option.key}
                    className={`block cursor-pointer px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 ${option.value === item.value ? "bg-gray-50" : ""}`}
                    onClick={() => {
                      item.onValueChange?.(option.value);
                      if (item.href) {
                        router.replace(
                          item.href.replace(":value", option.value)
                        );
                      }
                    }}
                  >
                    {option.label}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        );
      case "text":
        return (
          <span
            className="max-w-[150px] truncate text-xs text-gray-700 sm:max-w-none sm:text-sm"
            title={item.label}
          >
            {item.label}
          </span>
        );
      default:
        return null;
    }
  };

  return (
    <div className="border-b">
      <div className="container mx-auto px-4 py-2">
        <div className="flex flex-col gap-2 md:flex-row md:items-center md:justify-between">
          <div className="scrollbar-hide relative w-full overflow-x-auto md:w-auto">
            <div className="flex items-center whitespace-nowrap py-1 text-sm text-gray-500">
              {items.map((item, index) => (
                <React.Fragment key={index}>
                  {renderBreadcrumbItem(item, index)}
                  {index < items.length - 1 && <span className="mx-2">/</span>}
                </React.Fragment>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
