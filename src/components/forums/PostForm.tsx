"use client";

import QuillEditor from "@/components/common/QuillEditor";
import forumsServices from "@/services/forums/forumsServices";
import Link from "next/link";
import { useParams } from "next/navigation";
import { useEffect, useState } from "react";
import { toast } from "react-toastify";

interface Topic {
  topicId: number;
  topicName: string;
  forumId: number;
  eduEcosystemId: number;
  postCount: number;
  fileId: string;
}

interface PostFormProps {
  isLoading: boolean;
  handleSubmit: (data: {
    title: string;
    content: string;
    selectedTopicId: string | null;
    imageFiles: File[];
    hashTags: string[];
    removedExistingFiles?: {
      fileId: string;
      fileOriginalName: string;
      fileStoredName: string;
      fileType: string;
      fileSize: number;
    }[];
  }) => void;
  initialData?: {
    title?: string;
    content?: string;
    selectedTopicId?: string | null;
    hashTags?: string[];
    existingFiles?: {
      fileId: string;
      fileOriginalName: string;
      fileStoredName: string;
      fileType: string;
      fileSize: number;
    }[];
  };
  isEditing?: boolean;
}

const PostForm = ({
  isLoading,
  handleSubmit,
  initialData,
  isEditing = false
}: PostFormProps) => {
  const { eduEcosystemId, forumId } = useParams();

  const [title, setTitle] = useState(initialData?.title || "");
  const [content, setContent] = useState(initialData?.content || "");
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);
  const [uploadedFilePreviews, setUploadedFilePreviews] = useState<string[]>([]);
  const [hashTags, setHashTags] = useState<string[]>(
    initialData?.hashTags || []
  );
  const [newTag, setNewTag] = useState("");
  const [selectedTopicId, setSelectedTopicId] = useState<string | null>(
    initialData?.selectedTopicId || null
  );
  const [existingFiles, setExistingFiles] = useState<{
    fileId: string;
    fileOriginalName: string;
    fileStoredName: string;
    fileType: string;
    fileSize: number;
  }[]>(
    initialData?.existingFiles || []
  );
  const [removedExistingFiles, setRemovedExistingFiles] = useState<{
    fileId: string;
    fileOriginalName: string;
    fileStoredName: string;
    fileType: string;
    fileSize: number;
  }[]>(
    []
  );

  const [topics, setTopics] = useState<Topic[]>([]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const files = Array.from(e.target.files);
      // Không cần kiểm tra định dạng file, chấp nhận tất cả các loại file
      const validFiles = files;

      // Kiểm tra kích thước file (tối đa 10MB)
      const oversizedFiles = validFiles.filter(file => file.size > 10 * 1024 * 1024);
      if (oversizedFiles.length > 0) {
        toast.error("File không được vượt quá 10MB");
        return;
      }

      // Tạo previews cho các file mới
      const newPreviews = validFiles.map((file) => {
        if (file.type.startsWith("image/")) {
          return URL.createObjectURL(file);
        } else {
          return '/icon/icon-file.svg';
        }
      });

      setUploadedFiles((prev) => [...prev, ...validFiles]);
      setUploadedFilePreviews((prev) => [...prev, ...newPreviews]);
    }
  };

  const removeFile = (index: number) => {
    setUploadedFiles((prev) => prev.filter((_, i) => i !== index));
    setUploadedFilePreviews((prev) => {
      // Revoke URL để tránh memory leak nếu là URL object
      if (prev[index] && prev[index].startsWith('blob:')) {
        URL.revokeObjectURL(prev[index]);
      }
      return prev.filter((_, i) => i !== index);
    });
  };

  const handleRemoveExistingFile = (file: {
    fileId: string;
    fileOriginalName: string;
    fileStoredName: string;
    fileType: string;
    fileSize: number;
  }) => {
    setExistingFiles((prev) => prev.filter((_) => _.fileId !== file.fileId));
    setRemovedExistingFiles((prev) => [...prev, file]);
  };

  const handleAddTag = () => {
    if (!newTag.trim()) {
      toast.warning("Vui lòng nhập tag!");
      return;
    }

    if (hashTags.includes(newTag.trim())) {
      toast.warning("Tag này đã tồn tại!");
      return;
    }

    setHashTags((prev) => [...prev, newTag.trim()]);
    setNewTag("");
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setHashTags((prev) => prev.filter((tag) => tag !== tagToRemove));
  };

  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      e.preventDefault();
      handleAddTag();
    }
  };

  useEffect(() => {
    const fetchTopics = async () => {
      try {
        const response = await forumsServices.getTopicsByForumId(
          Number(eduEcosystemId)
        );
        if (response.data.data?.content) {
          setTopics(response.data.data.content);
        }
      } catch (error) {
        console.error("Error fetching topics:", error);
      }
    };
    fetchTopics();
  }, [eduEcosystemId, forumId]);

  return (
    <div className="container mx-auto px-4 py-6">
      <div className="mx-auto max-w-4xl">
        <h1 className="mb-6 text-2xl font-bold">
          {isEditing ? "Chỉnh sửa bài viết" : "Tạo bài viết mới"}
        </h1>

        {/* Guidelines Panel */}
        <div className="mb-6 rounded-lg border border-blue-200 bg-blue-50 p-4">
          <div className="flex items-start">
            <div className="mr-2 mt-0.5 flex h-5 w-5 items-center justify-center text-blue-500">
              <i className="ri-information-line"></i>
            </div>
            <div>
              <h3 className="mb-1 font-medium text-blue-800">
                Hướng dẫn đăng bài
              </h3>
              <ul className="ml-1 list-inside list-disc space-y-1 text-sm text-blue-700">
                <li>
                  Tiêu đề bài viết nên ngắn gọn, rõ ràng và mô tả chính xác nội
                  dung.
                </li>
                <li>
                  Chọn chủ đề phù hợp để bài viết của bạn dễ dàng được tìm thấy.
                </li>
                <li>
                  Nội dung bài viết cần đầy đủ, rõ ràng và tôn trọng quy tắc
                  cộng đồng.
                </li>
                <li>
                  Sử dụng thẻ (tags) để phân loại nội dung và tăng khả năng tìm
                  kiếm.
                </li>
                <li>Kiểm tra chính tả và định dạng trước khi đăng bài.</li>
              </ul>
              <a
                href="#"
                className="mt-2 inline-block text-sm font-medium text-primary hover:underline"
              >
                Xem đầy đủ quy định diễn đàn
              </a>
            </div>
          </div>
        </div>

        {/* Post Form */}
        <form
          id="post-form"
          className="space-y-6"
          onSubmit={(e) => {
            e.preventDefault();
            const data = {
              title,
              content,
              imageFiles: uploadedFiles,
              hashTags,
              selectedTopicId,
              removedExistingFiles
            };
            handleSubmit(data);
          }}
        >
          {/* Tiêu đề */}
          <div>
            <label className="mb-1 block text-sm font-medium text-gray-700">
              Tiêu đề bài viết <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              className="w-full rounded-lg border border-gray-300 px-4 py-2 focus:border-indigo-500 focus:ring-1 focus:ring-indigo-500"
              placeholder="Nhập tiêu đề bài viết (tối đa 100 ký tự)"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              maxLength={100}
            />
            {/* <p className="mt-1 text-sm text-gray-500">
            {title.length}/100 ký tự (tối thiểu 5 ký tự)
          </p> */}
          </div>

          {/* Chọn chủ đề */}
          <div>
            <label className="mb-1 block text-sm font-medium text-gray-700">
              Chủ đề <span className="text-red-500">*</span>
            </label>
            <div className="relative">
              <select
                value={selectedTopicId || ""}
                onChange={(e) => setSelectedTopicId(e.target.value)}
                className="w-full appearance-none rounded-lg border border-gray-300 px-4 py-2 pr-8 focus:border-indigo-500 focus:ring-1 focus:ring-indigo-500"
              >
                <option value="">Chọn chủ đề</option>
                {topics.map((topic) => (
                  <option key={topic.topicId} value={topic.topicId}>
                    {topic.topicName} ({topic.postCount} bài viết)
                  </option>
                ))}
              </select>
              <div className="pointer-events-none absolute right-3 top-1/2 flex h-4 w-4 -translate-y-1/2 transform items-center justify-center">
                <i className="ri-arrow-down-s-line text-gray-500"></i>
              </div>
            </div>
          </div>

          {/* Nội dung */}
          <div>
            <label className="mb-1 block text-sm font-medium text-gray-700">
              Nội dung <span className="text-red-500">*</span>
            </label>
            <div className="rounded-lg border border-gray-500 bg-white">
              <QuillEditor
                value={content}
                onChange={setContent}
                placeholder="Nhập nội dung bài viết của bạn ở đây..."
              />
            </div>
          </div>

          {/* Upload file */}
          <div>
            <label className="mb-1 block text-sm font-medium text-gray-700">
              Đính kèm tệp/hình ảnh
            </label>
            <div className="space-y-4">
              <input
                type="file"
                accept="*"
                onChange={handleFileChange}
                className="hidden"
                id="file-upload"
                multiple
              />
              {uploadedFilePreviews.length === 0 && existingFiles.length === 0 ? (
                <div className="flex flex-col items-center justify-center rounded-md border border-dashed border-gray-300 bg-white p-6">
                  <div className="mb-3 flex h-12 w-12 items-center justify-center text-gray-400">
                    <i className="ri-file-list-line ri-2x"></i>
                  </div>
                  <p className="mb-2 text-sm text-gray-600">
                    Chọn file để tải lên
                  </p>
                  <div className="flex flex-col items-center">
                    <label
                      htmlFor="file-upload"
                      className="cursor-pointer whitespace-nowrap rounded-lg bg-gray-100 px-4 py-2 text-sm text-gray-700 hover:bg-gray-200"
                    >
                      <span>Chọn tệp</span>
                    </label>
                    <p className="mt-2 text-xs text-gray-500">
                      Hỗ trợ tất cả các định dạng file (tối đa 10MB)
                    </p>
                  </div>
                </div>
              ) : (
                <div>
                  {existingFiles.length > 0 && (
                    <div className="mb-4">
                      <h4 className="mb-2 text-sm font-medium text-gray-700">
                        File hiện tại:
                      </h4>
                      <div className="space-y-2">
                        {existingFiles.map((file) => (
                          <div
                            key={file.fileId}
                            className="flex items-center justify-between rounded-md border border-gray-200 bg-white p-3"
                          >
                            <div className="truncate pr-2">
                              <p className="truncate text-sm font-medium text-gray-900">
                                {file.fileOriginalName}
                              </p>
                            </div>
                            <button
                              type="button"
                              onClick={() => handleRemoveExistingFile(file)}
                              className="flex h-6 w-6 items-center justify-center rounded-full bg-red-50 text-red-600 hover:bg-red-100"
                            >
                              <i className="ri-close-line text-sm"></i>
                            </button>
                          </div>
                        ))}
                        {uploadedFilePreviews.length === 0 && <div className="flex items-center justify-center py-2">
                          <label
                            htmlFor="file-upload"
                            className="cursor-pointer rounded-lg bg-gray-100 px-4 py-2 text-sm text-gray-700 hover:bg-gray-200"
                          >
                            <span>Thêm tệp</span>
                          </label>
                        </div>}
                      </div>
                    </div>
                  )}

                  {uploadedFilePreviews.length > 0 && (
                    <div className="mb-4">
                      {existingFiles.length > 0 && (
                        <h4 className="mb-2 text-sm font-medium text-gray-700">
                          File mới thêm:
                        </h4>
                      )}
                      <div className="space-y-2">
                        {uploadedFiles.map((file, index) => (
                          <div
                            key={index}
                            className="flex items-center justify-between rounded-md border border-gray-200 bg-white p-3"
                          >
                            <div className="truncate pr-2">
                              <p className="truncate text-sm font-medium text-gray-900">
                                {file.name}
                              </p>
                              <p className="text-xs text-gray-500">
                                {file.size
                                  ? (
                                      file.size /
                                      1024 /
                                      1024
                                    ).toFixed(2)
                                  : 0}{" "}
                                MB
                              </p>
                            </div>
                            <button
                              type="button"
                              onClick={() => removeFile(index)}
                              className="flex h-6 w-6 items-center justify-center rounded-full bg-red-50 text-red-600 hover:bg-red-100"
                            >
                              <i className="ri-close-line text-sm"></i>
                            </button>
                          </div>
                        ))}
                        <div className="flex items-center justify-center py-2">
                          <label
                            htmlFor="file-upload"
                            className="cursor-pointer rounded-lg bg-gray-100 px-4 py-2 text-sm text-gray-700 hover:bg-gray-200"
                          >
                            <span>Thêm tệp</span>
                          </label>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Hashtags */}
          <div>
            <label className="mb-1 block text-sm font-medium text-gray-700">
              Hashtag
            </label>
            <div className="tag-input rounded border border-gray-300 bg-white px-3 py-2 focus-within:border-transparent focus-within:ring-2 focus-within:ring-primary">
              <div className="mb-2 flex flex-wrap gap-2">
                {hashTags.map((tag, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center gap-1 rounded-full bg-gray-100 px-2 py-1 text-xs text-gray-700"
                  >
                    {tag}
                    <button
                      type="button"
                      onClick={() => handleRemoveTag(tag)}
                      className="ml-1 text-gray-500 hover:text-gray-700"
                    >
                      <i className="ri-close-line text-xs"></i>
                    </button>
                  </span>
                ))}
              </div>
              <div className="flex gap-2">
                <input
                  type="text"
                  value={newTag}
                  onChange={(e) => setNewTag(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Nhập thẻ và nhấn Enter (tối đa 5 thẻ)"
                  className="w-full border-none p-0 text-sm outline-none"
                />
              </div>
            </div>
          </div>

          {/* Auto-save Status */}

          {/* Buttons */}
          <div className="flex flex-wrap gap-3 border-t pt-4">
            <button
              type="submit"
              disabled={isLoading || !title.trim() || !content.trim()}
              className="rounded- flex items-center whitespace-nowrap bg-primary px-5 py-2 text-white hover:bg-primary/90 disabled:cursor-not-allowed disabled:opacity-50"
            >
              {isLoading ? (
                <>
                  <div className="mr-2 flex h-5 w-5 items-center justify-center">
                    <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
                  </div>
                  Đang đăng...
                </>
              ) : (
                <>
                  <div className="mr-2 flex h-5 w-5 items-center justify-center">
                    <i className="ri-send-plane-line"></i>
                  </div>
                  Đăng bài
                </>
              )}
            </button>

            <Link
              href={`/forums/${eduEcosystemId}/topics/${forumId}`}
              className="flex items-center whitespace-nowrap px-5 py-2 text-gray-500 hover:text-gray-700"
            >
              <div className="mr-2 flex h-5 w-5 items-center justify-center">
                <i className="ri-close-line"></i>
              </div>
              Hủy
            </Link>
          </div>
        </form>
      </div>
    </div>
  );
};

export default PostForm;
