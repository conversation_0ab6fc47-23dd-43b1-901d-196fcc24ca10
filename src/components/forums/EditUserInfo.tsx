import React, { useState, useRef } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON>Footer
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import userServices from "@/services/user/userServices";
import { toast } from "react-toastify";
import { UserInfo } from "@/types/user";
import useUploadFile from "@/hook/useUploadFile";
import Image from "next/image";
import Loading from "@/components/ui/Loading";
import { Mail, Phone, MapPin } from "lucide-react";

interface EditUserInfoProps {
  isOpen: boolean;
  onClose: () => void;
  userInfo: UserInfo | undefined;
  onSuccess: () => void;
  isEdit?: boolean;
}

export default function EditUserInfo({
  isOpen,
  onClose,
  userInfo,
  onSuccess,
  isEdit = true
}: EditUserInfoProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { uploadForumFile, viewFile } = useUploadFile();

  const formatDateForInput = (dateString: string) => {
    if (!dateString) return "";
    const [day, month, year] = dateString.split("/");
    return `${year}-${month.padStart(2, "0")}-${day.padStart(2, "0")}`;
  };

  const formatDateForServer = (dateString: string) => {
    if (!dateString) return "";
    const [year, month, day] = dateString.split("-");
    return `${day}/${month}/${year}`;
  };

  const [isEditMode, setIsEditMode] = useState(false);
  const [formData, setFormData] = useState({
    username: userInfo?.username || "",
    education: userInfo?.education || "",
    job: userInfo?.job || "",
    firstName: userInfo?.firstName || "",
    lastName: userInfo?.lastName || "",
    phoneNumber: userInfo?.phoneNumber || "",
    address: userInfo?.address || "",
    dateOfBirth: userInfo?.dateOfBirth
      ? formatDateForInput(userInfo.dateOfBirth)
      : "",
    hometown: userInfo?.hometown || "",
    gender: userInfo?.gender || "",
    orgName: userInfo?.orgName || "",
    position: userInfo?.position || "",
    bio: userInfo?.bio || "",
    hobbies: userInfo?.hobbies || "",
    careerGoal: userInfo?.careerGoal || "",
    email: userInfo?.email || "",
    thumbnail: userInfo?.thumbnail || "",
    thumbnailUrl: userInfo?.thumbnailUrl || ""
  });
  const [isLoading, setIsLoading] = useState(false);
  const [thumbnailFile, setThumbnailFile] = useState<File | null>(null);
  const [thumbnailPreview, setThumbnailPreview] = useState<string | null>(null);

  const handleThumbnailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setThumbnailFile(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setThumbnailPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleEditToggle = () => {
    setIsEditMode((prev) => !prev);
    if (isEditMode)
      setFormData({
        username: userInfo?.username || "",
        firstName: userInfo?.firstName || "",
        lastName: userInfo?.lastName || "",
        phoneNumber: userInfo?.phoneNumber || "",
        education: userInfo?.education || "",
        job: userInfo?.job || "",
        address: userInfo?.address || "",
        dateOfBirth: userInfo?.dateOfBirth
          ? formatDateForInput(userInfo.dateOfBirth)
          : "",
        hometown: userInfo?.hometown || "",
        gender: userInfo?.gender || "",
        orgName: userInfo?.orgName || "",
        position: userInfo?.position || "",
        bio: userInfo?.bio || "",
        hobbies: userInfo?.hobbies || "",
        careerGoal: userInfo?.careerGoal || "",
        email: userInfo?.email || "",
        thumbnail: userInfo?.thumbnail || "",
        thumbnailUrl: userInfo?.thumbnailUrl || ""
      });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setIsLoading(true);
      let thumbnailUrl = formData.thumbnailUrl as any;

      if (thumbnailFile) {
        const { data } = await uploadForumFile(thumbnailFile);
        thumbnailUrl = data;
      }

      await userServices.editUserInfo({
        fullName: `${formData.lastName} ${formData.firstName}`,
        phoneNumber: formData.phoneNumber,
        education: formData.education,
        address: formData.address,
        job: formData.job,
        gender: formData.gender,
        dateOfBirth: formatDateForServer(formData.dateOfBirth),
        bio: formData.bio,
        thumbnail: thumbnailUrl,
        numberOfPost: userInfo?.numberOfPost || 0,
        firstName: formData.firstName,
        lastName: formData.lastName
      });
      toast.success("Cập nhật thông tin thành công");
      onSuccess();
      onClose();
    } catch (error) {
      toast.error("Cập nhật thông tin thất bại");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <section className="mx-auto w-full rounded-[8px] bg-white p-6 shadow">
      <form onSubmit={handleSubmit} className="space-y-8">
        <div className="mb-6 flex flex-col border-b pb-4 md:flex-row md:items-center md:justify-between">
          <h2 className="text-lg font-semibold text-gray-900">
            Thông tin cá nhân
          </h2>
          {isEdit && (
            <Button
              type="button"
              variant="ghost"
              className="flex items-center gap-2 text-primary"
              onClick={handleEditToggle}
            >
              <span>{isEditMode ? "Hủy" : "Chỉnh sửa"}</span>
              <svg width="16" height="16" fill="none" viewBox="0 0 24 24">
                <path
                  stroke="currentColor"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M16.862 5.487a2.06 2.06 0 1 1 2.915 2.914L8.5 19.678l-4.06 1.145 1.145-4.06 11.277-11.276Z"
                />
              </svg>
            </Button>
          )}
        </div>
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          <div>
            <Label>Họ và tên</Label>
            {isEditMode ? (
              <div className="flex gap-2">
                <Input
                  id="lastName"
                  value={formData.lastName}
                  onChange={(e) =>
                    setFormData({ ...formData, lastName: e.target.value })
                  }
                  placeholder="Họ"
                  required
                />
                <Input
                  id="firstName"
                  value={formData.firstName}
                  onChange={(e) =>
                    setFormData({ ...formData, firstName: e.target.value })
                  }
                  placeholder="Tên"
                  required
                />
              </div>
            ) : (
              <div className="mt-1 text-base text-gray-900">
                {formData.lastName} {formData.firstName}
              </div>
            )}
          </div>
          <div>
            <Label>Số điện thoại</Label>
            {isEditMode ? (
              <Input
                id="phoneNumber"
                value={formData.phoneNumber}
                onChange={(e) =>
                  setFormData({ ...formData, phoneNumber: e.target.value })
                }
                placeholder="Nhập số điện thoại"
                required
              />
            ) : formData.phoneNumber ? (
              <div className="mt-1 text-base text-gray-900">
                {formData.phoneNumber}
              </div>
            ) : (
                <p className="mt-1 whitespace-pre-line text-gray-400 text-sm">Chưa cập nhật</p>
            )}
          </div>
          <div>
            <Label>Giới tính</Label>
            {isEditMode ? (
              <Select
                value={formData.gender}
                onValueChange={(value) =>
                  setFormData({ ...formData, gender: value })
                }
              >
                <SelectTrigger id="gender">
                  <SelectValue placeholder="Chọn giới tính" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Nam">Nam</SelectItem>
                  <SelectItem value="Nữ">Nữ</SelectItem>
                </SelectContent>
              </Select>
            ) : formData.gender ? (
              <div className="mt-1 text-base text-gray-900">
                {formData.gender}
              </div>
            ) : (
                <p className="mt-1 whitespace-pre-line text-gray-400 text-sm">Chưa cập nhật</p>
            )}
          </div>
          <div>
            <Label>Ngày sinh</Label>
            {isEditMode ? (
              <Input
                id="dateOfBirth"
                type="date"
                value={formData.dateOfBirth}
                onChange={(e) =>
                  setFormData({ ...formData, dateOfBirth: e.target.value })
                }
                required
              />
            ) : userInfo?.dateOfBirth ? (
              <div className="mt-1 text-base text-gray-900">
                {userInfo?.dateOfBirth}
              </div>
            ) : (
                <p className="mt-1 whitespace-pre-line text-gray-400 text-sm">Chưa cập nhật</p>
            )}
          </div>

          <div>
            <Label>Học vấn</Label>
            {isEditMode ? (
              <Input
                id="education"
                value={formData.education}
                onChange={(e) =>
                  setFormData({ ...formData, education: e.target.value })
                }
                placeholder="Vui lòng nhập học vấn"
              />
            ) : formData.education ? (
              <div className="mt-1 whitespace-pre-line text-base text-gray-900">
                {formData.education}
              </div>
            ) : (
                <p className="mt-1 whitespace-pre-line text-gray-400 text-sm">Chưa cập nhật</p>
            )}
          </div>

          <div>
            <Label>Nghề nghiệp</Label>
            {isEditMode ? (
              <Input
                id="job"
                value={formData.job}
                onChange={(e) =>
                  setFormData({ ...formData, job: e.target.value })
                }
                placeholder="Vui lòng nhập nghề nghiệp"
              />
            ) : formData.job ? (
              <div className="mt-1 whitespace-pre-line text-base text-gray-900">
                {formData.job}
              </div>
            ) : (
                <p className="mt-1 whitespace-pre-line text-gray-400 text-sm">Chưa cập nhật</p>
            )}
          </div>

          <div>
            <Label>Giới thiệu bản thân</Label>
            {isEditMode ? (
              <textarea
                id="bio"
                className="min-h-[80px] w-full rounded-[8px] border p-2 focus:outline-none focus:ring-2 focus:ring-primary"
                value={formData.bio}
                onChange={(e) =>
                  setFormData({ ...formData, bio: e.target.value })
                }
                placeholder="Giới thiệu về bản thân bạn"
              />
            ) : formData.bio ? (
              <div className="mt-1 whitespace-pre-line text-base text-gray-900">
                {formData.bio}
              </div>
            ) : (
                <p className="mt-1 whitespace-pre-line text-gray-400 text-sm">Chưa cập nhật</p>
            )}
          </div>
          {isEditMode && (
            <div>
              <Label>Địa chỉ</Label>
              <textarea
                id="address"
                className="min-h-[80px] w-full rounded-[8px] border p-2 focus:outline-none focus:ring-2 focus:ring-primary"
                value={formData.address}
                onChange={(e) =>
                  setFormData({ ...formData, address: e.target.value })
                }
                placeholder="Vui lòng nhập địa chỉ"
              />
            </div>
          )}
        </div>
        {isEditMode && (
          <div className="mt-6 flex justify-end gap-4">
            <Button
              type="button"
              className="rounded-[8px] border border-[#D1D5DB] bg-white font-[400] text-[#374151]"
              onClick={handleEditToggle}
              disabled={isLoading}
            >
              Hủy
            </Button>
            <Button
              type="submit"
              className="rounded-[8px] font-[400] text-white"
              disabled={isLoading}
            >
              {isLoading ? <Loading size="sm" color="white" /> : "Lưu thay đổi"}
            </Button>
          </div>
        )}
        {isEditMode && (
          <div className="mt-6 border-t pt-6">
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-gray-900">
                Ảnh đại diện
              </h3>
              <div className="mt-4 flex items-center gap-6">
                <div className="relative h-32 w-32 overflow-hidden rounded-full">
                  {thumbnailPreview || userInfo?.thumbnail ? (
                    <Image
                      src={thumbnailPreview || userInfo?.thumbnail}
                      alt="Avatar"
                      fill
                      className="object-cover"
                    />
                  ) : (
                    <div className="flex h-full w-full items-center justify-center bg-gray-100">
                      <svg
                        width="40"
                        height="40"
                        viewBox="0 0 40 40"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M20 20C22.7614 20 25 17.7614 25 15C25 12.2386 22.7614 10 20 10C17.2386 10 15 12.2386 15 15C15 17.7614 17.2386 20 20 20Z"
                          fill="#9CA3AF"
                        />
                        <path
                          d="M20 22.5C15.8579 22.5 12.5 25.8579 12.5 30H27.5C27.5 25.8579 24.1421 22.5 20 22.5Z"
                          fill="#9CA3AF"
                        />
                      </svg>
                    </div>
                  )}
                </div>
                {isEditMode && (
                  <div className="flex flex-col gap-2">
                    <input
                      type="file"
                      ref={fileInputRef}
                      className="hidden"
                      accept="image/*"
                      onChange={handleThumbnailChange}
                    />
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => fileInputRef.current?.click()}
                      className="w-fit"
                    >
                      Chọn ảnh
                    </Button>
                    <p className="text-sm text-gray-500">
                      Định dạng: JPG, PNG. Kích thước tối đa: 5MB
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        <div className="mt-6 flex flex-col gap-6 border-t pt-6 md:flex-row">
          <div className="flex-1 space-y-2">
            <ContactInfo
              email={formData.username}
              address={formData.address}
              orgName={formData.orgName}
            />
          </div>
        </div>
      </form>
    </section>
  );
}

interface ContactInfoProps {
  email: string;
  address: string;
  orgName: string;
}

export function ContactInfo({ email, address, orgName }: ContactInfoProps) {
  return (
    <section className="w-full">
      <h3 className="mb-6 text-xl font-semibold text-[#1F2937]">
        Thông tin liên hệ
      </h3>
      <div className="flex flex-col gap-2">
        <div className="flex flex-col md:flex-row md:items-start md:gap-8">
          <div className="mb-2 flex flex-1 items-center gap-3 md:mb-0">
            <svg
              width="40"
              height="40"
              viewBox="0 0 40 40"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M0 20C0 8.95431 8.95431 0 20 0C31.0457 0 40 8.95431 40 20C40 31.0457 31.0457 40 20 40C8.95431 40 0 31.0457 0 20Z"
                fill="#2E228B"
                fillOpacity="0.1"
              />
              <path
                d="M13.9963 14H25.9888C26.1754 14 26.3331 14.0644 26.4619 14.1933C26.5907 14.3222 26.6551 14.48 26.6551 14.6667V25.3333C26.6551 25.52 26.5907 25.6778 26.4619 25.8067C26.3331 25.9356 26.1754 26 25.9888 26H13.9963C13.8098 26 13.6521 25.9356 13.5233 25.8067C13.3945 25.6778 13.3301 25.52 13.3301 25.3333V14.6667C13.3301 14.48 13.3945 14.3222 13.5233 14.1933C13.6521 14.0644 13.8098 14 13.9963 14ZM25.3226 16.8267L20.0459 21.56L14.6626 16.8133V24.6667H25.3226V16.8267ZM15.009 15.3333L20.0326 19.7733L24.9895 15.3333H15.009Z"
                fill="#2E228B"
              />
            </svg>

            <div>
              <div className="text-sm font-medium text-[#6B7280]">Email</div>
              <div className="break-all text-base font-[400] text-[#1F2937]">
                {email}
              </div>
            </div>
          </div>
        </div>
        <div className="mt-2 flex items-center gap-3">
          <svg
            width="40"
            height="40"
            viewBox="0 0 40 40"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M0 20C0 8.95431 8.95431 0 20 0C31.0457 0 40 8.95431 40 20C40 31.0457 31.0457 40 20 40C8.95431 40 0 31.0457 0 20Z"
              fill="#2E228B"
              fillOpacity="0.1"
            />
            <path
              d="M19.9923 25.3602L23.2836 22.0536C23.8877 21.458 24.2963 20.7558 24.5095 19.9469C24.7138 19.1558 24.7138 18.3647 24.5095 17.5736C24.2963 16.7647 23.8899 16.0602 23.2903 15.4602C22.6907 14.8602 21.9867 14.4536 21.1783 14.2402C20.3877 14.0358 19.597 14.0358 18.8064 14.2402C17.998 14.4536 17.294 14.8602 16.6944 15.4602C16.0948 16.0602 15.6884 16.7647 15.4752 17.5736C15.2709 18.3647 15.2709 19.1558 15.4752 19.9469C15.6884 20.7558 16.097 21.458 16.7011 22.0536L19.9923 25.3602ZM19.9923 27.2402L15.755 23.0002C14.9821 22.2358 14.4625 21.3291 14.196 20.2802C13.9295 19.2669 13.9295 18.2536 14.196 17.2402C14.4625 16.1913 14.9799 15.2825 15.7483 14.5136C16.5167 13.7447 17.4251 13.2225 18.4733 12.9469C19.486 12.6891 20.4987 12.6891 21.5114 12.9469C22.5596 13.2225 23.4679 13.7447 24.2364 14.5136C25.0048 15.2825 25.5222 16.1913 25.7887 17.2402C26.0552 18.2536 26.0552 19.2669 25.7887 20.2802C25.5222 21.3291 25.0025 22.2358 24.2297 23.0002L19.9923 27.2402ZM19.9923 20.0936C20.2322 20.0936 20.4543 20.0336 20.6586 19.9136C20.8629 19.7936 21.025 19.6313 21.145 19.4269C21.2649 19.2225 21.3248 19.0002 21.3248 18.7602C21.3248 18.5202 21.2649 18.298 21.145 18.0936C21.025 17.8891 20.8629 17.7269 20.6586 17.6069C20.4543 17.4869 20.2322 17.4269 19.9923 17.4269C19.7525 17.4269 19.5304 17.4869 19.3261 17.6069C19.1218 17.7269 18.9597 17.8891 18.8397 18.0936C18.7198 18.298 18.6598 18.5202 18.6598 18.7602C18.6598 19.0002 18.7198 19.2225 18.8397 19.4269C18.9597 19.6313 19.1218 19.7936 19.3261 19.9136C19.5304 20.0336 19.7525 20.0936 19.9923 20.0936ZM19.9923 21.4269C19.5126 21.4269 19.0685 21.3069 18.6598 21.0669C18.2512 20.8269 17.927 20.5025 17.6871 20.0936C17.4473 19.6847 17.3273 19.238 17.3273 18.7536C17.3273 18.2691 17.4473 17.8247 17.6871 17.4202C17.927 17.0158 18.2512 16.6936 18.6598 16.4536C19.0685 16.2136 19.5126 16.0936 19.9923 16.0936C20.472 16.0936 20.9162 16.2136 21.3248 16.4536C21.7335 16.6936 22.0577 17.0158 22.2976 17.4202C22.5374 17.8247 22.6573 18.2691 22.6573 18.7536C22.6573 19.238 22.5374 19.6847 22.2976 20.0936C22.0577 20.5025 21.7335 20.8269 21.3248 21.0669C20.9162 21.3069 20.472 21.4269 19.9923 21.4269Z"
              fill="#2E228B"
            />
          </svg>

          <div>
            <div className="text-sm font-medium text-[#6B7280]">Địa chỉ</div>
            {address ? (
                <div className="break-words text-base font-[400] text-[#1F2937]">
                  {address}
                </div>
            ) : (
                <p className="whitespace-pre-line text-gray-400 text-sm">Chưa cập nhật</p>
            )}

          </div>
        </div>

        {orgName && (
          <div className="mt-2 flex items-center gap-3">
            <svg
              width="40"
              height="40"
              viewBox="0 0 40 40"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <circle
                cx="20"
                cy="20"
                r="20"
                fill="#2E228B"
                fill-opacity="0.1"
              />
              <path
                d="M15 26V14H25V26M17 18H19M21 18H23M17 21H19M21 21H23M17 24H19M21 24H23"
                stroke="#2E228B"
                stroke-width="1.5"
                stroke-linecap="round"
              />
            </svg>

            <div>
              <div className="text-sm font-medium text-[#6B7280]">Tổ chức</div>
              <div className="mt-1 break-words text-base font-[400] text-[#1F2937]">
                {orgName}
              </div>
            </div>
          </div>
        )}
      </div>
    </section>
  );
}
