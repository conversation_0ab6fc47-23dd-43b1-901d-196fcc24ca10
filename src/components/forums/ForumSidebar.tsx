"use client";

import Image from "next/image";
import Link from "next/link";
import { useParams } from "next/navigation";
import { useEffect, useState } from "react";

import Loading from "@/components/ui/Loading";
import useUploadFile from "@/hook/useUploadFile";
import anonymousServices from "@/services/anonymousServices";
import { RiEyeLine, RiHeartFill, RiHeartLine, RiMessage2Line } from "react-icons/ri";

interface PopularPost {
  postId: number;
  title: string;
  topicThumbNail: string | null;
  likeCount: number;
  isLike: boolean;
  forumId: number;
  viewCount: number;
  commentCount: number;
}

export default function ForumSidebar({ isFull }: { isFull?: boolean }) {
  const { viewFile } = useUploadFile();
  const { eduEcosystemId, forumId, topicId } = useParams();

  const [popularPosts, setPopularPosts] = useState<PopularPost[]>([]);
  const [trendingTags, setTrendingTags] = useState<string[]>([]);
  const [isLoadingTopPost, setLoadingTopPost] = useState(true);
  const [isLoadingHashTag, setLoadingHashTag] = useState(true);

  const payload = {
    eduEcosystemId: eduEcosystemId,
    forumId: forumId,
    topicId: topicId
  };
  useEffect(() => {
    const fetchData = async () => {
      try {
        const responsePopularPosts = await anonymousServices.getPopularPosts(
          payload as any
        );
        const responseTrendingTags = await anonymousServices.getTrendingTags(
          payload as any
        );
        for (const post of responsePopularPosts.data.data) {
          const fileId = post.topicThumbNail;
          try {
            const file = await viewFile(fileId, "forums");
            post.topicThumbNail = file;
          } catch (error) {
            post.topicThumbNail = null;
          }
        }
        setPopularPosts(responsePopularPosts.data.data);
        setTrendingTags(responseTrendingTags.data.data);
      } catch (error) {
      } finally {
        setLoadingTopPost(false);
        setLoadingHashTag(false);
      }
    };

    fetchData();
  }, []);

  return (
    <div className={`w-full ${isFull ? "w-full" : "lg:w-1/3"}`}>
      {/* Top Posts */}
      <div className="mb-6 overflow-hidden rounded-md bg-white shadow-sm">
        <div className="border-b px-4 py-3">
          <h3 className="text-center font-medium sm:text-left">
            Top 10 bài viết nổi bật
          </h3>
        </div>
        <div className="divide-y">
          {isLoadingTopPost ? (
            <div className="flex items-center justify-center py-8">
              <Loading color="primary" size="lg" variant="spinner" />
            </div>
          ) : popularPosts.length !== 0 ? (
            popularPosts.slice(0, 10).map((item, index) => (
              <Link
                className="block"
                href={`/forums/${eduEcosystemId}/topics/${item?.forumId}/posts/${item.postId}`}
                key={item.postId + item.title}
              >
                <div className="flex justify-between space-y-2 px-4 py-3">
                  <div className="flex items-center">
                    <div className="mr-3 flex h-8 w-8 items-center justify-center rounded-full bg-purple-100 text-purple-600">
                      {item.topicThumbNail ? (
                        <Image
                          src={`${item.topicThumbNail}`}
                          alt={item.title}
                          width={32}
                          height={32}
                          className="rounded-full object-cover"
                        />
                      ) : (
                        <i className="ri-building-4-line"></i>
                      )}
                    </div>
                    <a
                      href={`/forums/${eduEcosystemId}/topics/${item?.forumId}/posts/${item.postId}`}
                      className="text-sm hover:text-primary"
                    >
                      {item.title}
                    </a>
                  </div>
                  <div className="flex items-center justify-between gap-3">
                    <span className="flex items-center gap-1 text-xs text-gray-500">
                      <RiEyeLine className="mr-0.5" />
                      <span>{item.viewCount}</span>
                    </span>
                    <span className="flex items-center gap-1 text-xs text-gray-500">
                      <RiMessage2Line className="mr-0.5" />
                      <span>{item.commentCount}</span>
                    </span>
                    <span className="flex items-center gap-1 text-xs text-gray-500">
                     {item.isLike ? <RiHeartFill className="mr-0.5" /> : <RiHeartLine className="mr-0.5" />}
                      <span>{item.likeCount}</span>
                    </span>
                  </div>
                </div>
              </Link>
            ))
          ) : (
            <div className="p-8 text-center text-gray-500">Chưa có dữ liệu</div>
          )}
        </div>
      </div>

      {/* Popular Tags */}
      <div className="overflow-hidden rounded-md bg-white shadow-sm">
        <div className="border-b px-4 py-3">
          <h3 className="font-medium">Từ khóa tìm kiếm nhiều nhất</h3>
        </div>
        <div className="p-4">
          {isLoadingHashTag ? (
            <div className="flex items-center justify-center py-8">
              <Loading color="primary" size="lg" variant="spinner" />
            </div>
          ) : trendingTags.length !== 0 ? (
            <div className="w-100 flex flex-wrap gap-2">
              {trendingTags.map((tag, index) => (
                <Link
                  key={index + tag}
                  href={`/forums/${eduEcosystemId}/search?keyword=${tag}`}
                  className="rounded-full bg-blue-50 px-3 py-1 text-sm text-blue-600 hover:bg-blue-100"
                >
                  {tag}
                </Link>
              ))}
            </div>
          ) : (
            <div className="p-4 text-center text-gray-500">Chưa có dữ liệu</div>
          )}
        </div>
      </div>
    </div>
  );
}
