import Image from "next/image";
import React from "react";

type OverlayTextProps = {
  url?: string;
  scale?: string;
};
const OverlayText: React.FC<OverlayTextProps> = ({
  scale = "scale-[0.8]",
  url
}) => (
  <div className="absolute bottom-0 left-0 right-0 top-0 z-[-1]">
    <Image
      src={url || "/logo-2.svg"}
      className={`h-full ${scale} w-full object-contain opacity-[10%]`}
      alt="logo"
      width={120}
      height={120}
    />
  </div>
);

export default OverlayText;
