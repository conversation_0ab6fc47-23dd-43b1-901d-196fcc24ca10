import React from "react";
import { MdChevronLeft, MdChevronRight, MdFirstPage, MdLastPage } from "react-icons/md";

interface PaginationProps {
  totalPages: number;
  currentPage: number;
  onPageChange: (page: number) => void;
}

const Pagination: React.FC<PaginationProps> = ({
  totalPages = 10,
  currentPage = 2,
  onPageChange,
}) => {
  const safeTotalPages = Number.isFinite(totalPages) && totalPages > 0 ? totalPages : 1;
  const safeCurrentPage =
    Number.isFinite(currentPage) && currentPage > 0 && currentPage <= safeTotalPages
      ? currentPage
      : 1;

  if (safeTotalPages <= 1) return null;

  const handleClick = (page: number) => {
    if (page !== safeCurrentPage && page >= 1 && page <= safeTotalPages) {
      onPageChange(page);
    }
  };

  // Hiển thị tối đa 5 số trang liên tiếp
  const getPageNumbers = () => {
    let start = Math.max(1, safeCurrentPage - 2);
    let end = Math.min(safeTotalPages, safeCurrentPage + 2);

    // Nếu ở đầu, luôn là 1-5
    if (safeCurrentPage <= 3) {
      start = 1;
      end = Math.min(5, safeTotalPages);
    }
    // Nếu ở cuối, luôn là (totalPages-4)-totalPages
    if (safeCurrentPage >= safeTotalPages - 2) {
      end = safeTotalPages;
      start = Math.max(1, safeTotalPages - 4);
    }

    const pages: number[] = [];
    for (let i = start; i <= end; i++) {
      pages.push(i);
    }
    return pages;
  };

  return (
    <div className="flex items-center justify-center mt-4 gap-1 select-none">
      <button
        onClick={() => handleClick(1)}
        disabled={safeCurrentPage === 1}
        className="p-2 rounded hover:bg-gray-100 disabled:opacity-50"
        aria-label="First"
      >
        <MdFirstPage size={20} />
      </button>
      <button
        onClick={() => handleClick(safeCurrentPage - 1)}
        disabled={safeCurrentPage === 1}
        className="p-2 rounded hover:bg-gray-100 disabled:opacity-50"
        aria-label="Previous"
      >
        <MdChevronLeft size={20} />
      </button>
      {getPageNumbers().map((page) => (
        <button
          key={page}
          onClick={() => handleClick(page)}
          className={`px-2 py-1 rounded text-sm font-medium transition ${
            page === safeCurrentPage
              ? "bg-blue-500 text-white"
              : "bg-gray-100 text-gray-700 hover:bg-blue-100"
          }`}
          disabled={page === safeCurrentPage}
        >
          {page}
        </button>
      ))}
      <button
        onClick={() => handleClick(safeCurrentPage + 1)}
        disabled={safeCurrentPage === safeTotalPages}
        className="p-2 rounded hover:bg-gray-100 disabled:opacity-50"
        aria-label="Next"
      >
        <MdChevronRight size={20} />
      </button>
      <button
        onClick={() => handleClick(safeTotalPages)}
        disabled={safeCurrentPage === safeTotalPages}
        className="p-2 rounded hover:bg-gray-100 disabled:opacity-50"
        aria-label="Last"
      >
        <MdLastPage size={20} />
      </button>
    </div>
  );
};

export default Pagination;
