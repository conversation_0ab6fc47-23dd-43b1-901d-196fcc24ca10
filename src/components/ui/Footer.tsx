"use client";
import { usePathname } from "next/navigation";
import {
  FaFacebookF,
  FaTiktok,
  FaYoutube
} from "react-icons/fa";

export default function Footer() {
  const pathname = usePathname();

  if (pathname.startsWith("/login") || pathname.startsWith("/register")) {
    return null;
  }
  return (
    <footer className="bg-white border-t">
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Contact Info */}
          <div>
            <h3 className="text-lg font-medium mb-4"><PERSON><PERSON><PERSON> hệ</h3>
            <div className="space-y-2 text-gray-600 text-sm">
              <p>
                Trụ sở chính: Số 98 Phố Dương Quảng Hàm, Phường Quan Hoa, Quận
                Cầu Giấy, TP. Hà Nội
              </p>
              <p>(+84) 24.3833.0708</p>
              <p>(+84) 24.3833.5426</p>
              <p><EMAIL></p>
            </div>
          </div>
          {/* Social Links */}
          <div>
            <h3 className="text-lg font-medium mb-4">Mạng xã hội</h3>
            <div className="flex space-x-4">
              <a
                href="https://www.facebook.com/hnmu.edu.vn"
                target="_blank"
                rel="noreferrer"
                className="text-gray-600 hover:text-primary flex items-center"
              >
                <div className="w-5 h-5 flex items-center justify-center mr-2">
                  <FaFacebookF />
                </div>
                Facebook
              </a>
              <a
                href="https://www.youtube.com/@truongaihocthuohanoi8802/videos"
                target="_blank"
                rel="noreferrer"
                className="text-gray-600 hover:text-primary flex items-center"
              >
                <div className="w-5 h-5 flex items-center justify-center mr-2">
                  <FaYoutube />
                </div>
                YouTube
              </a>
              <a
                href="https://www.tiktok.com/@hnmuofficial1959"
                target="_blank"
                rel="noreferrer"
                className="text-gray-600 hover:text-primary flex items-center"
              >
                <div className="w-5 h-5 flex items-center justify-center mr-2">
                  <FaTiktok />
                </div>
                TikTok
              </a>
            </div>
          </div>
        </div>
        {/* Copyright */}
        <div className="border-t mt-8 pt-4 text-sm text-gray-500">
          <p>Copyright ©2025 Đại học Thủ đô Hà Nội</p>
        </div>
      </div>
    </footer>
  );
}
