import React from "react";
import { Worker } from "@react-pdf-viewer/core";
import { Viewer, SpecialZoomLevel } from "@react-pdf-viewer/core";
import { defaultLayoutPlugin } from "@react-pdf-viewer/default-layout";

import "@react-pdf-viewer/core/lib/styles/index.css";
import "@react-pdf-viewer/default-layout/lib/styles/index.css";

const PdfViewer = ({ pdfUrl }: { pdfUrl: string }) => {
  // Initialize the default layout plugin with all toolbar controls
  const defaultLayoutPluginInstance = defaultLayoutPlugin({
    toolbarPlugin: {}
  });

  return (
    <div className="max-h-[750px] w-full overflow-y-auto">
      <Worker
        workerUrl={`https://unpkg.com/pdfjs-dist@3.11.174/build/pdf.worker.min.js`}
      >
        <Viewer
          fileUrl={pdfUrl}
          plugins={[defaultLayoutPluginInstance]}
          defaultScale={1.0}
        />
      </Worker>
    </div>
  );
};

export default PdfViewer;
