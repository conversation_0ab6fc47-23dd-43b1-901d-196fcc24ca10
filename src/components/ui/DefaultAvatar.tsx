'use client';

import React from 'react';

interface DefaultAvatarProps {
  name: string;
  size?: number | string;
  className?: string;
  width?: number;
  height?: number;
}

/**
 * Component hiển thị avatar mặc định với chữ cái đầu tiên của tên người dùng
 */
const DefaultAvatar: React.FC<DefaultAvatarProps> = ({ name, size = '100%', className = '', width, height }) => {
  // Lấy chữ cái đầu tiên của tên
  const initial = name.trim()?.charAt(0)?.toUpperCase() || '?';
  
  // Tạo màu ngẫu nhiên dựa trên tên người dùng
  const getColorFromName = (name: string) => {
    const colors = [
      '#F44336', '#E91E63', '#9C27B0', '#673AB7', '#3F51B5',
      '#2196F3', '#03A9F4', '#00BCD4', '#009688', '#4CAF50',
      '#8BC34A', '#CDDC39', '#FFC107', '#FF9800', '#FF5722'
    ];
    
    let hash = 0;
    for (let i = 0; i < name.length; i++) {
      hash = name.charCodeAt(i) + ((hash << 5) - hash);
    }
    
    return colors[Math.abs(hash) % colors.length];
  };
  
  const bgColor = getColorFromName(name);
  
  return (
    <div 
      className={`flex items-center justify-center rounded-full text-white font-semibold aspect-square ${className}`}
      style={{
        backgroundColor: bgColor,
        width: width ? width : '100%',
        height: height ? height : '100%',
        fontSize: typeof size === 'number' ? `${size / 2}px` : '100%'
      }}
    >
      {initial}
    </div>
  );
};

export default DefaultAvatar;
