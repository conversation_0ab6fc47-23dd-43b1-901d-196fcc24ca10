import React from "react";
import Image from "next/image";

type HeroSectionProps = {
  title: string;
  description?: string;
  backgroundImageUrl: string;
  backgroundSize?: string;
  backgroundPosition?: string;
  onImageError?: (e: React.SyntheticEvent<HTMLDivElement, Event>) => void;
};

const HeroSection: React.FC<HeroSectionProps> = ({
  title,
  description,
  backgroundImageUrl,
  backgroundSize,
  backgroundPosition,
  onImageError
}) => (
  <section className="relative">
    <div
      className="relative flex h-[300px] items-center bg-blue-900 lg:h-[420px]"
      style={{
        backgroundImage: `url(${backgroundImageUrl.startsWith('http') ? backgroundImageUrl : `/${backgroundImageUrl}`})`,
        backgroundSize: `${backgroundSize || "cover"}`,
        backgroundPosition: `${backgroundPosition || "bottom"}`
      }}
      onError={onImageError}
    >
      {/* Overlay background color black with 2% opacity */}
      <div className="absolute inset-0 bg-black opacity-[40%]"></div>
      <div className="container relative z-10 mx-auto space-y-6 text-pretty pt-[4rem]">
        <h1 className="text-[28px] font-bold !leading-[1.3] text-white lg:text-6xl ">
          {title}
        </h1>
        <p className="hidden w-[95%] text-xl text-white lg:block ">
          {description}
        </p>
      </div>
    </div>
  </section>
);

export default HeroSection;
