'use client';

import React, { ChangeEvent } from 'react';
import { FaCamera, FaTrash } from 'react-icons/fa';
import Image from 'next/image';

interface ImageUploaderProps {
  selectedImage: string | null;
  onImageSelect: (imageDataUrl: string) => void;
  onImageRemove: () => void;
  buttonText?: string;
}

const ImageUploader: React.FC<ImageUploaderProps> = ({
  selectedImage,
  onImageSelect,
  onImageRemove,
  buttonText = 'Thêm ảnh'
}) => {
  const handleImageChange = (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (event) => {
        if (event.target?.result) {
          onImageSelect(event.target.result as string);
        }
      };
      reader.readAsDataURL(file);
    }
  };

  return (
    <div>
      {selectedImage && (
        <div className="mt-3 relative">
          <div className="relative h-48 w-full rounded-lg overflow-hidden">
            <Image 
              src={selectedImage} 
              alt="Selected image" 
              fill 
              className="object-cover" 
            />
          </div>
          <button 
            onClick={onImageRemove}
            className="absolute top-2 right-2 bg-white rounded-full p-1 shadow-md"
          >
            <FaTrash className="text-red-500" />
          </button>
        </div>
      )}
      
      <div className="mt-3">
        <label htmlFor="image-upload" className="flex items-center gap-1 text-gray-600 hover:text-gray-800 cursor-pointer">
          <FaCamera />
          <span>{buttonText}</span>
          <input 
            id="image-upload" 
            type="file" 
            accept="image/*" 
            className="hidden" 
            onChange={handleImageChange}
          />
        </label>
      </div>
    </div>
  );
};

export default ImageUploader;
