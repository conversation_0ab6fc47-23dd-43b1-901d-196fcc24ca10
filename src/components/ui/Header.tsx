"use client";

import { FaAngleDown } from "react-icons/fa6";

import { useLayoutEffect, useState } from "react";
import Image from "next/image";
import { usePathname } from "next/navigation";
import Link from "next/link";
import { useAuth } from "@/context/AuthContext";

const Header = ({
  dataMenu,
  dataOfMenuChild
}: {
  dataMenu: { name: string; href: string }[];
  dataOfMenuChild: {
    title: string;
    button: { name: string }[];
    items: { name: string; href: string; image: string }[];
  }[];
}) => {
  const pathname = usePathname();
  const [isActive, setIsActive] = useState<number | null>(null);
  const [checkCondition, setCheckCondition] = useState("/logo.png");
  const { isAuthenticated, logout } = useAuth();

  useLayoutEffect(() => {
    // const data = [
    //   "kinder-garten",
    //   "elementary",
    //   "middle-school",
    //   "high-school"
    // ];
    const pathNameSplit = pathname?.split("/")[1];
    if(pathNameSplit === "kinder-garten"){
      setCheckCondition("/logo-2.svg");
    }
    else if(pathNameSplit === "elementary"){
      setCheckCondition("/elementary/logo.png");
    }
    else if(pathNameSplit === "middle-school"){
      setCheckCondition("/middle-school/logo.png");
    }
    else if(pathNameSplit === "high-school"){
      setCheckCondition("/high-school/logo.png");
    }
    else{
      setCheckCondition("/logo.png");
    }
    // setCheckCondition(
    //   data.includes(pathname?.split("/")[1]) ? "/logo-2.svg" : "/logo.png"
    // );
  }, []);
  const handleSetActive = (index: number | null) => {
    if (index === null) {
      setIsActive(null);
      return;
    }
    if (dataOfMenuChild[index]?.items?.length) {
      setIsActive(isActive === index ? null : index);
    }
  };
  const handleRedirect = () => {
    if(pathname.startsWith('/kinder-garten'))
      window.location.href = "/kinder-garten";
    else if(pathname.startsWith('/elementary'))
      window.location.href = "/elementary";
    else if(pathname.startsWith('/middle-school'))
      window.location.href = "/middle-school";
    else if(pathname.startsWith('/high-school'))
      window.location.href = "/high-school";
    else
      window.location.href = "/";
  };
  if (pathname.startsWith("/login") || pathname.startsWith("/register")) {
    return null;
  }
  return (
    <section
      className={`group ${pathname.startsWith("/forums") && "!bg-[#10B3D6]"} ${typeof isActive === "number" && "!bg-[#3438a4]"} absolute left-0 right-0 top-0 z-[200] h-[115.19px] !px-0 transition-all`}
    >
      <div className="container relative mx-auto flex h-full items-center !px-0 ">
        <div className="flex w-full h-full items-center justify-between">
          <div className="cursor-pointer" onClick={() => handleRedirect()}>
            <Image src={checkCondition} alt="logo" width={120} height={120} />
          </div>
          <div className="">
            <ul className="flex items-center gap-10 text-white">
              {dataMenu?.map((item, index) => {
                if (item.href === "/login" && isAuthenticated) {
                  return (
                    <li
                      key={index + item.href}
                      onClick={() => logout()}
                      className="group flex cursor-pointer items-center gap-2"
                    >
                      <p className="flex gap-1">
                        <span
                          className={`relative flex gap-1 transition-all after:absolute after:bottom-[-2px] after:left-0 after:w-full after:border-b-[2px] after:content-[''] ${isActive === index
                            ? "after:border-white"
                            : "after:border-transparent group-hover:after:border-white"
                            }`}
                        >
                          Đăng xuất
                        </span>
                      </p>
                    </li>
                  );
                }
                return (
                  <li
                    key={index + item.href}
                    onClick={() => handleSetActive(index)}
                    className="group flex cursor-pointer items-center gap-2"
                  >
                    <p className="flex gap-1">
                      <Link
                        href={item.href}
                        className={`relative flex gap-1 transition-all after:absolute after:bottom-[-2px] after:left-0 after:w-full after:border-b-[2px] after:content-[''] ${isActive === index
                          ? "after:border-white"
                          : "after:border-transparent group-hover:after:border-white"
                          }`}
                      >
                        {item.name}
                      </Link>
                    </p>
                    {!!dataOfMenuChild[index]?.items?.length && (
                      <FaAngleDown
                        className={`${isActive === index ? "rotate-180" : ""}`}
                      />
                    )}
                  </li>
                );
              })}
              <div
                className={`group/edit flex items-center gap-2 text-white xl:top-[3rem] 2xl:absolute 2xl:right-[-14rem]`}
              >
                <div className="relative flex items-center gap-2">
                  VN <FaAngleDown className={`group-hover/edit:rotate-180`} />
                  <div className="absolute left-[-2rem] top-[1.5rem] z-50 hidden w-[100px] rounded-lg bg-white p-2 text-black group-hover/edit:block">
                    <div className="flex flex-col gap-4">
                      <p className="cursor-pointer transition-all hover:bg-gray-200">
                        EN
                      </p>
                      <p className="cursor-pointer transition-all hover:bg-gray-200">
                        VN
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </ul>
          </div>
        </div>
        {!pathname.startsWith("/forums") && (
            <div className="absolute opacity-0 lg:opacity-100 bottom-0 left-0 right-0 h-[1px] bg-white transition-all"></div>
        )}
      </div>
      {isActive !== null && (
        <div className="h-fit animate-fadeIn bg-[#3438a4] pb-12">
          <div className=" container relative mx-auto flex !px-0 pt-[2rem] text-white">
            <div className="w-[30%] space-y-4">
              <p className="pr-3 text-2xl font-semibold">
                {dataOfMenuChild[isActive]?.title}
              </p>
              <div className="flex w-fit flex-col gap-4">
                {dataOfMenuChild[isActive]?.button?.map((btn, btnIndex) => (
                  <button
                    key={btnIndex}
                    className="w-fit rounded-3xl bg-[#0e4280] px-4 py-2"
                  >
                    {btn?.name}
                  </button>
                ))}
              </div>
            </div>
            <div className="w-[80%]">
              <div className="grid grid-cols-4 gap-[20px]">
                {dataOfMenuChild[isActive]?.items?.map((item, itemIndex) => (
                  <div
                    key={itemIndex + item.href}
                    className="relative flex transform items-center justify-center overflow-hidden rounded-lg transition-transform duration-300 ease-in-out hover:scale-110 bg-black bg-opacity-70"
                  >
                    <Link onClick={() => handleSetActive(null)} href={item.href}>
                      <Image
                        src={item.image}
                        alt={item.name}
                        width={226}
                        height={299}
                        className="rounded-lg h-[299px] object-cover"
                      />
                      {/* Overlay mờ */}
                      <div className="absolute inset-0 bg-black opacity-50"></div>
                      <span className="text-center absolute inset-0 flex items-center justify-center px-[1rem] text-lg font-semibold text-white z-10">
                        {item.name}
                      </span>
                    </Link>
                  </div>
                ))}
              </div>
            </div>

          </div>
        </div>
      )}
    </section>
  );
};

export default Header;
