"use client";

import { FaAngleDown, FaCircleArrowRight } from "react-icons/fa6";

import {
  AwaitedReactNode,
  JSXElementConstructor,
  Key,
  ReactElement,
  ReactNode,
  ReactPortal,
  useState
} from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { UrlObject } from "url";

const MenuSelected = ({ dataMenu }: { dataMenu: any }) => {
  const router = useRouter();
  const [isActive, setIsActive] = useState<number | null>(null);

  const handleSetActive = (index: number, link: string) => {
    if (link && (link !== '/')) {
      router.push(link);
    }
    setIsActive(isActive === index ? null : index);
  };
  const handleResetActive = () => {
    setIsActive(null);
  }

  return (
    <div className="relative  !mt-0 bg-[#f1f4f6]">
      <div className="container mx-auto h-12">
        <div className=" flex h-full items-center gap-4 space-x-4 overflow-y-auto">
          {dataMenu?.map(
            (
              menu: {
                href: string;
                title:
                  | string
                  | number
                  | bigint
                  | boolean
                  | ReactElement<any, string | JSXElementConstructor<any>>
                  | Iterable<ReactNode>
                  | ReactPortal
                  | Promise<AwaitedReactNode>
                  | null
                  | undefined;
                child: string | any[];
              },
              index: number
            ) => (
              <>
                <div
                  key={index}
                  onClick={() => handleSetActive(index, menu.href)}
                  className="relative flex h-full cursor-pointer items-center whitespace-nowrap"
                >
                  <span className="text-[#414B5B]">{menu?.title}</span>
                  {!!menu?.child?.length && (
                    <FaAngleDown className="ml-2 text-[#b1babe]" size={12} />
                  )}
                  {index === isActive && (
                    <div className="absolute bottom-0 left-0 right-0 h-[1px] animate-fadeIn bg-main"></div>
                  )}
                </div>
              </>
            )
          )}
        </div>
      </div>
      {typeof isActive === "number" && !!dataMenu[isActive]?.child?.length && (
        <div className="absolute left-0 right-0 top-full z-10 mt-2 flex w-full animate-fadeIn gap-20  bg-white shadow-lg">
          <div className="container mx-auto flex h-[10rem] items-center justify-between md:justify-normal md:gap-20">
            {dataMenu[isActive]?.child?.map(
              (
                child: {
                  href: string | UrlObject;
                  title:
                    | string
                    | number
                    | bigint
                    | boolean
                    | ReactElement<any, string | JSXElementConstructor<any>>
                    | Iterable<ReactNode>
                    | ReactPortal
                    | Promise<AwaitedReactNode>
                    | null
                    | undefined;
                },
                childIndex: Key | null | undefined
              ) => (
                <Link
                  onClick={handleResetActive}
                  key={childIndex}
                  href={child.href}
                  className=" text-gray-700 "
                >
                  {child.title}
                </Link>
              )
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default MenuSelected;
