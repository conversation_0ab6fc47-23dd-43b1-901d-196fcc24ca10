'use client';

import React from 'react';

interface DefaultCoverProps {
  className?: string;
  height?: number | string;
}

/**
 * Component hiển thị cover mặc định với nền màu xám
 */
const DefaultCover: React.FC<DefaultCoverProps> = ({ className = '', height = '200px' }) => {
  return (
    <div 
      className={`bg-gray-200 w-full flex items-center justify-center ${className}`}
      style={{ height }}
    >
    </div>
  );
};

export default DefaultCover;
