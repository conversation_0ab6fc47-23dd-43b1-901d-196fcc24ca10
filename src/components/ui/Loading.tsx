import React from 'react';

interface LoadingProps {
  /**
   * <PERSON><PERSON><PERSON> sắc của loading
   * @default 'primary'
   */
  color?: 'primary' | 'secondary' | 'white' | 'black';

  /**
   * <PERSON><PERSON><PERSON> thước của loading
   * @default 'md'
   */
  size?: 'sm' | 'md' | 'lg' | 'xl';

  /**
   * <PERSON><PERSON>u dáng của loading
   * @default 'spinner'
   */
  variant?: 'spinner' | 'dots' | 'pulse';

  /**
   * Text hiển thị bên dư<PERSON> loading
   */
  text?: string;

  /**
   * Lớp CSS tùy chỉnh
   */
  className?: string;
}

const Loading: React.FC<LoadingProps> = ({
  color = 'primary',
  size = 'md',
  variant = 'spinner',
  text,
  className = '',
}) => {
  // Map màu sắc
  const colorMap = {
    primary: '#4F46E5',
    secondary: '#1d2746',
    white: 'white',
    black: 'black',
  };

  // Map kích thước
  const sizeMap = {
    sm: {
      spinner: 'h-4 w-4',
      dots: 'gap-1',
      dotSize: 'h-1.5 w-1.5',
      pulse: 'h-6 w-6',
      text: 'text-xs',
    },
    md: {
      spinner: 'h-6 w-6',
      dots: 'gap-1.5',
      dotSize: 'h-2 w-2',
      pulse: 'h-8 w-8',
      text: 'text-sm',
    },
    lg: {
      spinner: 'h-8 w-8',
      dots: 'gap-2',
      dotSize: 'h-2.5 w-2.5',
      pulse: 'h-12 w-12',
      text: 'text-base',
    },
    xl: {
      spinner: 'h-12 w-12',
      dots: 'gap-3',
      dotSize: 'h-3 w-3',
      pulse: 'h-16 w-16',
      text: 'text-lg',
    },
  };

  // Render spinner
  const renderSpinner = () => (
    <div
      className={`${sizeMap[size].spinner} border-2 border-t-transparent rounded-full animate-spin`}
      style={{ 
        borderColor: `${colorMap[color]} transparent ${colorMap[color]} ${colorMap[color]}`,
        animationDuration: '0.6s'
      }}
    />
  );

  // Render dots
  const renderDots = () => (
    <div className={`flex ${sizeMap[size].dots}`}>
      {[0, 1, 2].map((i) => (
        <div
          key={i}
          className={`${sizeMap[size].dotSize} rounded-full animate-bounce`}
          style={{
            backgroundColor: colorMap[color],
            animationDelay: `${i * 0.1}s`,
            animationDuration: '0.5s'
          }}
        />
      ))}
    </div>
  );

  // Render pulse
  const renderPulse = () => (
    <div
      className={`${sizeMap[size].pulse} rounded-full animate-pulse`}
      style={{ 
        backgroundColor: colorMap[color],
        animationDuration: '0.8s'
      }}
    />
  );

  // Render content dựa trên variant
  const renderContent = () => {
    switch (variant) {
      case 'spinner':
        return renderSpinner();
      case 'dots':
        return renderDots();
      case 'pulse':
        return renderPulse();
      default:
        return renderSpinner();
    }
  };

  return (
    <div className={`flex flex-col items-center justify-center ${className}`}>
      {renderContent()}
      {text && <p className={`mt-2 ${sizeMap[size].text}`} style={{ color: colorMap[color] }}>{text}</p>}
    </div>
  );
};

export default Loading; 