"use client";

import PdfViewer from "../ui/PdfViewer";
import React, { useState } from "react";

type IconButtonProps = {
  label: string;
  onClick: () => void;
  icon: React.ReactNode;
};

type IconButtonExample = {
  fileUrl: string;
  label?: string;
  isBtnDownload?: boolean;
  isPreview?: boolean;
};

const IconButton: React.FC<IconButtonProps> = ({ label, onClick, icon }) => {
  return (
    <button
      className="flex h-[64px] min-w-[149px] items-center justify-center gap-2 border border-[#414B5B] px-[20px] text-[#414B5B]"
      onClick={onClick}
    >
      {label}
      {icon}
    </button>
  );
};

const PreviewIcon = (
  <svg
    width="25"
    height="24"
    viewBox="0 0 25 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M10.5002 5H8.7002C7.58009 5 7.01962 5 6.5918 5.21799C6.21547 5.40973 5.90973 5.71547 5.71799 6.0918C5.5 6.51962 5.5 7.08009 5.5 8.2002V15.8002C5.5 16.9203 5.5 17.4801 5.71799 17.9079C5.90973 18.2842 6.21547 18.5905 6.5918 18.7822C7.0192 19 7.57899 19 8.69691 19H16.3031C17.421 19 17.98 19 18.4074 18.7822C18.7837 18.5905 19.0905 18.2839 19.2822 17.9076C19.5 17.4802 19.5 16.921 19.5 15.8031V14M20.5 9V4M20.5 4H15.5M20.5 4L13.5 11"
      stroke="#414B5B"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

const DownloadIcon = (
  <svg
    width="25"
    height="24"
    viewBox="0 0 25 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12.5 12V18M12.5 18L15.5 16M12.5 18L9.5 16M13.5 3.00087C13.4045 3 13.2973 3 13.1747 3H8.7002C7.58009 3 7.01962 3 6.5918 3.21799C6.21547 3.40973 5.90973 3.71547 5.71799 4.0918C5.5 4.51962 5.5 5.08009 5.5 6.2002V17.8002C5.5 18.9203 5.5 19.4801 5.71799 19.9079C5.90973 20.2842 6.21547 20.5905 6.5918 20.7822C7.0192 21 7.57899 21 8.69691 21H16.3031C17.421 21 17.98 21 18.4074 20.7822C18.7837 20.5905 19.0905 20.2842 19.2822 19.9079C19.5 19.4805 19.5 18.9215 19.5 17.8036V9.32568C19.5 9.20296 19.5 9.09561 19.4991 9M13.5 3.00087C13.7856 3.00347 13.9663 3.01385 14.1388 3.05526C14.3429 3.10425 14.5379 3.18526 14.7168 3.29492C14.9186 3.41857 15.0918 3.59182 15.4375 3.9375L18.563 7.06298C18.9089 7.40889 19.0809 7.58136 19.2046 7.78319C19.3142 7.96214 19.3953 8.15726 19.4443 8.36133C19.4857 8.53376 19.4963 8.71451 19.4991 9M13.5 3.00087V5.8C13.5 6.9201 13.5 7.47977 13.718 7.90759C13.9097 8.28392 14.2155 8.59048 14.5918 8.78223C15.0192 9 15.579 9 16.6969 9H19.4991M19.4991 9H19.5002"
      stroke="#414B5B"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

const Modal: React.FC<{ onClose: () => void; fileUrl: string }> = ({
  onClose,
  fileUrl
}) => {
  return (
    <div className="fixed inset-0 z-[200] flex items-center justify-center bg-black bg-opacity-50">
      <div className="w-[80%] rounded bg-white p-4">
        <button onClick={onClose} className="mb-4">
          Đóng
        </button>
        <PdfViewer pdfUrl={fileUrl} />
      </div>
    </div>
  );
};

const IconButtonExample: React.FC<IconButtonExample> = ({
  fileUrl,
  label = "Preview",
  isBtnDownload = true,
  isPreview = true
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handlePreview = () => {
    setIsModalOpen(true);
  };

  const handleDownload = () => {
    const getTypeOfFile = fileUrl.split(".").pop(); // Get the file type
    const filename = `${Math.random().toString(36).substring(2, 15)}.${getTypeOfFile}`; // Generate a random file name
    downloadFile(fileUrl, filename);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  const downloadFile = (url: string, filename: string) => {
    fetch(url)
      .then((response) => response.blob())
      .then((blob) => {
        const link = document.createElement("a");
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      })
      .catch(console.error);
  };

  return (
    <div className="flex gap-4">
      {isModalOpen && <Modal fileUrl={fileUrl} onClose={handleCloseModal} />}
      {isPreview && (
        <IconButton label={label} onClick={handlePreview} icon={PreviewIcon} />
      )}
      {isBtnDownload && (
        <IconButton
          label="Tải xuống"
          onClick={handleDownload}
          icon={DownloadIcon}
        />
      )}
    </div>
  );
};

export default IconButtonExample;
