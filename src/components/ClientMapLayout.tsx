'use client';

import { SWRProvider } from "@/context/SwrProvider";
import Footer from "@/components/ui/Footer";
import HeroSection from "@/components/ui/HeroSection";
import MenuSelected from "@/components/ui/MenuSelected";
import ClientHeader from "@/components/ClientHeader";

interface ClientMapLayoutProps {
  children: React.ReactNode;
  dataMenu: any[];
  dataOfMenuChild: any[];
  dataMenuSelected: any[];
}

export default function ClientMapLayout({
  children,
  dataMenu,
  dataOfMenuChild,
  dataMenuSelected
}: ClientMapLayoutProps) {
  return (
    <>
      <ClientHeader dataMenu={dataMenu} dataOfMenuChild={dataOfMenuChild} />
      <main className="flex h-svh flex-col">
        <section className="flex-1">
          <HeroSection
            description="<PERSON>ản đồ hệ sinh thái học tập, sáng tạo"
            title="BẢN ĐỒ HỆ SINH THÁI HỌC TẬP, SÁNG TẠO"
            backgroundImageUrl="/banner.png"
          />
          <MenuSelected dataMenu={dataMenuSelected} />
          <SWRProvider>{children}</SWRProvider>
        </section>
        <Footer />
      </main>
    </>
  );
} 