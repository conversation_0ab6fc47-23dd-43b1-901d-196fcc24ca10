"use client";

import Image from "next/image";
import Link from "next/link";
import { useEffect, useState } from "react";
import { useParams } from "next/navigation";

import anonymousServices from "@/services/anonymousServices";
import useUploadFile from "@/hook/useUploadFile";

interface PopularPost {
  postId: number;
  title: string;
  userCreatedThumbNail: string | null;
  likeCount: number;
  forumId: number;
}

export default function ForumSidebar() {
  const { viewFile } = useUploadFile();
  const { eduEcosystemId, forumId } = useParams();

  const [popularPosts, setPopularPosts] = useState<PopularPost[]>([]);
  const [trendingTags, setTrendingTags] = useState<string[]>([]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const responsePopularPosts = await anonymousServices.getPopularPosts();
        const responseTrendingTags = await anonymousServices.getTrendingTags();
        for (const post of responsePopularPosts.data.data) {
          const fileId = post.userCreatedThumbNail;
          try {
            const file = await viewFile(fileId, "forums");
            post.userCreatedThumbNail = file;
          } catch (error) {
            post.userCreatedThumbNail = null;
          }
        }
        setPopularPosts(responsePopularPosts.data.data);
        setTrendingTags(responseTrendingTags.data.data);
      } catch (error) {}
    };

    fetchData();
  }, []);

  return (
    <div className="w-full lg:w-4/12">
      {/* Popular Posts Widget */}
      <div className="mb-6 rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
        <h4 className="mb-4 border-b pb-2 text-lg font-semibold text-indigo-900">
          Bài viết nổi bật
        </h4>
        <ul className="space-y-4">
          {popularPosts?.slice(0, 10).map((item) => (
            <Link className="block" href={`/forums/${eduEcosystemId}/topics/${forumId}/posts/${item.postId}`} key={item.postId}>
              <li className="group flex items-center gap-3">
                <div className="relative h-8 w-8">
                  {item.userCreatedThumbNail ? (
                    <Image
                      src={`${item.userCreatedThumbNail}`}
                      alt={item.title}
                      fill
                      className="rounded object-cover"
                      sizes="(max-width: 768px) 32px, 32px"
                    />
                  ) : (
                    <div className="flex h-full w-full items-center justify-center rounded bg-gray-100">
                      <span className="text-xs text-gray-500"></span>
                    </div>
                  )}
                </div>
                <p
                  className="flex-1 truncate text-gray-700 transition-colors hover:text-indigo-600"
                >
                  {item.title}
                </p>
                <span className="text-sm text-gray-500">
                  {item.likeCount} likes
                </span>
              </li>
            </Link>
          ))}
        </ul>
      </div>

      {/* Tags Widget */}
      <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
        <h4 className="mb-4 border-b pb-2 text-lg font-semibold text-indigo-900">
          Từ khóa tìm kiếm nhiều nhất
        </h4>
        <div className="flex flex-wrap gap-2">
          {trendingTags?.map((tag, index) => (
            <p
              key={index}
              className="rounded-full bg-indigo-50 px-3 py-1.5 text-sm text-indigo-700 transition-colors hover:bg-indigo-100 hover:text-indigo-700"
            >
              {tag}
            </p>
          ))}
        </div>
      </div>
    </div>
  );
}
