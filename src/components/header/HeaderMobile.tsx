"use client";

import { FaAngleDown, FaBars, FaX, FaAngleLeft } from "react-icons/fa6";

import { useState } from "react";
import Image from "next/image";
import { usePathname } from "next/navigation";
import Link from "next/link";

type HeaderProps = {

  dataMenu: { name: string; href: string }[];

  dataOfMenuChild: {

    title: string;

    button: { name: string }[];

    items: { name: string; href: string; image: string }[];

  }[];

}

const Header: React.FC<HeaderProps> = ({ dataMenu, dataOfMenuChild }) => {
  const [isActive, setIsActive] = useState<number | null>(null);
  const [isShow, setIsShow] = useState(false);
  const pathname = usePathname();

  const handleSetActive = (index: any) => {
    if (dataOfMenuChild[index]?.items?.length) {
      setIsActive(isActive === index ? null : index);
    }
  };

  const handleRedirect = () => {
    window.location.href = "/";
  };

  const handleShowActive = () => {
    setIsShow(!isShow);
    setIsActive(null);
    if (!isShow) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "auto";
    }
  };
  if (pathname.startsWith('/login') || pathname.startsWith('/register')) {
    return null;
  }
  return (
    <header
      className={`group !bg-[#3438a4] ${typeof isActive === "number" && "!bg-[#3438a4]"} absolute left-0 right-0 top-0 z-[200] h-[115.19px] px-4 transition-all hover:bg-[#3438a4] lg:!px-0`}
    >
      <div className="container relative mx-auto flex h-full items-center !px-0 ">
        <div className="flex w-full items-center justify-between">
          <div
            className="w-[80px] lg:w-[120px]"
            onClick={() => handleRedirect()}
          >
            <Image src="/logo.png" alt="logo" width={120} height={120} />
          </div>
          <div
            onClick={handleShowActive}
            className="flex aspect-square w-[40px] items-center justify-center rounded-full border border-[#115a9e] bg-[#115a9e] text-white"
          >
            {isShow ? <FaX /> : <FaBars />}
          </div>
          {isShow && (
            <div className="fixed bottom-0 right-0 top-[7rem] w-full animate-fadeIn border-t border-white bg-[#3438a4] pt-8">
              <ul className="flex w-full flex-col items-start gap-8 px-4 text-white">
                {dataMenu.map((item, index) => (
                  <li
                    key={index}
                    className="group flex cursor-pointer items-center gap-2"
                  >
                    <Link href={item.href}>
                      <span
                        className={`relative transition-all after:absolute after:bottom-[-2px] after:left-0 after:w-full after:border-b-[2px] after:content-[''] ${isActive === index
                          ? "after:border-white"
                          : "after:border-transparent group-hover:after:border-white"
                          }`}
                      >
                        {item.name}
                      </span>
                    </Link>
                    {!!dataOfMenuChild[index]?.items?.length && <FaAngleDown
                      className={`${isActive === index ? "rotate-180" : ""}`}
                    />}
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
        <div className="absolute opacity-0 lg:opacity-100 bottom-0 left-0 right-0 h-[1px] bg-white transition-all group-hover:h-0"></div>
      </div>
      {isActive !== null && (
        <div className="fixed bottom-0 left-0 right-0  top-0 z-50 animate-fadeIn bg-[#3438a4] px-4 pb-12">
          <div className="container relative mx-auto flex flex-col !px-0 pt-[1rem] text-white">
            <div className="mb-4 flex items-center justify-between">
              <div onClick={() => handleSetActive(null)} className="text-xl">
                <FaAngleLeft />
              </div>
              <div className="text-xl">{dataMenu[isActive]?.name}</div>
              <div
                onClick={handleShowActive}
                className="flex aspect-square w-[40px] items-center justify-center rounded-full border border-[#115a9e] bg-[#115a9e] text-white"
              >
                {isShow ? <FaX /> : <FaBars />}
              </div>
            </div>
            <div className="w-full space-y-4">
              <p className="pr-3 text-2xl font-semibold">
                {dataOfMenuChild[isActive]?.title}
              </p>
              <div className="flex w-fit flex-col gap-4">
                {dataOfMenuChild[isActive]?.button?.map((btn, btnIndex) => (
                  <button
                    key={btnIndex}
                    className="w-fit rounded-3xl bg-[#0e4280] px-4 py-2"
                  >
                    {btn?.name}
                  </button>
                ))}
              </div>
            </div>
            <div className="w-full">
              <div className="grid grid-cols-4 gap-[20px] ">
                {dataOfMenuChild[isActive]?.items?.map((item, itemIndex) => (
                  <div
                    key={itemIndex}
                    className="relative col-span-2 flex transform items-center justify-center overflow-hidden rounded-lg transition-transform duration-300 ease-in-out hover:scale-110"
                  >
                    <a href={item.href}>
                      <Image
                        src={item.image}
                        alt={item.name}
                        width={226}
                        height={299}
                        className="rounded-lg "
                      />
                      <span className="absolute inset-0 flex items-center justify-center px-[1rem] text-lg font-semibold text-white">
                        {item.name}
                      </span>
                    </a>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
    </header>
  );
};
export default Header;
