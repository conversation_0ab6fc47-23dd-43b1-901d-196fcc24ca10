"use client";

import { FaAngleDown } from "react-icons/fa6";

import { useState } from "react";
import Image from "next/image";
import { usePathname } from "next/navigation";
import Link from "next/link";

type HeaderProps = {

  dataMenu: { name: string; href: string }[];

  dataOfMenuChild: {

    title: string;

    button: { name: string }[];

    items: { name: string; href: string; image: string }[];

  }[];

}

const Header: React.FC<HeaderProps> = ({ dataMenu, dataOfMenuChild }) => {
  const [isActive, setIsActive] = useState<number | null>(null);
  const pathname = usePathname();

  // const dataMenu = [
  //   { name: "<PERSON><PERSON><PERSON><PERSON> thiệu", href: "/" },
  //   { name: "<PERSON> tức", href: "/user/news" },
  //   { name: "Thống kê", href: "" },
  //   { name: "<PERSON><PERSON><PERSON> gi<PERSON>", href: "" },
  //   { name: "<PERSON><PERSON><PERSON> đ<PERSON>", href: "" },
  //   { name: "<PERSON><PERSON><PERSON> nguyên", href: "" },
  //   { name: "<PERSON>ế<PERSON> hạng", href: "" },
  //   { name: "Đăng nhập", href: "/login" }
  // ];

  // const dataOfMenuChild = [
  //   {
  //     title:
  //       "Hệ sinh thái học tập sáng tạo: kết nối kiến thức, phát triển tư duy, chia sẻ.",
  //     button: [],
  //     items: [
  //       // {
  //       //   name: "Hệ sinh thái học tập, sáng tạo",
  //       //   href: "/",
  //       //   image: "/mangluoi.jpg"
  //       // }
  //     ]
  //   },
  //   {
  //     title:
  //       "Tin tức mới nhất về hoạt động của hệ sinh thái học tập, sáng tạo.",
  //     button: [],
  //     items: [
  //       // {
  //       //   name: "Tin tức và sự kiện hệ sinh thái",
  //       //   href: "/user/news",
  //       //   image: "/tintuc.jpg"
  //       // }
  //     ]
  //   },
  //   {
  //     title:
  //       "Thống kê các trường các cấp của thuộc hệ sinh thái học tập, sáng tạo.",
  //     button: [],
  //     items: [
  //       // {
  //       //   name: "Thống kê các trường các cấp",
  //       //   href: "/user/school",
  //       //   image: "/thongke.jpg"
  //       // }
  //     ]
  //   },
  //   {
  //     title:
  //       "Đánh giá xếp hạng các trường các cấp thuộc hệ sinh thái học tập, sáng tạo.",
  //     button: [],
  //     items: [
  //       // {
  //       //   name: "Đánh giá xếp hạng",
  //       //   href: "/",
  //       //   image: "/danhgia.jpg"
  //       // }
  //     ]
  //   },
  //   {
  //     title:
  //       "Nơi thảo luận các vấn đề về học tập, sáng tạo của học sinh, phụ huynh và giáo viên.",
  //     button: [],
  //     items: [
  //       {
  //         name: "Diễn đàn nhà trường",
  //         href: "/forums",
  //         image: "/diendandong1.jpg"
  //       },
  //       {
  //         name: "Diễn đàn xã hội",
  //         href: "/forums",
  //         image: "/diendanmo.jpg"
  //       }
  //     ]
  //   },
  //   {
  //     title: "Tổng hợp các phần mềm hỗ trợ trong học tập, giảng dạy.",
  //     button: [],
  //     items: [
  //       {
  //         name: "Nghiên cứu",
  //         href: "#",
  //         image: "/tieuhoc.jpg"
  //       },
  //       {
  //         name: "Học liệu",
  //         href: "#",
  //         image: "/trunghoc.jpg"
  //       },
  //       {
  //         name: "Dự án",
  //         href: "#",
  //         image: "/trunghoc1.jpg"
  //       },
  //       {
  //         name: "Kỹ năng",
  //         href: "#",
  //         image: "/boicanh.jpg"
  //       },
  //       {
  //         name: "Thống kê",
  //         href: "#",
  //         image: "/thongke.jpg"
  //       },
  //       {
  //         name: "Hệ sinh thái số",
  //         href: "#",
  //         image: "/vanhoa.jpg"
  //       },
  //       // ,
  //       // {
  //       //   name: "Cấp trung học cơ sở",
  //       //   href: "/user/resource",
  //       //   image: "/trunghoc.jpg"
  //       // },
  //       // {
  //       //   name: "Cấp trung học phổ thông",
  //       //   href: "/user/resource",
  //       //   image: "/trunghoc1.jpg"
  //       // },
  //       // {
  //       //   name: "Cấp đại học",
  //       //   href: "/user/resource",
  //       //   image: "/daihoc.jpg"
  //       // }
  //     ]
  //   }
  // ];

  const handleSetActive = (index: number) => {
    if (dataOfMenuChild[index]?.items?.length) {
      setIsActive(isActive === index ? null : index);
    }
  };

  const handleRedirect = () => {
    window.location.href = "/";
  };
  if (pathname.startsWith('/login') || pathname.startsWith('/register')) {
    return null;
  }
  return (
    <header
      className={`group ${pathname.startsWith("/forums") && "!bg-[#3438a4]"} ${typeof isActive === "number" && "!bg-[#3438a4]"} absolute left-0 right-0 top-0 z-[200] h-[115.19px] !px-0 transition-all hover:bg-[#3438a4]`}
    >
      <div className="container relative mx-auto flex h-full items-center !px-0 ">
        <div className="flex w-full items-center justify-between">
          <div className="cursor-pointer" onClick={() => handleRedirect()}>
            <Image src="/logo.png" alt="logo" width={120} height={120} />
          </div>
          <div className="">
            <ul className="flex items-center gap-10 text-white">
              {dataMenu?.map((item, index) => (
                <li
                  key={index}
                  onClick={() => handleSetActive(index)}
                  className="group flex cursor-pointer items-center gap-2"
                >
                  <p className="flex gap-1">
                    <Link href={item.href}
                      className={`flex gap-1 relative transition-all after:absolute after:bottom-[-2px] after:left-0 after:w-full after:border-b-[2px] after:content-[''] ${isActive === index
                        ? "after:border-white"
                        : "after:border-transparent group-hover:after:border-white"
                        }`}
                    >
                      {item.name}
                    </Link>
                  </p>
                  {!!dataOfMenuChild[index]?.items?.length && <FaAngleDown
                    className={`${isActive === index ? "rotate-180" : ""}`}
                  />}
                </li>
              ))}
              <div className={`2xl:absolute flex 2xl:right-[-14rem] xl:top-[3rem] text-white group/edit items-center gap-2`}>
                VN <FaAngleDown
                  className={`group-hover/edit:rotate-180`}
                />
                <div className="absolute top-[1.5rem] text-black z-50 left-[-2rem] w-[100px] bg-white rounded-lg p-2 hidden group-hover/edit:block">
                  <div className="flex flex-col gap-4">
                    <p className="cursor-pointer hover:bg-gray-200 transition-all">EN</p>
                    <p className="cursor-pointer hover:bg-gray-200 transition-all">VN</p>
                  </div>
                </div>
              </div>
            </ul>
          </div>
        </div>
        <div className="absolute opacity-0 lg:opacity-100 bottom-0 left-0 right-0 h-[1px] bg-white transition-all group-hover:h-0"></div>
      </div>
      {isActive !== null && (
        <div className="h-fit animate-fadeIn bg-[#3438a4] pb-12">
          <div className=" container relative mx-auto flex !px-0 pt-[2rem] text-white">
            <div className="w-[30%] space-y-4">
              <p className="pr-3 text-2xl font-semibold">
                {dataOfMenuChild[isActive]?.title}
              </p>
              <div className="flex w-fit flex-col gap-4">
                {dataOfMenuChild[isActive]?.button?.map((btn, btnIndex) => (
                  <button
                    key={btnIndex}
                    className="w-fit rounded-3xl bg-[#0e4280] px-4 py-2"
                  >
                    {btn?.name}
                  </button>
                ))}
              </div>
            </div>
            <div className="w-[80%]">
              <div className="grid grid-cols-4 gap-[20px] ">
                {dataOfMenuChild[isActive]?.items?.map((item, itemIndex) => (
                  <div
                    key={itemIndex}
                    className="relative flex h-[299px] w-[226px] transform items-center justify-center overflow-hidden rounded-lg transition-transform duration-300 ease-in-out hover:scale-110"
                  >
                    <Link href={item.href}>
                      <Image
                        src={item.image}
                        alt={item.name}
                        width={226}
                        height={299}
                        className="rounded-lg "
                      />
                      <span className="absolute inset-0 flex items-center justify-center px-[1rem] text-lg font-semibold text-white">
                        {item.name}
                      </span>
                    </Link>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
    </header>
  );
};

export default Header;
