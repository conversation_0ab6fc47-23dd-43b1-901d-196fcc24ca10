'use client';

import dynamic from "next/dynamic";

const Header = dynamic(() => import("@/components/ui/Header"), { ssr: false });
const HeaderMobile = dynamic(() => import("@/components/ui/HeaderMobile"), { ssr: false });

interface ClientHeaderProps {
  dataMenu: any[];
  dataOfMenuChild: any[];
}

export default function ClientHeader({ dataMenu, dataOfMenuChild }: ClientHeaderProps) {
  return (
    <>
      <div className="hidden lg:block">
        <Header
          dataMenu={dataMenu}
          dataOfMenuChild={dataOfMenuChild}
        />
      </div>
      <div className="lg:hidden">
        <HeaderMobile
          dataMenu={dataMenu}
          dataOfMenuChild={dataOfMenuChild} 
        />
      </div>
    </>
  );
} 