'use client';

import Footer from "@/components/ui/Footer";
import { SWRProvider } from "@/context/SwrProvider";
import Header from "./Header";

interface ClientForumsLayoutProps {
  children: React.ReactNode;
  dataMenu: {
    name: string;
    href: string;
  }[];
  dataOfMenuChild: any[];
}

export default function ClientForumsLayout({
  children,
  dataMenu,
  dataOfMenuChild
}: ClientForumsLayoutProps) {
  return (
    <div className="min-h-screen flex flex-col">
      <Header dataMenu={dataMenu} dataOfMenuChild={dataOfMenuChild} />
      <main className="flex-1 bg-gray-50">
        <SWRProvider>{children}</SWRProvider>
      </main>
      <Footer />
    </div>
  );
} 