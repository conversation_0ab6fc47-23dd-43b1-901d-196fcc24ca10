'use client';

import { SWRProvider } from "@/context/SwrProvider";
import Footer from "@/components/ui/Footer";
import HeroSection from "@/components/ui/HeroSection";
import MenuSelected from "@/components/ui/MenuSelected";
import ClientHeader from "@/components/ClientHeader";

interface ClientElementaryLayoutProps {
  children: React.ReactNode;
  dataMenu: any[];
  dataOfMenuChild: any[];
  dataMenuSelected: any[];
}

export default function ClientElementaryLayout({
  children,
  dataMenu,
  dataOfMenuChild,
  dataMenuSelected
}: ClientElementaryLayoutProps) {
  return (
    <>
      <ClientHeader dataMenu={dataMenu} dataOfMenuChild={dataOfMenuChild} />
      <main className="flex h-svh flex-col">
        <section className="flex-1">
          <HeroSection
            description="<PERSON><PERSON> sinh thái học tập sáng tạo cấp Ti<PERSON><PERSON> học khuyến khích sự phát triển toàn diện của trẻ, nuôi dưỡng tư duy sáng tạo và khả năng hợp tác thông qua các hoạt động học tập đa dạng và trải nghiệm thực tế."
            title="HỆ SINH THÁI HỌC TẬP SÁNG TẠO CẤP TIỂU HỌC"
            backgroundImageUrl="elementary/banner.png"
            backgroundPosition="center"
          />
          <MenuSelected dataMenu={dataMenuSelected} />
          <SWRProvider>{children}</SWRProvider>
        </section>
        <Footer />
      </main>
    </>
  );
} 