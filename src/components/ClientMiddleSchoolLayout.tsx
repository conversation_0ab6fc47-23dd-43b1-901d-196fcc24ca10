'use client';

import { SWRProvider } from "@/context/SwrProvider";
import Footer from "@/components/ui/Footer";
import HeroSection from "@/components/ui/HeroSection";
import MenuSelected from "@/components/ui/MenuSelected";
import ClientHeader from "@/components/ClientHeader";

interface ClientMiddleSchoolLayoutProps {
  children: React.ReactNode;
  dataMenu: any[];
  dataOfMenuChild: any[];
  dataMenuSelected: any[];
}

export default function ClientMiddleSchoolLayout({
  children,
  dataMenu,
  dataOfMenuChild,
  dataMenuSelected
}: ClientMiddleSchoolLayoutProps) {
  return (
    <>
      <ClientHeader dataMenu={dataMenu} dataOfMenuChild={dataOfMenuChild} />
      <main className="flex h-svh flex-col">
        <section className="flex-1">
          <HeroSection
            description="<PERSON><PERSON> sinh thái học tập sáng tạo cấp THCS hướng tới hình thành môi trường giáo dục đa dạng, tích hợp các phương pháp giảng dạy hiện đại, công nghệ thông tin và hoạt động ngoại khóa, nhằm khuyến khích học sinh phát triển tư duy sáng tạo, khả năng giải quyết vấn đề và làm việc nhóm trong một không gian học tập thân thiện và hỗ trợ."
            title="HỆ SINH THÁI HỌC TẬP SÁNG TẠO CẤP THCS"
            backgroundImageUrl="middle-school/banner1.png"
            backgroundPosition="center"
          />
          <MenuSelected dataMenu={dataMenuSelected} />
          <SWRProvider>{children}</SWRProvider>
        </section>
        <Footer />
      </main>
    </>
  );
} 