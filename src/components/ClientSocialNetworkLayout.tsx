"use client";

import { useCallback, useEffect, useRef, useState } from "react";
import {
  FaBell,
  FaNewspaper,
  FaSignInAlt,
  FaSignOutAlt,
  FaUser
} from "react-icons/fa";

import { useAuth } from "@/context/AuthContext";
import Image from "next/image";
import Link from "next/link";
import { usePathname } from "next/navigation";
import socialNetworkServices from "../services/social-network/socialNetworkServices";
import NotificationDropdown from "./NotificationDropdown";

export default function ClientSocialNetworkLayout() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const [unreadCount, setUnreadCount] = useState<number>(0);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const pathname = usePathname();
  const { isAuthenticated, user, logout } = useAuth();

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 10) {
        setScrolled(true);
      } else {
        setScrolled(false);
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // Đóng menu và sidebar khi chuyển trang
  useEffect(() => {
    setIsMenuOpen(false);
    setIsSidebarOpen(false);
  }, [pathname]);

  // Xử lý click bên ngoài dropdown để đóng
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const isOutsideDropdown =
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node);

      // Nếu click bên ngoài cả hai dropdown hoặc bên ngoài dropdown đang hiển thị
      if (isOutsideDropdown) {
        setIsSidebarOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Định nghĩa các menu items
  const menuItems = [
    {
      href: "/social-network",
      icon: <FaNewspaper className="text-lg" />,
      label: "Bảng tin"
    },
    {
      href: "/social-network/profile",
      icon: <FaUser className="text-lg" />,
      label: "Trang cá nhân"
    }
    // { href: '/social-network/manage-posts', icon: <FaCog className="text-lg" />, label: 'Quản lý bài đăng' },
  ];

  // Xử lý đăng xuất
  const handleLogout = () => {
    logout();
  };

  // Hàm lấy số lượng thông báo chưa đọc
  const fetchUnreadCount = useCallback(async () => {
    if (!isAuthenticated) return;
    
    try {
      const response = await socialNetworkServices.getNotificationCount();
      setUnreadCount(response?.data?.data || 0);
    } catch (error) {
      console.error("Lỗi khi lấy số lượng thông báo chưa đọc:", error);
    }
  }, [isAuthenticated]);

  // Gọi API lấy số lượng thông báo chưa đọc khi component mount và khi người dùng đã đăng nhập
  useEffect(() => {
    fetchUnreadCount();
  }, []);

  // Xử lý mở/đóng sidebar
  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };
  

  return (
    <nav
      className={`${scrolled ? "py-2 shadow-md" : "py-3"} sticky top-0 z-50 border-b border-gray-200 bg-white transition-all duration-300 ease-in-out`}
    >
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <div className="flex items-center">
            <Link href="/" className="flex items-center space-x-2">
              <Image
                src="/logo.png"
                alt="Logo"
                width={50}
                height={50}
                className="object-contain"
              />
            </Link>
          </div>

          {/* Desktop Menu */}
          <div className=" items-center space-x-1 flex">
            <div className="hidden md:flex items-center">
              {menuItems.map((item, index) => {
                const isActive = pathname === item.href;
                return (
                  <Link
                    key={index}
                    href={item.href}
                    className={`${
                      isActive
                        ? "bg-blue-50 text-blue-600"
                        : "text-gray-700 hover:bg-blue-50 hover:text-blue-600"
                    } flex items-center gap-1 rounded-md px-3 py-2 font-medium transition-all duration-200`}
                  >
                    {item.icon}
                    <span>{item.label}</span>
                  </Link>
                );
              })}
            </div>

            {/* Button để mở dropdown */}
            {isAuthenticated && (
              <div className="relative" ref={dropdownRef}>
                <button
                  onClick={toggleSidebar}
                  className="flex items-center gap-1 rounded-md px-3 py-2 font-medium text-gray-700 transition-all duration-200 hover:bg-blue-50 hover:text-blue-600 relative"
                  aria-label="Mở thông báo"
                >
                  <div className="relative">
                    <FaBell className="text-lg" />
                    {unreadCount > 0 && (
                      <span className="absolute -right-2 -top-2 flex h-4 w-4 items-center justify-center rounded-full bg-red-500 text-xs text-white">
                        {unreadCount > 9 ? '9+' : unreadCount}
                      </span>
                    )}
                  </div>
                  <span className="hidden md:block">Thông báo</span>
                </button>

                {/* Dropdown Menu */}
                {isSidebarOpen && (
                  <div className="absolute right-0 z-50 mt-2">
                    <NotificationDropdown 
                      unreadCount={unreadCount}
                      onUnreadCountChange={()=>fetchUnreadCount()} 
                    />
                  </div>
                )}
              </div>
            )}

            {/* Đăng nhập/Đăng xuất */}
            <div className="hidden md:flex">
              {isAuthenticated ? (
                <button
                  onClick={handleLogout}
                  className="flex items-center gap-1 rounded-md px-3 py-2 font-medium text-gray-700 transition-all duration-200 hover:bg-blue-50 hover:text-blue-600"
                >
                  <FaSignOutAlt className="text-lg" />
                  <span>Đăng xuất</span>
                </button>
              ) : (
                <Link
                  href="/login"
                  className="flex items-center gap-1 rounded-md px-3 py-2 font-medium text-gray-700 transition-all duration-200 hover:bg-blue-50 hover:text-blue-600"
                >
                  <FaSignInAlt className="text-lg" />
                  <span>Đăng nhập</span>
                </Link>
              )}
            </div>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="mt-3 border-t border-gray-200 pb-3 pt-2 transition-all duration-300 ease-in-out md:hidden">
            <div className="flex flex-col space-y-2">
              {menuItems.map((item, index) => {
                const isActive = pathname === item.href;
                return (
                  <Link
                    key={index}
                    href={item.href}
                    className={`${
                      isActive
                        ? "bg-blue-50 text-blue-600"
                        : "text-gray-700 hover:bg-blue-50 hover:text-blue-600"
                    } flex items-center gap-2 rounded-md px-4 py-3 font-medium transition-all duration-200`}
                  >
                    {item.icon}
                    <span>{item.label}</span>
                  </Link>
                );
              })}

              {/* Đăng nhập/Đăng xuất cho mobile */}
              {isAuthenticated ? (
                <button
                  onClick={handleLogout}
                  className="flex items-center gap-2 rounded-md px-4 py-3 font-medium text-gray-700 transition-all duration-200 hover:bg-blue-50 hover:text-blue-600"
                >
                  <FaSignOutAlt className="text-lg" />
                  <span>Đăng xuất</span>
                </button>
              ) : (
                <Link
                  href="/login"
                  className="flex items-center gap-2 rounded-md px-4 py-3 font-medium text-gray-700 transition-all duration-200 hover:bg-blue-50 hover:text-blue-600"
                >
                  <FaSignInAlt className="text-lg" />
                  <span>Đăng nhập</span>
                </Link>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Không cần sidebar popup nữa vì đã chuyển sang dropdown */}
    </nav>
  );
}
