import { FaCircleArrowRight } from "react-icons/fa6";
import React from "react";

interface CardItem {
  backgroundImageUrl: string;
  title: string;
  description: string;
}

interface CardListProps {
  dataCard: CardItem[];
}

const CardList: React.FC<CardListProps> = ({ dataCard }) => {
  return (
    <>
      {dataCard?.map((item, index) => (
        <div
          key={index}
          style={{ backgroundImage: `url(${item.backgroundImageUrl})`, backgroundSize: 'cover' }}
          className={`group  ${!index ? "w-full lg:w-[50%] " : "w-full lg:w-[25%] "}cursor-pointer rounded-lg p-7  text-white`}
        >
          <div className="flex items-start justify-between">
            <div className="max-w-[15rem] space-y-4">
              <h3 className="text-3xl font-bold">{item.title}</h3>
              <p className="text">{item.description}</p>
            </div>
            <FaCircleArrowRight className="group-hover:animate-translate text-xl text-black transition-all" />
          </div>
        </div>
      ))}
    </>
  );
};

export default CardList;
