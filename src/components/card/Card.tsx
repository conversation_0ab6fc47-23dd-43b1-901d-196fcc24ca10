import React from "react";

type CardItem = {
  backgroundImageUrl: string;
  title: string;
  description: string;
}

type CardListProps = {
  dataCard: CardItem[];
}

const Card: React.FC<CardListProps> = ({ dataCard }) => {
  return (
    <div className="flex flex-wrap gap-6">
      {dataCard?.map((item, index) => (
        <div
          key={index}
          style={{ backgroundImage: `url(${item.backgroundImageUrl})`, backgroundSize: 'cover' }}
          className={`group h-[400px] w-full lg:w-[30%] xl:w-[32%] cursor-pointer rounded-lg p-7 text-white mx-auto`}
        >
          <div className="flex items-start justify-between">
            <div className="max-w-[15rem] space-y-4">
              <h3 className="text-3xl font-bold">{item.title}</h3>
              <p >{item.description}</p>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default Card;
