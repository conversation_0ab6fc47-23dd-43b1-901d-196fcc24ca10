"use client";

import { useEffect, useState } from "react";
import { <PERSON>a<PERSON><PERSON><PERSON><PERSON>, FaSpinner } from "react-icons/fa";

import usePostStore from "@/stores/postStore";

import { cn } from "@/lib/utils";
import socialNetworkServices from "@/services/social-network/socialNetworkServices";
import { UserHistorySocialNetwork } from "@/services/social-network/types/types";
import { useRouter } from "next/navigation";
import { ExtendedPost } from "../app/(homepage)/social-network/profile/components/PostsTab";
import PostDetailModal from "./common/PostDetailModal";

// Hàm lấy nhãn cho loại hoạt động
const getActivityTypeLabel = (type: string): string => {
  const types: Record<string, string> = {
    CREATE: "Tạo mới",
    UNLIKE: "Bỏ thích",
    LIKE: "Đã thích",
    SAVE: "Đã lưu",
    COMMENT: "<PERSON><PERSON><PERSON> luận",
    SHARE: "Chia sẻ"
  };
  return types[type] || type;
};

// Hàm lấy màu cho loại hoạt động
const getActivityTypeColor = (type: string): string => {
  const colors: Record<string, string> = {
    CREATE: "bg-green-100 text-green-600",
    UNLIKE: "bg-red-100 text-red-600",
    LIKE: "bg-pink-100 text-pink-600",
    SAVE: "bg-indigo-100 text-indigo-600",
    COMMENT: "bg-blue-100 text-blue-600",
    SHARE: "bg-yellow-100 text-yellow-600"
  };
  return colors[type] || "bg-gray-100 text-gray-600";
};

export default function History({className}: {className?: string}) {
  const router = useRouter();
  
  const [userHistory, setUserHistory] = useState<UserHistorySocialNetwork[]>(
    []
  );
  const [loadingUserHistory, setLoadingUserHistory] = useState(true);
  const [errorUserHistory, setErrorUserHistory] = useState<string | null>(null);
  const [currentPageUserHistory, setCurrentPageUserHistory] = useState(0);
  const [totalPagesUserHistory, setTotalPagesUserHistory] = useState(0);
  const [showPostModal, setShowPostModal] = useState(false);
  const [selectedPost, setSelectedPost] = useState<ExtendedPost | null>(null);

  // Lấy dữ liệu lịch sử hoạt động từ API
  const fetchUserHistory = async (page: number = 0) => {
    try {
      setLoadingUserHistory(true);
      const response = await socialNetworkServices.userHistorySocialNetwork({
        page,
        size: 5,
        sort: "DESC"
      });

      if (response?.data?.data) {
        setUserHistory(response.data.data.content);
        setTotalPagesUserHistory(response.data.data.totalPagesUserHistory);
        setCurrentPageUserHistory(response.data.data.number);
      }
    } catch (err) {
      console.error("Lỗi khi lấy dữ liệu lịch sử:", err);
      setErrorUserHistory("Không thể tải lịch sử hoạt động");
    } finally {
      setLoadingUserHistory(false);
    }
  };

  // Xử lý chuyển trang
  const handlePageChange = (newPage: number) => {
    if (newPage >= 0 && newPage < totalPagesUserHistory) {
      fetchUserHistory(newPage);
    }
  };

  // Xử lý điều hướng đến bài đăng
  const handleNavigateToPost = (postId: number) => {
    // Lưu ID bài đăng vào state để truyền cho PostDetailModal
    setSelectedPost({ postSocialNetworkId: postId } as ExtendedPost);
    setShowPostModal(true);
  };

  // Gọi API khi component được mount
  useEffect(() => {
    fetchUserHistory();
  }, []);
  // Hàm để đóng modal và reset state
  const handleClosePostModal = () => {
    setShowPostModal(false);
    setSelectedPost(null);
  };

  // Lấy resetCounter và triggerReset từ store
  const { triggerReset } = usePostStore();

  // Hàm reset danh sách (có thể cần khi có thay đổi trên bài đăng)
  const resetPostsList = () => {
    triggerReset();
    fetchUserHistory(currentPageUserHistory);
  };

  return (
    <div className={cn("overflow-y-auto rounded-lg border border-gray-200 bg-white shadow-lg", className)}>
      <div className="sticky top-0 border-b border-gray-200 bg-white p-3">
        <h2 className="flex items-center gap-2 text-lg font-semibold">
          <FaChartLine className="text-blue-600" />
          Hoạt động gần đây
        </h2>
      </div>
      {/* Hoạt động gần đây */}
      {loadingUserHistory ? (
        <div className="flex items-center justify-center py-4">
          <FaSpinner className="animate-spin text-xl text-blue-600" />
        </div>
      ) : errorUserHistory ? (
        <div className="py-4 text-center text-red-500">{errorUserHistory}</div>
      ) : userHistory.length === 0 ? (
        <div className="py-4 text-center text-gray-500">
          Chưa có hoạt động nào
        </div>
      ) : (
        <>
          <div>
            {userHistory.map((activity, index) => (
              <div
                key={`${activity.objectId}-${index}`}
                className="cursor-pointer border-b border-gray-100 p-4 transition-colors last:border-0 hover:bg-gray-50"
                onClick={() => handleNavigateToPost(activity.objectId)}
              >
                <div className="flex items-start gap-2">
                  <div className="mt-2 h-2 w-2 rounded-full bg-blue-500"></div>
                  <div className="flex-1">
                    <div className="space-x-2 text-sm">
                      <span
                        className={`rounded-full px-2 py-1 text-xs ${getActivityTypeColor(activity.objectType)}`}
                      >
                        {getActivityTypeLabel(activity.objectType)}
                      </span>
                      <span>{activity.content}</span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Phân trang */}
          {totalPagesUserHistory > 1 && (
            <div className="mt-4 flex items-center justify-center gap-2">
              <button
                className={`flex h-8 w-8 items-center justify-center rounded-full ${currentPageUserHistory === 0 ? "cursor-not-allowed bg-gray-100 text-gray-400" : "bg-blue-100 text-blue-600 hover:bg-blue-200"}`}
                onClick={() => handlePageChange(currentPageUserHistory - 1)}
                disabled={currentPageUserHistory === 0}
              >
                &lt;
              </button>

              <span className="text-sm text-gray-600">
                {currentPageUserHistory + 1}/{totalPagesUserHistory}
              </span>

              <button
                className={`flex h-8 w-8 items-center justify-center rounded-full ${currentPageUserHistory === totalPagesUserHistory - 1 ? "cursor-not-allowed bg-gray-100 text-gray-400" : "bg-blue-100 text-blue-600 hover:bg-blue-200"}`}
                onClick={() => handlePageChange(currentPageUserHistory + 1)}
                disabled={currentPageUserHistory === totalPagesUserHistory - 1}
              >
                &gt;
              </button>
            </div>
          )}
        </>
      )}

      {/* Sử dụng component PostDetailModal để hiển thị chi tiết bài đăng */}
      <PostDetailModal
        isOpen={showPostModal}
        onClose={handleClosePostModal}
        postId={selectedPost?.postSocialNetworkId || 0}
        resetList={resetPostsList}
      />
    </div>
  );
}
