"use client";

import Image from "next/image";
import { useState } from "react";
import { FaPaperPlane } from "react-icons/fa";
import { toast } from "react-toastify";

import { useUserSocialNetwork } from "@/app/(homepage)/social-network/_contexts/UserSocialNetworkContext";
import DefaultAvatar from "@/components/ui/DefaultAvatar";
import socialNetworkServices from "@/services/social-network/socialNetworkServices";
import { Post } from "@/services/social-network/types/types";

type ExtendedPost = Post & {
  avatarUrl?: string;
  fileUrls?: string[];
};

interface CommentFormProps {
  post: ExtendedPost;
  onCommentAdded: () => void;
}

const CommentForm = ({ post, onCommentAdded }: CommentFormProps) => {
  const [newCommentContent, setNewCommentContent] = useState("");
  const [commentLoading, setCommentLoading] = useState(false);

  const { userData } = useUserSocialNetwork();

  // Thêm bình luận mới
  const handleAddComment = async () => {
    if (!newCommentContent.trim() || commentLoading) return;
    setCommentLoading(true);
    
    try {
      await socialNetworkServices.createCommentPostSocialNetwork({
        postSocialNetworkId: post.postSocialNetworkId,
        content: newCommentContent,
        imageId: ""
      });
      
      // Reset content và thông báo cho component cha
      setNewCommentContent("");
      onCommentAdded();
      toast.success("Đã thêm bình luận");
    } catch (error) {
      console.error("Lỗi khi thêm bình luận:", error);
      toast.error("Không thể thêm bình luận");
    } finally {
      setCommentLoading(false);
    }
  };

  return (
    <div className="mb-3 sm:mb-4 flex items-start gap-2 sm:gap-3">
      <div className="relative h-7 w-7 sm:h-8 sm:w-8 overflow-hidden rounded-full">
        {userData?.avatarUrl ? (
          <Image
            src={userData?.avatarUrl}
            alt={`${userData?.firstName} ${userData?.lastName}`}
            fill
            className="object-cover"
          />
        ) : (
          <DefaultAvatar
            name={`${userData?.firstName} ${userData?.lastName}`}
            size="100%"
          />
        )}
      </div>
      <div className="flex-1">
        <div className="flex items-center gap-1.5 sm:gap-2">
          <input
            type="text"
            value={newCommentContent}
            onChange={(e) => setNewCommentContent(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === "Enter") {
                handleAddComment();
              }
            }}
            placeholder="Viết bình luận..."
            className="flex-1 rounded-full border border-gray-200 px-3 sm:px-4 py-1.5 sm:py-2 text-sm sm:text-base focus:outline-none focus:ring-2 focus:ring-blue-500/30"
          />
          <button
            onClick={handleAddComment}
            disabled={!newCommentContent.trim() || commentLoading}
            className="rounded-full bg-blue-500 p-1.5 sm:p-2 text-white hover:bg-blue-600 disabled:cursor-not-allowed disabled:opacity-50 transition-colors duration-200"
          >
            <FaPaperPlane size={12} className="sm:text-sm md:text-base" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default CommentForm;
