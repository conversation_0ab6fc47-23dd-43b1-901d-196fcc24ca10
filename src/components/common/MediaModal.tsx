"use client";

import Image from 'next/image';
import { FaBookmark, FaComment, FaEllipsisV, FaGlobe, FaLock, FaPlay, FaShare, FaThumbsUp, FaTimes, FaUserFriends } from 'react-icons/fa';

type MediaType = 'photo' | 'video';

type MediaItem = {
  id: number;
  type: MediaType;
  url: string;
  postId: number;
  thumbnail?: string;
};

type User = {
  id: number;
  name: string;
  avatar: string;
};

type Comment = {
  id: number;
  user: User;
  content: string;
  likes: number;
  createdAt: string;
  replies?: Reply[];
};

type Reply = {
  id: number;
  user: User;
  content: string;
  likes: number;
  createdAt: string;
};

type Post = {
  id: number;
  user: User;
  content: string;
  createdAt: string;
  privacy: 'PUBLIC' | 'FRIENDS' | 'PRIVATE';
  likes: number;
  comments: number;
  shares: number;
  saved?: boolean;
  showComments?: boolean;
  hashtags: { id: number; name: string }[];
  commentsList?: Comment[];
  userId: number;
};

interface MediaModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedMedia: MediaItem;
  relatedPost: Post;
  currentUser: {
    id: number;
    name: string;
    avatar: string;
    userRole?: string;
  };
  onPostUpdate?: (updatedPost: Post) => void;
}

const MediaModal = ({
  isOpen,
  onClose,
  selectedMedia,
  relatedPost,
  currentUser,
  onPostUpdate
}: MediaModalProps) => {
  
  // Hàm xử lý cập nhật bài viết
  const handlePostUpdate = (updatedData: Partial<Post>) => {
    if (onPostUpdate) {
      onPostUpdate({...relatedPost, ...updatedData});
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75 p-4 overflow-y-auto">
      <button 
        onClick={onClose}
        className="absolute top-4 right-4 p-2 rounded-full bg-gray-800 text-white hover:bg-gray-700 z-50"
      >
        <FaTimes />
      </button>
      <div className="bg-white rounded-lg w-full overflow-hidden flex min-h-[90vh]">
        {/* Phần bên trái: Ảnh/Video */}
        <div className="w-2/3 bg-gray-900 relative">
          {selectedMedia.type === 'photo' ? (
            <div className="relative h-full w-full">
              <Image
                src={selectedMedia.url}
                alt="Post image"
                fill
                className="object-contain"
              />
            </div>
          ) : (
            <div className="h-full w-full flex items-center justify-center">
              {/* Trong thực tế, đây sẽ là video player */}
              <div className="text-center">
                <div className="bg-white bg-opacity-20 rounded-full p-4 inline-block mb-3">
                  <FaPlay className="text-white text-xl" />
                </div>
                <p className="text-white">Video demo</p>
              </div>
            </div>
          )}
        </div>
        
        {/* Phần bên phải: Thông tin bài viết và bình luận */}
        <div className="w-1/3 flex flex-col overflow-hidden">
          {/* Khoảng trống thay thế cho header cũ */}
          <div className="h-2"></div>
          
          {/* Nội dung bài viết và bình luận */}
          <div className="flex-1 overflow-y-auto">
            {/* Thông tin người đăng */}
            <div className="flex items-center justify-between p-4 border-b">
              <div className="flex items-center gap-3">
                <Image
                  src={relatedPost.user.avatar}
                  alt={relatedPost.user.name}
                  width={40}
                  height={40}
                  className="rounded-full object-cover"
                />
                <div>
                  <div className="font-medium">{relatedPost.user.name}</div>
                  <div className="flex items-center gap-2 text-sm text-gray-500">
                    <span>{relatedPost.createdAt}</span>
                    <span className="flex items-center gap-1">
                      {relatedPost.privacy === 'PUBLIC' ? (
                        <><FaGlobe className="text-gray-600" /><span>Công khai</span></>
                      ) : relatedPost.privacy === 'FRIENDS' ? (
                        <><FaUserFriends className="text-gray-600" /><span>Bạn bè</span></>
                      ) : (
                        <><FaLock className="text-gray-600" /><span>Chỉ mình tôi</span></>
                      )}
                    </span>
                  </div>
                </div>
              </div>

              {(relatedPost.userId === currentUser.id || currentUser.userRole === 'ROLE_ADMIN') && (
                <div className="relative">
                  <button
                    className="rounded-full p-2 text-gray-500 hover:bg-gray-100 hover:text-gray-700"
                  >
                    <FaEllipsisV />
                  </button>
                </div>
              )}
            </div>

            {/* Nội dung bài viết */}
            <div className="p-4 border-b">
              <p className="mb-2">{relatedPost.content}</p>

              {/* Hashtags */}
              {relatedPost.hashtags && Array.isArray(relatedPost.hashtags) && relatedPost.hashtags.length > 0 && (
                <div className="mb-3">
                  {relatedPost.hashtags.map((tag, index) => (
                    <span
                      key={tag.id}
                      className="cursor-pointer text-blue-600 hover:underline"
                    >
                      #{tag.name}
                      {index < relatedPost.hashtags.length - 1 ? " " : ""}
                    </span>
                  ))}
                </div>
              )}
            </div>

            {/* Thống kê tương tác */}
            <div className="flex justify-between px-4 py-2 text-sm text-gray-500 border-b">
              <div>{relatedPost.likes} lượt thích</div>
              <div>
                {relatedPost.comments} bình luận • {relatedPost.shares} chia sẻ
              </div>
            </div>

            {/* Các nút tương tác */}
            <div className="flex justify-between px-2 py-2 border-b">
              <button
                onClick={() => handlePostUpdate({ likes: relatedPost.likes + 1 })}
                className="flex flex-1 items-center justify-center gap-2 rounded-md py-2 text-gray-600 transition hover:bg-gray-100"
              >
                <FaThumbsUp />
                <span>Thích</span>
              </button>
              <button
                onClick={() => handlePostUpdate({ showComments: !relatedPost.showComments })}
                className="flex flex-1 items-center justify-center gap-2 rounded-md py-2 text-gray-600 transition hover:bg-gray-100"
              >
                <FaComment />
                <span>Bình luận</span>
              </button>
              <button
                onClick={() => handlePostUpdate({ shares: relatedPost.shares + 1 })}
                className="flex flex-1 items-center justify-center gap-2 rounded-md py-2 text-gray-600 transition hover:bg-gray-100"
              >
                <FaShare />
                <span>Chia sẻ</span>
              </button>
              <button
                onClick={() => handlePostUpdate({ saved: !relatedPost.saved })}
                className={`flex flex-1 items-center justify-center gap-2 py-2 ${relatedPost.saved ? "text-blue-600" : "text-gray-600"} rounded-md transition hover:bg-gray-100`}
              >
                <FaBookmark />
                <span>Lưu</span>
              </button>
            </div>

            {/* Phần bình luận - luôn hiển thị */}
            <div className="p-4">
              {/* Form nhập bình luận */}
              <div className="flex items-start gap-3 mb-4">
                <Image
                  src={currentUser.avatar}
                  alt={currentUser.name}
                  width={32}
                  height={32}
                  className="rounded-full object-cover"
                />
                <div className="flex-1">
                  <input
                    type="text"
                    placeholder="Viết bình luận..."
                    className="w-full rounded-full border border-gray-200 px-4 py-2 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                  />
                </div>
              </div>

              {/* Danh sách bình luận - luôn hiển thị */}
              {relatedPost.commentsList && relatedPost.commentsList.length > 0 ? (
                <div className="space-y-4">
                  {relatedPost.commentsList.map((comment) => (
                    <div key={comment.id} className="border-b border-gray-100 pb-3 last:border-0">
                      <div className="flex items-start gap-3">
                        <Image
                          src={comment.user.avatar}
                          alt={comment.user.name}
                          width={32}
                          height={32}
                          className="rounded-full object-cover"
                        />
                        <div className="flex-1">
                          <div className="rounded-lg bg-gray-100 p-3">
                            <div className="font-medium">
                              {comment.user.name}
                            </div>
                            <div>{comment.content}</div>
                          </div>

                          <div className="mt-1 flex items-center gap-4 text-sm">
                            <button className="text-gray-500 hover:text-blue-600">
                              Thích ({comment.likes})
                            </button>
                            <button className="text-gray-500 hover:text-blue-600">
                              Trả lời
                            </button>
                            <span className="text-gray-500">
                              {comment.createdAt}
                            </span>
                          </div>

                          {/* Replies */}
                          {comment.replies && comment.replies.length > 0 && (
                            <div className="ml-6 mt-2 space-y-3">
                              {comment.replies.map((reply) => (
                                <div key={reply.id} className="flex items-start gap-2">
                                  <Image
                                    src={reply.user.avatar}
                                    alt={reply.user.name}
                                    width={24}
                                    height={24}
                                    className="rounded-full object-cover"
                                  />
                                  <div className="flex-1">
                                    <div className="rounded-lg bg-gray-100 p-2">
                                      <div className="font-medium text-sm">
                                        {reply.user.name}
                                      </div>
                                      <div className="text-sm">{reply.content}</div>
                                    </div>
                                    <div className="mt-1 flex items-center gap-3 text-xs">
                                      <button className="text-gray-500 hover:text-blue-600">
                                        Thích ({reply.likes})
                                      </button>
                                      <span className="text-gray-500">
                                        {reply.createdAt}
                                      </span>
                                    </div>
                                  </div>
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-4 text-gray-500">
                  Chưa có bình luận nào. Hãy là người đầu tiên bình luận!
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MediaModal;
