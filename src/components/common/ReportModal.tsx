"use client";

import { useState } from "react";
import socialNetworkServices from "@/services/social-network/socialNetworkServices";
import { toast } from "react-toastify";

interface ReportModalProps {
  isOpen: boolean;
  postId: number;
  onClose: () => void;
}

const ReportModal = ({ isOpen, postId, onClose }: ReportModalProps) => {
  const [reportReason, setReportReason] = useState("");
  const [isReporting, setIsReporting] = useState(false);

  if (!isOpen) return null;

  /**
   * Xử lý gửi báo cáo vi phạm
   */
  const handleReportPost = async () => {
    if (!reportReason.trim()) return;

    try {
      setIsReporting(true);
      await socialNetworkServices.reportPostSocialNetwork(postId, reportReason);
      toast.success("Báo cáo đã được gửi thành công");
      onClose();
      setReportReason("");
    } catch (error) {
      toast.error("Có lỗi xảy ra khi gửi báo cáo");
      console.error("Lỗi khi báo cáo bài đăng:", error);
    } finally {
      setIsReporting(false);
    }
  };

  /**
   * Đóng modal và xóa nội dung báo cáo
   */
  const handleCloseModal = () => {
    onClose();
    setReportReason("");
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-2 sm:p-4">
      <div className="relative w-full max-w-md rounded-lg bg-white p-4 sm:p-6">
        <button
          onClick={handleCloseModal}
          className="absolute right-7 top-5 bg-white text-gray-500 hover:bg-gray-100"
        >
          <i className="ri-close-line h-6 w-6 text-xl"></i>
        </button>
        <h3 className="mb-4 text-lg font-semibold text-gray-900">
          Báo cáo bài viết
        </h3>
        <textarea
          className="w-full rounded border border-gray-300 px-4 py-2 text-sm"
          placeholder="Nhập lý do báo cáo..."
          value={reportReason}
          onChange={(e) => setReportReason(e.target.value)}
          rows={4}
        />
        <div className="mt-4 flex flex-col justify-end gap-2 sm:flex-row">
          <button
            onClick={handleCloseModal}
            className="mb-2 rounded-lg border border-gray-300 px-4 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 sm:mb-0"
          >
            Hủy bỏ
          </button>
          <button
            onClick={handleReportPost}
            disabled={!reportReason.trim() || isReporting}
            className="rounded-lg bg-primary px-4 py-2 text-sm font-medium text-white hover:bg-primary/80 disabled:bg-gray-400"
          >
            {isReporting ? "Đang gửi..." : "Gửi báo cáo"}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ReportModal;
