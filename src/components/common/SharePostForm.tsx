"use client";

import { useEffect, useRef, useState } from "react";

import DefaultAvatar from "@/components/ui/DefaultAvatar";
import socialNetworkServices from "@/services/social-network/socialNetworkServices";
import { PrivacyLevel } from "@/services/social-network/types/types";
import Image from "next/image";
import { FaTimes } from "react-icons/fa";
import HashtagInput from "../../app/(homepage)/social-network/profile/components/posts/HashtagInput";
import PrivacySelector from "../../app/(homepage)/social-network/profile/components/posts/PrivacySelector";

interface SharePostFormProps {
  postId: number;
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  userAvatar?: string;
  userName: string;
}

const SharePostForm = ({
  postId,
  isOpen,
  onClose,
  onSuccess,
  userAvatar,
  userName
}: SharePostFormProps) => {
  const [content, setContent] = useState("");
  const [privacy, setPrivacy] = useState<PrivacyLevel>(PrivacyLevel.PUBLIC);
  const [hashtags, setHashtags] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const modalRef = useRef<HTMLDivElement>(null);

  // Reset form khi đóng
  const resetForm = () => {
    setContent("");
    setPrivacy(PrivacyLevel.PUBLIC);
    setHashtags([]);
  };

  // Xử lý đóng form với reset dữ liệu
  const handleClose = () => {
    resetForm();
    onClose();
  };

  // Xử lý click bên ngoài modal để đóng
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
        handleClose();
      }
    };

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen, onClose]);

  // Xử lý chia sẻ bài đăng
  const handleSharePost = async () => {
    if (isLoading) return;
    setIsLoading(true);

    try {
      await socialNetworkServices.sharePostSocialNetwork({
        postReferId: postId,
        content: content.trim() || "",
        privacyPolicy: privacy,
        hashTags: hashtags.length > 0 ? hashtags : []
      });
      onSuccess();
      handleClose();
    } catch (error) {
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 md:p-4">
      <div
        ref={modalRef}
        className="w-full max-w-lg rounded-lg bg-white shadow-xl"
      >
        {/* Header */}
        <div className="flex items-center justify-between border-b p-4">
          <h3 className="text-lg font-medium">Chia sẻ bài đăng</h3>
          <button
            onClick={handleClose}
            className="rounded-full p-1 text-gray-500 hover:bg-gray-100"
          >
            <FaTimes />
          </button>
        </div>

        {/* Form content */}
        <div className="p-4">
          {/* User info */}
          <div className="mb-4 flex items-center gap-2">
            <div className="relative h-10 w-10 overflow-hidden rounded-full">
              {userAvatar ? (
                <Image
                  src={userAvatar}
                  alt={userName}
                  fill
                  className="object-cover"
                />
              ) : (
                <DefaultAvatar name={userName} size="100%" />
              )}
            </div>
            <div>
              <div className="font-medium">{userName}</div>
              <PrivacySelector
                privacy={privacy}
                onChange={setPrivacy}
                buttonClassName="flex items-center gap-1 rounded px-2 py-1 text-sm text-gray-600 hover:bg-gray-100"
              />
            </div>
          </div>

          {/* Content textarea */}
          <textarea
            value={content}
            onChange={(e) => setContent(e.target.value)}
            placeholder="Viết gì đó về bài đăng này..."
            className="mb-4 h-24 w-full resize-none rounded-lg border border-gray-300 p-3 focus:border-blue-500 focus:outline-none"
          />

          {/* Hashtags */}
          <HashtagInput 
            hashtags={hashtags} 
            onChange={setHashtags} 
          />
        </div>

        {/* Footer */}
        <div className="flex justify-end border-t p-4">
          <button
            onClick={handleClose}
            className="mr-2 rounded-lg border border-gray-300 px-4 py-2 text-gray-700 hover:bg-gray-100"
          >
            Hủy
          </button>
          <button
            onClick={handleSharePost}
            disabled={isLoading}
            className="rounded-lg bg-blue-500 px-4 py-2 text-white hover:bg-blue-600 disabled:bg-blue-400"
          >
            {isLoading ? "Đang chia sẻ..." : "Chia sẻ"}
          </button>
        </div>
      </div>
    </div>
  );
};

export default SharePostForm;
