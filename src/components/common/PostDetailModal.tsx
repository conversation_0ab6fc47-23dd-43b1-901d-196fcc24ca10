import { ExtendedPost } from "@/app/(homepage)/social-network/profile/components/PostsTab";
import useUploadFile from "@/hook/useUploadFile";
import socialNetworkServices from "@/services/social-network/socialNetworkServices";
import { useEffect, useState } from "react";
import { FaSpinner } from "react-icons/fa";
import PostContent from "../common/PostContent";

interface PostDetailModalProps {
  /**
   * Trạng thái hiển thị của modal
   */
  isOpen: boolean;
  
  /**
   * Hàm xử lý đóng modal
   */
  onClose: () => void;
  
  /**
   * ID của bài đăng cần hiển thị chi tiết
   */
  postId: number;
  
  /**
   * Hàm reset danh sách bài đăng sau khi có thay đổi
   */
  resetList?: () => void;
}

/**
 * Component modal hiển thị chi tiết bài đăng
 * 
 * @param props - <PERSON><PERSON><PERSON> thuộc tính của component
 * @returns JSX.Element - Giao diện modal hiển thị chi tiết bài đăng
 */
const PostDetailModal = ({
  isOpen,
  onClose,
  postId,
  resetList
}: PostDetailModalProps) => {
  const { viewFile } = useUploadFile();
  const [selectedPost, setSelectedPost] = useState<ExtendedPost | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  /**
   * Hàm lấy chi tiết bài đăng từ API
   */
  const fetchPostDetail = async () => {
    if (!isOpen || !postId) return;
    
    try {
      setIsLoading(true);
      setErrorMessage(null); // Reset lỗi trước khi tải bài đăng mới
      
      const response = await socialNetworkServices.getPostSocialNetworkById(postId);

      if (response && response.data.data) {
        const postData = response.data.data;
        let avatarUrl = undefined;
        let fileUrls: string[] = [];
        let postReferWithUrls = null;

        // Xử lý avatar URL
        try {
          if (postData.thumbnail) {
            avatarUrl = await viewFile(postData.thumbnail, "social-network") as string;
          }
        } catch (error) {
          console.error("Lỗi khi xử lý avatar:", error);
        }

        // Xử lý file URLs
        try {
          if (postData.files && postData.files.length > 0) {
            // Gọi API viewFile cho từng fileId trong mảng files
            const filePromises = postData.files.map((fileId: string) =>
              viewFile(fileId, "social-network")
            );
            // Ép kiểu kết quả trả về thành string[]
            fileUrls = (await Promise.all(filePromises)) as string[];
          }
        } catch (error) {
          console.error("Lỗi khi xử lý files:", error);
        }

        // Xử lý postRefer nếu có
        if (postData.postRefer) {
          try {
            let referAvatarUrl = undefined;
            let referFileUrls: string[] = [];

            // Xử lý avatar của postRefer
            if (postData.postRefer.thumbnail) {
              referAvatarUrl = await viewFile(
                postData.postRefer.thumbnail,
                "social-network"
              ) as string;
            }

            // Xử lý files của postRefer
            if (postData.postRefer.files && postData.postRefer.files.length > 0) {
              const referFilePromises = postData.postRefer.files.map(
                (fileId: string) => viewFile(fileId, "social-network")
              );
              referFileUrls = (await Promise.all(
                referFilePromises
              )) as string[];
            }

            postReferWithUrls = {
              ...postData.postRefer,
              avatarUrl: referAvatarUrl,
              fileUrls: referFileUrls
            };
          } catch (error) {
            console.error("Lỗi khi xử lý postRefer:", error);
            setErrorMessage("Không thể tải thông tin bài viết được tham chiếu");
          }
        }

        const extendedPost: ExtendedPost = {
          ...postData,
          avatarUrl,
          fileUrls,
          postRefer: postReferWithUrls
        };

        setSelectedPost(extendedPost);
      } else {
        setErrorMessage("Bài viết không khả dụng hoặc đã bị xóa");
      }
    } catch (error) {
      console.error("Lỗi khi lấy dữ liệu bài đăng:", error);
      setErrorMessage("Bài viết không khả dụng hoặc đã bị xóa");
    } finally {
      setIsLoading(false);
    }
  };
  useEffect(() => {
    fetchPostDetail();
  }, [isOpen, postId]);

  const handleResetList = () => {
    fetchPostDetail();
    resetList?.();
  };

  // Nếu modal không được hiển thị, không render gì cả
  if (!isOpen) return null;
  if (isLoading) {
    return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-30">
      <div className="rounded-lg bg-white p-6 shadow-xl">
        <div className="flex items-center space-x-3">
          <FaSpinner className="animate-spin text-2xl text-blue-600" />
          <span>Đang tải bài đăng...</span>
        </div>
      </div>
    </div>
  )}
  
  return (
    <>
      {/* Modal hiển thị chi tiết bài đăng */}
      <div className="fixed inset-0 z-50 flex items-center justify-center overflow-hidden bg-black bg-opacity-50 md:p-4">
        <div className="relative max-h-[90vh] w-full max-w-2xl overflow-y-auto rounded-lg bg-white p-4 shadow-xl">
          {/* Nút đóng */}
          <button
            className="modal-close-button absolute right-4 top-4 text-gray-500 hover:text-gray-700"
            onClick={onClose}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-6 w-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>

          {/* Sử dụng PostContent để hiển thị nội dung bài đăng */}
          <div className="mt-2">
            <PostContent
              post={selectedPost}
              resetList={handleResetList}
              hasBorder={false}
              isItemView={true}
              errorMessage={errorMessage || undefined}
            />
          </div>
        </div>
      </div>
    </>
  );
};

export default PostDetailModal;
