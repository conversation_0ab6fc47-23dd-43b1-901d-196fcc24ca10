"use client";

import Loading from "@/components/ui/Loading";
import useUploadFile from "@/hook/useUploadFile";
import dynamic from "next/dynamic";
import { useState } from "react";
import "react-quill-new/dist/quill.snow.css";
import { toast } from "react-toastify";

// Dynamic import của ReactQuill để tránh lỗi SSR
const ReactQuill = dynamic(() => import("react-quill-new"), {
  ssr: false,
  loading: () => <Loading color="primary" size="sm" variant="spinner" />
});

interface QuillEditorProps {
  value: string;
  onChange: (content: string) => void;
  placeholder?: string;
  className?: string;
}

const QuillEditor = ({
  value,
  onChange,
  placeholder = "Nhập nội dung...",
  className = "relative max-w-none"
}: QuillEditorProps) => {
  const [fileInput, setFileInput] = useState<HTMLInputElement | null>(null);
  const [uploading, setUploading] = useState(false);
  const [quillInstance, setQuillInstance] = useState<any>(null);
  const { uploadForumFile, viewFile } = useUploadFile();

  // Xử lý thêm ảnh vào editor
  const imageHandler = () => {
    if (uploading) {
      toast.info("Đang tải ảnh lên, vui lòng đợi...");
      return;
    }
    
    // Tạo input file ẩn và kích hoạt sự kiện click
    if (!fileInput) {
      const input = document.createElement('input');
      input.setAttribute('type', 'file');
      input.setAttribute('accept', 'image/*');
      input.style.display = 'none';
      input.onchange = handleImageUpload;
      document.body.appendChild(input);
      setFileInput(input);
    }
    
    fileInput?.click();
  };

  // Xử lý khi người dùng chọn file
  const handleImageUpload = async (e: Event) => {
    const input = e.target as HTMLInputElement;
    const file = input.files?.[0];
    
    if (!file) return;
    
    // Kiểm tra file có phải là ảnh không
    if (!file.type.includes('image')) {
      toast.error('Vui lòng chọn file ảnh');
      return;
    }
    
    try {
      setUploading(true);
      
      // Tải file lên server
      const response = await uploadForumFile(file);
      
      if (response && response.data) {
        // Lấy URL của ảnh trực tiếp (không chuyển đổi sang base64)
        const imageUrl = await viewFile(response.data, 'forums');
        
        // Chèn ảnh vào editor
        if (quillInstance) {
          try {
            const range = quillInstance.getSelection(true);
            quillInstance.insertEmbed(range.index, 'image', imageUrl);
            quillInstance.setSelection(range.index + 1);
          } catch (error) {
            console.error('Lỗi khi chèn ảnh vào editor:', error);
            // Fallback: chèn ảnh vào nội dung hiện tại
            const imgTag = `<img src="${imageUrl}" alt="Uploaded Image" />`;  
            onChange(value + imgTag);
          }
        } else {
          // Fallback nếu không có instance Quill
          const imgTag = `<img src="${imageUrl}" alt="Uploaded Image" />`;  
          onChange(value + imgTag);
        }
      
      }
    } catch (error) {
      console.error('Lỗi khi tải ảnh lên:', error);
      toast.error('Không thể tải ảnh lên. Vui lòng thử lại sau.');
    } finally {
      setUploading(false);
      // Reset input file
      if (fileInput) {
        fileInput.value = '';
      }
    }
  };

  return (
    <ReactQuill
      value={value}
      onChange={(content) => {
        // Cập nhật instance Quill mỗi khi nội dung thay đổi
        setTimeout(() => {
          const editor = document.querySelector('.ql-editor');
          if (editor && editor.parentElement) {
            const quill = (editor.parentElement as any).__quill;
            if (quill) {
              setQuillInstance(quill);
            }
          }
        }, 0);
        
        onChange(content);
      }}
      placeholder={placeholder}
      className={className}
      theme="snow"
      modules={{
        clipboard: {
          matchVisual: false
        },
        toolbar: {
          container: [
            // Row 1: Định dạng văn bản cơ bản
            [{ header: [1, 2, 3, 4, 5, 6, false] }],
            [{ font: [] }],
            [{ size: [] }],
            [{ color: [] }, { background: [] }],

            // Row 2: Định dạng ký tự
            ["bold", "italic", "underline", "strike", "blockquote"],
            [{ script: "sub" }, { script: "super" }],

            // Row 3: Định dạng đoạn văn
            [{ list: "ordered" }, { list: "bullet" }],
            [{ indent: "-1" }, { indent: "+1" }],
            [{ align: [] }],

            // Row 4: Các chức năng bổ sung
            ["link", "image", "video"],
            ["clean", "code-block"],

            // Row 5: Chức năng bảng
            // ['table'],

            // Row 6: Chức năng đặc biệt
            [{ direction: "rtl" }]
          ],
          handlers: {
            image: imageHandler
          },
        },
      }}
    />
  );
};

export default QuillEditor;
