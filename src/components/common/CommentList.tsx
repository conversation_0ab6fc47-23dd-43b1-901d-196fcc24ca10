"use client";

import { useEffect, useState } from "react";
import { toast } from "react-toastify";

import useUploadFile from "@/hook/useUploadFile";
import socialNetworkServices from "@/services/social-network/socialNetworkServices";
import CommentItem, { Comment } from "./CommentItem";

interface CommentListProps {
  postId: number;
}

const CommentList = ({ postId }: CommentListProps) => {
  const [comments, setComments] = useState<Comment[]>([]);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(0);
  const [hasMore, setHasMore] = useState(true);
  const { viewFile } = useUploadFile();

  // Lấy danh sách bình luận
  const fetchComments = async (pageIndex: number) => {
    if (loading) return;
    setLoading(true);
    
    try {
      const response = await socialNetworkServices.getListCommentPostSocialNetwork({
        page: pageIndex,
        size: 5,
        sort: "DESC",
        postSocialNetworkId: postId
      });

      if (response.data?.data?.content) {
        const commentsData = response.data.data.content;
        
        // Xử lý lấy URL cho avatar của mỗi bình luận
        const commentsWithAvatars = await Promise.all(
          commentsData.map(async (comment: any) => {
            let avatarUrl = undefined;
            
            // Nếu có avatar, gọi API viewFile để lấy URL
            if (comment.thumbnail) {
              try {
                avatarUrl = await viewFile(comment.thumbnail, "social-network") as string;
              } catch (error) {
                console.error("Lỗi khi lấy URL cho avatar:", error);
              }
            }
            
            return {
              ...comment,
              avatarUrl
            };
          })
        );
        
        setComments(pageIndex === 0 ? commentsWithAvatars : [...comments, ...commentsWithAvatars]);
        setHasMore(!response.data.data.last);
        if (!response.data.data.last) {
          setPage(pageIndex + 1);
        }
      }
    } catch (error) {
      console.error("Lỗi khi lấy danh sách bình luận:", error);
    } finally {
      setLoading(false);
    }
  };

  // Lấy danh sách bình luận khi component được mount
  useEffect(() => {
    fetchComments(page);
  }, []);

  // Xử lý thích bình luận
  const handleLikeComment = async (commentId: number) => {
    try {
      await socialNetworkServices.likeCommentPostSocialNetwork(
        postId, 
        commentId
      );
      
      // Cập nhật UI
      setComments(comments.map(comment => {
        if (comment.commentPostSnId === commentId) {
          return { ...comment, isLiked: true, likeCount: comment.likeCount + 1 };
        }
        // Kiểm tra nếu comment có childComments
        if (comment.childComments) {
          const updatedChildComments = comment.childComments.map(childComment => {
            if (childComment.commentPostSnId === commentId) {
              return { ...childComment, isLiked: true, likeCount: childComment.likeCount + 1 };
            }
            return childComment;
          });
          return { ...comment, childComments: updatedChildComments };
        }
        return comment;
      }));
    } catch (error) {
      console.error("Lỗi khi thích bình luận:", error);
      toast.error("Không thể thích bình luận");
    }
  };

  // Xử lý bỏ thích bình luận
  const handleUnlikeComment = async (commentId: number) => {
    try {
      await socialNetworkServices.unlikeCommentPostSocialNetwork(
        postId, 
        commentId
      );
      
      // Cập nhật UI
      setComments(comments.map(comment => {
        if (comment.commentPostSnId === commentId) {
          return { ...comment, isLiked: false, likeCount: Math.max(0, comment.likeCount - 1) };
        }
        // Kiểm tra nếu comment có childComments
        if (comment.childComments) {
          const updatedChildComments = comment.childComments.map(childComment => {
            if (childComment.commentPostSnId === commentId) {
              return { ...childComment, isLiked: false, likeCount: Math.max(0, childComment.likeCount - 1) };
            }
            return childComment;
          });
          return { ...comment, childComments: updatedChildComments };
        }
        return comment;
      }));
    } catch (error) {
      console.error("Lỗi khi bỏ thích bình luận:", error);
      toast.error("Không thể bỏ thích bình luận");
    }
  };

  // Xử lý xóa bình luận
  const handleDeleteComment = async (commentId: number) => {
    if (!confirm("Bạn có chắc chắn muốn xóa bình luận này không?")) return;
    
    try {
      await socialNetworkServices.deleteCommentPostSocialNetwork(commentId);
      
      // Cập nhật UI - xóa comment hoặc child comment
      setComments(comments.filter(comment => {
        // Nếu comment chính bị xóa
        if (comment.commentPostSnId === commentId) {
          return false;
        }
        
        // Nếu có child comments, kiểm tra và lọc
        if (comment.childComments) {
          comment.childComments = comment.childComments.filter(
            childComment => childComment.commentPostSnId !== commentId
          );
        }
        
        return true;
      }));
      
      toast.success("Đã xóa bình luận");
    } catch (error) {
      console.error("Lỗi khi xóa bình luận:", error);
      toast.error("Không thể xóa bình luận");
    }
  };

  // Xử lý khi có reply mới được thêm vào
  const handleReplyAdded = () => {
    // Có thể thực hiện các hành động khác nếu cần
  };
  
  // Xử lý khi comment được cập nhật (thích, xóa)
  const handleCommentUpdated = () => {
    // Tải lại danh sách comment
    setPage(0);
    fetchComments(0);
  };

  return (
    <div className="space-y-4">
      {comments.length > 0 ? (
        <div className="space-y-3 sm:space-y-4">
          {comments.map((comment) => (
            <CommentItem
              key={comment.commentPostSnId}
              comment={comment}
              postId={postId}
              onLike={handleLikeComment}
              onUnlike={handleUnlikeComment}
              onDelete={handleDeleteComment}
              onReplyAdded={handleReplyAdded}
              onCommentUpdated={handleCommentUpdated}
            />
          ))}
          
          {hasMore && (
            <button 
              onClick={() => fetchComments(page)}
              className="w-full py-1.5 sm:py-2 text-center text-xs sm:text-sm text-blue-500 hover:text-blue-600 hover:underline"
              disabled={loading}
            >
              {loading ? 'Đang tải...' : 'Xem thêm bình luận'}
            </button>
          )}
        </div>
      ) : (
        <div className="py-3 sm:py-4 text-center text-xs sm:text-sm text-gray-500">
          {loading ? 'Đang tải bình luận...' : 'Chưa có bình luận nào. Hãy là người đầu tiên bình luận!'}
        </div>
      )}
    </div>
  );
};

export default CommentList;
