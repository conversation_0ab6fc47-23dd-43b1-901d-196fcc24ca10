"use client";

import { cn } from "@/lib/utils";
import { useRouter } from "next/navigation";
import { FaTag, FaTimes } from "react-icons/fa";

interface HashtagDisplayProps {
  hashtags: string[];
  onRemove?: (tag: string) => void;
  className?: string;
  tagClassName?: string;
  showTagIcon?: boolean;
  readOnly?: boolean;
}

const HashtagDisplay = ({
  hashtags,
  onRemove,
  className = "mb-2 flex flex-wrap gap-2",
  tagClassName = "flex items-center gap-1 rounded-full bg-blue-100 px-3 py-1 text-sm text-blue-700",
  showTagIcon = true,
  readOnly = false
}: HashtagDisplayProps) => {
  if (hashtags.length === 0) {
    return null;
  }

  const router = useRouter();

  return (
    <div className={className}>
      {hashtags.map((tag, index) => (
        <div key={index} className={cn(tagClassName, readOnly && 'cursor-pointer')} onClick={() => {
          if (readOnly) {
            router.push(`/social-network?type=hashtags&q=${tag}`);
          }
        }}>
          {showTagIcon && <FaTag className="text-blue-600" size={12} />}
          <span>{tag}</span>
          {!readOnly && onRemove && (
            <button
              onClick={() => onRemove(tag)}
              className="ml-1 text-xs text-blue-700 hover:text-blue-900"
              type="button"
            >
              <FaTimes />
            </button>
          )}
        </div>
      ))}
    </div>
  );
};

export default HashtagDisplay;
