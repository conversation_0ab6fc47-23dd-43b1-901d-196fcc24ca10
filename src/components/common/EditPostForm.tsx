"use client";

import { Post, PrivacyLevel } from "@/services/social-network/types/types";
import { useEffect, useState } from "react";

import DefaultAvatar from "@/components/ui/DefaultAvatar";
import useUploadFile from "@/hook/useUploadFile";
import { cn } from "@/lib/utils";
import socialNetworkServices from "@/services/social-network/socialNetworkServices";
import usePostStore from "@/stores/postStore";
import Image from "next/image";
import { toast } from "react-toastify";
import HashtagInput from "../../app/(homepage)/social-network/profile/components/posts/HashtagInput";
import ImageUploader from "../../app/(homepage)/social-network/profile/components/posts/ImageUploader";
import PrivacySelector from "../../app/(homepage)/social-network/profile/components/posts/PrivacySelector";

interface EditPostFormProps {
  post: Post & { fileUrls?: string[] , avatarUrl?: string };
  onCancelEdit: () => void;
  resetList: () => void;
  hasBorder?: boolean;
}

const EditPostForm = ({ post, onCancelEdit, resetList, hasBorder = true }: EditPostFormProps) => {
  const { uploadSocialNetworkFile } = useUploadFile();
  const [content, setContent] = useState(post?.content || "");
  const [hashtags, setHashtags] = useState<string[]>(
    post?.hashTags || []
  );
  const [privacy, setPrivacy] = useState<PrivacyLevel>(
    (post?.privacyPolicy as PrivacyLevel) || PrivacyLevel.PUBLIC
  );
  const [images, setImages] = useState<string[]>(
    post?.fileUrls || post?.files || []
  );
  const [imageFiles, setImageFiles] = useState<File[]>([]);
  const [loading, setLoading] = useState(false);


  // Effect để cập nhật state khi props thay đổi
  useEffect(() => {
    setContent(post.content || "");
    setHashtags(post.hashTags || []);
    setPrivacy((post.privacyPolicy as PrivacyLevel) || PrivacyLevel.PUBLIC);
    setImages(post.fileUrls || post.files || []);
    setImageFiles([]); // Reset imageFiles khi edit post
  }, [post]);



  const handleSubmit = async () => {
    if (!content.trim() || loading) return;
    setLoading(true);

    try {
      // Sử dụng hashtags trực tiếp từ state

      // Tải lên các file ảnh mới và lấy fileIds
      let fileIds: string[] = [];

      // Nếu có file mới, tải lên và lấy fileIds
      if (imageFiles.length > 0) {
        // Tải lên từng file và lấy fileId
        const uploadPromises = imageFiles.map((file) =>
          uploadSocialNetworkFile(file)
        );
        const uploadResults = await Promise.all(uploadPromises);

        // Lấy fileIds từ kết quả tải lên
        const newFileIds = uploadResults.map((result) => result.data);
        fileIds = [...newFileIds];
      }

      // Thêm các file ảnh cũ
      if (post?.files) {
        // Khi edit, chúng ta đã có fileUrls để hiển thị, nhưng cần giữ lại fileIds gốc để gửi API
        // Chỉ giữ lại các fileId cũ nếu chúng vẫn còn trong danh sách images (so sánh index)
        const originalFileUrls = post.fileUrls || [];
        const keptFileIndices = images
          .map((url, index) => (originalFileUrls.includes(url) ? index : -1))
          .filter((index) => index !== -1);

        // Lấy các fileId tương ứng với các fileUrl được giữ lại
        const existingFileIds = keptFileIndices.map(
          (index) => post.files[index]
        );
        fileIds = [...fileIds, ...existingFileIds];
      }

      // Chuẩn bị dữ liệu để gửi
      const postData = {
        content: content.trim(),
        privacyPolicy: privacy,
        hashTags: hashtags,
        files: fileIds.length > 0 ? fileIds : undefined
      };

      // Cập nhật bài đăng
      await socialNetworkServices.updatePostSocialNetwork({
        ...postData,
        postSocialNetworkId: post.postSocialNetworkId
      });
      toast.success("Đã cập nhật bài đăng");
      onCancelEdit();
      
      // Gọi cả hàm resetList local và triggerReset global
      resetList();
      usePostStore.getState().triggerReset();
    } catch (error) {
      console.error("Lỗi khi cập nhật bài viết:", error);
      toast.error("Không thể cập nhật bài viết");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={cn("overflow-hidden bg-white transition-all duration-300", hasBorder ? "rounded-xl shadow-lg" : "")}>

      <div className="p-5">
        {/* Header với avatar và tên người dùng */}
        <div className="mb-4 flex items-center gap-3">
        <div className="relative w-10 h-10 overflow-hidden rounded-full">
            {post.avatarUrl ? (
              <Image
                src={post.avatarUrl}
                alt={
                  `${post.firstName} ${post.lastName}`
                }
                fill
                className="rounded-full object-cover"
              />
            ) : (
              <DefaultAvatar
                name={
                  `${post.firstName} ${post.lastName}` ||
                  "User"
                }
                size="100%"
              />
            )}
          </div>
          <div>
            <div className="font-medium text-gray-800">{`${post.firstName} ${post.lastName}` }</div>
            <div className="mt-1">
              <PrivacySelector 
                privacy={privacy} 
                onChange={setPrivacy} 
                buttonClassName="flex items-center gap-2 rounded-md bg-gray-100 px-3 py-1 text-sm font-medium text-gray-700 transition-colors hover:bg-gray-200"
              />
            </div>
          </div>
        </div>

        {/* Textarea nhập nội dung */}
        <div className="mb-4">
          <textarea
            value={content}
            onChange={(e) => setContent(e.target.value)}
            placeholder="Bạn đang nghĩ gì?"
            className="min-h-[120px] w-full resize-none rounded-lg border-0 bg-gray-50 p-4 text-gray-800 placeholder-gray-500 focus:bg-white focus:outline-none focus:ring-2 focus:ring-blue-500/50"
          />
        </div>

        <HashtagInput hashtags={hashtags} onChange={setHashtags} />

        <ImageUploader 
          images={images}
          onImagesChange={setImages}
          imageFiles={imageFiles}
          onImageFilesChange={setImageFiles}
          className="mb-3 mt-2"
        />

        {/* Footer với các nút chức năng */}
        <div className="mt-4 flex items-center justify-end gap-2 border-t border-gray-100 pt-4">
          <div className="flex items-center space-x-2">

            <button
              onClick={onCancelEdit}
              className="ml-2 flex items-center gap-2 rounded-full bg-gray-100 px-4 py-2 text-gray-700 transition-all hover:bg-gray-200"
              type="button"
            >
              <span className="font-medium">Hủy</span>
            </button>
          </div>

          <button
            onClick={handleSubmit}
            className="rounded-full bg-blue-600 px-6 py-2 font-medium text-white shadow-sm transition-all hover:bg-blue-700 hover:shadow-md disabled:opacity-70 disabled:hover:bg-blue-600 disabled:hover:shadow-none"
            disabled={!content.trim() || loading}
          >
            {loading ? (
              <span className="flex items-center gap-2">
                <svg className="h-4 w-4 animate-spin" viewBox="0 0 24 24">
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                    fill="none"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                Đang lưu...
              </span>
            ) : (
              "Lưu"
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default EditPostForm;
