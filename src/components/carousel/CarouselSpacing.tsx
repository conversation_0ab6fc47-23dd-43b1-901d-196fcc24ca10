import * as React from "react";
import Image from "next/image";

import { type CarouselApi } from "@/components/ui/carousel";

import { Card, CardContent } from "@/components/ui/card";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious
} from "@/components/ui/carousel";

export default function CarouselSpacing({ data }: { data: any }) {
  const [api, setApi] = React.useState<CarouselApi>();
  const [current, setCurrent] = React.useState(0);
  const [count, setCount] = React.useState(0);

  React.useEffect(() => {
    if (!api) {
      return;
    }

    setCount(api.scrollSnapList().length);
    setCurrent(api.selectedScrollSnap() + 1);

    api.on("select", () => {
      setCurrent(api.selectedScrollSnap() + 1);
    });
  }, [api]);

  return (
    <Carousel setApi={setApi} className="mni-h-[457px] w-full">
      <CarouselContent className="-ml-1 items-center">
        {data?.map((item: any, index: any) => (
          <CarouselItem
            key={index}
            className={`pl-1 md:!basis-1/5 ${current !== index + 1 && index + 1 <= current + 3 ? "md:h-[457px] lg:basis-[30%]" : "!lg:basis-[5%] md:h-[222px]"} h-full md:basis-1/2`}
          >
            <div className="h-full p-1">
              <Card>
                <CardContent className="relative flex aspect-square flex-col items-center justify-center p-0">
                  <div
                    className={`h-full w-full ${current !== index + 1 && index + 1 <= current + 3 ? "md:h-[399px]" : "md:h-[222px]"}`}
                  >
                    <Image
                      className="h-full w-full rounded-[10px] object-cover"
                      src={item.imageUrl}
                      alt="Placeholder"
                      width={300}
                      height={300}
                    />
                  </div>

                  <p
                    className={`line-clamp-2 ${current !== index + 1 && index + 1 <= current + 3 ? "md:opacity-100" : "md:opacity-0"} text-[16.2px] font-[600] text-[#414B5B] opacity-100`}
                  >
                    {item.title}
                  </p>
                </CardContent>
              </Card>
            </div>
          </CarouselItem>
        ))}
      </CarouselContent>
      <CarouselPrevious className="hidden md:flex" />
      <CarouselNext className="hidden md:flex" />
    </Carousel>
  );
}
