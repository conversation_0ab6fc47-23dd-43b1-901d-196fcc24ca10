import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON>le,
  DialogFooter
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import userServices from "@/services/user/userServices";
import { toast } from "react-toastify";
import { Eye, EyeOff } from "lucide-react";

interface ChangePasswordModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function ChangePasswordModal({
  isOpen,
  onClose
}: ChangePasswordModalProps) {
  const [currentPassword, setCurrentPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isHasPassword, setIsHasPassword] = useState(false);
  const [isChecking, setIsChecking] = useState(true);

  // State để toggle hiện/ẩn mật khẩu
  const [showCurrent, setShowCurrent] = useState(false);
  const [showNew, setShowNew] = useState(false);
  const [showConfirm, setShowConfirm] = useState(false);

  useEffect(() => {
    const checkPassword = async () => {
      try {
        const res = await userServices.isHasPassword();
        setIsHasPassword(res?.data?.data);
      } catch (error) {
        toast.error("Không thể kiểm tra trạng thái mật khẩu");
      } finally {
        setIsChecking(false);
      }
    };

    if (isOpen) {
      checkPassword();
    }
  }, [isOpen]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Kiểm tra mật khẩu không được bỏ trống
    if (isHasPassword && !currentPassword.trim()) {
      toast.error("Vui lòng nhập mật khẩu cũ");
      return;
    }

    if (!newPassword.trim() || !confirmPassword.trim()) {
      toast.error("Vui lòng nhập đầy đủ thông tin");
      return;
    }

    // Kiểm tra độ dài mật khẩu
    if (newPassword.length < 6) {
      toast.error("Mật khẩu mới phải có ít nhất 6 ký tự");
      return;
    }

    // Kiểm tra mật khẩu mới và xác nhận mật khẩu phải giống nhau
    if (newPassword !== confirmPassword) {
      toast.error("Mật khẩu mới và xác nhận mật khẩu không giống nhau");
      return;
    }

    try {
      setIsLoading(true);
      await userServices.changePassword({
        oldPassword: isHasPassword ? currentPassword : "",
        newPassword
      });
      toast.success("Đổi mật khẩu thành công");
      onClose();
    } catch (error) {
      toast.error("Đổi mật khẩu thất bại");
    } finally {
      setIsLoading(false);
      setCurrentPassword("");
      setNewPassword("");
      setConfirmPassword("");
    }
  };

  if (isChecking) {
    return (
      <div className="flex items-center justify-center min-h-[418px] bg-white rounded-[8px] shadow">
        <div className="text-center">Đang kiểm tra...</div>
      </div>
    );
  }

  return (
    <div className="flex items-center justify-center min-h-[418px] bg-white rounded-[8px] shadow w-full px-4 sm:px-6">
      <form
        onSubmit={handleSubmit}
        className="w-full max-w-[448px] space-y-4 sm:space-y-6 py-4 sm:py-6"
      >
        <h2 className="text-start text-[18px] sm:text-[20px] font-bold text-[#1F2937]">
          {isHasPassword ? "Đổi mật khẩu" : "Tạo mật khẩu"}
        </h2>
        {isHasPassword && (
          <div className="space-y-1">
            <Label htmlFor="currentPassword" className="text-[13px] sm:text-[14px] text-[#374151] font-[500]">Mật khẩu cũ</Label>
            <div className="relative">
              <Input
                id="currentPassword"
                type={showCurrent ? "text" : "password"}
                value={currentPassword}
                onChange={(e) => setCurrentPassword(e.target.value)}
                required
                placeholder="Nhập mật khẩu cũ"
                className="rounded-[8px] pr-12 text-[14px] sm:text-base"
              />
              <button
                type="button"
                className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400"
                tabIndex={-1}
                onClick={() => setShowCurrent((v) => !v)}
              >
                {showCurrent ? <EyeOff size={18} className="sm:w-5 sm:h-5" /> : <Eye size={18} className="sm:w-5 sm:h-5" />}
              </button>
            </div>
          </div>
        )}
        <div className="space-y-1">
          <Label htmlFor="newPassword" className="text-[13px] sm:text-[14px] text-[#374151] font-[500]">Mật khẩu mới</Label>
          <div className="relative">
            <Input
              id="newPassword"
              type={showNew ? "text" : "password"}
              value={newPassword}
              onChange={(e) => setNewPassword(e.target.value)}
              required
              placeholder="Nhập mật khẩu mới"
              minLength={6}
              className="rounded-[8px] pr-12 text-[14px] sm:text-base"
            />
            <button
              type="button"
              className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400"
              tabIndex={-1}
              onClick={() => setShowNew((v) => !v)}
            >
              {showNew ? <EyeOff size={18} className="sm:w-5 sm:h-5" /> : <Eye size={18} className="sm:w-5 sm:h-5" />}
            </button>
          </div>
        </div>
        <div className="space-y-1">
          <Label htmlFor="confirmPassword" className="text-[13px] sm:text-[14px] text-[#374151] font-[500]">Xác nhận mật khẩu</Label>
          <div className="relative">
            <Input
              id="confirmPassword"
              type={showConfirm ? "text" : "password"}
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              required
              placeholder="Nhập lại mật khẩu mới"
              minLength={6}
              className="rounded-[8px] pr-12 text-[14px] sm:text-base"
            />
            <button
              type="button"
              className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400"
              tabIndex={-1}
              onClick={() => setShowConfirm((v) => !v)}
            >
              {showConfirm ? <EyeOff size={18} className="sm:w-5 sm:h-5" /> : <Eye size={18} className="sm:w-5 sm:h-5" />}
            </button>
          </div>
        </div>
        <Button
          className="w-full rounded-[8px] bg-[#2E228B] text-[14px] sm:text-base font-[400] text-white transition hover:bg-[#1a1766]"
          type="submit"
          disabled={isLoading}
        >
          {isLoading ? "Đang xử lý..." : isHasPassword ? "Đổi mật khẩu" : "Tạo mật khẩu"}
        </Button>
      </form>
    </div>
  );
}
