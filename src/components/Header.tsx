"use client";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { useAuth } from "@/context/AuthContext";
import useUploadFile from "@/hook/useUploadFile";
import { useWebSocket } from "@/hook/useWebSocket";
import notificationsServices from "@/services/notifications/notificationsServices";
import { Menu, Search, User, X } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useParams, useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";
import { Button } from "./ui/button";
import { Input } from "./ui/input";
interface HeaderProps {
  dataMenu: {
    name: string;
    href: string;
  }[];
  dataOfMenuChild: {
    title: string;
    button: { name: string }[];
    items: { name: string; href: string; image: string }[];
  }[];
}

interface ForumNotification {
  notificationsId: number;
  recipientId: number;
  senderId: number;
  content: string;
  title?: string;
  subContent?: string;
  type: "REPLY_COMMENT" | "LIKE_COMMENT" | "COMMENT" | "LIKE_POST";
  objectId: number;
  createdDate: string;
  read: boolean;
}

interface NotificationResponse {
  content: ForumNotification[];
  pageable: {
    pageNumber: number;
    pageSize: number;
  };
  totalPages: number;
  totalElements: number;
  last: boolean;
}

export default function Header({ dataMenu, dataOfMenuChild }: HeaderProps) {
  const { isAuthenticated, user, logout } = useAuth();
  const { eduEcosystemId } = useParams();
  const router = useRouter();

  const [notifications, setNotifications] = useState<ForumNotification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [isActive, setIsActive] = useState<number | null>(null);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [page, setPage] = useState(0);
  const [hasMore, setHasMore] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const { viewFile } = useUploadFile();
  const [imageUser, setImageUser] = useState("");
  const [isLoadingNotification, setIsLoadingNotification] = useState(false);

  const getInitialAvatar = () => {
    const username = user?.forumInfo?.username;
    return typeof username === "string" && username.length > 0
      ? username.charAt(0).toUpperCase()
      : "U";
  };

  const fetchNotificationsNotRead = async () => {
    try {
      const response = await notificationsServices.getCountNotifyNotRead();
      const data = response.data.data as number;
      setUnreadCount(data);
    } catch (error) {
      console.error("Error fetching unread notifications:", error);
    }
  }

  const [searchQuery, setSearchQuery] = useState("");
  // Hàm lấy thông báo
  const fetchNotifications = async (pageNumber: number = 0) => {
    try {
      setIsLoading(true);
      const response = await notificationsServices.getForumNotifications(
        pageNumber,
        20
      );
      const data = response.data.data as NotificationResponse;
      if (pageNumber === 0) {
        setNotifications(data.content);
      } else {
        setNotifications((prev) => [...prev, ...data.content]);
      }

      setHasMore(!data.last);
      setPage(pageNumber);
    } catch (error) {
      console.error("Error fetching notifications:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Hàm xử lý infinite scroll
  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const target = e.currentTarget;
    if (
      target.scrollHeight - target.scrollTop === target.clientHeight &&
      !isLoading &&
      hasMore
    ) {
      fetchNotifications(page + 1);
    }
  };

  // Hàm đánh dấu đã đọc
  const markAsRead = async (id: number) => {
    try {
      setIsLoadingNotification(true);
      const response = await notificationsServices.markNotificationAsRead(id);
      router.push(response?.data?.data);
      setNotifications((prev) =>
        prev.map((notif) =>
          notif.notificationsId === id ? { ...notif, read: true } : notif
        )
      );
    } catch (error) {
      console.error("Error marking notification as read:", error);
    } finally {
      setIsLoadingNotification(false);
    }
  };

  // Hàm xóa thông báo
  const deleteNotification = async (id: number) => {
    try {
      await notificationsServices.deleteNotification(id);
      fetchNotifications(); // Refresh notifications
    } catch (error) {
      console.error("Error deleting notification:", error);
    }
  };

  // Lấy thông báo khi component mount
  useEffect(() => {
    if (isAuthenticated) {
      fetchNotifications(0);
    }
  }, [isAuthenticated]);

  const handleWebSocketMessage = (message: any) => {
    try {
      if (message.type === "notification") {
        // Thay vì gọi fetchNotifications() mỗi khi có thông báo
        // Chỉ cập nhật khi có thông báo mới
        const notificationData = message.data;
        if (notificationData) {
          // Thêm thông báo mới vào đầu danh sách
          setNotifications((prev) => [notificationData, ...prev]);
          // Tăng số lượng chưa đọc nếu thông báo mới chưa đọc
          fetchNotificationsNotRead();
        }
      }
    } catch (error) {
      console.error("Error handling WebSocket notification:", error);
    }
  };

  const { sendMessage } = useWebSocket(handleWebSocketMessage);

  const clearNotifications = async () => {
    try {
      setIsLoading(true);
      // Đánh dấu tất cả thông báo chưa đọc là đã đọc
        await notificationsServices.updateAllRead();
    } catch (error) {
      console.error("Error clearing notifications:", error);
    }finally {
        setIsLoading(false);
    }
  };

  const handleSetActive = (index: number | null) => {
    if (index === null) {
      setIsActive(null);
      return;
    }
    if (dataOfMenuChild[index]?.items?.length) {
      setIsActive(isActive === index ? null : index);
    }
  };

  // Format date from "20250418165842" to readable format
  const formatDate = (dateStr: string) => {
    if (dateStr?.length >= 14) {
      const year = dateStr.substring(0, 4);
      const month = dateStr.substring(4, 6);
      const day = dateStr.substring(6, 8);
      const hour = dateStr.substring(8, 10);
      const minute = dateStr.substring(10, 12);
      const second = dateStr.substring(12, 14);

      const date = new Date(
        `${year}-${month}-${day}T${hour}:${minute}:${second}`
      );
      return new Intl.DateTimeFormat("vi-VN", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
        hour12: false
      }).format(date);
    }
    return "Không xác định";
  };

    // Fetch unread notifications count on mount
    useEffect(() => {
        if (isAuthenticated) {
            fetchNotificationsNotRead();
        }
    }, [isAuthenticated,clearNotifications]);

  // Get notification icon based on type
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case "LIKE_POST":
      case "LIKE_COMMENT":
        return (
          <svg
            width="32"
            height="33"
            viewBox="0 0 32 33"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M0 16.0469C0 7.21032 7.16344 0.046875 16 0.046875C24.8366 0.046875 32 7.21032 32 16.0469C32 24.8834 24.8366 32.0469 16 32.0469C7.16344 32.0469 0 24.8834 0 16.0469Z"
              fill="#FEE2E2"
            />
            <path
              d="M15.992 10.9004C16.5073 10.4381 17.098 10.1337 17.7643 9.98703C18.4305 9.84036 19.0879 9.85592 19.7364 10.0337C20.4115 10.2204 20.9956 10.5604 21.4886 11.0537C21.9816 11.547 22.3214 12.1315 22.508 12.807C22.6856 13.4559 22.7012 14.1115 22.5546 14.7737C22.408 15.4359 22.1038 16.0293 21.6418 16.5537L15.992 22.207L10.3422 16.5537C9.8803 16.0293 9.57604 15.4359 9.42947 14.7737C9.28289 14.1115 9.29844 13.4559 9.47611 12.807C9.66266 12.1315 10.0047 11.547 10.5021 11.0537C10.9996 10.5604 11.5815 10.2204 12.2477 10.0337C12.8962 9.85592 13.5536 9.84036 14.2198 9.98703C14.8861 10.1337 15.4768 10.4381 15.992 10.9004ZM20.5359 11.9937C20.2072 11.6648 19.8252 11.4404 19.3899 11.3204C18.9546 11.2004 18.5149 11.1893 18.0707 11.287C17.6266 11.3848 17.2313 11.5893 16.8848 11.9004L15.992 12.7004L15.0993 11.9004C14.7617 11.5893 14.3686 11.3848 13.92 11.287C13.4714 11.1893 13.0294 11.2004 12.5942 11.3204C12.1589 11.4404 11.7769 11.6648 11.4482 11.9937C11.1195 12.3226 10.893 12.7048 10.7686 13.1404C10.6443 13.5759 10.6287 14.0137 10.722 14.4537C10.8153 14.8937 11.0129 15.287 11.315 15.6337L15.992 20.327L20.6691 15.6337C20.9711 15.287 21.1688 14.8937 21.2621 14.4537C21.3553 14.0137 21.3398 13.5759 21.2154 13.1404C21.0911 12.7048 20.8645 12.3226 20.5359 11.9937Z"
              fill="#EF4444"
            />
          </svg>
        );
      case "COMMENT":
      case "REPLY_COMMENT":
        return (
          <svg
            width="32"
            height="33"
            viewBox="0 0 32 33"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M0 16.0469C0 7.21032 7.16344 0.046875 16 0.046875C24.8366 0.046875 32 7.21032 32 16.0469C32 24.8834 24.8366 32.0469 16 32.0469C7.16344 32.0469 0 24.8834 0 16.0469Z"
              fill="#DBEAFE"
            />
            <path
              d="M12.3016 20.2067L9.33008 22.54V10.2067C9.33008 10.0289 9.39448 9.87337 9.52329 9.74004C9.6521 9.60671 9.80978 9.54004 9.99633 9.54004H21.9888C22.1754 9.54004 22.3331 9.60671 22.4619 9.74004C22.5907 9.87337 22.6551 10.0289 22.6551 10.2067V19.54C22.6551 19.7267 22.5907 19.8867 22.4619 20.02C22.3331 20.1534 22.1754 20.2156 21.9888 20.2067H12.3016ZM11.8352 18.8734H21.3226V10.8734H10.6626V19.8067L11.8352 18.8734ZM15.3263 14.2067H16.6588V15.54H15.3263V14.2067ZM12.6613 14.2067H13.9938V15.54H12.6613V14.2067ZM17.9913 14.2067H19.3238V15.54H17.9913V14.2067Z"
              fill="#3B82F6"
            />
          </svg>
        );
      default:
        return "📢";
    }
  };

  // Get notification background color based on type
  const getNotificationBg = (type: string) => {
    switch (type) {
      case "LIKE_POST":
      case "LIKE_COMMENT":
        return "#FFD700";
      case "COMMENT":
      case "REPLY_COMMENT":
        return "#007BFF";
      default:
        return "#FF0000";
    }
  };

  const markAllAsRead = async () => {
    try {
      setIsLoadingNotification(true);
      await clearNotifications();
    } catch (error) {
      console.error("Error marking all notifications as read:", error);
    } finally {
      setIsLoadingNotification(false);
    }
  };

  const convertIdImageToImage = async () => {
    try {
      const thumbnail = await viewFile(user?.forumInfo?.thumbnail, "forums");
      setImageUser(thumbnail as string);
    } catch (e) {}
  };

  useEffect(() => {
    if (imageUser) return;
    convertIdImageToImage();
  }, [user]);

  return (
    <header className="bg-[#2E2E8B] text-white">
      <div className="container mx-auto px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4 md:space-x-6 lg:space-x-8">
            {/* Logo */}
            <Link
              href="/"
              className="flex items-center font-['Pacifico'] text-xl"
            >
              <div className="mr-2 flex h-8 w-8 items-center justify-center">
                <Image
                  src="/logo.png"
                  alt="Logo"
                  width={32}
                  height={32}
                  className="h-full w-full rounded-lg object-cover"
                />
              </div>
            </Link>

            {/* Search input */}
            <div className="hidden md:flex items-center relative">
              <div className="relative w-36 md:w-40 lg:w-56">
                <Input
                  type="text"
                  placeholder="Tìm kiếm..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === "Enter" && searchQuery.trim() && eduEcosystemId) {
                      router.push(`/forums/${eduEcosystemId}/search?keyword=${encodeURIComponent(searchQuery.trim())}`);
                      setSearchQuery('')
                    }
                  }}
                  className="pl-10 pr-3 py-2 text-xs md:text-sm rounded-full bg-white/10 text-white placeholder:text-white/70 border-none focus-visible:ring-1 focus-visible:ring-white/30 focus-visible:ring-offset-0"
                />
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-white/70" />
              </div>
            </div>

            {/* Mobile menu button */}
            <button 
              className="md:hidden text-white focus:outline-none" 
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            >
              {isMobileMenuOpen ? (
                <X className="h-6 w-6" />
              ) : (
                <Menu className="h-6 w-6" />
              )}
            </button>

            {/* Main menu - desktop */}
            <nav className="hidden md:flex space-x-2 md:space-x-3 lg:space-x-5">
              {dataMenu.slice(0, -1).map((item, index) => (
                <div key={index} className="group relative">
                  <Link
                    href={item.href}
                    className="text-[11px] md:text-xs lg:text-sm font-medium hover:text-gray-200 whitespace-nowrap tracking-tight"
                    onClick={() => handleSetActive(index)}
                  >
                    {item.name}
                  </Link>
                  {isActive === index &&
                    dataOfMenuChild[index]?.items?.length > 0 && (
                      <div className="absolute left-0 top-full z-[100] mt-2 w-80 rounded-lg bg-white p-4 text-black shadow-lg">
                        <div className="mb-4">
                          <h3 className="mb-2 text-lg font-semibold">
                            {dataOfMenuChild[index].title}
                          </h3>
                          <div className="flex flex-wrap gap-2">
                            {dataOfMenuChild[index].button?.map(
                              (btn, btnIndex) => (
                                <button
                                  key={btnIndex}
                                  className="rounded-full bg-indigo-600 px-3 py-1 text-sm text-white"
                                >
                                  {btn.name}
                                </button>
                              )
                            )}
                          </div>
                        </div>
                        <div className="grid grid-cols-2 gap-4">
                          {dataOfMenuChild[index].items?.map(
                            (item, itemIndex) => (
                              <Link
                                key={itemIndex}
                                href={item.href}
                                className="group/item relative overflow-hidden rounded-lg"
                                onClick={() => handleSetActive(null)}
                              >
                                <Image
                                  src={item.image}
                                  alt={item.name}
                                  width={150}
                                  height={100}
                                  className="h-24 w-full object-cover"
                                />
                                <div className="absolute inset-0 bg-black bg-opacity-50 transition-all group-hover/item:bg-opacity-30" />
                                <span className="absolute inset-0 flex items-center justify-center text-sm font-medium text-white">
                                  {item.name}
                                </span>
                              </Link>
                            )
                          )}
                        </div>
                      </div>
                    )}
                </div>
              ))}
            </nav>
          </div>

          {/* Mobile menu */}
          {isMobileMenuOpen && (
            <div className="md:hidden absolute top-16 left-0 right-0 bg-[#2E2E8B] z-50 py-4 px-4 shadow-lg">
              {/* Mobile search */}
              <div className="flex items-center mb-4">
                <div className="relative flex-1">
                  <Input
                    type="text"
                    placeholder="Tìm kiếm..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === "Enter" && searchQuery.trim() && eduEcosystemId) {
                        router.push(`/forums/${eduEcosystemId}/search?keyword=${encodeURIComponent(searchQuery.trim())}`);
                        setIsMobileMenuOpen(false);
                        setSearchQuery('')
                      }
                    }}
                    className="pl-10 pr-4 py-2 rounded-lg bg-white/10 text-white placeholder:text-white/70 border-none focus-visible:ring-1 focus-visible:ring-white/30 focus-visible:ring-offset-0 w-full"
                  />
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-white/70" />
                </div>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="ml-2 text-white hover:bg-white/10 hover:text-white"
                  onClick={() => {
                    if (searchQuery.trim() && eduEcosystemId) {
                      router.push(`/forums/${eduEcosystemId}/search?keyword=${encodeURIComponent(searchQuery.trim())}`);
                      setIsMobileMenuOpen(false);
                      setSearchQuery('')
                    }
                  }}
                >
                  Tìm
                </Button>
              </div>
              <div className="flex flex-col space-y-4">
                {dataMenu.slice(0, -1).map((item, index) => (
                  <div key={index} className="relative">
                    <Link
                      href={item.href}
                      className="text-sm font-medium hover:text-gray-200 block py-2"
                      onClick={() => {
                        setIsMobileMenuOpen(false);
                      }}
                    >
                      {item.name}
                    </Link>
                    {dataOfMenuChild[index]?.items?.length > 0 && (
                      <div className="pl-4 mt-2 space-y-2 border-l-2 border-white/30">
                        {dataOfMenuChild[index].items?.map((subItem, subIndex) => (
                          <Link
                            key={subIndex}
                            href={subItem.href}
                            className="text-sm text-white/80 hover:text-white block py-1"
                            onClick={() => setIsMobileMenuOpen(false)}
                          >
                            {subItem.name}
                          </Link>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Auth buttons and notifications */}
          <div className="flex items-center ">
            {isAuthenticated ? (
              <>
                <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      className="!hover:bg-none relative h-auto py-0 px-1 md:px-2"
                    >
                      <svg
                        width="24"
                        height="22"
                        viewBox="0 0 24 22"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M19.1054 14.999H20.8816V16.7764H3.11914V14.999H4.89539V8.77832C4.89539 7.48678 5.22104 6.29004 5.87233 5.18809C6.49994 4.12168 7.34662 3.27448 8.41237 2.64648C9.51364 1.99479 10.7096 1.66895 12.0004 1.66895C13.2911 1.66895 14.4871 1.99479 15.5884 2.64648C16.6542 3.27448 17.5008 4.12168 18.1285 5.18809C18.7797 6.29004 19.1054 7.48678 19.1054 8.77832V14.999ZM17.3291 14.999V8.77832C17.3291 7.81855 17.0864 6.92396 16.6009 6.09453C16.1272 5.2888 15.4878 4.64896 14.6825 4.175C13.8536 3.68919 12.9596 3.44629 12.0004 3.44629C11.0412 3.44629 10.1472 3.68919 9.31825 4.175C8.51302 4.64896 7.87357 5.2888 7.3999 6.09453C6.91439 6.92396 6.67164 7.81855 6.67164 8.77832V14.999H17.3291ZM9.33602 18.5537H14.6648V20.3311H9.33602V18.5537Z"
                          fill="white"
                        />
                      </svg>

                      {unreadCount > 0 && (
                        <span className="absolute -right-1 -top-4 flex h-6 w-6 items-center justify-center rounded-full bg-[#EF4444] p-2 text-xs text-white">
                          {unreadCount}
                        </span>
                      )}
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent
                    align="end"
                    className="w-full max-w-[95vw] sm:w-[384px] sm:max-w-full rounded-lg sm:rounded-xl p-2 sm:p-0 right-0"
                  >
                    <div className="relative">
                      <div className="mb-2 flex h-[56px] sm:h-[65px] items-center justify-between border-b border-[#E5E7EB] p-2 sm:p-3">
                        <h3 className="text-sm sm:text-base font-[500] text-[#111827]">Thông báo</h3>
                        <button className="text-xs sm:text-sm font-medium text-[#2E228B] hover:underline" onClick={markAllAsRead}>
                          Đánh dấu tất cả đã đọc
                        </button>
                      </div>
                      {notifications.length === 0 ? (
                        <p className="text-sm text-gray-500 text-center py-4 pt-2">
                          Không có thông báo mới
                        </p>
                      ) : (
                        <div
                          className="max-h-[400px] relative overflow-y-auto"
                          onScroll={handleScroll}
                        >
                          {notifications.map((notification) => (
                            <div
                              key={notification.notificationsId}
                              className={`group relative flex max-h-[84px] cursor-pointer items-start gap-3 border border-b border-transparent border-b-[#E5E7EB] p-4 transition-colors`}
                              onClick={() => {
                                markAsRead(notification.notificationsId);
                              }}
                            >
                              <div
                                className="flex h-[32px] w-[32px] flex-shrink-0 items-center justify-center rounded-full"
                                style={{
                                  background: getNotificationBg(
                                    notification.type
                                  )
                                }}
                              >
                                {getNotificationIcon(notification.type)}
                              </div>
                              <div className="min-w-0 flex-1">
                                <div className="flex items-center justify-between gap-1">
                                  <span
                                    className="line-clamp-1 max-w-[264px] text-sm font-semibold text-gray-900"
                                    dangerouslySetInnerHTML={{
                                      __html:
                                        notification.title ||
                                        notification.content
                                    }}
                                  />
                                  {notification.read && (
                                    <svg
                                      width="18"
                                      height="25"
                                      viewBox="0 0 18 25"
                                      fill="none"
                                      xmlns="http://www.w3.org/2000/svg"
                                    >
                                      <path
                                        d="M7.58794 14.1667L13.7174 8.03339L14.6502 8.98006L7.58794 16.0601L3.35059 11.8067L4.28334 10.8734L7.58794 14.1667Z"
                                        fill="#9CA3AF"
                                      />
                                    </svg>
                                  )}
                                </div>
                                {notification.subContent && (
                                  <div className="mt-0.5 line-clamp-1 text-xs text-gray-500">
                                    {notification.subContent}
                                  </div>
                                )}
                                <div className="mt-1 text-xs text-gray-400">
                                  {formatDate(notification.createdDate)}
                                </div>
                              </div>
                            </div>
                          ))}
                          {isLoading && (
                            <div className="flex justify-center py-2">
                              <div className="h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-gray-600" />
                            </div>
                          )}
                        </div>
                      )}
                      <Link
                        href={`/forums/${eduEcosystemId}/history`}
                        className="sticky block rounded-lg bg-[#2E228B] p-2 text-center font-[500] text-white text-xs sm:text-base"
                        onClick={() => setIsOpen(false)}
                      >
                        Xem tất cả thông báo
                      </Link>
                    </div>
                  </DropdownMenuContent>
                </DropdownMenu>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      className="!hover:bg-none flex h-auto items-center gap-1 py-0 px-1 md:px-2"
                    >
                      <div className="h-[40px] w-[40px] rounded-full object-cover">
                        {imageUser ? (
                          <Image
                            src={imageUser}
                            alt="avatar"
                            width={40}
                            height={40}
                            className="h-full w-full rounded-full object-cover"
                          />
                        ) : (
                          <div className="flex h-[40px] w-[40px] items-center justify-center rounded-full bg-gray-200 text-xl font-bold text-gray-500">
                            {getInitialAvatar()}
                          </div>
                        )}
                      </div>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem asChild>
                      <Link href={`/forums/${eduEcosystemId}/user`}>
                        Trang cá nhân
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => logout()}>
                      Đăng xuất
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </>
            ) : (
              <Link href="/login">
                <Button
                  variant="ghost"
                  className="flex items-center gap-1 text-[11px] md:text-xs lg:text-sm font-medium hover:text-gray-200 px-1 md:px-2"
                >
                  <User className="h-5 w-5" />
                  Đăng nhập
                </Button>
              </Link>
            )}
          </div>
        </div>
      </div>
    </header>
  );
}
