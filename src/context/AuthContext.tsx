"use client";

import React, { createContext, useContext, useState, useEffect, Suspense } from "react";
import { useRouter, usePathname } from "next/navigation";
import { setCookie, getCookie, deleteCookie } from "cookies-next";
import { CookieName } from "@/constants/cookie";

import authServices from "@/services/authServices/authServies";

type User = {
  id: string;
  username: string;
  firstName: string;
  lastName: string;
  avatar?: string;
  forumInfo?: Record<string, string | number>;
  socialInfo?: Record<string, string | number>;
};

type AuthContextType = {
  user: User | null;
  isAuthenticated: boolean;
  loading: boolean;
  login: (userData: User) => void;
  logout: () => void;
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Separate component to use searchParams
import { useSearchParams } from "next/navigation";
import Loading from "@/components/ui/Loading";

const TokenHandler = () => {
  const searchParams = useSearchParams();
  const token = searchParams.get('token');
  const pathname = usePathname();
  const router = useRouter();
  
  useEffect(() => {
    if (pathname === '/' && token) {
      setCookie(CookieName.TOKEN, token, {
        maxAge: 30 * 24 * 60 * 60,
        path: '/'
      });
      router.replace('/');
    }
  }, [token, pathname, router]);
  
  return null;
};

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(!!getCookie(CookieName.USER));
  const [loading, setLoading] = useState(true);
  const router = useRouter();
  const pathname = usePathname();

  const token = getCookie(CookieName.TOKEN);
  
  useEffect(() => {
    const fetchUserInfo = async () => {
      try {
        const [forumInfo, socialInfo] = await Promise.all([
          authServices.getForumUserInfo(),
          authServices.getSocialNetworkUserInfo()
        ]);

        const userData = {
          id: "",
          username: "",
          firstName: "",
          lastName: "",
          forumInfo: forumInfo.data.data,
          socialInfo: socialInfo.data.data
        };

        setUser(userData);
        setCookie(CookieName.USER, JSON.stringify(userData), {
          maxAge: 30 * 24 * 60 * 60,
          path: '/'
        });
      } catch (error) {
        console.error('Error fetching user info:', error);
      }
    };

    const checkAuth = async () => {
      try {
        const tokenCookie = getCookie(CookieName.TOKEN);
        setIsAuthenticated(!!tokenCookie);
        
        const userCookie = getCookie(CookieName.USER);
        if (userCookie) {
          const userData = typeof userCookie === 'string' ? JSON.parse(userCookie) : userCookie;
          setUser(userData);
        } else if (tokenCookie) {
          await fetchUserInfo();
        }
      } catch (error) {
        console.error("Error checking authentication:", error);
        setUser(null);
        setIsAuthenticated(false);
      } finally {
        setLoading(false);
      }
    };

    if (typeof window !== 'undefined') {
      checkAuth();
    }
  }, [pathname, token]);

  const login = (userData: User) => {
    setUser(userData);
    setIsAuthenticated(true);
  };

  const logout = () => {
    setUser(null);
    setIsAuthenticated(false);
    
    deleteCookie(CookieName.USER);
    deleteCookie(CookieName.TOKEN);
    
    router.push("/");
  };

  return (
    <Suspense fallback={
      <div className="flex h-screen w-full items-center justify-center">
        <Loading color="primary" size="lg" variant="spinner" text="Đang tải..." />
      </div>
    }>
      <AuthContext.Provider
        value={{
          user,
          isAuthenticated: !!isAuthenticated,
          loading,
          login,
          logout,
        }}
      >
        <TokenHandler />
        {children}
      </AuthContext.Provider>
    </Suspense>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}; 