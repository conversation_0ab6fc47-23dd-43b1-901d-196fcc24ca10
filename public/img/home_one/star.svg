<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 261.2 76.71">
    <defs>
        <style>
            .cls-1 {
                mask: url(#mask);
                fill: red;
            }

            .cls-10,
            .cls-101,
            .cls-103,
            .cls-105,
            .cls-106,
            .cls-108,
            .cls-109,
            .cls-111,
            .cls-113,
            .cls-115,
            .cls-117,
            .cls-119,
            .cls-12,
            .cls-120,
            .cls-122,
            .cls-123,
            .cls-125,
            .cls-127,
            .cls-129,
            .cls-131,
            .cls-14,
            .cls-16,
            .cls-18,
            .cls-2,
            .cls-20,
            .cls-22,
            .cls-24,
            .cls-26,
            .cls-28,
            .cls-30,
            .cls-32,
            .cls-34,
            .cls-36,
            .cls-38,
            .cls-4,
            .cls-40,
            .cls-42,
            .cls-44,
            .cls-45,
            .cls-47,
            .cls-48,
            .cls-50,
            .cls-52,
            .cls-54,
            .cls-56,
            .cls-58,
            .cls-6,
            .cls-60,
            .cls-62,
            .cls-64,
            .cls-66,
            .cls-68,
            .cls-70,
            .cls-71,
            .cls-73,
            .cls-75,
            .cls-77,
            .cls-79,
            .cls-8,
            .cls-81,
            .cls-83,
            .cls-84,
            .cls-86,
            .cls-87,
            .cls-89,
            .cls-91,
            .cls-93,
            .cls-95,
            .cls-97,
            .cls-99 {
                mix-blend-mode: multiply;
            }

            .cls-2 {
                fill: url(#radial-gradient);
            }

            .cls-3 {
                mask: url(#mask-2);
            }

            .cls-4 {
                fill: url(#radial-gradient-2);
            }

            .cls-5 {
                mask: url(#mask-3);
            }

            .cls-6 {
                fill: url(#radial-gradient-3);
            }

            .cls-7 {
                mask: url(#mask-4);
            }

            .cls-8 {
                fill: url(#radial-gradient-4);
            }

            .cls-9 {
                mask: url(#mask-5);
            }

            .cls-10 {
                fill: url(#radial-gradient-5);
            }

            .cls-11 {
                mask: url(#mask-6);
            }

            .cls-12 {
                fill: url(#radial-gradient-6);
            }

            .cls-13 {
                mask: url(#mask-7);
            }

            .cls-14 {
                fill: url(#radial-gradient-7);
            }

            .cls-15 {
                mask: url(#mask-8);
            }

            .cls-16 {
                fill: url(#radial-gradient-8);
            }

            .cls-17 {
                mask: url(#mask-9);
            }

            .cls-18 {
                fill: url(#radial-gradient-9);
            }

            .cls-19 {
                mask: url(#mask-10);
            }

            .cls-20 {
                fill: url(#radial-gradient-10);
            }

            .cls-21 {
                mask: url(#mask-11);
            }

            .cls-22 {
                fill: url(#radial-gradient-11);
            }

            .cls-23 {
                mask: url(#mask-12);
            }

            .cls-24 {
                fill: url(#radial-gradient-12);
            }

            .cls-25 {
                mask: url(#mask-13);
            }

            .cls-26 {
                fill: url(#radial-gradient-13);
            }

            .cls-27 {
                mask: url(#mask-14);
            }

            .cls-28 {
                fill: url(#radial-gradient-14);
            }

            .cls-29 {
                mask: url(#mask-16);
            }

            .cls-30 {
                fill: url(#radial-gradient-16);
            }

            .cls-31 {
                mask: url(#mask-17);
            }

            .cls-32 {
                fill: url(#radial-gradient-17);
            }

            .cls-33 {
                mask: url(#mask-18);
            }

            .cls-34 {
                fill: url(#radial-gradient-18);
            }

            .cls-35 {
                mask: url(#mask-20);
            }

            .cls-36 {
                fill: url(#radial-gradient-20);
            }

            .cls-37 {
                mask: url(#mask-21);
            }

            .cls-38 {
                fill: url(#radial-gradient-21);
            }

            .cls-39 {
                mask: url(#mask-22);
            }

            .cls-40 {
                fill: url(#radial-gradient-22);
            }

            .cls-41 {
                mask: url(#mask-24);
            }

            .cls-42 {
                fill: url(#radial-gradient-24);
            }

            .cls-43 {
                mask: url(#mask-25);
            }

            .cls-44 {
                fill: url(#radial-gradient-25);
            }

            .cls-45 {
                fill: url(#radial-gradient-26);
            }

            .cls-46 {
                mask: url(#mask-27);
            }

            .cls-47 {
                fill: url(#radial-gradient-27);
            }

            .cls-48 {
                fill: url(#radial-gradient-28);
            }

            .cls-49 {
                mask: url(#mask-29);
            }

            .cls-50 {
                fill: url(#radial-gradient-29);
            }

            .cls-51 {
                mask: url(#mask-30);
            }

            .cls-52 {
                fill: url(#radial-gradient-30);
            }

            .cls-53 {
                mask: url(#mask-31);
            }

            .cls-54 {
                fill: url(#radial-gradient-31);
            }

            .cls-55 {
                mask: url(#mask-32);
            }

            .cls-56 {
                fill: url(#radial-gradient-32);
            }

            .cls-57 {
                mask: url(#mask-33);
            }

            .cls-58 {
                fill: url(#radial-gradient-33);
            }

            .cls-59 {
                mask: url(#mask-34);
            }

            .cls-60 {
                fill: url(#radial-gradient-34);
            }

            .cls-61 {
                mask: url(#mask-35);
            }

            .cls-62 {
                fill: url(#radial-gradient-35);
            }

            .cls-63 {
                mask: url(#mask-36);
            }

            .cls-64 {
                fill: url(#radial-gradient-36);
            }

            .cls-65 {
                mask: url(#mask-37);
            }

            .cls-66 {
                fill: url(#radial-gradient-37);
            }

            .cls-67 {
                mask: url(#mask-38);
            }

            .cls-68 {
                fill: url(#radial-gradient-38);
            }

            .cls-69 {
                mask: url(#mask-39);
            }

            .cls-70 {
                fill: url(#radial-gradient-39);
            }

            .cls-71 {
                fill: url(#radial-gradient-40);
            }

            .cls-72 {
                mask: url(#mask-41);
            }

            .cls-73 {
                fill: url(#radial-gradient-41);
            }

            .cls-74 {
                mask: url(#mask-43);
            }

            .cls-75 {
                fill: url(#radial-gradient-43);
            }

            .cls-76 {
                mask: url(#mask-44);
            }

            .cls-77 {
                fill: url(#radial-gradient-44);
            }

            .cls-78 {
                mask: url(#mask-45);
            }

            .cls-79 {
                fill: url(#radial-gradient-45);
            }

            .cls-80 {
                mask: url(#mask-50);
            }

            .cls-81 {
                fill: url(#radial-gradient-50);
            }

            .cls-82 {
                mask: url(#mask-52);
            }

            .cls-83 {
                fill: url(#radial-gradient-52);
            }

            .cls-84 {
                fill: url(#radial-gradient-53);
            }

            .cls-85 {
                mask: url(#mask-54);
            }

            .cls-86 {
                fill: url(#radial-gradient-54);
            }

            .cls-87 {
                fill: url(#radial-gradient-55);
            }

            .cls-88 {
                mask: url(#mask-56);
            }

            .cls-89 {
                fill: url(#radial-gradient-56);
            }

            .cls-90 {
                mask: url(#mask-58);
            }

            .cls-91 {
                fill: url(#radial-gradient-58);
            }

            .cls-92 {
                mask: url(#mask-59);
            }

            .cls-93 {
                fill: url(#radial-gradient-59);
            }

            .cls-94 {
                mask: url(#mask-60);
            }

            .cls-95 {
                fill: url(#radial-gradient-60);
            }

            .cls-96 {
                mask: url(#mask-61);
            }

            .cls-97 {
                fill: url(#radial-gradient-61);
            }

            .cls-98 {
                mask: url(#mask-63);
            }

            .cls-99 {
                fill: url(#radial-gradient-63);
            }

            .cls-100 {
                mask: url(#mask-64);
            }

            .cls-101 {
                fill: url(#radial-gradient-64);
            }

            .cls-102 {
                mask: url(#mask-65);
            }

            .cls-103 {
                fill: url(#radial-gradient-65);
            }

            .cls-104 {
                mask: url(#mask-67);
            }

            .cls-105 {
                fill: url(#radial-gradient-67);
            }

            .cls-106 {
                fill: url(#radial-gradient-68);
            }

            .cls-107 {
                mask: url(#mask-69);
            }

            .cls-108 {
                fill: url(#radial-gradient-69);
            }

            .cls-109 {
                fill: url(#radial-gradient-70);
            }

            .cls-110 {
                mask: url(#mask-71);
            }

            .cls-111 {
                fill: url(#radial-gradient-71);
            }

            .cls-112 {
                mask: url(#mask-72);
            }

            .cls-113 {
                fill: url(#radial-gradient-72);
            }

            .cls-114 {
                mask: url(#mask-73);
            }

            .cls-115 {
                fill: url(#radial-gradient-73);
            }

            .cls-116 {
                mask: url(#mask-74);
            }

            .cls-117 {
                fill: url(#radial-gradient-74);
            }

            .cls-118 {
                mask: url(#mask-75);
            }

            .cls-119 {
                fill: url(#radial-gradient-75);
            }

            .cls-120 {
                fill: url(#radial-gradient-76);
            }

            .cls-121 {
                mask: url(#mask-77);
            }

            .cls-122 {
                fill: url(#radial-gradient-77);
            }

            .cls-123 {
                fill: url(#radial-gradient-78);
            }

            .cls-124 {
                mask: url(#mask-79);
            }

            .cls-125 {
                fill: url(#radial-gradient-79);
            }

            .cls-126 {
                mask: url(#mask-80);
            }

            .cls-127 {
                fill: url(#radial-gradient-80);
            }

            .cls-128 {
                mask: url(#mask-81);
            }

            .cls-129 {
                fill: url(#radial-gradient-81);
            }

            .cls-130 {
                mask: url(#mask-82);
            }

            .cls-131 {
                fill: url(#radial-gradient-82);
            }

            .cls-132 {
                isolation: isolate;
            }

            .cls-133 {
                mask: url(#mask-83);
            }

            .cls-134 {
                fill: url(#radial-gradient-83);
            }

            .cls-135 {
                mask: url(#mask-84);
            }

            .cls-136 {
                fill: url(#radial-gradient-84);
            }

            .cls-137 {
                mask: url(#mask-85);
            }

            .cls-138 {
                fill: url(#radial-gradient-85);
            }

            .cls-139 {
                mask: url(#mask-86);
            }

            .cls-140 {
                fill: url(#radial-gradient-86);
            }

            .cls-141 {
                mask: url(#mask-87);
            }

            .cls-142 {
                fill: url(#radial-gradient-87);
            }

            .cls-143 {
                mask: url(#mask-88);
            }

            .cls-144 {
                fill: url(#radial-gradient-88);
            }

            .cls-145 {
                mask: url(#mask-89);
            }

            .cls-146 {
                fill: url(#radial-gradient-89);
            }

            .cls-147 {
                mask: url(#mask-90);
            }

            .cls-148 {
                fill: url(#radial-gradient-90);
            }

            .cls-149 {
                mask: url(#mask-91);
            }

            .cls-150 {
                fill: url(#radial-gradient-91);
            }

            .cls-151 {
                mask: url(#mask-92);
            }

            .cls-152 {
                fill: url(#radial-gradient-92);
            }

            .cls-153 {
                mask: url(#mask-94);
            }

            .cls-154 {
                fill: url(#radial-gradient-93);
            }

            .cls-155 {
                mask: url(#mask-96);
            }

            .cls-156 {
                fill: url(#radial-gradient-95);
            }

            .cls-157 {
                mask: url(#mask-97);
            }

            .cls-158 {
                fill: url(#radial-gradient-96);
            }

            .cls-159 {
                mask: url(#mask-98);
            }

            .cls-160 {
                fill: url(#radial-gradient-97);
            }

            .cls-161 {
                fill: url(#radial-gradient-103);
            }

            .cls-162 {
                mask: url(#mask-104);
            }

            .cls-163 {
                fill: url(#radial-gradient-104);
            }

            .cls-164 {
                mask: url(#mask-106);
            }

            .cls-165 {
                fill: url(#radial-gradient-105);
            }

            .cls-166 {
                mask: url(#mask-108);
            }

            .cls-167 {
                fill: url(#radial-gradient-106);
            }

            .cls-168 {
                mask: url(#mask-110);
            }

            .cls-169 {
                fill: url(#radial-gradient-108);
            }

            .cls-170 {
                mask: url(#mask-111);
            }

            .cls-171 {
                fill: url(#radial-gradient-109);
            }

            .cls-172 {
                mask: url(#mask-112);
            }

            .cls-173 {
                fill: url(#radial-gradient-110);
            }

            .cls-174 {
                mask: url(#mask-114);
            }

            .cls-175 {
                fill: url(#radial-gradient-112);
            }

            .cls-176 {
                mask: url(#mask-115);
            }

            .cls-177 {
                fill: url(#radial-gradient-113);
            }

            .cls-178 {
                mask: url(#mask-116);
            }

            .cls-179 {
                fill: url(#radial-gradient-114);
            }

            .cls-180 {
                fill: url(#radial-gradient-116);
            }

            .cls-181 {
                mask: url(#mask-118);
            }

            .cls-182 {
                fill: url(#radial-gradient-117);
            }

            .cls-183 {
                mask: url(#mask-120);
            }

            .cls-184 {
                fill: url(#radial-gradient-118);
            }

            .cls-185 {
                mask: url(#mask-122);
            }

            .cls-186 {
                fill: url(#radial-gradient-119);
            }

            .cls-187 {
                mask: url(#mask-123);
            }

            .cls-188 {
                fill: url(#radial-gradient-120);
            }

            .cls-189 {
                mask: url(#mask-124);
            }

            .cls-190 {
                fill: url(#radial-gradient-121);
            }

            .cls-191 {
                fill: url(#radial-gradient-122);
            }

            .cls-192 {
                mask: url(#mask-125);
            }

            .cls-193 {
                fill: url(#radial-gradient-123);
            }

            .cls-194 {
                mask: url(#mask-127);
            }

            .cls-195 {
                fill: url(#radial-gradient-124);
            }

            .cls-196 {
                mask: url(#mask-129);
            }

            .cls-197 {
                fill: url(#radial-gradient-125);
            }

            .cls-198 {
                mask: url(#mask-130);
            }

            .cls-199 {
                fill: url(#radial-gradient-126);
            }

            .cls-200 {
                mask: url(#mask-131);
            }

            .cls-201 {
                fill: url(#radial-gradient-127);
            }

            .cls-202 {
                mask: url(#mask-132);
            }

            .cls-203 {
                fill: url(#radial-gradient-128);
            }

            .cls-204 {
                mask: url(#mask-133);
            }

            .cls-205 {
                fill: url(#radial-gradient-129);
            }

            .cls-206 {
                mask: url(#mask-134);
            }

            .cls-207 {
                fill: url(#radial-gradient-130);
            }

            .cls-208 {
                mask: url(#mask-135);
            }

            .cls-209 {
                fill: url(#radial-gradient-131);
            }

            .cls-210 {
                mask: url(#mask-136);
            }

            .cls-211 {
                fill: url(#radial-gradient-132);
            }

            .cls-212 {
                mask: url(#mask-137);
            }

            .cls-213 {
                fill: url(#radial-gradient-133);
            }

            .cls-214 {
                mask: url(#mask-138);
            }

            .cls-215 {
                fill: url(#radial-gradient-134);
            }

            .cls-216 {
                mask: url(#mask-139);
            }

            .cls-217 {
                fill: url(#radial-gradient-135);
            }

            .cls-218 {
                mask: url(#mask-140);
            }

            .cls-219 {
                fill: url(#radial-gradient-136);
            }

            .cls-220 {
                mask: url(#mask-141);
            }

            .cls-221 {
                fill: url(#radial-gradient-137);
            }

            .cls-222 {
                mask: url(#mask-142);
            }

            .cls-223 {
                fill: url(#radial-gradient-138);
            }

            .cls-224 {
                mask: url(#mask-143);
            }

            .cls-225 {
                fill: url(#radial-gradient-139);
            }

            .cls-226 {
                mask: url(#mask-145);
            }

            .cls-227 {
                fill: url(#radial-gradient-141);
            }

            .cls-228 {
                mask: url(#mask-146);
            }

            .cls-229 {
                fill: url(#radial-gradient-142);
            }

            .cls-230 {
                mask: url(#mask-147);
            }

            .cls-231 {
                fill: url(#radial-gradient-143);
            }

            .cls-232 {
                mask: url(#mask-149);
            }

            .cls-233 {
                fill: url(#radial-gradient-145);
            }

            .cls-234 {
                mask: url(#mask-150);
            }

            .cls-235 {
                fill: url(#radial-gradient-146);
            }

            .cls-236 {
                mask: url(#mask-151);
            }

            .cls-237 {
                fill: url(#radial-gradient-147);
            }

            .cls-238 {
                fill: url(#radial-gradient-149);
            }

            .cls-239 {
                mask: url(#mask-153);
            }

            .cls-240 {
                fill: url(#radial-gradient-150);
            }

            .cls-241 {
                mask: url(#mask-155);
            }

            .cls-242 {
                fill: url(#radial-gradient-151);
            }

            .cls-243 {
                mask: url(#mask-157);
            }

            .cls-244 {
                fill: url(#radial-gradient-152);
            }

            .cls-245 {
                mask: url(#mask-158);
            }

            .cls-246 {
                fill: url(#radial-gradient-153);
            }

            .cls-247 {
                mask: url(#mask-159);
            }

            .cls-248 {
                fill: url(#radial-gradient-154);
            }

            .cls-249 {
                mask: url(#mask-160);
            }

            .cls-250 {
                fill: url(#radial-gradient-155);
            }

            .cls-251 {
                mask: url(#mask-161);
            }

            .cls-252 {
                fill: url(#radial-gradient-156);
            }

            .cls-253 {
                mask: url(#mask-162);
            }

            .cls-254 {
                fill: url(#radial-gradient-157);
            }

            .cls-255 {
                mask: url(#mask-163);
            }

            .cls-256 {
                fill: url(#radial-gradient-158);
            }

            .cls-257 {
                mask: url(#mask-164);
            }

            .cls-258 {
                fill: url(#radial-gradient-159);
            }

            .cls-259 {
                opacity: 0.7;
            }

            .cls-260 {
                opacity: 0.5;
            }

            .cls-260,
            .cls-261 {
                mix-blend-mode: screen;
            }

            .cls-262 {
                filter: url(#luminosity-noclip-155);
            }

            .cls-263 {
                filter: url(#luminosity-noclip-154);
            }

            .cls-264 {
                filter: url(#luminosity-noclip-153);
            }

            .cls-265 {
                filter: url(#luminosity-noclip-152);
            }

            .cls-266 {
                filter: url(#luminosity-noclip-151);
            }

            .cls-267 {
                filter: url(#luminosity-noclip-150);
            }

            .cls-268 {
                filter: url(#luminosity-noclip-149);
            }

            .cls-269 {
                filter: url(#luminosity-noclip-148);
            }

            .cls-270 {
                filter: url(#luminosity-noclip-144);
            }

            .cls-271 {
                filter: url(#luminosity-noclip-143);
            }

            .cls-272 {
                filter: url(#luminosity-noclip-142);
            }

            .cls-273 {
                filter: url(#luminosity-noclip-140);
            }

            .cls-274 {
                filter: url(#luminosity-noclip-139);
            }

            .cls-275 {
                filter: url(#luminosity-noclip-138);
            }

            .cls-276 {
                filter: url(#luminosity-noclip-136);
            }

            .cls-277 {
                filter: url(#luminosity-noclip-135);
            }

            .cls-278 {
                filter: url(#luminosity-noclip-134);
            }

            .cls-279 {
                filter: url(#luminosity-noclip-133);
            }

            .cls-280 {
                filter: url(#luminosity-noclip-132);
            }

            .cls-281 {
                filter: url(#luminosity-noclip-131);
            }

            .cls-282 {
                filter: url(#luminosity-noclip-130);
            }

            .cls-283 {
                filter: url(#luminosity-noclip-129);
            }

            .cls-284 {
                filter: url(#luminosity-noclip-128);
            }

            .cls-285 {
                filter: url(#luminosity-noclip-127);
            }

            .cls-286 {
                filter: url(#luminosity-noclip-126);
            }

            .cls-287 {
                filter: url(#luminosity-noclip-125);
            }

            .cls-288 {
                filter: url(#luminosity-noclip-124);
            }

            .cls-289 {
                filter: url(#luminosity-noclip-123);
            }

            .cls-290 {
                filter: url(#luminosity-noclip-122);
            }

            .cls-291 {
                filter: url(#luminosity-noclip-119);
            }

            .cls-292 {
                filter: url(#luminosity-noclip-118);
            }

            .cls-293 {
                filter: url(#luminosity-noclip-117);
            }

            .cls-294 {
                filter: url(#luminosity-noclip-113);
            }

            .cls-295 {
                filter: url(#luminosity-noclip-112);
            }

            .cls-296 {
                filter: url(#luminosity-noclip-111);
            }

            .cls-297 {
                filter: url(#luminosity-noclip-109);
            }

            .cls-298 {
                filter: url(#luminosity-noclip-108);
            }

            .cls-299 {
                filter: url(#luminosity-noclip-107);
            }

            .cls-300 {
                filter: url(#luminosity-noclip-105);
            }

            .cls-301 {
                filter: url(#luminosity-noclip-97);
            }

            .cls-302 {
                filter: url(#luminosity-noclip-96);
            }

            .cls-303 {
                filter: url(#luminosity-noclip-95);
            }

            .cls-304 {
                filter: url(#luminosity-noclip-93);
            }

            .cls-305 {
                filter: url(#luminosity-noclip-91);
            }

            .cls-306 {
                filter: url(#luminosity-noclip-90);
            }

            .cls-307 {
                filter: url(#luminosity-noclip-89);
            }

            .cls-308 {
                filter: url(#luminosity-noclip-88);
            }

            .cls-309 {
                filter: url(#luminosity-noclip-87);
            }

            .cls-310 {
                filter: url(#luminosity-noclip-86);
            }

            .cls-311 {
                filter: url(#luminosity-noclip-85);
            }

            .cls-312 {
                filter: url(#luminosity-noclip-84);
            }

            .cls-313 {
                filter: url(#luminosity-noclip-83);
            }

            .cls-314 {
                filter: url(#luminosity-noclip-82);
            }

            .cls-315 {
                filter: url(#luminosity-noclip-81);
            }

            .cls-316 {
                filter: url(#luminosity-noclip-80);
            }

            .cls-317 {
                filter: url(#luminosity-noclip-79);
            }

            .cls-318 {
                filter: url(#luminosity-noclip-77);
            }

            .cls-319 {
                filter: url(#luminosity-noclip-75);
            }

            .cls-320 {
                filter: url(#luminosity-noclip-74);
            }

            .cls-321 {
                filter: url(#luminosity-noclip-73);
            }

            .cls-322 {
                filter: url(#luminosity-noclip-72);
            }

            .cls-323 {
                filter: url(#luminosity-noclip-71);
            }

            .cls-324 {
                filter: url(#luminosity-noclip-69);
            }

            .cls-325 {
                filter: url(#luminosity-noclip-67);
            }

            .cls-326 {
                filter: url(#luminosity-noclip-65);
            }

            .cls-327 {
                filter: url(#luminosity-noclip-64);
            }

            .cls-328 {
                filter: url(#luminosity-noclip-63);
            }

            .cls-329 {
                filter: url(#luminosity-noclip-61);
            }

            .cls-330 {
                filter: url(#luminosity-noclip-60);
            }

            .cls-331 {
                filter: url(#luminosity-noclip-59);
            }

            .cls-332 {
                filter: url(#luminosity-noclip-58);
            }

            .cls-333 {
                filter: url(#luminosity-noclip-56);
            }

            .cls-334 {
                filter: url(#luminosity-noclip-54);
            }

            .cls-335 {
                filter: url(#luminosity-noclip-52);
            }

            .cls-336 {
                filter: url(#luminosity-noclip-50);
            }

            .cls-337 {
                filter: url(#luminosity-noclip-45);
            }

            .cls-338 {
                filter: url(#luminosity-noclip-44);
            }

            .cls-339 {
                filter: url(#luminosity-noclip-43);
            }

            .cls-340 {
                filter: url(#luminosity-noclip-41);
            }

            .cls-341 {
                filter: url(#luminosity-noclip-39);
            }

            .cls-342 {
                filter: url(#luminosity-noclip-38);
            }

            .cls-343 {
                filter: url(#luminosity-noclip-37);
            }

            .cls-344 {
                filter: url(#luminosity-noclip-36);
            }

            .cls-345 {
                filter: url(#luminosity-noclip-35);
            }

            .cls-346 {
                filter: url(#luminosity-noclip-34);
            }

            .cls-347 {
                filter: url(#luminosity-noclip-33);
            }

            .cls-348 {
                filter: url(#luminosity-noclip-32);
            }

            .cls-349 {
                filter: url(#luminosity-noclip-31);
            }

            .cls-350 {
                filter: url(#luminosity-noclip-30);
            }

            .cls-351 {
                filter: url(#luminosity-noclip-29);
            }

            .cls-352 {
                filter: url(#luminosity-noclip-27);
            }

            .cls-353 {
                filter: url(#luminosity-noclip-25);
            }

            .cls-354 {
                filter: url(#luminosity-noclip-24);
            }

            .cls-355 {
                filter: url(#luminosity-noclip-22);
            }

            .cls-356 {
                filter: url(#luminosity-noclip-21);
            }

            .cls-357 {
                filter: url(#luminosity-noclip-20);
            }

            .cls-358 {
                filter: url(#luminosity-noclip-18);
            }

            .cls-359 {
                filter: url(#luminosity-noclip-17);
            }

            .cls-360 {
                filter: url(#luminosity-noclip-16);
            }

            .cls-361 {
                filter: url(#luminosity-noclip-14);
            }

            .cls-362 {
                filter: url(#luminosity-noclip-13);
            }

            .cls-363 {
                filter: url(#luminosity-noclip-12);
            }

            .cls-364 {
                filter: url(#luminosity-noclip-11);
            }

            .cls-365 {
                filter: url(#luminosity-noclip-10);
            }

            .cls-366 {
                filter: url(#luminosity-noclip-9);
            }

            .cls-367 {
                filter: url(#luminosity-noclip-8);
            }

            .cls-368 {
                filter: url(#luminosity-noclip-7);
            }

            .cls-369 {
                filter: url(#luminosity-noclip-6);
            }

            .cls-370 {
                filter: url(#luminosity-noclip-5);
            }

            .cls-371 {
                filter: url(#luminosity-noclip-4);
            }

            .cls-372 {
                filter: url(#luminosity-noclip-3);
            }

            .cls-373 {
                filter: url(#luminosity-noclip-2);
            }

            .cls-374 {
                filter: url(#luminosity-noclip);
            }
        </style>
        <filter id="luminosity-noclip" x="110.01" y="-8242" width="0.34" height="32766" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask" x="110.01" y="-8242" width="0.34" height="32766" maskUnits="userSpaceOnUse">
            <g class="cls-374" />
        </mask>
        <radialGradient id="radial-gradient" cx="-3473.59" cy="23.15" r="0.32" gradientTransform="translate(3583.73)" gradientUnits="userSpaceOnUse">
            <stop offset="0.25" stop-color="#fff" />
            <stop offset="0.35" stop-color="#d1d1d1" />
            <stop offset="0.53" stop-color="#888" />
            <stop offset="0.69" stop-color="#4e4e4e" />
            <stop offset="0.82" stop-color="#232323" />
            <stop offset="0.93" stop-color="#0a0a0a" />
            <stop offset="0.99" />
        </radialGradient>
        <filter id="luminosity-noclip-2" x="180.38" y="-8242" width="0.34" height="32766" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-2" x="180.38" y="-8242" width="0.34" height="32766" maskUnits="userSpaceOnUse">
            <g class="cls-373" />
        </mask>
        <radialGradient id="radial-gradient-2" cx="-3403.22" cy="23.29" r="0.32" xlink:href="#radial-gradient" />
        <filter id="luminosity-noclip-3" x="99.06" y="-8242" width="0.95" height="32766" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-3" x="99.06" y="-8242" width="0.95" height="32766" maskUnits="userSpaceOnUse">
            <g class="cls-372" />
        </mask>
        <radialGradient id="radial-gradient-3" cx="-6863.71" cy="3400.91" r="0.67" gradientTransform="matrix(0, 1, 1, 0, -3301.49, 6885.22)" xlink:href="#radial-gradient" />
        <filter id="luminosity-noclip-4" x="61.93" y="-8242" width="0.95" height="32766" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-4" x="61.93" y="-8242" width="0.95" height="32766" maskUnits="userSpaceOnUse">
            <g class="cls-371" />
        </mask>
        <radialGradient id="radial-gradient-4" cx="-6860.08" cy="3363.78" r="0.66" gradientTransform="matrix(0, 1, 1, 0, -3301.49, 6885.22)" xlink:href="#radial-gradient" />
        <filter id="luminosity-noclip-5" x="52.24" y="-8242" width="1.63" height="32766" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-5" x="52.24" y="-8242" width="1.63" height="32766" maskUnits="userSpaceOnUse">
            <g class="cls-370" />
        </mask>
        <radialGradient id="radial-gradient-5" cx="-6856.03" cy="3354.34" r="1.14" gradientTransform="matrix(0, 1, 1, 0, -3301.49, 6885.22)" xlink:href="#radial-gradient" />
        <filter id="luminosity-noclip-6" x="-71.72" y="-8242" width="0.95" height="32766" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-6" x="-71.72" y="-8242" width="0.95" height="32766" maskUnits="userSpaceOnUse">
            <g class="cls-369" />
        </mask>
        <radialGradient id="radial-gradient-6" cx="-6853.72" cy="224.17" r="0.66" gradientTransform="translate(153.04 6885.22) rotate(90)" xlink:href="#radial-gradient" />
        <filter id="luminosity-noclip-7" x="22.96" y="-8242" width="0.78" height="32766" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-7" x="22.96" y="-8242" width="0.78" height="32766" maskUnits="userSpaceOnUse">
            <g class="cls-368" />
        </mask>
        <radialGradient id="radial-gradient-7" cx="-6839.44" cy="129.6" r="0.54" gradientTransform="translate(153.04 6885.22) rotate(90)" xlink:href="#radial-gradient" />
        <filter id="luminosity-noclip-8" x="82.49" y="-8242" width="0.34" height="32766" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-8" x="82.49" y="-8242" width="0.34" height="32766" maskUnits="userSpaceOnUse">
            <g class="cls-367" />
        </mask>
        <radialGradient id="radial-gradient-8" cx="-3501.11" cy="12.93" r="0.32" xlink:href="#radial-gradient" />
        <filter id="luminosity-noclip-9" x="50.3" y="-8242" width="0.43" height="32766" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-9" x="50.3" y="-8242" width="0.43" height="32766" maskUnits="userSpaceOnUse">
            <g class="cls-366" />
        </mask>
        <radialGradient id="radial-gradient-9" cx="-3533.27" cy="11.18" r="0.4" xlink:href="#radial-gradient" />
        <filter id="luminosity-noclip-10" x="45.69" y="-8242" width="0.43" height="32766" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-10" x="45.69" y="-8242" width="0.43" height="32766" maskUnits="userSpaceOnUse">
            <g class="cls-365" />
        </mask>
        <radialGradient id="radial-gradient-10" cx="-3537.88" cy="12.55" r="0.4" xlink:href="#radial-gradient" />
        <filter id="luminosity-noclip-11" x="42.66" y="-8242" width="0.34" height="32766" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-11" x="42.66" y="-8242" width="0.34" height="32766" maskUnits="userSpaceOnUse">
            <g class="cls-364" />
        </mask>
        <radialGradient id="radial-gradient-11" cx="-3540.94" cy="13.18" r="0.32" xlink:href="#radial-gradient" />
        <filter id="luminosity-noclip-12" x="40.29" y="-8242" width="0.28" height="32766" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-12" x="40.29" y="-8242" width="0.28" height="32766" maskUnits="userSpaceOnUse">
            <g class="cls-363" />
        </mask>
        <radialGradient id="radial-gradient-12" cx="-3543.34" cy="20.16" r="0.26" xlink:href="#radial-gradient" />
        <filter id="luminosity-noclip-13" x="131.3" y="-8242" width="0.34" height="32766" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-13" x="131.3" y="-8242" width="0.34" height="32766" maskUnits="userSpaceOnUse">
            <g class="cls-362" />
        </mask>
        <radialGradient id="radial-gradient-13" cx="-3452.3" cy="39.65" r="0.32" xlink:href="#radial-gradient" />
        <filter id="luminosity-noclip-14" x="21.95" y="-8242" width="0.45" height="32766" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-14" x="21.95" y="-8242" width="0.45" height="32766" maskUnits="userSpaceOnUse">
            <g class="cls-361" />
        </mask>
        <radialGradient id="radial-gradient-14" cx="-3008.65" cy="591.65" r="0.4" gradientTransform="matrix(1, 0, 0, 1, 3030.24, -587.37)" xlink:href="#radial-gradient" />
        <filter id="luminosity-noclip-16" x="18.07" y="-8242" width="0.45" height="32766" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-16" x="18.07" y="-8242" width="0.45" height="32766" maskUnits="userSpaceOnUse">
            <g class="cls-360" />
        </mask>
        <radialGradient id="radial-gradient-16" cx="-3013.27" cy="593.02" r="0.4" gradientTransform="translate(3030.51 -588.24) rotate(-0.09)" xlink:href="#radial-gradient" />
        <filter id="luminosity-noclip-17" x="11.02" y="-8242" width="0.36" height="32766" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-17" x="11.02" y="-8242" width="0.36" height="32766" maskUnits="userSpaceOnUse">
            <g class="cls-359" />
        </mask>
        <radialGradient id="radial-gradient-17" cx="-3023.92" cy="601.72" r="0.32" gradientTransform="translate(3035.23 -579.3) rotate(0.02)" xlink:href="#radial-gradient" />
        <filter id="luminosity-noclip-18" x="11.08" y="-8242" width="0.36" height="32766" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-18" x="11.08" y="-8242" width="0.36" height="32766" maskUnits="userSpaceOnUse">
            <g class="cls-358" />
        </mask>
        <radialGradient id="radial-gradient-18" cx="-3018.93" cy="588" r="0.32" gradientTransform="translate(3028.96 -586.99) rotate(-0.11)" xlink:href="#radial-gradient" />
        <filter id="luminosity-noclip-20" x="109.48" y="-8242" width="0.45" height="32766" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-20" x="109.48" y="-8242" width="0.45" height="32766" maskUnits="userSpaceOnUse">
            <g class="cls-357" />
        </mask>
        <radialGradient id="radial-gradient-20" cx="-2943.58" cy="669.49" r="0.4" gradientTransform="matrix(1, 0, 0, 1, 3051.9, -617.05)" xlink:href="#radial-gradient" />
        <filter id="luminosity-noclip-21" x="22.5" y="-8242" width="0.45" height="32766" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-21" x="22.5" y="-8242" width="0.45" height="32766" maskUnits="userSpaceOnUse">
            <g class="cls-356" />
        </mask>
        <radialGradient id="radial-gradient-21" cx="-3010.81" cy="599.3" r="0.4" gradientTransform="translate(3032.52 -589.2) rotate(-0.08)" xlink:href="#radial-gradient" />
        <filter id="luminosity-noclip-22" x="1.73" y="-8242" width="0.36" height="32766" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-22" x="1.73" y="-8242" width="0.36" height="32766" maskUnits="userSpaceOnUse">
            <g class="cls-355" />
        </mask>
        <radialGradient id="radial-gradient-22" cx="-3033.7" cy="601.5" r="0.32" gradientTransform="translate(3035.13 -578.96) rotate(-0.03)" xlink:href="#radial-gradient" />
        <filter id="luminosity-noclip-24" x="179.25" y="-8242" width="0.28" height="32766" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-24" x="179.25" y="-8242" width="0.28" height="32766" maskUnits="userSpaceOnUse">
            <g class="cls-354" />
        </mask>
        <radialGradient id="radial-gradient-24" cx="-3404.37" cy="14.24" r="0.26" xlink:href="#radial-gradient" />
        <filter id="luminosity-noclip-25" x="29.42" y="-8242" width="2.2" height="32766" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-25" x="29.42" y="-8242" width="2.2" height="32766" maskUnits="userSpaceOnUse">
            <g class="cls-353" />
        </mask>
        <radialGradient id="radial-gradient-25" cx="583.78" cy="592.66" r="1.1" gradientTransform="translate(-553.36 -587.98) rotate(-0.01)" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="gray" />
            <stop offset="0.14" stop-color="#7a7a7a" />
            <stop offset="0.34" stop-color="#686868" />
            <stop offset="0.57" stop-color="#4a4a4a" />
            <stop offset="0.82" stop-color="#212121" />
            <stop offset="1" />
        </radialGradient>
        <radialGradient id="radial-gradient-26" cx="583.78" cy="592.66" r="1.1" gradientTransform="translate(-553.36 -587.98) rotate(-0.01)" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#fff" />
            <stop offset="0.09" stop-color="#f9f9f9" />
            <stop offset="0.22" stop-color="#e7e7e7" />
            <stop offset="0.36" stop-color="#c9c9c9" />
            <stop offset="0.53" stop-color="#a0a0a0" />
            <stop offset="0.7" stop-color="#6b6b6b" />
            <stop offset="0.89" stop-color="#2b2b2b" />
            <stop offset="1" />
        </radialGradient>
        <filter id="luminosity-noclip-27" x="29.94" y="-8242" width="1.15" height="32766" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-27" x="29.94" y="-8242" width="1.15" height="32766" maskUnits="userSpaceOnUse">
            <g class="cls-352" />
        </mask>
        <radialGradient id="radial-gradient-27" cx="583.78" cy="592.67" r="0.58" gradientTransform="translate(-553.39 -587.95) rotate(-0.01)" xlink:href="#radial-gradient-25" />
        <radialGradient id="radial-gradient-28" cx="583.78" cy="592.67" r="0.58" gradientTransform="translate(-553.39 -587.95) rotate(-0.01)" xlink:href="#radial-gradient-26" />
        <filter id="luminosity-noclip-29" x="151.91" y="-8242" width="0.34" height="32766" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-29" x="151.91" y="-8242" width="0.34" height="32766" maskUnits="userSpaceOnUse">
            <g class="cls-351" />
        </mask>
        <radialGradient id="radial-gradient-29" cx="-3431.69" cy="36.24" r="0.32" xlink:href="#radial-gradient" />
        <filter id="luminosity-noclip-30" x="126.67" y="-8242" width="0.34" height="32766" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-30" x="126.67" y="-8242" width="0.34" height="32766" maskUnits="userSpaceOnUse">
            <g class="cls-350" />
        </mask>
        <radialGradient id="radial-gradient-30" cx="-3456.93" cy="37.81" r="0.32" xlink:href="#radial-gradient" />
        <filter id="luminosity-noclip-31" x="135.46" y="-8242" width="0.28" height="32766" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-31" x="135.46" y="-8242" width="0.28" height="32766" maskUnits="userSpaceOnUse">
            <g class="cls-349" />
        </mask>
        <radialGradient id="radial-gradient-31" cx="-3448.17" cy="36.44" r="0.26" xlink:href="#radial-gradient" />
        <filter id="luminosity-noclip-32" x="149.03" y="-8242" width="0.43" height="32766" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-32" x="149.03" y="-8242" width="0.43" height="32766" maskUnits="userSpaceOnUse">
            <g class="cls-348" />
        </mask>
        <radialGradient id="radial-gradient-32" cx="-3434.54" cy="62.29" r="0.4" xlink:href="#radial-gradient" />
        <filter id="luminosity-noclip-33" x="135.03" y="-8242" width="0.34" height="32766" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-33" x="135.03" y="-8242" width="0.34" height="32766" maskUnits="userSpaceOnUse">
            <g class="cls-347" />
        </mask>
        <radialGradient id="radial-gradient-33" cx="-3448.57" cy="77.07" r="0.32" xlink:href="#radial-gradient" />
        <filter id="luminosity-noclip-34" x="143.4" y="-8242" width="0.34" height="32766" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-34" x="143.4" y="-8242" width="0.34" height="32766" maskUnits="userSpaceOnUse">
            <g class="cls-346" />
        </mask>
        <radialGradient id="radial-gradient-34" cx="-3440.2" cy="57.27" r="0.32" xlink:href="#radial-gradient" />
        <filter id="luminosity-noclip-35" x="166.15" y="-8242" width="0.59" height="32766" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-35" x="166.15" y="-8242" width="0.59" height="32766" maskUnits="userSpaceOnUse">
            <g class="cls-345" />
        </mask>
        <radialGradient id="radial-gradient-35" cx="-3417.36" cy="13.88" r="0.54" xlink:href="#radial-gradient" />
        <filter id="luminosity-noclip-36" x="119.37" y="-8242" width="0.34" height="32766" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-36" x="119.37" y="-8242" width="0.34" height="32766" maskUnits="userSpaceOnUse">
            <g class="cls-344" />
        </mask>
        <radialGradient id="radial-gradient-36" cx="-3464.24" cy="17.59" r="0.32" xlink:href="#radial-gradient" />
        <filter id="luminosity-noclip-37" x="169.71" y="-8242" width="0.28" height="32766" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-37" x="169.71" y="-8242" width="0.28" height="32766" maskUnits="userSpaceOnUse">
            <g class="cls-343" />
        </mask>
        <radialGradient id="radial-gradient-37" cx="-3413.91" cy="24.29" r="0.26" xlink:href="#radial-gradient" />
        <filter id="luminosity-noclip-38" x="113.24" y="-8242" width="1.46" height="32766" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-38" x="113.24" y="-8242" width="1.46" height="32766" maskUnits="userSpaceOnUse">
            <g class="cls-342" />
        </mask>
        <radialGradient id="radial-gradient-38" cx="113.97" cy="17.23" r="0.73" gradientTransform="matrix(1, 0, 0, 1, 0, 0)" xlink:href="#radial-gradient-25" />
        <filter id="luminosity-noclip-39" x="189.2" y="-8242" width="0.28" height="32766" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-39" x="189.2" y="-8242" width="0.28" height="32766" maskUnits="userSpaceOnUse">
            <g class="cls-341" />
        </mask>
        <radialGradient id="radial-gradient-39" cx="-3394.43" cy="48.03" r="0.26" xlink:href="#radial-gradient" />
        <radialGradient id="radial-gradient-40" cx="113.97" cy="17.23" r="0.73" gradientTransform="matrix(1, 0, 0, 1, 0, 0)" xlink:href="#radial-gradient-26" />
        <filter id="luminosity-noclip-41" x="42.29" y="-8242" width="0.43" height="32766" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-41" x="42.29" y="-8242" width="0.43" height="32766" maskUnits="userSpaceOnUse">
            <g class="cls-340" />
        </mask>
        <radialGradient id="radial-gradient-41" cx="-3541.28" cy="6.28" r="0.4" xlink:href="#radial-gradient" />
        <filter id="luminosity-noclip-43" x="37.67" y="-8242" width="0.43" height="32766" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-43" x="37.67" y="-8242" width="0.43" height="32766" maskUnits="userSpaceOnUse">
            <g class="cls-339" />
        </mask>
        <radialGradient id="radial-gradient-43" cx="-3545.89" cy="7.65" r="0.4" xlink:href="#radial-gradient" />
        <filter id="luminosity-noclip-44" x="34.65" y="-8242" width="0.34" height="32766" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-44" x="34.65" y="-8242" width="0.34" height="32766" maskUnits="userSpaceOnUse">
            <g class="cls-338" />
        </mask>
        <radialGradient id="radial-gradient-44" cx="-3548.96" cy="8.27" r="0.32" xlink:href="#radial-gradient" />
        <filter id="luminosity-noclip-45" x="32.05" y="-8242" width="0.34" height="32766" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-45" x="32.05" y="-8242" width="0.34" height="32766" maskUnits="userSpaceOnUse">
            <g class="cls-337" />
        </mask>
        <radialGradient id="radial-gradient-45" cx="-3551.55" cy="2.63" r="0.32" xlink:href="#radial-gradient" />
        <filter id="luminosity-noclip-50" x="163.65" y="-8242" width="0.34" height="32766" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-50" x="163.65" y="-8242" width="0.34" height="32766" maskUnits="userSpaceOnUse">
            <g class="cls-336" />
        </mask>
        <radialGradient id="radial-gradient-50" cx="-3419.96" cy="3.84" r="0.32" xlink:href="#radial-gradient" />
        <filter id="luminosity-noclip-52" x="139.97" y="-8242" width="2.2" height="32766" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-52" x="139.97" y="-8242" width="2.2" height="32766" maskUnits="userSpaceOnUse">
            <g class="cls-335" />
        </mask>
        <radialGradient id="radial-gradient-52" cx="141.07" cy="7.17" r="1.1" gradientTransform="matrix(1, 0, 0, 1, 0, 0)" xlink:href="#radial-gradient-25" />
        <radialGradient id="radial-gradient-53" cx="141.07" cy="7.17" r="1.1" gradientTransform="matrix(1, 0, 0, 1, 0, 0)" xlink:href="#radial-gradient-26" />
        <filter id="luminosity-noclip-54" x="140.49" y="-8242" width="1.15" height="32766" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-54" x="140.49" y="-8242" width="1.15" height="32766" maskUnits="userSpaceOnUse">
            <g class="cls-334" />
        </mask>
        <radialGradient id="radial-gradient-54" cx="141.07" cy="7.17" r="0.58" gradientTransform="matrix(1, 0, 0, 1, 0, 0)" xlink:href="#radial-gradient-25" />
        <radialGradient id="radial-gradient-55" cx="141.07" cy="7.17" r="0.58" gradientTransform="matrix(1, 0, 0, 1, 0, 0)" xlink:href="#radial-gradient-26" />
        <filter id="luminosity-noclip-56" x="147.45" y="-8242" width="0.45" height="32766" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-56" x="147.45" y="-8242" width="0.45" height="32766" maskUnits="userSpaceOnUse">
            <g class="cls-333" />
        </mask>
        <radialGradient id="radial-gradient-56" cx="-2893.24" cy="641.6" r="0.4" gradientTransform="translate(3040.85 -626.65) rotate(0.01)" xlink:href="#radial-gradient" />
        <filter id="luminosity-noclip-58" x="143.57" y="-8242" width="0.45" height="32766" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-58" x="143.57" y="-8242" width="0.45" height="32766" maskUnits="userSpaceOnUse">
            <g class="cls-332" />
        </mask>
        <radialGradient id="radial-gradient-58" cx="-2897.85" cy="642.97" r="0.4" gradientTransform="translate(3041.52 -625.44)" xlink:href="#radial-gradient" />
        <filter id="luminosity-noclip-59" x="136.52" y="-8242" width="0.36" height="32766" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-59" x="136.52" y="-8242" width="0.36" height="32766" maskUnits="userSpaceOnUse">
            <g class="cls-331" />
        </mask>
        <radialGradient id="radial-gradient-59" cx="-2908.5" cy="651.67" r="0.32" gradientTransform="matrix(1, 0, 0, 1, 3043.86, -627.79)" xlink:href="#radial-gradient" />
        <filter id="luminosity-noclip-60" x="136.58" y="-8242" width="0.36" height="32766" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-60" x="136.58" y="-8242" width="0.36" height="32766" maskUnits="userSpaceOnUse">
            <g class="cls-330" />
        </mask>
        <radialGradient id="radial-gradient-60" cx="-2903.51" cy="637.95" r="0.32" gradientTransform="translate(3038.89 -628.94) rotate(-0.11)" xlink:href="#radial-gradient" />
        <filter id="luminosity-noclip-61" x="172.43" y="-8242" width="0.28" height="32766" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-61" x="172.43" y="-8242" width="0.28" height="32766" maskUnits="userSpaceOnUse">
            <g class="cls-329" />
        </mask>
        <radialGradient id="radial-gradient-61" cx="-3411.19" cy="2.46" r="0.26" xlink:href="#radial-gradient" />
        <filter id="luminosity-noclip-63" x="152.72" y="-8242" width="0.45" height="32766" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-63" x="152.72" y="-8242" width="0.45" height="32766" maskUnits="userSpaceOnUse">
            <g class="cls-328" />
        </mask>
        <radialGradient id="radial-gradient-63" cx="-2885.25" cy="634.94" r="0.4" gradientTransform="matrix(1, 0, 0, 1, 3037.58, -631.46)" xlink:href="#radial-gradient" />
        <filter id="luminosity-noclip-64" x="148" y="-8242" width="0.45" height="32766" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-64" x="148" y="-8242" width="0.45" height="32766" maskUnits="userSpaceOnUse">
            <g class="cls-327" />
        </mask>
        <radialGradient id="radial-gradient-64" cx="-2895.39" cy="649.25" r="0.4" gradientTransform="translate(3042.79 -629.79) rotate(-0.06)" xlink:href="#radial-gradient" />
        <filter id="luminosity-noclip-65" x="127.23" y="-8242" width="0.36" height="32766" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-65" x="127.23" y="-8242" width="0.36" height="32766" maskUnits="userSpaceOnUse">
            <g class="cls-326" />
        </mask>
        <radialGradient id="radial-gradient-65" cx="-2918.29" cy="651.44" r="0.32" gradientTransform="translate(3047.42 -610.71) rotate(0.16)" xlink:href="#radial-gradient" />
        <filter id="luminosity-noclip-67" x="154.91" y="-8242" width="2.2" height="32766" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-67" x="154.91" y="-8242" width="2.2" height="32766" maskUnits="userSpaceOnUse">
            <g class="cls-325" />
        </mask>
        <radialGradient id="radial-gradient-67" cx="699.2" cy="642.61" r="1.1" gradientTransform="translate(-543.14 -630)" xlink:href="#radial-gradient-25" />
        <radialGradient id="radial-gradient-68" cx="699.2" cy="642.61" r="1.1" gradientTransform="translate(-543.14 -630)" xlink:href="#radial-gradient-26" />
        <filter id="luminosity-noclip-69" x="155.44" y="-8242" width="1.15" height="32766" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-69" x="155.44" y="-8242" width="1.15" height="32766" maskUnits="userSpaceOnUse">
            <g class="cls-324" />
        </mask>
        <radialGradient id="radial-gradient-69" cx="699.2" cy="642.61" r="0.58" gradientTransform="translate(-543.57 -629.53) rotate(-0.04)" xlink:href="#radial-gradient-25" />
        <radialGradient id="radial-gradient-70" cx="699.2" cy="642.61" r="0.58" gradientTransform="translate(-543.57 -629.53) rotate(-0.04)" xlink:href="#radial-gradient-26" />
        <filter id="luminosity-noclip-71" x="146.3" y="-8242" width="0.28" height="32766" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-71" x="146.3" y="-8242" width="0.28" height="32766" maskUnits="userSpaceOnUse">
            <g class="cls-323" />
        </mask>
        <radialGradient id="radial-gradient-71" cx="-2891.49" cy="633.16" r="0.25" gradientTransform="translate(3037.13 -630.18) rotate(-0.06)" xlink:href="#radial-gradient" />
        <filter id="luminosity-noclip-72" x="160.64" y="-8242" width="0.34" height="32766" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-72" x="160.64" y="-8242" width="0.34" height="32766" maskUnits="userSpaceOnUse">
            <g class="cls-322" />
        </mask>
        <radialGradient id="radial-gradient-72" cx="-3422.96" cy="48.47" r="0.32" xlink:href="#radial-gradient" />
        <filter id="luminosity-noclip-73" x="155.43" y="-8242" width="0.36" height="32766" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-73" x="155.43" y="-8242" width="0.36" height="32766" maskUnits="userSpaceOnUse">
            <g class="cls-321" />
        </mask>
        <radialGradient id="radial-gradient-73" cx="-2881.07" cy="631.23" r="0.32" gradientTransform="matrix(1, 0, 0, 1, 3036.5, -630.73)" xlink:href="#radial-gradient" />
        <filter id="luminosity-noclip-74" x="138.92" y="-8242" width="0.28" height="32766" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-74" x="138.92" y="-8242" width="0.28" height="32766" maskUnits="userSpaceOnUse">
            <g class="cls-320" />
        </mask>
        <radialGradient id="radial-gradient-74" cx="-2899.27" cy="632.97" r="0.25" gradientTransform="translate(3037.54 -627.47) rotate(-0.06)" xlink:href="#radial-gradient" />
        <filter id="luminosity-noclip-75" x="166.74" y="-8242" width="7.81" height="32766" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-75" x="166.74" y="-8242" width="7.81" height="32766" maskUnits="userSpaceOnUse">
            <g class="cls-319" />
        </mask>
        <radialGradient id="radial-gradient-75" cx="170.64" cy="40.38" r="3.9" gradientTransform="matrix(1, 0, 0, 1, 0, 0)" xlink:href="#radial-gradient-25" />
        <radialGradient id="radial-gradient-76" cx="170.64" cy="40.38" r="3.9" gradientTransform="matrix(1, 0, 0, 1, 0, 0)" xlink:href="#radial-gradient-26" />
        <filter id="luminosity-noclip-77" x="168.6" y="-8242" width="4.09" height="32766" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-77" x="168.6" y="-8242" width="4.09" height="32766" maskUnits="userSpaceOnUse">
            <g class="cls-318" />
        </mask>
        <radialGradient id="radial-gradient-77" cx="170.64" cy="40.38" r="2.04" gradientTransform="matrix(1, 0, 0, 1, 0, 0)" xlink:href="#radial-gradient-25" />
        <radialGradient id="radial-gradient-78" cx="170.64" cy="40.38" r="2.04" gradientTransform="matrix(1, 0, 0, 1, 0, 0)" xlink:href="#radial-gradient-26" />
        <filter id="luminosity-noclip-79" x="73.19" y="-8242" width="1.21" height="32766" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-79" x="73.19" y="-8242" width="1.21" height="32766" maskUnits="userSpaceOnUse">
            <g class="cls-317" />
        </mask>
        <radialGradient id="radial-gradient-79" cx="-6864.73" cy="3375.14" r="0.84" gradientTransform="matrix(0, 1, 1, 0, -3301.49, 6885.22)" xlink:href="#radial-gradient" />
        <filter id="luminosity-noclip-80" x="76.06" y="-8242" width="1.21" height="32766" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-80" x="76.06" y="-8242" width="1.21" height="32766" maskUnits="userSpaceOnUse">
            <g class="cls-316" />
        </mask>
        <radialGradient id="radial-gradient-80" cx="-6874.38" cy="3378" r="0.84" gradientTransform="matrix(0, 1, 1, 0, -3301.49, 6885.22)" xlink:href="#radial-gradient" />
        <filter id="luminosity-noclip-81" x="77.46" y="-8242" width="0.96" height="32766" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-81" x="77.46" y="-8242" width="0.96" height="32766" maskUnits="userSpaceOnUse">
            <g class="cls-315" />
        </mask>
        <radialGradient id="radial-gradient-81" cx="-6880.79" cy="3379.31" r="0.67" gradientTransform="matrix(0, 1, 1, 0, -3301.49, 6885.22)" xlink:href="#radial-gradient" />
        <filter id="luminosity-noclip-82" x="50.97" y="-8242" width="0.78" height="32766" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-82" x="50.97" y="-8242" width="0.78" height="32766" maskUnits="userSpaceOnUse">
            <g class="cls-314" />
        </mask>
        <radialGradient id="radial-gradient-82" cx="-6837" cy="3352.75" r="0.54" gradientTransform="matrix(0, 1, 1, 0, -3301.49, 6885.22)" xlink:href="#radial-gradient" />
        <filter id="luminosity-noclip-83" x="110.01" y="22.98" width="0.34" height="0.46" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-83" x="181.73" y="22.34" width="0.34" height="0.46" maskUnits="userSpaceOnUse">
            <g class="cls-313">
                <g transform="translate(71.72 -0.64)">
                    <g class="cls-1">
                        <ellipse class="cls-2" cx="110.18" cy="23.21" rx="0.17" ry="0.23" />
                    </g>
                </g>
            </g>
        </mask>
        <radialGradient id="radial-gradient-83" cx="-3473.59" cy="23.15" r="0.32" gradientTransform="translate(3655.45 -0.64)" gradientUnits="userSpaceOnUse">
            <stop offset="0.25" stop-color="#ffe358" />
            <stop offset="0.36" stop-color="#ffe878" />
            <stop offset="0.58" stop-color="#fff2b2" />
            <stop offset="0.76" stop-color="#fff9dc" />
            <stop offset="0.91" stop-color="#fffdf5" />
            <stop offset="0.99" stop-color="#fff" />
        </radialGradient>
        <filter id="luminosity-noclip-84" x="189.2" y="47.89" width="0.28" height="0.37" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-84" x="260.92" y="47.25" width="0.28" height="0.37" maskUnits="userSpaceOnUse">
            <g class="cls-312">
                <g transform="translate(71.72 -0.64)">
                    <g class="cls-69">
                        <ellipse class="cls-70" cx="189.34" cy="48.07" rx="0.14" ry="0.19" />
                    </g>
                </g>
            </g>
        </mask>
        <radialGradient id="radial-gradient-84" cx="-3394.43" cy="48.03" r="0.26" xlink:href="#radial-gradient-83" />
        <filter id="luminosity-noclip-85" x="163.65" y="3.67" width="0.34" height="0.46" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-85" x="235.36" y="3.02" width="0.34" height="0.46" maskUnits="userSpaceOnUse">
            <g class="cls-311">
                <g transform="translate(71.72 -0.64)">
                    <g class="cls-80">
                        <ellipse class="cls-81" cx="163.82" cy="3.89" rx="0.17" ry="0.23" />
                    </g>
                </g>
            </g>
        </mask>
        <radialGradient id="radial-gradient-85" cx="-3419.96" cy="3.84" r="0.32" xlink:href="#radial-gradient-83" />
        <filter id="luminosity-noclip-86" x="172.43" y="2.32" width="0.28" height="0.37" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-86" x="244.15" y="1.68" width="0.28" height="0.37" maskUnits="userSpaceOnUse">
            <g class="cls-310">
                <g transform="translate(71.72 -0.64)">
                    <g class="cls-96">
                        <ellipse class="cls-97" cx="172.57" cy="2.51" rx="0.14" ry="0.19" />
                    </g>
                </g>
            </g>
        </mask>
        <radialGradient id="radial-gradient-86" cx="-3411.19" cy="2.46" r="0.26" xlink:href="#radial-gradient-83" />
        <filter id="luminosity-noclip-87" x="160.64" y="48.3" width="0.34" height="0.46" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-87" x="232.36" y="47.66" width="0.34" height="0.46" maskUnits="userSpaceOnUse">
            <g class="cls-309">
                <g transform="translate(71.72 -0.64)">
                    <g class="cls-112">
                        <ellipse class="cls-113" cx="160.81" cy="48.53" rx="0.17" ry="0.23" />
                    </g>
                </g>
            </g>
        </mask>
        <radialGradient id="radial-gradient-87" cx="-3422.96" cy="48.47" r="0.32" xlink:href="#radial-gradient-83" />
        <filter id="luminosity-noclip-88" x="180.38" y="23.12" width="0.34" height="0.46" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-88" x="252.1" y="22.48" width="0.34" height="0.46" maskUnits="userSpaceOnUse">
            <g class="cls-308">
                <g transform="translate(71.72 -0.64)">
                    <g class="cls-3">
                        <ellipse class="cls-4" cx="180.55" cy="23.35" rx="0.17" ry="0.23" />
                    </g>
                </g>
            </g>
        </mask>
        <radialGradient id="radial-gradient-88" cx="-3403.22" cy="23.29" r="0.32" xlink:href="#radial-gradient-83" />
        <filter id="luminosity-noclip-89" x="131.3" y="39.48" width="0.34" height="0.46" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-89" x="203.02" y="38.84" width="0.34" height="0.46" maskUnits="userSpaceOnUse">
            <g class="cls-307">
                <g transform="translate(71.72 -0.64)">
                    <g class="cls-25">
                        <ellipse class="cls-26" cx="131.48" cy="39.71" rx="0.17" ry="0.23" />
                    </g>
                </g>
            </g>
        </mask>
        <radialGradient id="radial-gradient-89" cx="-3452.3" cy="39.65" r="0.32" xlink:href="#radial-gradient-83" />
        <filter id="luminosity-noclip-90" x="179.25" y="14.1" width="0.28" height="0.37" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-90" x="250.97" y="13.46" width="0.28" height="0.37" maskUnits="userSpaceOnUse">
            <g class="cls-306">
                <g transform="translate(71.72 -0.64)">
                    <g class="cls-41">
                        <ellipse class="cls-42" cx="179.4" cy="14.29" rx="0.14" ry="0.19" />
                    </g>
                </g>
            </g>
        </mask>
        <radialGradient id="radial-gradient-90" cx="-3404.37" cy="14.24" r="0.26" xlink:href="#radial-gradient-83" />
        <filter id="luminosity-noclip-91" x="166.15" y="13.59" width="0.59" height="0.78" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-91" x="237.87" y="12.95" width="0.59" height="0.78" maskUnits="userSpaceOnUse">
            <g class="cls-305">
                <g transform="translate(71.72 -0.64)">
                    <g class="cls-61">
                        <ellipse class="cls-62" cx="166.44" cy="13.98" rx="0.29" ry="0.39" />
                    </g>
                </g>
            </g>
        </mask>
        <radialGradient id="radial-gradient-91" cx="-3417.36" cy="13.88" r="0.54" xlink:href="#radial-gradient-83" />
        <mask id="mask-92" x="184.96" y="15.86" width="1.46" height="1.46" maskUnits="userSpaceOnUse">
            <g transform="translate(71.72 -0.64)">
                <g class="cls-67">
                    <circle class="cls-68" cx="113.97" cy="17.23" r="0.73" />
                </g>
            </g>
        </mask>
        <filter id="luminosity-noclip-92" x="113.24" y="16.5" width="1.46" height="1.46" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <radialGradient id="radial-gradient-92" cx="185.69" cy="16.59" r="0.73" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#fff" />
            <stop offset="1" stop-color="#fff" />
        </radialGradient>
        <filter id="luminosity-noclip-93" x="42.29" y="6.06" width="0.43" height="0.58" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-94" x="114.01" y="5.42" width="0.43" height="0.58" maskUnits="userSpaceOnUse">
            <g class="cls-304">
                <g transform="translate(71.72 -0.64)">
                    <g class="cls-72">
                        <ellipse class="cls-73" cx="42.51" cy="6.35" rx="0.22" ry="0.29" />
                    </g>
                </g>
            </g>
        </mask>
        <radialGradient id="radial-gradient-93" cx="-3541.28" cy="6.28" r="0.4" xlink:href="#radial-gradient-83" />
        <filter id="luminosity-noclip-95" x="37.67" y="7.43" width="0.43" height="0.58" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-96" x="109.39" y="6.79" width="0.43" height="0.58" maskUnits="userSpaceOnUse">
            <g class="cls-303">
                <g transform="translate(71.72 -0.64)">
                    <g class="cls-74">
                        <ellipse class="cls-75" cx="37.89" cy="7.72" rx="0.22" ry="0.29" />
                    </g>
                </g>
            </g>
        </mask>
        <radialGradient id="radial-gradient-95" cx="-3545.89" cy="7.65" r="0.4" xlink:href="#radial-gradient-83" />
        <filter id="luminosity-noclip-96" x="34.65" y="8.1" width="0.34" height="0.46" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-97" x="106.36" y="7.46" width="0.34" height="0.46" maskUnits="userSpaceOnUse">
            <g class="cls-302">
                <g transform="translate(71.72 -0.64)">
                    <g class="cls-76">
                        <ellipse class="cls-77" cx="34.82" cy="8.33" rx="0.17" ry="0.23" />
                    </g>
                </g>
            </g>
        </mask>
        <radialGradient id="radial-gradient-96" cx="-3548.96" cy="8.27" r="0.32" xlink:href="#radial-gradient-83" />
        <filter id="luminosity-noclip-97" x="32.05" y="2.46" width="0.34" height="0.46" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-98" x="103.77" y="1.82" width="0.34" height="0.46" maskUnits="userSpaceOnUse">
            <g class="cls-301">
                <g transform="translate(71.72 -0.64)">
                    <g class="cls-78">
                        <ellipse class="cls-79" cx="32.22" cy="2.69" rx="0.17" ry="0.23" />
                    </g>
                </g>
            </g>
        </mask>
        <radialGradient id="radial-gradient-97" cx="-3551.55" cy="2.63" r="0.32" xlink:href="#radial-gradient-83" />
        <radialGradient id="radial-gradient-103" cx="212.8" cy="6.47" r="0.64" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#fff" />
            <stop offset="0.99" stop-color="#fff" />
        </radialGradient>
        <mask id="mask-104" x="211.69" y="5.43" width="2.2" height="2.2" maskUnits="userSpaceOnUse">
            <g transform="translate(71.72 -0.64)">
                <g class="cls-82">
                    <circle class="cls-83" cx="141.07" cy="7.17" r="1.1" />
                </g>
            </g>
        </mask>
        <filter id="luminosity-noclip-103" x="139.97" y="6.07" width="2.2" height="2.2" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <radialGradient id="radial-gradient-104" cx="212.79" cy="6.53" r="1.1" xlink:href="#radial-gradient-92" />
        <mask id="mask-106" x="212.21" y="5.96" width="1.15" height="1.15" maskUnits="userSpaceOnUse">
            <g transform="translate(71.72 -0.64)">
                <g class="cls-85">
                    <circle class="cls-86" cx="141.07" cy="7.17" r="0.58" />
                </g>
            </g>
        </mask>
        <filter id="luminosity-noclip-104" x="140.49" y="6.6" width="1.15" height="1.15" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <radialGradient id="radial-gradient-105" cx="212.79" cy="6.53" r="0.58" xlink:href="#radial-gradient-92" />
        <filter id="luminosity-noclip-105" x="147.45" y="14.46" width="0.45" height="0.56" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-108" x="219.17" y="13.82" width="0.45" height="0.56" maskUnits="userSpaceOnUse">
            <g class="cls-300">
                <g transform="translate(71.72 -0.64)">
                    <g class="cls-88">
                        <ellipse class="cls-89" cx="147.67" cy="14.74" rx="0.22" ry="0.29" transform="translate(3.69 50.72) rotate(-19.73)" />
                    </g>
                </g>
            </g>
        </mask>
        <radialGradient id="radial-gradient-106" cx="-2893.24" cy="641.6" r="0.4" gradientTransform="translate(3040.85 -626.65) rotate(0.01)" xlink:href="#radial-gradient-83" />
        <filter id="luminosity-noclip-107" x="143.57" y="17.31" width="0.45" height="0.56" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-110" x="215.29" y="16.67" width="0.45" height="0.56" maskUnits="userSpaceOnUse">
            <g class="cls-299">
                <g transform="translate(71.72 -0.64)">
                    <g class="cls-90">
                        <ellipse class="cls-91" cx="143.79" cy="17.59" rx="0.22" ry="0.29" transform="translate(2.5 49.56) rotate(-19.73)" />
                    </g>
                </g>
            </g>
        </mask>
        <radialGradient id="radial-gradient-108" cx="-2897.85" cy="642.97" r="0.4" gradientTransform="translate(3041.52 -625.44)" xlink:href="#radial-gradient-83" />
        <filter id="luminosity-noclip-108" x="136.52" y="29.14" width="0.36" height="0.44" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-111" x="208.23" y="28.5" width="0.36" height="0.44" maskUnits="userSpaceOnUse">
            <g class="cls-298">
                <g transform="translate(71.72 -0.64)">
                    <g class="cls-92">
                        <ellipse class="cls-93" cx="136.69" cy="29.37" rx="0.17" ry="0.23" transform="translate(-1.93 47.6) rotate(-19.62)" />
                    </g>
                </g>
            </g>
        </mask>
        <radialGradient id="radial-gradient-109" cx="-2908.5" cy="651.67" r="0.32" gradientTransform="matrix(1, 0, 0, 1, 3043.86, -627.79)" xlink:href="#radial-gradient-83" />
        <filter id="luminosity-noclip-109" x="136.58" y="14.54" width="0.36" height="0.45" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-112" x="208.3" y="13.9" width="0.36" height="0.45" maskUnits="userSpaceOnUse">
            <g class="cls-297">
                <g transform="translate(71.72 -0.64)">
                    <g class="cls-94">
                        <ellipse class="cls-95" cx="136.76" cy="14.76" rx="0.17" ry="0.23" transform="translate(2.98 46.76) rotate(-19.61)" />
                    </g>
                </g>
            </g>
        </mask>
        <radialGradient id="radial-gradient-110" cx="-2903.51" cy="637.95" r="0.32" gradientTransform="translate(3038.89 -628.94) rotate(-0.11)" xlink:href="#radial-gradient-83" />
        <filter id="luminosity-noclip-111" x="152.72" y="5.49" width="0.45" height="0.56" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-114" x="224.44" y="4.85" width="0.45" height="0.56" maskUnits="userSpaceOnUse">
            <g class="cls-296">
                <g transform="translate(71.72 -0.64)">
                    <g class="cls-98">
                        <ellipse class="cls-99" cx="152.95" cy="5.78" rx="0.22" ry="0.29" transform="translate(6.99 51.85) rotate(-19.68)" />
                    </g>
                </g>
            </g>
        </mask>
        <radialGradient id="radial-gradient-112" cx="-2885.25" cy="634.94" r="0.4" gradientTransform="matrix(1, 0, 0, 1, 3037.58, -631.46)" xlink:href="#radial-gradient-83" />
        <filter id="luminosity-noclip-112" x="148" y="22.39" width="0.45" height="0.56" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-115" x="219.72" y="21.75" width="0.45" height="0.56" maskUnits="userSpaceOnUse">
            <g class="cls-295">
                <g transform="translate(71.72 -0.64)">
                    <g class="cls-100">
                        <ellipse class="cls-101" cx="148.23" cy="22.67" rx="0.22" ry="0.29" transform="matrix(0.94, -0.34, 0.34, 0.94, 1.02, 51.2)" />
                    </g>
                </g>
            </g>
        </mask>
        <radialGradient id="radial-gradient-113" cx="-2895.39" cy="649.25" r="0.4" gradientTransform="translate(3042.79 -629.79) rotate(-0.06)" xlink:href="#radial-gradient-83" />
        <filter id="luminosity-noclip-113" x="127.23" y="32.23" width="0.36" height="0.44" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-116" x="198.95" y="31.59" width="0.36" height="0.44" maskUnits="userSpaceOnUse">
            <g class="cls-294">
                <g transform="translate(71.72 -0.64)">
                    <g class="cls-102">
                        <ellipse class="cls-103" cx="127.41" cy="32.45" rx="0.17" ry="0.23" transform="translate(-3.44 45.28) rotate(-19.89)" />
                    </g>
                </g>
            </g>
        </mask>
        <radialGradient id="radial-gradient-114" cx="-2918.29" cy="651.44" r="0.32" gradientTransform="translate(3047.42 -610.71) rotate(0.16)" xlink:href="#radial-gradient-83" />
        <radialGradient id="radial-gradient-116" cx="699.21" cy="642.56" r="0.64" gradientTransform="translate(-647.3 -356.93) rotate(-19.73)" xlink:href="#radial-gradient-103" />
        <mask id="mask-118" x="226.63" y="10.88" width="2.2" height="2.2" maskUnits="userSpaceOnUse">
            <g transform="translate(71.72 -0.64)">
                <g class="cls-104">
                    <circle class="cls-105" cx="156.01" cy="12.63" r="1.1" transform="translate(4.89 53.4) rotate(-19.73)" />
                </g>
            </g>
        </mask>
        <filter id="luminosity-noclip-115" x="154.91" y="11.53" width="2.2" height="2.2" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <radialGradient id="radial-gradient-117" cx="699.2" cy="642.61" r="1.1" gradientTransform="translate(-543.14 -630)" xlink:href="#radial-gradient-92" />
        <mask id="mask-120" x="227.16" y="11.41" width="1.15" height="1.15" maskUnits="userSpaceOnUse">
            <g transform="translate(71.72 -0.64)">
                <g class="cls-107">
                    <circle class="cls-108" cx="156.01" cy="12.63" r="0.58" transform="translate(4.87 53.3) rotate(-19.69)" />
                </g>
            </g>
        </mask>
        <filter id="luminosity-noclip-116" x="155.44" y="12.05" width="1.15" height="1.15" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <radialGradient id="radial-gradient-118" cx="699.2" cy="642.61" r="0.58" gradientTransform="translate(-543.57 -629.53) rotate(-0.04)" xlink:href="#radial-gradient-92" />
        <filter id="luminosity-noclip-117" x="146.3" y="6.01" width="0.28" height="0.35" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-122" x="218.02" y="5.37" width="0.28" height="0.35" maskUnits="userSpaceOnUse">
            <g class="cls-293">
                <g transform="translate(71.72 -0.64)">
                    <g class="cls-110">
                        <ellipse class="cls-111" cx="146.44" cy="6.19" rx="0.14" ry="0.18" transform="translate(6.46 49.63) rotate(-19.66)" />
                    </g>
                </g>
            </g>
        </mask>
        <radialGradient id="radial-gradient-119" cx="-2891.49" cy="633.16" r="0.25" gradientTransform="translate(3037.13 -630.18) rotate(-0.06)" xlink:href="#radial-gradient-83" />
        <filter id="luminosity-noclip-118" x="155.43" y="0.64" width="0.36" height="0.45" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-123" x="227.15" y="0" width="0.36" height="0.45" maskUnits="userSpaceOnUse">
            <g class="cls-292">
                <g transform="translate(71.72 -0.64)">
                    <g class="cls-114">
                        <ellipse class="cls-115" cx="155.61" cy="0.86" rx="0.17" ry="0.23" transform="translate(8.83 52.55) rotate(-19.72)" />
                    </g>
                </g>
            </g>
        </mask>
        <radialGradient id="radial-gradient-120" cx="-2881.07" cy="631.23" r="0.32" gradientTransform="matrix(1, 0, 0, 1, 3036.5, -630.73)" xlink:href="#radial-gradient-83" />
        <filter id="luminosity-noclip-119" x="138.92" y="8.47" width="0.28" height="0.35" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-124" x="210.63" y="7.82" width="0.28" height="0.35" maskUnits="userSpaceOnUse">
            <g class="cls-291">
                <g transform="translate(71.72 -0.64)">
                    <g class="cls-116">
                        <ellipse class="cls-117" cx="139.06" cy="8.64" rx="0.14" ry="0.18" transform="matrix(0.94, -0.34, 0.34, 0.94, 5.2, 47.3)" />
                    </g>
                </g>
            </g>
        </mask>
        <radialGradient id="radial-gradient-121" cx="-2899.27" cy="632.97" r="0.25" gradientTransform="translate(3037.54 -627.47) rotate(-0.06)" xlink:href="#radial-gradient-83" />
        <radialGradient id="radial-gradient-122" cx="242.39" cy="39.54" r="2.26" xlink:href="#radial-gradient-103" />
        <mask id="mask-125" x="238.46" y="35.84" width="7.81" height="7.81" maskUnits="userSpaceOnUse">
            <g transform="translate(71.72 -0.64)">
                <g class="cls-118">
                    <circle class="cls-119" cx="170.64" cy="40.38" r="3.9" />
                </g>
            </g>
        </mask>
        <filter id="luminosity-noclip-120" x="166.74" y="36.48" width="7.81" height="7.81" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <radialGradient id="radial-gradient-123" cx="242.36" cy="39.74" r="3.9" xlink:href="#radial-gradient-92" />
        <mask id="mask-127" x="240.31" y="37.7" width="4.09" height="4.09" maskUnits="userSpaceOnUse">
            <g transform="translate(71.72 -0.64)">
                <g class="cls-121">
                    <circle class="cls-122" cx="170.64" cy="40.38" r="2.04" />
                </g>
            </g>
        </mask>
        <filter id="luminosity-noclip-121" x="168.6" y="38.34" width="4.09" height="4.09" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <radialGradient id="radial-gradient-124" cx="242.36" cy="39.74" r="2.04" xlink:href="#radial-gradient-92" />
        <filter id="luminosity-noclip-122" x="73.19" y="20.16" width="1.21" height="0.9" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-129" x="144.91" y="19.51" width="1.21" height="0.9" maskUnits="userSpaceOnUse">
            <g class="cls-290">
                <g transform="translate(71.72 -0.64)">
                    <g class="cls-124">
                        <ellipse class="cls-125" cx="73.8" cy="20.61" rx="0.6" ry="0.45" />
                    </g>
                </g>
            </g>
        </mask>
        <radialGradient id="radial-gradient-125" cx="-6864.73" cy="3375.14" r="0.84" gradientTransform="matrix(0, 1, 1, 0, -3229.77, 6884.58)" xlink:href="#radial-gradient-83" />
        <filter id="luminosity-noclip-123" x="76.06" y="10.5" width="1.21" height="0.9" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-130" x="147.78" y="9.86" width="1.21" height="0.9" maskUnits="userSpaceOnUse">
            <g class="cls-289">
                <g transform="translate(71.72 -0.64)">
                    <g class="cls-126">
                        <ellipse class="cls-127" cx="76.66" cy="10.96" rx="0.6" ry="0.45" />
                    </g>
                </g>
            </g>
        </mask>
        <radialGradient id="radial-gradient-126" cx="-6874.38" cy="3378" r="0.84" gradientTransform="matrix(0, 1, 1, 0, -3229.77, 6884.58)" xlink:href="#radial-gradient-83" />
        <filter id="luminosity-noclip-124" x="77.46" y="4.16" width="0.96" height="0.72" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-131" x="149.18" y="3.52" width="0.96" height="0.72" maskUnits="userSpaceOnUse">
            <g class="cls-288">
                <g transform="translate(71.72 -0.64)">
                    <g class="cls-128">
                        <ellipse class="cls-129" cx="77.94" cy="4.52" rx="0.48" ry="0.36" />
                    </g>
                </g>
            </g>
        </mask>
        <radialGradient id="radial-gradient-127" cx="-6880.79" cy="3379.31" r="0.67" gradientTransform="matrix(0, 1, 1, 0, -3229.77, 6884.58)" xlink:href="#radial-gradient-83" />
        <filter id="luminosity-noclip-125" x="50.97" y="48" width="0.78" height="0.59" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-132" x="122.69" y="47.36" width="0.78" height="0.59" maskUnits="userSpaceOnUse">
            <g class="cls-287">
                <g transform="translate(71.72 -0.64)">
                    <g class="cls-130">
                        <ellipse class="cls-131" cx="51.36" cy="48.29" rx="0.39" ry="0.29" />
                    </g>
                </g>
            </g>
        </mask>
        <radialGradient id="radial-gradient-128" cx="-6837" cy="3352.75" r="0.54" gradientTransform="matrix(0, 1, 1, 0, -3229.77, 6884.58)" xlink:href="#radial-gradient-83" />
        <filter id="luminosity-noclip-126" x="99.06" y="21.24" width="0.95" height="0.72" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-133" x="170.78" y="20.6" width="0.95" height="0.72" maskUnits="userSpaceOnUse">
            <g class="cls-286">
                <g transform="translate(71.72 -0.64)">
                    <g class="cls-5">
                        <ellipse class="cls-6" cx="99.53" cy="21.6" rx="0.48" ry="0.36" />
                    </g>
                </g>
            </g>
        </mask>
        <radialGradient id="radial-gradient-129" cx="-6863.71" cy="3400.91" r="0.67" gradientTransform="matrix(0, 1, 1, 0, -3229.77, 6884.58)" xlink:href="#radial-gradient-83" />
        <filter id="luminosity-noclip-127" x="61.93" y="24.87" width="0.95" height="0.72" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-134" x="133.65" y="24.23" width="0.95" height="0.72" maskUnits="userSpaceOnUse">
            <g class="cls-285">
                <g transform="translate(71.72 -0.64)">
                    <g class="cls-7">
                        <ellipse class="cls-8" cx="62.41" cy="25.23" rx="0.48" ry="0.36" />
                    </g>
                </g>
            </g>
        </mask>
        <radialGradient id="radial-gradient-130" cx="-6860.08" cy="3363.78" r="0.66" gradientTransform="matrix(0, 1, 1, 0, -3229.77, 6884.58)" xlink:href="#radial-gradient-83" />
        <filter id="luminosity-noclip-128" x="52.24" y="28.74" width="1.63" height="1.23" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-135" x="123.95" y="28.09" width="1.63" height="1.23" maskUnits="userSpaceOnUse">
            <g class="cls-284">
                <g transform="translate(71.72 -0.64)">
                    <g class="cls-9">
                        <ellipse class="cls-10" cx="53.05" cy="29.35" rx="0.81" ry="0.61" />
                    </g>
                </g>
            </g>
        </mask>
        <radialGradient id="radial-gradient-131" cx="-6856.03" cy="3354.34" r="1.14" gradientTransform="matrix(0, 1, 1, 0, -3229.77, 6884.58)" xlink:href="#radial-gradient-83" />
        <filter id="luminosity-noclip-129" x="-71.72" y="31.24" width="0.95" height="0.72" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-136" x="0" y="30.59" width="0.95" height="0.72" maskUnits="userSpaceOnUse">
            <g class="cls-283">
                <g transform="translate(71.72 -0.64)">
                    <g class="cls-11">
                        <ellipse class="cls-12" cx="-71.24" cy="31.59" rx="0.48" ry="0.36" />
                    </g>
                </g>
            </g>
        </mask>
        <radialGradient id="radial-gradient-132" cx="-6853.72" cy="224.17" r="0.66" gradientTransform="translate(224.76 6884.58) rotate(90)" xlink:href="#radial-gradient-83" />
        <filter id="luminosity-noclip-130" x="22.96" y="45.56" width="0.78" height="0.59" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-137" x="94.68" y="44.92" width="0.78" height="0.59" maskUnits="userSpaceOnUse">
            <g class="cls-282">
                <g transform="translate(71.72 -0.64)">
                    <g class="cls-13">
                        <ellipse class="cls-14" cx="23.35" cy="45.85" rx="0.39" ry="0.29" />
                    </g>
                </g>
            </g>
        </mask>
        <radialGradient id="radial-gradient-133" cx="-6839.44" cy="129.6" r="0.54" gradientTransform="translate(224.76 6884.58) rotate(90)" xlink:href="#radial-gradient-83" />
        <filter id="luminosity-noclip-131" x="82.49" y="12.76" width="0.34" height="0.46" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-138" x="154.21" y="12.12" width="0.34" height="0.46" maskUnits="userSpaceOnUse">
            <g class="cls-281">
                <g transform="translate(71.72 -0.64)">
                    <g class="cls-15">
                        <ellipse class="cls-16" cx="82.66" cy="12.99" rx="0.17" ry="0.23" />
                    </g>
                </g>
            </g>
        </mask>
        <radialGradient id="radial-gradient-134" cx="-3501.11" cy="12.93" r="0.32" xlink:href="#radial-gradient-83" />
        <filter id="luminosity-noclip-132" x="50.3" y="10.96" width="0.43" height="0.58" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-139" x="122.02" y="10.32" width="0.43" height="0.58" maskUnits="userSpaceOnUse">
            <g class="cls-280">
                <g transform="translate(71.72 -0.64)">
                    <g class="cls-17">
                        <ellipse class="cls-18" cx="50.52" cy="11.25" rx="0.22" ry="0.29" />
                    </g>
                </g>
            </g>
        </mask>
        <radialGradient id="radial-gradient-135" cx="-3533.27" cy="11.18" r="0.4" xlink:href="#radial-gradient-83" />
        <filter id="luminosity-noclip-133" x="45.69" y="12.33" width="0.43" height="0.58" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-140" x="117.41" y="11.69" width="0.43" height="0.58" maskUnits="userSpaceOnUse">
            <g class="cls-279">
                <g transform="translate(71.72 -0.64)">
                    <g class="cls-19">
                        <ellipse class="cls-20" cx="45.9" cy="12.62" rx="0.22" ry="0.29" />
                    </g>
                </g>
            </g>
        </mask>
        <radialGradient id="radial-gradient-136" cx="-3537.88" cy="12.55" r="0.4" xlink:href="#radial-gradient-83" />
        <filter id="luminosity-noclip-134" x="42.66" y="13.01" width="0.34" height="0.46" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-141" x="114.38" y="12.36" width="0.34" height="0.46" maskUnits="userSpaceOnUse">
            <g class="cls-278">
                <g transform="translate(71.72 -0.64)">
                    <g class="cls-21">
                        <ellipse class="cls-22" cx="42.83" cy="13.23" rx="0.17" ry="0.23" />
                    </g>
                </g>
            </g>
        </mask>
        <radialGradient id="radial-gradient-137" cx="-3540.94" cy="13.18" r="0.32" xlink:href="#radial-gradient-83" />
        <filter id="luminosity-noclip-135" x="40.29" y="20.02" width="0.28" height="0.37" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-142" x="112.01" y="19.38" width="0.28" height="0.37" maskUnits="userSpaceOnUse">
            <g class="cls-277">
                <g transform="translate(71.72 -0.64)">
                    <g class="cls-23">
                        <ellipse class="cls-24" cx="40.43" cy="20.21" rx="0.14" ry="0.19" />
                    </g>
                </g>
            </g>
        </mask>
        <radialGradient id="radial-gradient-138" cx="-3543.34" cy="20.16" r="0.26" xlink:href="#radial-gradient-83" />
        <filter id="luminosity-noclip-136" x="21.95" y="6.4" width="0.45" height="0.56" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-143" x="93.67" y="5.76" width="0.45" height="0.56" maskUnits="userSpaceOnUse">
            <g class="cls-276">
                <g transform="translate(71.72 -0.64)">
                    <g class="cls-27">
                        <ellipse class="cls-28" cx="22.18" cy="6.68" rx="0.22" ry="0.29" transform="translate(-0.95 7.86) rotate(-19.68)" />
                    </g>
                </g>
            </g>
        </mask>
        <radialGradient id="radial-gradient-139" cx="-3008.65" cy="591.65" r="0.4" gradientTransform="matrix(1, 0, 0, 1, 3030.24, -587.37)" xlink:href="#radial-gradient-83" />
        <filter id="luminosity-noclip-138" x="18.07" y="9.25" width="0.45" height="0.56" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-145" x="89.79" y="8.6" width="0.45" height="0.56" maskUnits="userSpaceOnUse">
            <g class="cls-275">
                <g transform="translate(71.72 -0.64)">
                    <g class="cls-29">
                        <ellipse class="cls-30" cx="18.3" cy="9.53" rx="0.22" ry="0.29" transform="translate(-2.14 6.7) rotate(-19.64)" />
                    </g>
                </g>
            </g>
        </mask>
        <radialGradient id="radial-gradient-141" cx="-3013.27" cy="593.02" r="0.4" gradientTransform="translate(3030.51 -588.24) rotate(-0.09)" xlink:href="#radial-gradient-83" />
        <filter id="luminosity-noclip-139" x="11.02" y="21.08" width="0.36" height="0.45" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-146" x="82.74" y="20.44" width="0.36" height="0.45" maskUnits="userSpaceOnUse">
            <g class="cls-274">
                <g transform="translate(71.72 -0.64)">
                    <g class="cls-31">
                        <ellipse class="cls-32" cx="11.2" cy="21.3" rx="0.17" ry="0.23" transform="translate(-6.54 5.04) rotate(-19.75)" />
                    </g>
                </g>
            </g>
        </mask>
        <radialGradient id="radial-gradient-142" cx="-3023.92" cy="601.72" r="0.32" gradientTransform="translate(3035.23 -579.3) rotate(0.02)" xlink:href="#radial-gradient-83" />
        <filter id="luminosity-noclip-140" x="11.08" y="6.48" width="0.36" height="0.45" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-147" x="82.8" y="5.84" width="0.36" height="0.45" maskUnits="userSpaceOnUse">
            <g class="cls-273">
                <g transform="translate(71.72 -0.64)">
                    <g class="cls-33">
                        <ellipse class="cls-34" cx="11.26" cy="6.7" rx="0.17" ry="0.23" transform="translate(-1.6 4.17) rotate(-19.62)" />
                    </g>
                </g>
            </g>
        </mask>
        <radialGradient id="radial-gradient-143" cx="-3018.93" cy="588" r="0.32" gradientTransform="translate(3028.96 -586.99) rotate(-0.11)" xlink:href="#radial-gradient-83" />
        <filter id="luminosity-noclip-142" x="109.48" y="57.71" width="0.45" height="0.56" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-149" x="181.2" y="57.07" width="0.45" height="0.56" maskUnits="userSpaceOnUse">
            <g class="cls-272">
                <g transform="translate(71.72 -0.64)">
                    <g class="cls-35">
                        <ellipse class="cls-36" cx="109.71" cy="57.99" rx="0.22" ry="0.29" transform="translate(-13.1 40.2) rotate(-19.62)" />
                    </g>
                </g>
            </g>
        </mask>
        <radialGradient id="radial-gradient-145" cx="-2943.58" cy="669.49" r="0.4" gradientTransform="matrix(1, 0, 0, 1, 3051.9, -617.05)" xlink:href="#radial-gradient-83" />
        <filter id="luminosity-noclip-143" x="22.5" y="14.33" width="0.45" height="0.56" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-150" x="94.22" y="13.69" width="0.45" height="0.56" maskUnits="userSpaceOnUse">
            <g class="cls-271">
                <g transform="translate(71.72 -0.64)">
                    <g class="cls-37">
                        <ellipse class="cls-38" cx="22.73" cy="14.61" rx="0.22" ry="0.29" transform="translate(-3.59 8.49) rotate(-19.64)" />
                    </g>
                </g>
            </g>
        </mask>
        <radialGradient id="radial-gradient-146" cx="-3010.81" cy="599.3" r="0.4" gradientTransform="translate(3032.52 -589.2) rotate(-0.08)" xlink:href="#radial-gradient-83" />
        <filter id="luminosity-noclip-144" x="1.73" y="24.17" width="0.36" height="0.44" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-151" x="73.45" y="23.53" width="0.36" height="0.44" maskUnits="userSpaceOnUse">
            <g class="cls-270">
                <g transform="translate(71.72 -0.64)">
                    <g class="cls-39">
                        <ellipse class="cls-40" cx="1.91" cy="24.39" rx="0.17" ry="0.23" transform="translate(-8.11 2.07) rotate(-19.69)" />
                    </g>
                </g>
            </g>
        </mask>
        <radialGradient id="radial-gradient-147" cx="-3033.7" cy="601.5" r="0.32" gradientTransform="translate(3035.13 -578.96) rotate(-0.03)" xlink:href="#radial-gradient-83" />
        <radialGradient id="radial-gradient-149" cx="583.79" cy="592.61" r="0.64" gradientTransform="translate(-647.3 -356.93) rotate(-19.73)" xlink:href="#radial-gradient-103" />
        <mask id="mask-153" x="101.13" y="2.82" width="2.2" height="2.2" maskUnits="userSpaceOnUse">
            <g transform="translate(71.72 -0.64)">
                <g class="cls-43">
                    <circle class="cls-44" cx="30.52" cy="4.56" r="1.1" transform="translate(0.25 10.56) rotate(-19.71)" />
                </g>
            </g>
        </mask>
        <filter id="luminosity-noclip-146" x="29.42" y="3.46" width="2.2" height="2.2" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <radialGradient id="radial-gradient-150" cx="583.78" cy="592.66" r="1.1" gradientTransform="translate(-553.36 -587.98) rotate(-0.01)" xlink:href="#radial-gradient-92" />
        <mask id="mask-155" x="101.66" y="3.35" width="1.15" height="1.15" maskUnits="userSpaceOnUse">
            <g transform="translate(71.72 -0.64)">
                <g class="cls-46">
                    <circle class="cls-47" cx="30.52" cy="4.56" r="0.58" transform="translate(0.25 10.56) rotate(-19.71)" />
                </g>
            </g>
        </mask>
        <filter id="luminosity-noclip-147" x="29.94" y="3.99" width="1.15" height="1.15" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <radialGradient id="radial-gradient-151" cx="583.78" cy="592.67" r="0.58" gradientTransform="translate(-553.39 -587.95) rotate(-0.01)" xlink:href="#radial-gradient-92" />
        <filter id="luminosity-noclip-148" x="151.91" y="36.07" width="0.34" height="0.46" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-157" x="223.63" y="35.43" width="0.34" height="0.46" maskUnits="userSpaceOnUse">
            <g class="cls-269">
                <g transform="translate(71.72 -0.64)">
                    <g class="cls-49">
                        <ellipse class="cls-50" cx="152.08" cy="36.3" rx="0.17" ry="0.23" />
                    </g>
                </g>
            </g>
        </mask>
        <radialGradient id="radial-gradient-152" cx="-3431.69" cy="36.24" r="0.32" xlink:href="#radial-gradient-83" />
        <filter id="luminosity-noclip-149" x="126.67" y="37.64" width="0.34" height="0.46" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-158" x="198.39" y="37" width="0.34" height="0.46" maskUnits="userSpaceOnUse">
            <g class="cls-268">
                <g transform="translate(71.72 -0.64)">
                    <g class="cls-51">
                        <ellipse class="cls-52" cx="126.84" cy="37.87" rx="0.17" ry="0.23" />
                    </g>
                </g>
            </g>
        </mask>
        <radialGradient id="radial-gradient-153" cx="-3456.93" cy="37.81" r="0.32" xlink:href="#radial-gradient-83" />
        <filter id="luminosity-noclip-150" x="135.46" y="36.3" width="0.28" height="0.37" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-159" x="207.17" y="35.66" width="0.28" height="0.37" maskUnits="userSpaceOnUse">
            <g class="cls-267">
                <g transform="translate(71.72 -0.64)">
                    <g class="cls-53">
                        <ellipse class="cls-54" cx="135.6" cy="36.48" rx="0.14" ry="0.19" />
                    </g>
                </g>
            </g>
        </mask>
        <radialGradient id="radial-gradient-154" cx="-3448.17" cy="36.44" r="0.26" xlink:href="#radial-gradient-83" />
        <filter id="luminosity-noclip-151" x="149.03" y="62.07" width="0.43" height="0.58" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-160" x="220.74" y="61.43" width="0.43" height="0.58" maskUnits="userSpaceOnUse">
            <g class="cls-266">
                <g transform="translate(71.72 -0.64)">
                    <g class="cls-55">
                        <ellipse class="cls-56" cx="149.24" cy="62.36" rx="0.22" ry="0.29" />
                    </g>
                </g>
            </g>
        </mask>
        <radialGradient id="radial-gradient-155" cx="-3434.54" cy="62.29" r="0.4" xlink:href="#radial-gradient-83" />
        <filter id="luminosity-noclip-152" x="135.03" y="76.9" width="0.34" height="0.46" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-161" x="206.75" y="76.25" width="0.34" height="0.46" maskUnits="userSpaceOnUse">
            <g class="cls-265">
                <g transform="translate(71.72 -0.64)">
                    <g class="cls-57">
                        <ellipse class="cls-58" cx="135.2" cy="77.12" rx="0.17" ry="0.23" />
                    </g>
                </g>
            </g>
        </mask>
        <radialGradient id="radial-gradient-156" cx="-3448.57" cy="77.07" r="0.32" xlink:href="#radial-gradient-83" />
        <filter id="luminosity-noclip-153" x="143.4" y="57.1" width="0.34" height="0.46" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-162" x="215.12" y="56.45" width="0.34" height="0.46" maskUnits="userSpaceOnUse">
            <g class="cls-264">
                <g transform="translate(71.72 -0.64)">
                    <g class="cls-59">
                        <ellipse class="cls-60" cx="143.57" cy="57.32" rx="0.17" ry="0.23" />
                    </g>
                </g>
            </g>
        </mask>
        <radialGradient id="radial-gradient-157" cx="-3440.2" cy="57.27" r="0.32" xlink:href="#radial-gradient-83" />
        <filter id="luminosity-noclip-154" x="119.37" y="17.42" width="0.34" height="0.46" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-163" x="191.08" y="16.78" width="0.34" height="0.46" maskUnits="userSpaceOnUse">
            <g class="cls-263">
                <g transform="translate(71.72 -0.64)">
                    <g class="cls-63">
                        <ellipse class="cls-64" cx="119.54" cy="17.65" rx="0.17" ry="0.23" />
                    </g>
                </g>
            </g>
        </mask>
        <radialGradient id="radial-gradient-158" cx="-3464.24" cy="17.59" r="0.32" xlink:href="#radial-gradient-83" />
        <filter id="luminosity-noclip-155" x="169.71" y="24.15" width="0.28" height="0.37" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-color="#fff" result="bg" />
            <feBlend in="SourceGraphic" in2="bg" />
        </filter>
        <mask id="mask-164" x="241.43" y="23.51" width="0.28" height="0.37" maskUnits="userSpaceOnUse">
            <g class="cls-262">
                <g transform="translate(71.72 -0.64)">
                    <g class="cls-65">
                        <ellipse class="cls-66" cx="169.85" cy="24.33" rx="0.14" ry="0.19" />
                    </g>
                </g>
            </g>
        </mask>
        <radialGradient id="radial-gradient-159" cx="-3413.91" cy="24.29" r="0.26" xlink:href="#radial-gradient-83" />
        <image id="image" width="9" height="1" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAkAAAABCAYAAAAMwoR9AAAA" />
    </defs>
    <title>star</title>
    <g class="cls-132">
        <g id="Layer_1" data-name="Layer 1">
            <g class="cls-133">
                <ellipse class="cls-134" cx="181.9" cy="22.57" rx="0.17" ry="0.23" />
            </g>
            <g class="cls-135">
                <ellipse class="cls-136" cx="261.06" cy="47.43" rx="0.14" ry="0.19" />
            </g>
            <g class="cls-137">
                <ellipse class="cls-138" cx="235.54" cy="3.25" rx="0.17" ry="0.23" />
            </g>
            <g class="cls-139">
                <ellipse class="cls-140" cx="244.29" cy="1.87" rx="0.14" ry="0.19" />
            </g>
            <g class="cls-141">
                <ellipse class="cls-142" cx="232.53" cy="47.89" rx="0.17" ry="0.23" />
            </g>
            <g class="cls-143">
                <ellipse class="cls-144" cx="252.27" cy="22.71" rx="0.17" ry="0.23" />
            </g>
            <g class="cls-145">
                <ellipse class="cls-146" cx="203.19" cy="39.07" rx="0.17" ry="0.23" />
            </g>
            <g class="cls-147">
                <ellipse class="cls-148" cx="251.11" cy="13.64" rx="0.14" ry="0.19" />
            </g>
            <g class="cls-149">
                <ellipse class="cls-150" cx="238.16" cy="13.34" rx="0.29" ry="0.39" />
            </g>
            <g class="cls-151">
                <g class="cls-151">
                    <circle class="cls-152" cx="185.69" cy="16.59" r="0.73" />
                </g>
            </g>
            <g class="cls-153">
                <ellipse class="cls-154" cx="114.22" cy="5.71" rx="0.22" ry="0.29" />
            </g>
            <g class="cls-153">
                <ellipse class="cls-154" cx="114.22" cy="5.71" rx="0.22" ry="0.29" />
            </g>
            <g class="cls-155">
                <ellipse class="cls-156" cx="109.61" cy="7.08" rx="0.22" ry="0.29" />
            </g>
            <g class="cls-157">
                <ellipse class="cls-158" cx="106.54" cy="7.69" rx="0.17" ry="0.23" />
            </g>
            <g class="cls-159">
                <ellipse class="cls-160" cx="103.94" cy="2.05" rx="0.17" ry="0.23" />
            </g>
            <g class="cls-153">
                <ellipse class="cls-154" cx="114.22" cy="5.71" rx="0.22" ry="0.29" />
            </g>
            <g class="cls-153">
                <ellipse class="cls-154" cx="114.22" cy="5.71" rx="0.22" ry="0.29" />
            </g>
            <g class="cls-155">
                <ellipse class="cls-156" cx="109.61" cy="7.08" rx="0.22" ry="0.29" />
            </g>
            <g class="cls-157">
                <ellipse class="cls-158" cx="106.54" cy="7.69" rx="0.17" ry="0.23" />
            </g>
            <g class="cls-159">
                <ellipse class="cls-160" cx="103.94" cy="2.05" rx="0.17" ry="0.23" />
            </g>
            <polygon class="cls-161" points="212.8 5.85 212.82 6.54 213.45 6.33 212.82 6.55 213.2 7.09 212.8 6.57 212.39 7.09 212.78 6.56 212.15 6.33 212.79 6.53 212.8 5.85" />
            <g class="cls-162">
                <g class="cls-162">
                    <circle class="cls-163" cx="212.79" cy="6.53" r="1.1" />
                </g>
            </g>
            <g class="cls-164">
                <g class="cls-164">
                    <circle class="cls-165" cx="212.79" cy="6.53" r="0.58" />
                </g>
            </g>
            <g class="cls-166">
                <ellipse class="cls-167" cx="147.67" cy="14.74" rx="0.22" ry="0.29" transform="translate(75.41 50.08) rotate(-19.73)" />
            </g>
            <g class="cls-166">
                <ellipse class="cls-167" cx="147.67" cy="14.74" rx="0.22" ry="0.29" transform="translate(75.41 50.08) rotate(-19.73)" />
            </g>
            <g class="cls-168">
                <ellipse class="cls-169" cx="143.79" cy="17.59" rx="0.22" ry="0.29" transform="translate(74.22 48.92) rotate(-19.73)" />
            </g>
            <g class="cls-170">
                <ellipse class="cls-171" cx="136.69" cy="29.37" rx="0.17" ry="0.23" transform="translate(69.79 46.96) rotate(-19.62)" />
            </g>
            <g class="cls-172">
                <ellipse class="cls-173" cx="136.76" cy="14.76" rx="0.17" ry="0.23" transform="translate(74.7 46.12) rotate(-19.61)" />
            </g>
            <g class="cls-166">
                <ellipse class="cls-167" cx="147.67" cy="14.74" rx="0.22" ry="0.29" transform="translate(75.41 50.08) rotate(-19.73)" />
            </g>
            <g class="cls-174">
                <ellipse class="cls-175" cx="152.95" cy="5.78" rx="0.22" ry="0.29" transform="translate(78.71 51.2) rotate(-19.68)" />
            </g>
            <g class="cls-176">
                <ellipse class="cls-177" cx="148.23" cy="22.67" rx="0.22" ry="0.29" transform="matrix(0.94, -0.34, 0.34, 0.94, 72.73, 50.56)" />
            </g>
            <g class="cls-178">
                <ellipse class="cls-179" cx="127.41" cy="32.45" rx="0.17" ry="0.23" transform="translate(68.28 44.64) rotate(-19.89)" />
            </g>
            <g class="cls-172">
                <ellipse class="cls-173" cx="136.76" cy="14.76" rx="0.17" ry="0.23" transform="translate(74.7 46.12) rotate(-19.61)" />
            </g>
            <polygon class="cls-180" points="227.51 11.34 227.76 11.98 228.29 11.57 227.77 12 228.31 12.38 227.75 12.01 227.55 12.65 227.73 12.01 227.06 12.01 227.73 11.98 227.51 11.34" />
            <g class="cls-181">
                <g class="cls-181">
                    <circle class="cls-182" cx="156.01" cy="12.63" r="1.1" transform="translate(76.61 52.76) rotate(-19.73)" />
                </g>
            </g>
            <g class="cls-183">
                <g class="cls-183">
                    <circle class="cls-184" cx="156.01" cy="12.63" r="0.58" transform="translate(76.59 52.66) rotate(-19.69)" />
                </g>
            </g>
            <g class="cls-185">
                <ellipse class="cls-186" cx="146.44" cy="6.19" rx="0.14" ry="0.18" transform="translate(78.17 48.99) rotate(-19.66)" />
            </g>
            <g class="cls-187">
                <ellipse class="cls-188" cx="155.61" cy="0.86" rx="0.17" ry="0.23" transform="translate(80.55 51.91) rotate(-19.72)" />
            </g>
            <g class="cls-189">
                <ellipse class="cls-190" cx="139.06" cy="8.64" rx="0.14" ry="0.18" transform="matrix(0.94, -0.34, 0.34, 0.94, 76.92, 46.66)" />
            </g>
            <polygon class="cls-191" points="242.39 37.34 242.47 39.76 244.71 39.02 242.47 39.82 243.82 41.74 242.39 39.86 240.96 41.74 242.33 39.83 240.07 39.02 242.36 39.74 242.39 37.34" />
            <g class="cls-192">
                <g class="cls-192">
                    <circle class="cls-193" cx="242.36" cy="39.74" r="3.9" />
                </g>
            </g>
            <g class="cls-194">
                <g class="cls-194">
                    <circle class="cls-195" cx="242.36" cy="39.74" r="2.04" />
                </g>
            </g>
            <g class="cls-196">
                <ellipse class="cls-197" cx="145.52" cy="19.97" rx="0.6" ry="0.45" />
            </g>
            <g class="cls-198">
                <ellipse class="cls-199" cx="148.38" cy="10.32" rx="0.6" ry="0.45" />
            </g>
            <g class="cls-200">
                <ellipse class="cls-201" cx="149.66" cy="3.88" rx="0.48" ry="0.36" />
            </g>
            <g class="cls-202">
                <ellipse class="cls-203" cx="123.08" cy="47.65" rx="0.39" ry="0.29" />
            </g>
            <g class="cls-204">
                <ellipse class="cls-205" cx="171.25" cy="20.96" rx="0.48" ry="0.36" />
            </g>
            <g class="cls-206">
                <ellipse class="cls-207" cx="134.13" cy="24.59" rx="0.48" ry="0.36" />
            </g>
            <g class="cls-208">
                <ellipse class="cls-209" cx="124.77" cy="28.71" rx="0.81" ry="0.61" />
            </g>
            <g class="cls-210">
                <ellipse class="cls-211" cx="0.48" cy="30.95" rx="0.48" ry="0.36" />
            </g>
            <g class="cls-212">
                <ellipse class="cls-213" cx="95.07" cy="45.21" rx="0.39" ry="0.29" />
            </g>
            <g class="cls-214">
                <ellipse class="cls-215" cx="154.38" cy="12.35" rx="0.17" ry="0.23" />
            </g>
            <g class="cls-216">
                <ellipse class="cls-217" cx="122.24" cy="10.61" rx="0.22" ry="0.29" />
            </g>
            <g class="cls-218">
                <ellipse class="cls-219" cx="117.62" cy="11.98" rx="0.22" ry="0.29" />
            </g>
            <g class="cls-220">
                <ellipse class="cls-221" cx="114.55" cy="12.59" rx="0.17" ry="0.23" />
            </g>
            <g class="cls-222">
                <ellipse class="cls-223" cx="112.15" cy="19.57" rx="0.14" ry="0.19" />
            </g>
            <g class="cls-224">
                <ellipse class="cls-225" cx="22.18" cy="6.68" rx="0.22" ry="0.29" transform="translate(70.76 7.22) rotate(-19.68)" />
            </g>
            <g class="cls-224">
                <ellipse class="cls-225" cx="22.18" cy="6.68" rx="0.22" ry="0.29" transform="translate(70.76 7.22) rotate(-19.68)" />
            </g>
            <g class="cls-226">
                <ellipse class="cls-227" cx="18.3" cy="9.53" rx="0.22" ry="0.29" transform="translate(69.58 6.06) rotate(-19.64)" />
            </g>
            <g class="cls-228">
                <ellipse class="cls-229" cx="11.2" cy="21.3" rx="0.17" ry="0.23" transform="translate(65.18 4.39) rotate(-19.75)" />
            </g>
            <g class="cls-230">
                <ellipse class="cls-231" cx="11.26" cy="6.7" rx="0.17" ry="0.23" transform="translate(70.12 3.53) rotate(-19.62)" />
            </g>
            <g class="cls-224">
                <ellipse class="cls-225" cx="22.18" cy="6.68" rx="0.22" ry="0.29" transform="translate(70.76 7.22) rotate(-19.68)" />
            </g>
            <g class="cls-232">
                <ellipse class="cls-233" cx="109.71" cy="57.99" rx="0.22" ry="0.29" transform="translate(58.62 39.56) rotate(-19.62)" />
            </g>
            <g class="cls-234">
                <ellipse class="cls-235" cx="22.73" cy="14.61" rx="0.22" ry="0.29" transform="translate(68.13 7.85) rotate(-19.64)" />
            </g>
            <g class="cls-236">
                <ellipse class="cls-237" cx="1.91" cy="24.39" rx="0.17" ry="0.23" transform="translate(63.61 1.43) rotate(-19.69)" />
            </g>
            <g class="cls-230">
                <ellipse class="cls-231" cx="11.26" cy="6.7" rx="0.17" ry="0.23" transform="translate(70.12 3.53) rotate(-19.62)" />
            </g>
            <polygon class="cls-238" points="102.02 3.28 102.27 3.92 102.79 3.51 102.27 3.93 102.81 4.31 102.26 3.95 102.05 4.59 102.23 3.95 101.56 3.95 102.23 3.92 102.02 3.28" />
            <g class="cls-239">
                <g class="cls-239">
                    <circle class="cls-240" cx="30.52" cy="4.56" r="1.1" transform="translate(71.97 9.92) rotate(-19.71)" />
                </g>
            </g>
            <g class="cls-241">
                <g class="cls-241">
                    <circle class="cls-242" cx="30.52" cy="4.56" r="0.58" transform="translate(71.97 9.92) rotate(-19.71)" />
                </g>
            </g>
            <g class="cls-243">
                <ellipse class="cls-244" cx="223.8" cy="35.65" rx="0.17" ry="0.23" />
            </g>
            <g class="cls-245">
                <ellipse class="cls-246" cx="198.56" cy="37.23" rx="0.17" ry="0.23" />
            </g>
            <g class="cls-247">
                <ellipse class="cls-248" cx="207.31" cy="35.84" rx="0.14" ry="0.19" />
            </g>
            <g class="cls-249">
                <ellipse class="cls-250" cx="220.96" cy="61.72" rx="0.22" ry="0.29" />
            </g>
            <g class="cls-251">
                <ellipse class="cls-252" cx="206.92" cy="76.48" rx="0.17" ry="0.23" />
            </g>
            <g class="cls-253">
                <ellipse class="cls-254" cx="215.29" cy="56.68" rx="0.17" ry="0.23" />
            </g>
            <g class="cls-255">
                <ellipse class="cls-256" cx="191.26" cy="17" rx="0.17" ry="0.23" />
            </g>
            <g class="cls-257">
                <ellipse class="cls-258" cx="241.57" cy="23.69" rx="0.14" ry="0.19" />
            </g>
            <g class="cls-259">
                <image class="cls-260" width="7" height="5" transform="translate(233.43 8.6) scale(0.24)" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAFCAYAAAB4ka1VAAAACXBIWXMAAC4jAAAuIwF4pT92AAAAYUlEQVQIW2P4//8/gwwz139jNuH/Xpwy/xkZGP4zADFIHITBRDCX/P+Tkr5gHAhk67EKIhSAVKfyqMMVZPNqYppgwCb0P5FHFYxFmTgwFeizCv0=" />
                <image class="cls-261" width="4" height="4" transform="translate(233.91 8.84) scale(0.24)" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAFCAYAAACNbyblAAAACXBIWXMAAC4jAAAuIwF4pT92AAAAL0lEQVQIWw==" />
                <image class="cls-260" width="1" height="12" transform="translate(234.15 7.88) scale(0.24)" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAANCAYAAABo1wTyAAAACXBIWXMAAC4jAAAu" />
                <use class="cls-260" transform="translate(233.19 9.08) scale(0.24)" xlink:href="#image" />
            </g>
            <g class="cls-259">
                <image class="cls-260" width="7" height="7" transform="translate(231.03 3.08) scale(0.24)" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAYAAADED76LAAAACXBIWXMAAC4jAAAuIwF4pT92AAAAd0lEQVQYV2P4//8/gzIL739LdrH/tuzi/xkYGP6DxGCYgZuR5X8Fv97/a1JB/7eIuf4P5JL/r8UqAFcEAv+zeTX/v5KN+n9S0hfMRjYFbII10HiQBMgkISZ2VAUgAqQA5AZGIJePkRVTAQizMjBhOBBFAS5MUAEAXK+QpHSXBgIAAAAASUVORK5CYA==" />
                <image class="cls-261" width="4" height="4" transform="translate(231.51 3.32) scale(0.24)" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAFCAYAAACNbyblAAAACXBIWXMAAC4jAAAuIwF4pT92AAAALElEQVQIWw==" />
                <image class="cls-260" width="3" height="10" transform="translate(231.51 2.6) scale(0.24)" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAQAAAAKCAYAAACT+/8OAAAACXBIWXMAAC4jAAAuIwF4pT92AAAAS0lEQVQIW2P4//8/AxD8B9FgNjsL2392FlaEgIKQ9H9VEXmEgLGM1n8QhgtYKxr+RzEDJMvCxIwQEOcV/s/N" />
                <use class="cls-260" transform="translate(159.07 4.2) scale(0.24)" xlink:href="#image" />
            </g>
        </g>
    </g>
</svg>