<svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 450.81 345.08">
    <defs>
        <style>
            .cls-1 {
                fill: #abdbe5;
            }

            .cls-2 {
                fill: #474bca;
            }

            .cls-3 {
                fill: #5563ff;
            }

            .cls-4 {
                fill: #f1f3ff;
            }

            .cls-5 {
                fill: #fff;
            }

            .cls-6 {
                fill: none;
                stroke: #fff;
                stroke-miterlimit: 10;
                stroke-width: 5.01px;
            }

            .cls-7 {
                fill: #8f98f8;
            }

            .cls-8 {
                fill: #5a66d4;
            }

            .cls-9 {
                fill: #ffa3be;
            }

            .cls-10 {
                fill: #ffadc8;
            }

            .cls-11 {
                fill: #d4dae9;
            }

            .cls-12 {
                fill: #ff5e85;
            }

            .cls-13 {
                fill: #2c3955;
            }

            .cls-14 {
                fill: #4491ff;
            }

            .cls-15 {
                fill: #ff62a7;
            }

            .cls-16 {
                fill: #bfc4f8;
            }

            .cls-17 {
                fill: #3c81e3;
            }
            .leaf_two,.leaf_one,.left_three,.hand{
                animation: 2s ease-in-out infinite leafMotion;
                -webkit-animation: 2s ease-in-out infinite leafMotion;
                -moz-animation: 2s ease-in-out infinite leafMotion;
            }
            .leaf_two{
                animation-duration: 2.5s;
                -webkit-animation-duration: 2.5s;
                -moz-animation-duration: 2.5s;
                transform-origin: 270px 200px;
            }
            .leaf_one{
                animation-duration: 3s;
                -webkit-animation-duration: 3s;
                -moz-animation-duration: 3s;
                transform-origin: 270px 200px;
            }
            .left_three{
                animation-duration: 3.5s;
                -webkit-animation-duration: 3.5s;
                -moz-animation-duration: 3.5s;
                transform-origin: 270px 200px;
            }
            .hand{
                transform-origin: 270px 66px;
            }
            @keyframes leafMotion {
                50% {
                    transform: rotate(4deg)
                }
            }

            @-webkit-keyframes leafMotion {
                50% {
                    -webkit-transform: rotate(4deg)
                }
            }

            @-moz-keyframes leafMotion {
                50% {
                    -moz-transform: rotate(4deg)
                }
            }
        </style>
    </defs>
    <title>table</title>
    <g class="leaf_two">
        <path id="_Path_" data-name="&lt;Path&gt;" class="cls-1" d="M1783.11,346.92c1-5.79,4.9-9.38,10.57-10.64s3.91-6.15,4.56-11,7.22-7.34,11.65-7.18,6-2.45,7.58-6,7.86-5.5,10.3-2.29-1.09,7.18-3.14,8.56-3.12,4.86-3.12,8.31-1.53,8.19-7.18,8.8-7.66,5.49-7.42,9.17-1.29,9.93-4.5,11.16-9.63,5.45-9.93,12.35-9.36,10.09-9.36,10.09Z" transform="translate(-1437.4 -223.3)" />
        <g id="_Group_" data-name="&lt;Group&gt;">
            <path id="_Path_3" data-name="&lt;Path&gt;" class="cls-2" d="M1783.73,364l-.25-.08c0-.08,2.79-8.53,6.37-12.66a28.87,28.87,0,0,1,4-3.65c2.59-2.06,5-4,6.45-8.07,2.25-6.43,5.9-10.92,10.57-13s7.31-8.27,7.34-8.33l.24.1c0,.06-2.72,6.35-7.47,8.47-4.59,2-8.2,6.49-10.42,12.85-1.45,4.13-3.92,6.1-6.54,8.19a28.64,28.64,0,0,0-4,3.61C1786.5,355.53,1783.75,363.92,1783.73,364Z" transform="translate(-1437.4 -223.3)" />
        </g>
    </g>
    <g class="leaf_one">
        <path id="_Path_2" data-name="&lt;Path&gt;" class="cls-1" d="M1808,352.75c1.68-4.21,6.33-13.91,12.29-13.29s11.16-1.07,12.84-5.2,7.95-5.5,10.39-2.6-.31,7.17-2.9,9.77.15,10.25-3.36,12.84-8.83,3.06-12,6.11-.88,9.5-6.36,12.64-13.6,4.88-15,12.63-12.41,13.62-17.36,11.52l-4.95-2.1V368.15s2.1-5.47,4.95-8.38,6.1-3.6,10.4-3.54S1806.26,357.08,1808,352.75Z" transform="translate(-1437.4 -223.3)" />
        <g id="_Group_2" data-name="&lt;Group&gt;">
            <path id="_Path_4" data-name="&lt;Path&gt;" class="cls-2" d="M1782.42,384.46l-.22-.14c.06-.1,6.48-10.32,12.33-13.76a54,54,0,0,1,6.76-3.07c5.68-2.3,12.12-4.9,15-9.77,4.24-7.29,10.7-11.71,14.94-13.28l.09.25c-4.21,1.56-10.61,5.94-14.81,13.17-2.88,4.95-9.36,7.57-15.09,9.89a53.81,53.81,0,0,0-6.72,3.05C1788.86,374.2,1782.48,384.36,1782.42,384.46Z" transform="translate(-1437.4 -223.3)" />
        </g>
    </g>
    
    
    <g id="_Group_3" data-name="&lt;Group&gt;" class="left_three">
        <path id="_Path_5" data-name="&lt;Path&gt;" class="cls-1" d="M1494.4,457.9s-49.79-25.53-56.53-16.11,61.89,72.52,79.7,58.68S1494.4,457.9,1494.4,457.9Z" transform="translate(-1437.4 -223.3)" />
        <g id="_Group_4" data-name="&lt;Group&gt;">
            <path id="_Path_6" data-name="&lt;Path&gt;" class="cls-3" d="M1516.12,495.73c-6.1-12.89-57.75-41.28-58.28-41.56l.17-.31c.52.29,52.28,28.73,58.43,41.72Z" transform="translate(-1437.4 -223.3)" />
        </g>
        <path id="_Path_7" data-name="&lt;Path&gt;" class="cls-1" d="M1522.39,480.39c-10.49,15-58.81-29-70.1-56.08s-5.43-38.92-2.93-41.17,34.57,14,49.08,32.2C1512.64,433.17,1531.24,467.74,1522.39,480.39Z" transform="translate(-1437.4 -223.3)" />
        <path id="_Path_8" data-name="&lt;Path&gt;" class="cls-1" d="M1494.4,371.7c6.76-2.95,32,30.42,33.54,55.63s-4.12,34.17-14.4,25.24-15.15-20.56-19.14-37.22S1486,375.35,1494.4,371.7Z" transform="translate(-1437.4 -223.3)" />
        <g id="_Group_5" data-name="&lt;Group&gt;">
            <path id="_Path_9" data-name="&lt;Path&gt;" class="cls-3" d="M1518.84,475.47c-21.49-39.35-49.73-59.77-50-60l.21-.29c.28.2,28.58,20.67,50.12,60.09Z" transform="translate(-1437.4 -223.3)" />
        </g>
    </g>
    <path class="cls-4" d="M77.28,61.93h267a6.37,6.37,0,0,1,6.37,6.37v264a0,0,0,0,1,0,0H70.9a0,0,0,0,1,0,0V68.3A6.37,6.37,0,0,1,77.28,61.93Z" />
    <g id="_Group_6" data-name="&lt;Group&gt;">
        <path id="_Path_10" data-name="&lt;Path&gt;" class="cls-5" d="M1756.88,352.89H1539.49a14.76,14.76,0,0,1-14.76-14.76h0a14.76,14.76,0,0,1,14.76-14.76h217.39a14.76,14.76,0,0,1,14.76,14.76h0A14.76,14.76,0,0,1,1756.88,352.89Z" transform="translate(-1437.4 -223.3)" />
        <path id="_Path_11" data-name="&lt;Path&gt;" class="cls-2" d="M1756.59,347.62h0a9.49,9.49,0,0,1-9.49-9.49h0a9.49,9.49,0,0,1,9.49-9.49h0a9.49,9.49,0,0,1,9.49,9.49h0A9.49,9.49,0,0,1,1756.59,347.62Z" transform="translate(-1437.4 -223.3)" />
        <g id="_Group_7" data-name="&lt;Group&gt;">
            <path id="_Path_12" data-name="&lt;Path&gt;" class="cls-4" d="M1606.75,344.85h-64.69a2,2,0,0,1-2-2h0a2,2,0,0,1,2-2h64.69a2,2,0,0,1,2,2h0A2,2,0,0,1,1606.75,344.85Z" transform="translate(-1437.4 -223.3)" />
        </g>
        <g id="_Group_8" data-name="&lt;Group&gt;">
            <circle id="_Path_13" data-name="&lt;Path&gt;" class="cls-6" cx="318.38" cy="114.02" r="3.92" />
            <g id="_Group_9" data-name="&lt;Group&gt;">
                <path id="_Path_14" data-name="&lt;Path&gt;" class="cls-5" d="M1761.32,343.52a.66.66,0,0,1-.47-.19l-2.77-2.77a.66.66,0,0,1,.93-.93l2.77,2.77a.66.66,0,0,1-.47,1.13Z" transform="translate(-1437.4 -223.3)" />
            </g>
        </g>
    </g>
    <path class="cls-7" d="M1659,422.66h-30.35a5.49,5.49,0,0,1-5.49-5.49h0a5.49,5.49,0,0,1,5.49-5.49H1659a5.49,5.49,0,0,1,5.49,5.49h0A5.49,5.49,0,0,1,1659,422.66Z" transform="translate(-1437.4 -223.3)" />
    <path class="cls-8" d="M1508.3,307.34h279.76V291.61a6.37,6.37,0,0,0-6.37-6.37h-267a6.37,6.37,0,0,0-6.37,6.37Z" transform="translate(-1437.4 -223.3)" />
    <circle class="cls-9" cx="100.43" cy="72.98" r="4.69" />
    <g id="_Group_10" data-name="&lt;Group&gt;">
        <path id="_Path_15" data-name="&lt;Path&gt;" class="cls-10" d="M1786.1,431.85a3.44,3.44,0,0,0,.26-3c-.59-1.87,0-4-.31-4.18s-1,0-1.7,2.41-2.94-1.56-3.29-2.25-2.51-2-3-.62.77,5.4,1.41,6.56,4.11,2.88,4.11,2.88Z" transform="translate(-1437.4 -223.3)" />
        <path id="_Path_16" data-name="&lt;Path&gt;" class="cls-11" d="M1787.21,432.38l-.61-1.44a12,12,0,0,0-5.13,2l.51,2Z" transform="translate(-1437.4 -223.3)" />
        <path id="_Path_17" data-name="&lt;Path&gt;" class="cls-12" d="M1807.56,435.8c-5.24-1.21-10.76,6.36-13.33,6.75s-6.75-10.58-6.75-10.58-4.66,1.3-6.62,2.64l6,16.8,13.36,2.33Z" transform="translate(-1437.4 -223.3)" />
        <path id="_Path_18" data-name="&lt;Path&gt;" class="cls-10" d="M1800.61,434.33a9.12,9.12,0,0,1,1.09,3.36c.2,1.88,7.09.09,7.09.09s-1.18-5.72-.68-6.61S1800.61,434.33,1800.61,434.33Z" transform="translate(-1437.4 -223.3)" />
        <path id="_Path_19" data-name="&lt;Path&gt;" class="cls-11" d="M1809.48,433.89a14.1,14.1,0,0,0-9.26,3.3l-.56,4.08,10.79-3.7Z" transform="translate(-1437.4 -223.3)" />
        <path id="_Path_20" data-name="&lt;Path&gt;" class="cls-10" d="M1803.41,417.26c4.49.59,7.71.12,7.83,8.31s-12.39,10.09-13.86,9.35-.86-7.33-.73-11.12S1799.83,416.79,1803.41,417.26Z" transform="translate(-1437.4 -223.3)" />
        <path id="_Path_21" data-name="&lt;Path&gt;" class="cls-10" d="M1804.47,425.39s1.47-2,3.06-.61-.37,5.87-2.44,5.87Z" transform="translate(-1437.4 -223.3)" />
        <path id="_Path_22" data-name="&lt;Path&gt;" class="cls-13" d="M1812.51,420.1a3.53,3.53,0,0,0-3.41-4.26c-4.17-.47-7.3-3-10.61-1.61s-3.28,6.23-1.45,7.48,6.8,1,6.8,1l.64,2.73s.56,5,3.35,7.34c0,0,2.77.27,4.29-4A16.56,16.56,0,0,0,1812.51,420.1Z" transform="translate(-1437.4 -223.3)" />
        <path id="_Path_23" data-name="&lt;Path&gt;" class="cls-2" d="M1781.67,509.38s.83,4,0,6.39a37.74,37.74,0,0,1-6.2,1.37c-2.19.14-8.2,2.33-10,2.33s-3.25-1.7,1.56-4a16.38,16.38,0,0,0,6.86-6.06l1-1.58Z" transform="translate(-1437.4 -223.3)" />
        <path id="_Path_24" data-name="&lt;Path&gt;" class="cls-14" d="M1815.88,471s2.87,9.68-1.74,12.66-24.11,2.15-24.11,2.15l-7.43,24.59s-10.37,0-11.76-5.61c0,0,5.42-26.11,8.2-28.55S1815.88,471,1815.88,471Z" transform="translate(-1437.4 -223.3)" />
        <path id="_Path_25" data-name="&lt;Path&gt;" class="cls-12" d="M1809.92,435.58s-10.65-.62-13.66,12.79-5.15,23.22-2,28.27,4.08,6.77,9.13,5.42,15.45-6.69,15.55-10.09-3.3-10.88-3.21-16.32S1816.65,435.85,1809.92,435.58Z" transform="translate(-1437.4 -223.3)" />
        <path id="_Path_26" data-name="&lt;Path&gt;" class="cls-10" d="M1776.3,436.39a3.83,3.83,0,0,0-.78-3.26c-1.27-1.77-1.4-4.23-1.79-4.31s-1,.37-1,3.14-3.65-.62-4.26-1.23-3.35-1.22-3.39.39,2.7,5.44,3.78,6.44,5.35,1.61,5.35,1.61Z" transform="translate(-1437.4 -223.3)" />
        <path id="_Path_27" data-name="&lt;Path&gt;" class="cls-11" d="M1777.69,436.89l-1.16-1.09a8.36,8.36,0,0,0-4.61,4.22l2.21,2.52Z" transform="translate(-1437.4 -223.3)" />
        <path id="_Path_28" data-name="&lt;Path&gt;" class="cls-12" d="M1808.71,438.17c7.43,6.21-8.23,23.23-14,23.58s-23.31-18.69-23.31-18.69,3.35-4.57,6.17-6.67c0,0,11.52,11.6,15.08,11.79S1802,432.6,1808.71,438.17Z" transform="translate(-1437.4 -223.3)" />
    </g>
    <g id="_Group_11" data-name="&lt;Group&gt;">
        <path id="_Compound_Path_" data-name="&lt;Compound Path&gt;" class="cls-2" d="M1765.57,568.32a1.43,1.43,0,0,0,1.77-1l4.08-14h22.43L1790,566.55a1.43,1.43,0,1,0,2.75.8l13.7-47a1.43,1.43,0,0,0,.5-1.72l4.33-14.85a1.37,1.37,0,0,0,.35-1.2l4.77-16.35a1.43,1.43,0,0,0-1.37-1.83h-25.41a1.43,1.43,0,0,0-1.37,1l-23.66,81.1A1.43,1.43,0,0,0,1765.57,568.32Zm33.89-34.18H1777l3.94-13.5h22.43Zm-13.71-29.86h22.43l-3.94,13.5h-22.43Zm27.39-17L1809,501.42h-22.43l4.12-14.14Zm-40.87,63.22,3.94-13.5h22.43l-3.94,13.5Z" transform="translate(-1437.4 -223.3)" />
        <path id="_Compound_Path_2" data-name="&lt;Compound Path&gt;" class="cls-2" d="M1788.43,486.25l14.33,49.1a1.34,1.34,0,0,0,.34,1.16l9,30.84a1.43,1.43,0,0,0,2.75-.8L1811,553.37h22.43l4.08,14a1.43,1.43,0,1,0,2.75-.8l-4.18-14.32a1.42,1.42,0,0,0-.38-1.29l-9.33-32a1.42,1.42,0,0,0-.07-.24l-4.54-15.55a1.42,1.42,0,0,0-.38-1.31l-4.78-16.39a1.43,1.43,0,0,0-1.37-1H1789.8a1.43,1.43,0,0,0-1.37,1.83Zm8.24,18h22.43l3.94,13.5h-22.43Zm4.77,16.36h22.43l3.94,13.5h-22.43Zm8.71,29.86-3.94-13.5h22.43l3.94,13.5Zm-18.44-63.22h22.43l4.12,14.14h-22.43Z" transform="translate(-1437.4 -223.3)" />
    </g>
    <path class="cls-7" d="M1640.9,403.31h-57.07a5.49,5.49,0,0,1-5.49-5.49h0a5.49,5.49,0,0,1,5.49-5.49h57.07a5.49,5.49,0,0,1,5.49,5.49h0A5.49,5.49,0,0,1,1640.9,403.31Z" transform="translate(-1437.4 -223.3)" />
    <path class="cls-7" d="M1614.18,422.66h-30.35a5.49,5.49,0,0,1-5.49-5.49h0a5.49,5.49,0,0,1,5.49-5.49h30.35a5.49,5.49,0,0,1,5.49,5.49h0A5.49,5.49,0,0,1,1614.18,422.66Z" transform="translate(-1437.4 -223.3)" />
    <circle class="cls-15" cx="84.17" cy="72.98" r="4.69" />
    <circle class="cls-16" cx="117.42" cy="72.98" r="4.69" />
    <path class="cls-2" d="M1662.42,384.15h-78.59a5.49,5.49,0,0,1-5.49-5.49h0a5.49,5.49,0,0,1,5.49-5.49h78.59a5.49,5.49,0,0,1,5.49,5.49h0A5.49,5.49,0,0,1,1662.42,384.15Z" transform="translate(-1437.4 -223.3)" />
    <path class="cls-7" d="M1659,511.9h-30.35a5.49,5.49,0,0,1-5.49-5.49h0a5.49,5.49,0,0,1,5.49-5.49H1659a5.49,5.49,0,0,1,5.49,5.49h0A5.49,5.49,0,0,1,1659,511.9Z" transform="translate(-1437.4 -223.3)" />
    <rect class="cls-7" x="140.94" y="258.26" width="68.06" height="10.99" rx="5.49" ry="5.49" />
    <path class="cls-7" d="M1614.18,511.9h-30.35a5.49,5.49,0,0,1-5.49-5.49h0a5.49,5.49,0,0,1,5.49-5.49h30.35a5.49,5.49,0,0,1,5.49,5.49h0A5.49,5.49,0,0,1,1614.18,511.9Z" transform="translate(-1437.4 -223.3)" />
    <path class="cls-2" d="M1662.42,473.39h-78.59a5.49,5.49,0,0,1-5.49-5.49h0a5.49,5.49,0,0,1,5.49-5.49h78.59a5.49,5.49,0,0,1,5.49,5.49h0A5.49,5.49,0,0,1,1662.42,473.39Z" transform="translate(-1437.4 -223.3)" />
    <circle class="cls-16" cx="102.23" cy="255.42" r="19.69" />
    <path class="cls-4" d="M1534.14,487.24v-2.89l2.65-2.63,1-1,1-1,.83-.84.56-.6.69-.83a3.94,3.94,0,0,0,.49-.72,3.57,3.57,0,0,0,.28-.7,2.85,2.85,0,0,0,.1-.77,1.86,1.86,0,0,0-.63-1.4,2.42,2.42,0,0,0-1.73-.6,2.28,2.28,0,0,0-1.7.62,3.22,3.22,0,0,0-.83,1.4l-2.89-1.19a5.42,5.42,0,0,1,.63-1.35,5.06,5.06,0,0,1,1.12-1.24,5.86,5.86,0,0,1,1.62-.9,5.92,5.92,0,0,1,2.1-.35,6.57,6.57,0,0,1,2.29.38,5.08,5.08,0,0,1,1.73,1,4.62,4.62,0,0,1,1.09,1.54,4.69,4.69,0,0,1,.39,1.91,5.53,5.53,0,0,1-.77,2.86,12.53,12.53,0,0,1-1.86,2.42l-2.09,2.09-1.87,1.85.1.15h6.74v2.8Z" transform="translate(-1437.4 -223.3)" />
    <g id="_Group_12" data-name="&lt;Group&gt;">
        <path id="_Path_29" data-name="&lt;Path&gt;" class="cls-13" d="M1701.17,300.17a38.77,38.77,0,0,1-5.73,4.15c-2.22,1.11-7.36,3.34-6.76,4.28s3.94.43,9.07-.51,6.46-1,6.78-2.91.75-4.36.75-4.36Z" transform="translate(-1437.4 -223.3)" />
        <path id="_Path_30" data-name="&lt;Path&gt;" class="cls-10" d="M1693.84,305.09a8.65,8.65,0,0,0,3.83,1.28c2.31.09,6.05-2.91,7.18-3l.44-2.57-4.11-.64a38.78,38.78,0,0,1-5.73,4.15Z" transform="translate(-1437.4 -223.3)" />
        <path id="_Path_31" data-name="&lt;Path&gt;" class="cls-13" d="M1713,306.37s1.55,7.62,2.8,9.46,4.72,3.83,5.53,1.33-2.28-6.78-2.95-8.07-2.43-4-2.43-4Z" transform="translate(-1437.4 -223.3)" />
        <path id="_Path_32" data-name="&lt;Path&gt;" class="cls-10" d="M1717.24,313.26c1.59-.43.85-3.68.12-6-.69-1.13-1.4-2.23-1.4-2.23l-2.68,1.18C1713.85,308.64,1715.33,313.77,1717.24,313.26Z" transform="translate(-1437.4 -223.3)" />
        <path id="_Path_33" data-name="&lt;Path&gt;" class="cls-17" d="M1720.84,273.85h-17s-3.82,3.51-3.82,7.53,2.46,6.13,12.3,6.13,12.3-2.11,12.3-6.13S1720.84,273.85,1720.84,273.85Z" transform="translate(-1437.4 -223.3)" />
        
        <path id="_Path_35" data-name="&lt;Path&gt;" class="cls-10" d="M1731.83,283.37s-.22,3.71,2.93,3.71,4.24-1,5.2-1,1.92,1,3.49,1c1.2,0,2.51-.24,1.76-.85s-2.06-.48-3-1a8,8,0,0,0-3.56-1.4c-1.19,0-4.11-2-4.11-2Z" transform="translate(-1437.4 -223.3)" />
        <path id="_Path_36" data-name="&lt;Path&gt;" class="cls-12" d="M1724.35,250.34S1726,253,1729,261.47s7.9,18.89,7.9,18.89-1.26,2.73-6.26,3.18c0,0-3.55-10.54-4.82-13.44s-5.24-11.36-5.24-11.36Z" transform="translate(-1437.4 -223.3)" />
        
        <g class="hand">
            <path id="_Path_34" data-name="&lt;Path&gt;" class="cls-10" d="M1690.71,236.39c-.31.72-.61,1.42-.92,2-.4-1.51-.73-5-2-6.12-1.68-1.48-5.57,1.54-5.63,2.72a14.69,14.69,0,0,0,.87,6,14.42,14.42,0,0,0,2.75,4.27l1.14-.3c1,.95,3.29-.65,3.29-.65s1.95-2.45,2-3.92a18.09,18.09,0,0,1,.6-4.43C1693.19,234.79,1691.52,234.5,1690.71,236.39Z" transform="translate(-1437.4 -223.3)" />
            <path id="_Path_37" data-name="&lt;Path&gt;" class="cls-12" d="M1699.47,250.34l-6.48,4.31-2-10.72a17.12,17.12,0,0,0-6,1.54s1.73,13,3,14.72,4.45,1.45,7.18,0,7.09-4,7.09-4Z" transform="translate(-1437.4 -223.3)" />
        </g>
        
        <path id="_Path_38" data-name="&lt;Path&gt;" class="cls-13" d="M1707.74,225.53a5.36,5.36,0,0,1,4.94-2.21c3.46.23,8.59,2.72,9,8.69.28,4.29-2.43,9-1.65,10.2s-1.17,2.08-2,.91c0,0,0,3-6,3s-5.9-3-5.9-3-2.42,1.19-2.95.52.34-1.28.58-2.21-.44-2.86-1.16-5.81S1702.21,226,1707.74,225.53Z" transform="translate(-1437.4 -223.3)" />
        <path id="_Path_39" data-name="&lt;Path&gt;" class="cls-10" d="M1714.72,241.63l-2.76-.21-2.76.21a15.42,15.42,0,0,1,0,5.27,10.18,10.18,0,0,0,5.53,0A15.42,15.42,0,0,1,1714.72,241.63Z" transform="translate(-1437.4 -223.3)" />
        <path id="_Path_40" data-name="&lt;Path&gt;" class="cls-10" d="M1714.64,246.14h-5.36a12,12,0,0,1-3.26,1.49s.68,5.63,5.93,5.63,5.93-5.63,5.93-5.63A12,12,0,0,1,1714.64,246.14Z" transform="translate(-1437.4 -223.3)" />
        <g id="_Group_13" data-name="&lt;Group&gt;">
            <path id="_Path_41" data-name="&lt;Path&gt;" class="cls-10" d="M1706.39,234.53s-1.4-1-2.12,0-.32,4.34,1.43,4.16c1.25-.13,1.46-.42,1.46-.42Z" transform="translate(-1437.4 -223.3)" />
            <path id="_Path_42" data-name="&lt;Path&gt;" class="cls-10" d="M1717.53,234.53s1.4-1,2.12,0,.32,4.34-1.43,4.16c-1.25-.13-1.46-.42-1.46-.42Z" transform="translate(-1437.4 -223.3)" />
        </g>
        <path id="_Path_43" data-name="&lt;Path&gt;" class="cls-10" d="M1712,226.49c-4.7,0-6.92,4.93-6.22,9.39s3.46,7.23,6.22,7.23,5.53-2.76,6.22-7.23S1716.66,226.49,1712,226.49Z" transform="translate(-1437.4 -223.3)" />
        <path id="_Path_44" data-name="&lt;Path&gt;" class="cls-13" d="M1705.61,234.08s3.94-1,5.25-4.63c0,0,.92,4.36,7.44,4.71h.89s.12-8.25-8.25-8.25C1708,225.9,1704.28,230.89,1705.61,234.08Z" transform="translate(-1437.4 -223.3)" />
        <path id="_Path_45" data-name="&lt;Path&gt;" class="cls-16" d="M1708,246.86s.15,4.48,3.94,4.48,3.94-4.48,3.94-4.48l2.48.7-1,16.69,3.13,10.1s-5.72,1.73-8.58,1.73a35.37,35.37,0,0,1-7.79-1.53l3-11.13-2.65-15.6Z" transform="translate(-1437.4 -223.3)" />
        <path id="_Path_46" data-name="&lt;Path&gt;" class="cls-12" d="M1717,246.86s-2.18,12.93-2,19,4.8,12.86,4.8,12.86,3.39-1.28,4.6-3.13c0,0-2.42-5.76-2.46-9.24s2.52-15.13,2.46-16S1719.1,247.33,1717,246.86Z" transform="translate(-1437.4 -223.3)" />
        <path id="_Path_47" data-name="&lt;Path&gt;" class="cls-12" d="M1706.83,246.86s2.18,12.93,2,19-4.8,12.86-4.8,12.86-3.39-1.28-4.6-3.13c0,0,2.42-5.76,2.46-9.24s-2.52-15.13-2.46-16S1704.73,247.33,1706.83,246.86Z" transform="translate(-1437.4 -223.3)" />
        <path id="_Path_48" data-name="&lt;Path&gt;" class="cls-17" d="M1707.3,278.7c-5,.55-3.78,5.53-1.94,11.95s7,16.58,7,16.58a4,4,0,0,0,2.58,0,4.55,4.55,0,0,0,2.13-1.84s-2.81-13.07-4-17.23S1711.46,278.24,1707.3,278.7Z" transform="translate(-1437.4 -223.3)" />
        <path id="_Path_49" data-name="&lt;Path&gt;" class="cls-14" d="M1719.08,272.91c5.27,2.66,2.67,9.13-1.18,14.8S1706,302.27,1706,302.27s-3.66.19-5.62-2.1c0,0,8-15.47,10.16-19.17S1716.77,271.74,1719.08,272.91Z" transform="translate(-1437.4 -223.3)" />
    </g>
    <circle id="_Path_50" data-name="&lt;Path&gt;" class="cls-9" cx="1882.62" cy="318.34" r="5.59" transform="matrix(1, -0.02, 0.02, 1, -1442.43, -191.98)" />
    <circle id="_Path_51" data-name="&lt;Path&gt;" class="cls-9" cx="1488.32" cy="323.93" r="3.7" transform="translate(-1442.58 -198.54) rotate(-0.95)" />
    <path id="_Path_52" data-name="&lt;Path&gt;" class="cls-16" d="M1806,259.32l-2.75,0,0-2.75a2.07,2.07,0,0,0-2.11-2h0a2.07,2.07,0,0,0-2,2.11l0,2.75-2.76,0a2.07,2.07,0,0,0,.07,4.15l2.75,0,0,2.75a2.07,2.07,0,0,0,2.11,2h0a2.07,2.07,0,0,0,2-2.11l0-2.75,2.75,0a2.07,2.07,0,1,0-.07-4.15Z" transform="translate(-1437.4 -223.3)" />
    <path id="_Path_53" data-name="&lt;Path&gt;" class="cls-16" d="M1469.36,357.21l-2.9,0,0-2.9a2.18,2.18,0,0,0-2.22-2.15h0a2.18,2.18,0,0,0-2.15,2.22l0,2.9-2.9,0a2.18,2.18,0,0,0,.07,4.37l2.9,0,0,2.9a2.18,2.18,0,0,0,2.22,2.15h0a2.18,2.18,0,0,0,2.15-2.22l0-2.9,2.9,0a2.18,2.18,0,1,0-.07-4.37Z" transform="translate(-1437.4 -223.3)" />
</svg>