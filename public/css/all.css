/*!
 * Bootstrap v4.3.1 (https://getbootstrap.com/)
 * Copyright 2011-2019 The Bootstrap Authors
 * Copyright 2011-2019 Twitter, Inc.
 * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)
 */
 :root {
    --blue: #007bff;
    --indigo: #6610f2;
    --purple: #6f42c1;
    --pink: #e83e8c;
    --red: #dc3545;
    --orange: #fd7e14;
    --yellow: #ffc107;
    --green: #28a745;
    --teal: #20c997;
    --cyan: #17a2b8;
    --white: #fff;
    --gray: #6c757d;
    --gray-dark: #343a40;
    --primary: #007bff;
    --secondary: #6c757d;
    --success: #28a745;
    --info: #17a2b8;
    --warning: #ffc107;
    --danger: #dc3545;
    --light: #f8f9fa;
    --dark: #343a40;
    --breakpoint-xs: 0;
    --breakpoint-sm: 576px;
    --breakpoint-md: 768px;
    --breakpoint-lg: 992px;
    --breakpoint-xl: 1200px;
    --font-family-sans-serif: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    --font-family-monospace: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace
}

*, ::after, ::before {
    box-sizing: border-box
}

html {
    font-family: sans-serif;
    line-height: 1.15;
    -webkit-text-size-adjust: 100%;
    -webkit-tap-highlight-color: transparent
}

article, aside, figcaption, figure, footer, header, hgroup, main, nav, section {
    display: block
}

body {
    margin: 0;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #212529;
    text-align: left;
    background-color: #fff
}

[tabindex="-1"]:focus {
    outline: 0 !important
}

hr {
    box-sizing: content-box;
    height: 0;
    overflow: visible
}

h1, h2, h3, h4, h5, h6 {
    margin-top: 0;
    margin-bottom: .5rem
}

p {
    margin-top: 0;
    margin-bottom: 1rem
}

abbr[data-original-title], abbr[title] {
    text-decoration: underline;
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
    cursor: help;
    border-bottom: 0;
    -webkit-text-decoration-skip-ink: none;
    text-decoration-skip-ink: none
}

address {
    margin-bottom: 1rem;
    font-style: normal;
    line-height: inherit
}

dl, ol, ul {
    margin-top: 0;
    margin-bottom: 1rem
}

ol ol, ol ul, ul ol, ul ul {
    margin-bottom: 0
}

dt {
    font-weight: 700
}

dd {
    margin-bottom: .5rem;
    margin-left: 0
}

blockquote {
    margin: 0 0 1rem
}

b, strong {
    font-weight: bolder
}

small {
    font-size: 80%
}

sub, sup {
    position: relative;
    font-size: 75%;
    line-height: 0;
    vertical-align: baseline
}

sub {
    bottom: -.25em
}

sup {
    top: -.5em
}

a {
    color: #007bff;
    text-decoration: none;
    background-color: transparent
}

a:hover {
    color: #0056b3;
    text-decoration: underline
}

a:not([href]):not([tabindex]) {
    color: inherit;
    text-decoration: none
}

a:not([href]):not([tabindex]):focus, a:not([href]):not([tabindex]):hover {
    color: inherit;
    text-decoration: none
}

a:not([href]):not([tabindex]):focus {
    outline: 0
}

code, kbd, pre, samp {
    font-family: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
    font-size: 1em
}

pre {
    margin-top: 0;
    margin-bottom: 1rem;
    overflow: auto
}

figure {
    margin: 0 0 1rem
}

img {
    vertical-align: middle;
    border-style: none
}

svg {
    overflow: hidden;
    vertical-align: middle
}

table {
    border-collapse: collapse
}

caption {
    padding-top: .75rem;
    padding-bottom: .75rem;
    color: #6c757d;
    text-align: left;
    caption-side: bottom
}

th {
    text-align: inherit
}

label {
    display: inline-block;
    margin-bottom: .5rem
}

button {
    border-radius: 0
}

button:focus {
    outline: 1px dotted;
    outline: 5px auto -webkit-focus-ring-color
}

button, input, optgroup, select, textarea {
    margin: 0;
    font-family: inherit;
    font-size: inherit;
    line-height: inherit
}

button, input {
    overflow: visible
}

button, select {
    text-transform: none
}

select {
    word-wrap: normal
}

[type=button], [type=reset], [type=submit], button {
    -webkit-appearance: button
}

[type=button]:not(:disabled), [type=reset]:not(:disabled), [type=submit]:not(:disabled), button:not(:disabled) {
    cursor: pointer
}

[type=button]::-moz-focus-inner, [type=reset]::-moz-focus-inner, [type=submit]::-moz-focus-inner, button::-moz-focus-inner {
    padding: 0;
    border-style: none
}

input[type=checkbox], input[type=radio] {
    box-sizing: border-box;
    padding: 0
}

input[type=date], input[type=datetime-local], input[type=month], input[type=time] {
    -webkit-appearance: listbox
}

textarea {
    overflow: auto;
    resize: vertical
}

fieldset {
    min-width: 0;
    padding: 0;
    margin: 0;
    border: 0
}

legend {
    display: block;
    width: 100%;
    max-width: 100%;
    padding: 0;
    margin-bottom: .5rem;
    font-size: 1.5rem;
    line-height: inherit;
    color: inherit;
    white-space: normal
}

progress {
    vertical-align: baseline
}

[type=number]::-webkit-inner-spin-button, [type=number]::-webkit-outer-spin-button {
    height: auto
}

[type=search] {
    outline-offset: -2px;
    -webkit-appearance: none
}

[type=search]::-webkit-search-decoration {
    -webkit-appearance: none
}

::-webkit-file-upload-button {
    font: inherit;
    -webkit-appearance: button
}

output {
    display: inline-block
}

summary {
    display: list-item;
    cursor: pointer
}

template {
    display: none
}

[hidden] {
    display: none !important
}

.h1, .h2, .h3, .h4, .h5, .h6, h1, h2, h3, h4, h5, h6 {
    margin-bottom: .5rem;
    font-weight: 500;
    line-height: 1.2
}

.h1, h1 {
    font-size: 2.5rem
}

.h2, h2 {
    font-size: 2rem
}

.h3, h3 {
    font-size: 1.75rem
}

.h4, h4 {
    font-size: 1.5rem
}

.h5, h5 {
    font-size: 1.25rem
}

.h6, h6 {
    font-size: 1rem
}

.lead {
    font-size: 1.25rem;
    font-weight: 300
}

.display-1 {
    font-size: 6rem;
    font-weight: 300;
    line-height: 1.2
}

.display-2 {
    font-size: 5.5rem;
    font-weight: 300;
    line-height: 1.2
}

.display-3 {
    font-size: 4.5rem;
    font-weight: 300;
    line-height: 1.2
}

.display-4 {
    font-size: 3.5rem;
    font-weight: 300;
    line-height: 1.2
}

hr {
    margin-top: 1rem;
    margin-bottom: 1rem;
    border: 0;
    border-top: 1px solid rgba(0, 0, 0, .1)
}

.small, small {
    font-size: 80%;
    font-weight: 400
}

.mark, mark {
    padding: .2em;
    background-color: #fcf8e3
}

.list-unstyled {
    padding-left: 0;
    list-style: none
}

.list-inline {
    padding-left: 0;
    list-style: none
}

.list-inline-item {
    display: inline-block
}

.list-inline-item:not(:last-child) {
    margin-right: .5rem
}

.initialism {
    font-size: 90%;
    text-transform: uppercase
}

.blockquote {
    margin-bottom: 1rem;
    font-size: 1.25rem
}

.blockquote-footer {
    display: block;
    font-size: 80%;
    color: #6c757d
}

.blockquote-footer::before {
    content: "\2014\00A0"
}

.img-fluid {
    max-width: 100%;
    height: auto
}

.img-thumbnail {
    padding: .25rem;
    background-color: #fff;
    border: 1px solid #dee2e6;
    border-radius: .25rem;
    max-width: 100%;
    height: auto
}

.figure {
    display: inline-block
}

.figure-img {
    margin-bottom: .5rem;
    line-height: 1
}

.figure-caption {
    font-size: 90%;
    color: #6c757d
}

code {
    font-size: 87.5%;
    color: #e83e8c;
    word-break: break-word
}

a > code {
    color: inherit
}

kbd {
    padding: .2rem .4rem;
    font-size: 87.5%;
    color: #fff;
    background-color: #212529;
    border-radius: .2rem
}

kbd kbd {
    padding: 0;
    font-size: 100%;
    font-weight: 700
}

pre {
    display: block;
    font-size: 87.5%;
    color: #212529
}

pre code {
    font-size: inherit;
    color: inherit;
    word-break: normal
}

.pre-scrollable {
    max-height: 340px;
    overflow-y: scroll
}

/* .container {
    width: 100%;
    padding-right: 15px;
    padding-left: 15px;
    margin-right: auto;
    margin-left: auto
}

@media (min-width: 576px) {
    .container {
        max-width: 540px
    }
}

@media (min-width: 768px) {
    .container {
        max-width: 720px
    }
}

@media (min-width: 992px) {
    .container {
        max-width: 960px
    }
}

@media (min-width: 1200px) {
    .container {
        max-width: 1140px
    }
} */

.container-fluid {
    width: 100%;
    padding-right: 15px;
    padding-left: 15px;
    margin-right: auto;
    margin-left: auto
}

.row {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    margin-right: -15px;
    margin-left: -15px
}

.no-gutters {
    margin-right: 0;
    margin-left: 0
}

.no-gutters > .col, .no-gutters > [class*=col-] {
    padding-right: 0;
    padding-left: 0
}

.col, .col-1, .col-10, .col-11, .col-12, .col-2, .col-3, .col-4, .col-5, .col-6, .col-7, .col-8, .col-9, .col-auto, .col-lg, .col-lg-1, .col-lg-10, .col-lg-11, .col-lg-12, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6, .col-lg-7, .col-lg-8, .col-lg-9, .col-lg-auto, .col-md, .col-md-1, .col-md-10, .col-md-11, .col-md-12, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9, .col-md-auto, .col-sm, .col-sm-1, .col-sm-10, .col-sm-11, .col-sm-12, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6, .col-sm-7, .col-sm-8, .col-sm-9, .col-sm-auto, .col-xl, .col-xl-1, .col-xl-10, .col-xl-11, .col-xl-12, .col-xl-2, .col-xl-3, .col-xl-4, .col-xl-5, .col-xl-6, .col-xl-7, .col-xl-8, .col-xl-9, .col-xl-auto {
    position: relative;
    width: 100%;
    padding-right: 15px;
    padding-left: 15px
}

.col {
    -ms-flex-preferred-size: 0;
    flex-basis: 0;
    -ms-flex-positive: 1;
    flex-grow: 1;
    max-width: 100%
}

.col-auto {
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: auto;
    max-width: 100%
}

.col-1 {
    -ms-flex: 0 0 8.333333%;
    flex: 0 0 8.333333%;
    max-width: 8.333333%
}

.col-2 {
    -ms-flex: 0 0 16.666667%;
    flex: 0 0 16.666667%;
    max-width: 16.666667%
}

.col-3 {
    -ms-flex: 0 0 25%;
    flex: 0 0 25%;
    max-width: 25%
}

.col-4 {
    -ms-flex: 0 0 33.333333%;
    flex: 0 0 33.333333%;
    max-width: 33.333333%
}

.col-5 {
    -ms-flex: 0 0 41.666667%;
    flex: 0 0 41.666667%;
    max-width: 41.666667%
}

.col-6 {
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%
}

.col-7 {
    -ms-flex: 0 0 58.333333%;
    flex: 0 0 58.333333%;
    max-width: 58.333333%
}

.col-8 {
    -ms-flex: 0 0 66.666667%;
    flex: 0 0 66.666667%;
    max-width: 66.666667%
}

.col-9 {
    -ms-flex: 0 0 75%;
    flex: 0 0 75%;
    max-width: 75%
}

.col-10 {
    -ms-flex: 0 0 83.333333%;
    flex: 0 0 83.333333%;
    max-width: 83.333333%
}

.col-11 {
    -ms-flex: 0 0 91.666667%;
    flex: 0 0 91.666667%;
    max-width: 91.666667%
}

.col-12 {
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    max-width: 100%
}

.order-first {
    -ms-flex-order: -1;
    order: -1
}

.order-last {
    -ms-flex-order: 13;
    order: 13
}

.order-0 {
    -ms-flex-order: 0;
    order: 0
}

.order-1 {
    -ms-flex-order: 1;
    order: 1
}

.order-2 {
    -ms-flex-order: 2;
    order: 2
}

.order-3 {
    -ms-flex-order: 3;
    order: 3
}

.order-4 {
    -ms-flex-order: 4;
    order: 4
}

.order-5 {
    -ms-flex-order: 5;
    order: 5
}

.order-6 {
    -ms-flex-order: 6;
    order: 6
}

.order-7 {
    -ms-flex-order: 7;
    order: 7
}

.order-8 {
    -ms-flex-order: 8;
    order: 8
}

.order-9 {
    -ms-flex-order: 9;
    order: 9
}

.order-10 {
    -ms-flex-order: 10;
    order: 10
}

.order-11 {
    -ms-flex-order: 11;
    order: 11
}

.order-12 {
    -ms-flex-order: 12;
    order: 12
}

.offset-1 {
    margin-left: 8.333333%
}

.offset-2 {
    margin-left: 16.666667%
}

.offset-3 {
    margin-left: 25%
}

.offset-4 {
    margin-left: 33.333333%
}

.offset-5 {
    margin-left: 41.666667%
}

.offset-6 {
    margin-left: 50%
}

.offset-7 {
    margin-left: 58.333333%
}

.offset-8 {
    margin-left: 66.666667%
}

.offset-9 {
    margin-left: 75%
}

.offset-10 {
    margin-left: 83.333333%
}

.offset-11 {
    margin-left: 91.666667%
}

@media (min-width: 576px) {
    .col-sm {
        -ms-flex-preferred-size: 0;
        flex-basis: 0;
        -ms-flex-positive: 1;
        flex-grow: 1;
        max-width: 100%
    }

    .col-sm-auto {
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: auto;
        max-width: 100%
    }

    .col-sm-1 {
        -ms-flex: 0 0 8.333333%;
        flex: 0 0 8.333333%;
        max-width: 8.333333%
    }

    .col-sm-2 {
        -ms-flex: 0 0 16.666667%;
        flex: 0 0 16.666667%;
        max-width: 16.666667%
    }

    .col-sm-3 {
        -ms-flex: 0 0 25%;
        flex: 0 0 25%;
        max-width: 25%
    }

    .col-sm-4 {
        -ms-flex: 0 0 33.333333%;
        flex: 0 0 33.333333%;
        max-width: 33.333333%
    }

    .col-sm-5 {
        -ms-flex: 0 0 41.666667%;
        flex: 0 0 41.666667%;
        max-width: 41.666667%
    }

    .col-sm-6 {
        -ms-flex: 0 0 50%;
        flex: 0 0 50%;
        max-width: 50%
    }

    .col-sm-7 {
        -ms-flex: 0 0 58.333333%;
        flex: 0 0 58.333333%;
        max-width: 58.333333%
    }

    .col-sm-8 {
        -ms-flex: 0 0 66.666667%;
        flex: 0 0 66.666667%;
        max-width: 66.666667%
    }

    .col-sm-9 {
        -ms-flex: 0 0 75%;
        flex: 0 0 75%;
        max-width: 75%
    }

    .col-sm-10 {
        -ms-flex: 0 0 83.333333%;
        flex: 0 0 83.333333%;
        max-width: 83.333333%
    }

    .col-sm-11 {
        -ms-flex: 0 0 91.666667%;
        flex: 0 0 91.666667%;
        max-width: 91.666667%
    }

    .col-sm-12 {
        -ms-flex: 0 0 100%;
        flex: 0 0 100%;
        max-width: 100%
    }

    .order-sm-first {
        -ms-flex-order: -1;
        order: -1
    }

    .order-sm-last {
        -ms-flex-order: 13;
        order: 13
    }

    .order-sm-0 {
        -ms-flex-order: 0;
        order: 0
    }

    .order-sm-1 {
        -ms-flex-order: 1;
        order: 1
    }

    .order-sm-2 {
        -ms-flex-order: 2;
        order: 2
    }

    .order-sm-3 {
        -ms-flex-order: 3;
        order: 3
    }

    .order-sm-4 {
        -ms-flex-order: 4;
        order: 4
    }

    .order-sm-5 {
        -ms-flex-order: 5;
        order: 5
    }

    .order-sm-6 {
        -ms-flex-order: 6;
        order: 6
    }

    .order-sm-7 {
        -ms-flex-order: 7;
        order: 7
    }

    .order-sm-8 {
        -ms-flex-order: 8;
        order: 8
    }

    .order-sm-9 {
        -ms-flex-order: 9;
        order: 9
    }

    .order-sm-10 {
        -ms-flex-order: 10;
        order: 10
    }

    .order-sm-11 {
        -ms-flex-order: 11;
        order: 11
    }

    .order-sm-12 {
        -ms-flex-order: 12;
        order: 12
    }

    .offset-sm-0 {
        margin-left: 0
    }

    .offset-sm-1 {
        margin-left: 8.333333%
    }

    .offset-sm-2 {
        margin-left: 16.666667%
    }

    .offset-sm-3 {
        margin-left: 25%
    }

    .offset-sm-4 {
        margin-left: 33.333333%
    }

    .offset-sm-5 {
        margin-left: 41.666667%
    }

    .offset-sm-6 {
        margin-left: 50%
    }

    .offset-sm-7 {
        margin-left: 58.333333%
    }

    .offset-sm-8 {
        margin-left: 66.666667%
    }

    .offset-sm-9 {
        margin-left: 75%
    }

    .offset-sm-10 {
        margin-left: 83.333333%
    }

    .offset-sm-11 {
        margin-left: 91.666667%
    }
}

@media (min-width: 768px) {
    .col-md {
        -ms-flex-preferred-size: 0;
        flex-basis: 0;
        -ms-flex-positive: 1;
        flex-grow: 1;
        max-width: 100%
    }

    .col-md-auto {
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: auto;
        max-width: 100%
    }

    .col-md-1 {
        -ms-flex: 0 0 8.333333%;
        flex: 0 0 8.333333%;
        max-width: 8.333333%
    }

    .col-md-2 {
        -ms-flex: 0 0 16.666667%;
        flex: 0 0 16.666667%;
        max-width: 16.666667%
    }

    .col-md-3 {
        -ms-flex: 0 0 25%;
        flex: 0 0 25%;
        max-width: 25%
    }

    .col-md-4 {
        -ms-flex: 0 0 33.333333%;
        flex: 0 0 33.333333%;
        max-width: 33.333333%
    }

    .col-md-5 {
        -ms-flex: 0 0 41.666667%;
        flex: 0 0 41.666667%;
        max-width: 41.666667%
    }

    .col-md-6 {
        -ms-flex: 0 0 50%;
        flex: 0 0 50%;
        max-width: 50%
    }

    .col-md-7 {
        -ms-flex: 0 0 58.333333%;
        flex: 0 0 58.333333%;
        max-width: 58.333333%
    }

    .col-md-8 {
        -ms-flex: 0 0 66.666667%;
        flex: 0 0 66.666667%;
        max-width: 66.666667%
    }

    .col-md-9 {
        -ms-flex: 0 0 75%;
        flex: 0 0 75%;
        max-width: 75%
    }

    .col-md-10 {
        -ms-flex: 0 0 83.333333%;
        flex: 0 0 83.333333%;
        max-width: 83.333333%
    }

    .col-md-11 {
        -ms-flex: 0 0 91.666667%;
        flex: 0 0 91.666667%;
        max-width: 91.666667%
    }

    .col-md-12 {
        -ms-flex: 0 0 100%;
        flex: 0 0 100%;
        max-width: 100%
    }

    .order-md-first {
        -ms-flex-order: -1;
        order: -1
    }

    .order-md-last {
        -ms-flex-order: 13;
        order: 13
    }

    .order-md-0 {
        -ms-flex-order: 0;
        order: 0
    }

    .order-md-1 {
        -ms-flex-order: 1;
        order: 1
    }

    .order-md-2 {
        -ms-flex-order: 2;
        order: 2
    }

    .order-md-3 {
        -ms-flex-order: 3;
        order: 3
    }

    .order-md-4 {
        -ms-flex-order: 4;
        order: 4
    }

    .order-md-5 {
        -ms-flex-order: 5;
        order: 5
    }

    .order-md-6 {
        -ms-flex-order: 6;
        order: 6
    }

    .order-md-7 {
        -ms-flex-order: 7;
        order: 7
    }

    .order-md-8 {
        -ms-flex-order: 8;
        order: 8
    }

    .order-md-9 {
        -ms-flex-order: 9;
        order: 9
    }

    .order-md-10 {
        -ms-flex-order: 10;
        order: 10
    }

    .order-md-11 {
        -ms-flex-order: 11;
        order: 11
    }

    .order-md-12 {
        -ms-flex-order: 12;
        order: 12
    }

    .offset-md-0 {
        margin-left: 0
    }

    .offset-md-1 {
        margin-left: 8.333333%
    }

    .offset-md-2 {
        margin-left: 16.666667%
    }

    .offset-md-3 {
        margin-left: 25%
    }

    .offset-md-4 {
        margin-left: 33.333333%
    }

    .offset-md-5 {
        margin-left: 41.666667%
    }

    .offset-md-6 {
        margin-left: 50%
    }

    .offset-md-7 {
        margin-left: 58.333333%
    }

    .offset-md-8 {
        margin-left: 66.666667%
    }

    .offset-md-9 {
        margin-left: 75%
    }

    .offset-md-10 {
        margin-left: 83.333333%
    }

    .offset-md-11 {
        margin-left: 91.666667%
    }
}

@media (min-width: 992px) {
    .col-lg {
        -ms-flex-preferred-size: 0;
        flex-basis: 0;
        -ms-flex-positive: 1;
        flex-grow: 1;
        max-width: 100%
    }

    .col-lg-auto {
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: auto;
        max-width: 100%
    }

    .col-lg-1 {
        -ms-flex: 0 0 8.333333%;
        flex: 0 0 8.333333%;
        max-width: 8.333333%
    }

    .col-lg-2 {
        -ms-flex: 0 0 16.666667%;
        flex: 0 0 16.666667%;
        max-width: 16.666667%
    }

    .col-lg-3 {
        -ms-flex: 0 0 25%;
        flex: 0 0 25%;
        max-width: 25%
    }

    .col-lg-4 {
        -ms-flex: 0 0 33.333333%;
        flex: 0 0 33.333333%;
        max-width: 33.333333%
    }

    .col-lg-5 {
        -ms-flex: 0 0 41.666667%;
        flex: 0 0 41.666667%;
        max-width: 41.666667%
    }

    .col-lg-6 {
        -ms-flex: 0 0 50%;
        flex: 0 0 50%;
        max-width: 50%
    }

    .col-lg-7 {
        -ms-flex: 0 0 58.333333%;
        flex: 0 0 58.333333%;
        max-width: 58.333333%
    }

    .col-lg-8 {
        -ms-flex: 0 0 66.666667%;
        flex: 0 0 66.666667%;
        max-width: 66.666667%
    }

    .col-lg-9 {
        -ms-flex: 0 0 75%;
        flex: 0 0 75%;
        max-width: 75%
    }

    .col-lg-10 {
        -ms-flex: 0 0 83.333333%;
        flex: 0 0 83.333333%;
        max-width: 83.333333%
    }

    .col-lg-11 {
        -ms-flex: 0 0 91.666667%;
        flex: 0 0 91.666667%;
        max-width: 91.666667%
    }

    .col-lg-12 {
        -ms-flex: 0 0 100%;
        flex: 0 0 100%;
        max-width: 100%
    }

    .order-lg-first {
        -ms-flex-order: -1;
        order: -1
    }

    .order-lg-last {
        -ms-flex-order: 13;
        order: 13
    }

    .order-lg-0 {
        -ms-flex-order: 0;
        order: 0
    }

    .order-lg-1 {
        -ms-flex-order: 1;
        order: 1
    }

    .order-lg-2 {
        -ms-flex-order: 2;
        order: 2
    }

    .order-lg-3 {
        -ms-flex-order: 3;
        order: 3
    }

    .order-lg-4 {
        -ms-flex-order: 4;
        order: 4
    }

    .order-lg-5 {
        -ms-flex-order: 5;
        order: 5
    }

    .order-lg-6 {
        -ms-flex-order: 6;
        order: 6
    }

    .order-lg-7 {
        -ms-flex-order: 7;
        order: 7
    }

    .order-lg-8 {
        -ms-flex-order: 8;
        order: 8
    }

    .order-lg-9 {
        -ms-flex-order: 9;
        order: 9
    }

    .order-lg-10 {
        -ms-flex-order: 10;
        order: 10
    }

    .order-lg-11 {
        -ms-flex-order: 11;
        order: 11
    }

    .order-lg-12 {
        -ms-flex-order: 12;
        order: 12
    }

    .offset-lg-0 {
        margin-left: 0
    }

    .offset-lg-1 {
        margin-left: 8.333333%
    }

    .offset-lg-2 {
        margin-left: 16.666667%
    }

    .offset-lg-3 {
        margin-left: 25%
    }

    .offset-lg-4 {
        margin-left: 33.333333%
    }

    .offset-lg-5 {
        margin-left: 41.666667%
    }

    .offset-lg-6 {
        margin-left: 50%
    }

    .offset-lg-7 {
        margin-left: 58.333333%
    }

    .offset-lg-8 {
        margin-left: 66.666667%
    }

    .offset-lg-9 {
        margin-left: 75%
    }

    .offset-lg-10 {
        margin-left: 83.333333%
    }

    .offset-lg-11 {
        margin-left: 91.666667%
    }
}

@media (min-width: 1200px) {
    .col-xl {
        -ms-flex-preferred-size: 0;
        flex-basis: 0;
        -ms-flex-positive: 1;
        flex-grow: 1;
        max-width: 100%
    }

    .col-xl-auto {
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: auto;
        max-width: 100%
    }

    .col-xl-1 {
        -ms-flex: 0 0 8.333333%;
        flex: 0 0 8.333333%;
        max-width: 8.333333%
    }

    .col-xl-2 {
        -ms-flex: 0 0 16.666667%;
        flex: 0 0 16.666667%;
        max-width: 16.666667%
    }

    .col-xl-3 {
        -ms-flex: 0 0 25%;
        flex: 0 0 25%;
        max-width: 25%
    }

    .col-xl-4 {
        -ms-flex: 0 0 33.333333%;
        flex: 0 0 33.333333%;
        max-width: 33.333333%
    }

    .col-xl-5 {
        -ms-flex: 0 0 41.666667%;
        flex: 0 0 41.666667%;
        max-width: 41.666667%
    }

    .col-xl-6 {
        -ms-flex: 0 0 50%;
        flex: 0 0 50%;
        max-width: 50%
    }

    .col-xl-7 {
        -ms-flex: 0 0 58.333333%;
        flex: 0 0 58.333333%;
        max-width: 58.333333%
    }

    .col-xl-8 {
        -ms-flex: 0 0 66.666667%;
        flex: 0 0 66.666667%;
        max-width: 66.666667%
    }

    .col-xl-9 {
        -ms-flex: 0 0 75%;
        flex: 0 0 75%;
        max-width: 75%
    }

    .col-xl-10 {
        -ms-flex: 0 0 83.333333%;
        flex: 0 0 83.333333%;
        max-width: 83.333333%
    }

    .col-xl-11 {
        -ms-flex: 0 0 91.666667%;
        flex: 0 0 91.666667%;
        max-width: 91.666667%
    }

    .col-xl-12 {
        -ms-flex: 0 0 100%;
        flex: 0 0 100%;
        max-width: 100%
    }

    .order-xl-first {
        -ms-flex-order: -1;
        order: -1
    }

    .order-xl-last {
        -ms-flex-order: 13;
        order: 13
    }

    .order-xl-0 {
        -ms-flex-order: 0;
        order: 0
    }

    .order-xl-1 {
        -ms-flex-order: 1;
        order: 1
    }

    .order-xl-2 {
        -ms-flex-order: 2;
        order: 2
    }

    .order-xl-3 {
        -ms-flex-order: 3;
        order: 3
    }

    .order-xl-4 {
        -ms-flex-order: 4;
        order: 4
    }

    .order-xl-5 {
        -ms-flex-order: 5;
        order: 5
    }

    .order-xl-6 {
        -ms-flex-order: 6;
        order: 6
    }

    .order-xl-7 {
        -ms-flex-order: 7;
        order: 7
    }

    .order-xl-8 {
        -ms-flex-order: 8;
        order: 8
    }

    .order-xl-9 {
        -ms-flex-order: 9;
        order: 9
    }

    .order-xl-10 {
        -ms-flex-order: 10;
        order: 10
    }

    .order-xl-11 {
        -ms-flex-order: 11;
        order: 11
    }

    .order-xl-12 {
        -ms-flex-order: 12;
        order: 12
    }

    .offset-xl-0 {
        margin-left: 0
    }

    .offset-xl-1 {
        margin-left: 8.333333%
    }

    .offset-xl-2 {
        margin-left: 16.666667%
    }

    .offset-xl-3 {
        margin-left: 25%
    }

    .offset-xl-4 {
        margin-left: 33.333333%
    }

    .offset-xl-5 {
        margin-left: 41.666667%
    }

    .offset-xl-6 {
        margin-left: 50%
    }

    .offset-xl-7 {
        margin-left: 58.333333%
    }

    .offset-xl-8 {
        margin-left: 66.666667%
    }

    .offset-xl-9 {
        margin-left: 75%
    }

    .offset-xl-10 {
        margin-left: 83.333333%
    }

    .offset-xl-11 {
        margin-left: 91.666667%
    }
}

.table {
    width: 100%;
    margin-bottom: 1rem;
    color: #212529
}

.table td, .table th {
    padding: .75rem;
    vertical-align: top;
    border-top: 1px solid #dee2e6
}

.table thead th {
    vertical-align: bottom;
    border-bottom: 2px solid #dee2e6
}

.table tbody + tbody {
    border-top: 2px solid #dee2e6
}

.table-sm td, .table-sm th {
    padding: .3rem
}

.table-bordered {
    border: 1px solid #dee2e6
}

.table-bordered td, .table-bordered th {
    border: 1px solid #dee2e6
}

.table-bordered thead td, .table-bordered thead th {
    border-bottom-width: 2px
}

.table-borderless tbody + tbody, .table-borderless td, .table-borderless th, .table-borderless thead th {
    border: 0
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, .05)
}

.table-hover tbody tr:hover {
    color: #212529;
    background-color: rgba(0, 0, 0, .075)
}

.table-primary, .table-primary > td, .table-primary > th {
    background-color: #b8daff
}

.table-primary tbody + tbody, .table-primary td, .table-primary th, .table-primary thead th {
    border-color: #7abaff
}

.table-hover .table-primary:hover {
    background-color: #9fcdff
}

.table-hover .table-primary:hover > td, .table-hover .table-primary:hover > th {
    background-color: #9fcdff
}

.table-secondary, .table-secondary > td, .table-secondary > th {
    background-color: #d6d8db
}

.table-secondary tbody + tbody, .table-secondary td, .table-secondary th, .table-secondary thead th {
    border-color: #b3b7bb
}

.table-hover .table-secondary:hover {
    background-color: #c8cbcf
}

.table-hover .table-secondary:hover > td, .table-hover .table-secondary:hover > th {
    background-color: #c8cbcf
}

.table-success, .table-success > td, .table-success > th {
    background-color: #c3e6cb
}

.table-success tbody + tbody, .table-success td, .table-success th, .table-success thead th {
    border-color: #8fd19e
}

.table-hover .table-success:hover {
    background-color: #b1dfbb
}

.table-hover .table-success:hover > td, .table-hover .table-success:hover > th {
    background-color: #b1dfbb
}

.table-info, .table-info > td, .table-info > th {
    background-color: #bee5eb
}

.table-info tbody + tbody, .table-info td, .table-info th, .table-info thead th {
    border-color: #86cfda
}

.table-hover .table-info:hover {
    background-color: #abdde5
}

.table-hover .table-info:hover > td, .table-hover .table-info:hover > th {
    background-color: #abdde5
}

.table-warning, .table-warning > td, .table-warning > th {
    background-color: #ffeeba
}

.table-warning tbody + tbody, .table-warning td, .table-warning th, .table-warning thead th {
    border-color: #ffdf7e
}

.table-hover .table-warning:hover {
    background-color: #ffe8a1
}

.table-hover .table-warning:hover > td, .table-hover .table-warning:hover > th {
    background-color: #ffe8a1
}

.table-danger, .table-danger > td, .table-danger > th {
    background-color: #f5c6cb
}

.table-danger tbody + tbody, .table-danger td, .table-danger th, .table-danger thead th {
    border-color: #ed969e
}

.table-hover .table-danger:hover {
    background-color: #f1b0b7
}

.table-hover .table-danger:hover > td, .table-hover .table-danger:hover > th {
    background-color: #f1b0b7
}

.table-light, .table-light > td, .table-light > th {
    background-color: #fdfdfe
}

.table-light tbody + tbody, .table-light td, .table-light th, .table-light thead th {
    border-color: #fbfcfc
}

.table-hover .table-light:hover {
    background-color: #ececf6
}

.table-hover .table-light:hover > td, .table-hover .table-light:hover > th {
    background-color: #ececf6
}

.table-dark, .table-dark > td, .table-dark > th {
    background-color: #c6c8ca
}

.table-dark tbody + tbody, .table-dark td, .table-dark th, .table-dark thead th {
    border-color: #95999c
}

.table-hover .table-dark:hover {
    background-color: #b9bbbe
}

.table-hover .table-dark:hover > td, .table-hover .table-dark:hover > th {
    background-color: #b9bbbe
}

.table-active, .table-active > td, .table-active > th {
    background-color: rgba(0, 0, 0, .075)
}

.table-hover .table-active:hover {
    background-color: rgba(0, 0, 0, .075)
}

.table-hover .table-active:hover > td, .table-hover .table-active:hover > th {
    background-color: rgba(0, 0, 0, .075)
}

.table .thead-dark th {
    color: #fff;
    background-color: #343a40;
    border-color: #454d55
}

.table .thead-light th {
    color: #495057;
    background-color: #e9ecef;
    border-color: #dee2e6
}

.table-dark {
    color: #fff;
    background-color: #343a40
}

.table-dark td, .table-dark th, .table-dark thead th {
    border-color: #454d55
}

.table-dark.table-bordered {
    border: 0
}

.table-dark.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(255, 255, 255, .05)
}

.table-dark.table-hover tbody tr:hover {
    color: #fff;
    background-color: rgba(255, 255, 255, .075)
}

@media (max-width: 575.98px) {
    .table-responsive-sm {
        display: block;
        width: 100%;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch
    }

    .table-responsive-sm > .table-bordered {
        border: 0
    }
}

@media (max-width: 767.98px) {
    .table-responsive-md {
        display: block;
        width: 100%;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch
    }

    .table-responsive-md > .table-bordered {
        border: 0
    }
}

@media (max-width: 991.98px) {
    .table-responsive-lg {
        display: block;
        width: 100%;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch
    }

    .table-responsive-lg > .table-bordered {
        border: 0
    }
}

@media (max-width: 1199.98px) {
    .table-responsive-xl {
        display: block;
        width: 100%;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch
    }

    .table-responsive-xl > .table-bordered {
        border: 0
    }
}

.table-responsive {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch
}

.table-responsive > .table-bordered {
    border: 0
}

.form-control {
    display: block;
    width: 100%;
    height: calc(1.5em + .75rem + 2px);
    padding: .375rem .75rem;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #495057;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #ced4da;
    border-radius: .25rem;
    transition: border-color .15s ease-in-out, box-shadow .15s ease-in-out
}

@media (prefers-reduced-motion: reduce) {
    .form-control {
        transition: none
    }
}

.form-control::-ms-expand {
    background-color: transparent;
    border: 0
}

.form-control:focus {
    color: #495057;
    background-color: #fff;
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 .2rem rgba(0, 123, 255, .25)
}

.form-control::-webkit-input-placeholder {
    color: #6c757d;
    opacity: 1
}

.form-control::-moz-placeholder {
    color: #6c757d;
    opacity: 1
}

.form-control:-ms-input-placeholder {
    color: #6c757d;
    opacity: 1
}

.form-control::-ms-input-placeholder {
    color: #6c757d;
    opacity: 1
}

.form-control::placeholder {
    color: #6c757d;
    opacity: 1
}

.form-control:disabled, .form-control[readonly] {
    background-color: #e9ecef;
    opacity: 1
}

select.form-control:focus::-ms-value {
    color: #495057;
    background-color: #fff
}

.form-control-file, .form-control-range {
    display: block;
    width: 100%
}

.col-form-label {
    padding-top: calc(.375rem + 1px);
    padding-bottom: calc(.375rem + 1px);
    margin-bottom: 0;
    font-size: inherit;
    line-height: 1.5
}

.col-form-label-lg {
    padding-top: calc(.5rem + 1px);
    padding-bottom: calc(.5rem + 1px);
    font-size: 1.25rem;
    line-height: 1.5
}

.col-form-label-sm {
    padding-top: calc(.25rem + 1px);
    padding-bottom: calc(.25rem + 1px);
    font-size: .875rem;
    line-height: 1.5
}

.form-control-plaintext {
    display: block;
    width: 100%;
    padding-top: .375rem;
    padding-bottom: .375rem;
    margin-bottom: 0;
    line-height: 1.5;
    color: #212529;
    background-color: transparent;
    border: solid transparent;
    border-width: 1px 0
}

.form-control-plaintext.form-control-lg, .form-control-plaintext.form-control-sm {
    padding-right: 0;
    padding-left: 0
}

.form-control-sm {
    height: calc(1.5em + .5rem + 2px);
    padding: .25rem .5rem;
    font-size: .875rem;
    line-height: 1.5;
    border-radius: .2rem
}

.form-control-lg {
    height: calc(1.5em + 1rem + 2px);
    padding: .5rem 1rem;
    font-size: 1.25rem;
    line-height: 1.5;
    border-radius: .3rem
}

select.form-control[multiple], select.form-control[size] {
    height: auto
}

textarea.form-control {
    height: auto
}

.form-group {
    margin-bottom: 1rem
}

.form-text {
    display: block;
    margin-top: .25rem
}

.form-row {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    margin-right: -5px;
    margin-left: -5px
}

.form-row > .col, .form-row > [class*=col-] {
    padding-right: 5px;
    padding-left: 5px
}

.form-check {
    position: relative;
    display: block;
    padding-left: 1.25rem
}

.form-check-input {
    position: absolute;
    margin-top: .3rem;
    margin-left: -1.25rem
}

.form-check-input:disabled ~ .form-check-label {
    color: #6c757d
}

.form-check-label {
    margin-bottom: 0
}

.form-check-inline {
    display: -ms-inline-flexbox;
    display: inline-flex;
    -ms-flex-align: center;
    align-items: center;
    padding-left: 0;
    margin-right: .75rem
}

.form-check-inline .form-check-input {
    position: static;
    margin-top: 0;
    margin-right: .3125rem;
    margin-left: 0
}

.valid-feedback {
    display: none;
    width: 100%;
    margin-top: .25rem;
    font-size: 80%;
    color: #28a745
}

.valid-tooltip {
    position: absolute;
    top: 100%;
    z-index: 5;
    display: none;
    max-width: 100%;
    padding: .25rem .5rem;
    margin-top: .1rem;
    font-size: .875rem;
    line-height: 1.5;
    color: #fff;
    background-color: rgba(40, 167, 69, .9);
    border-radius: .25rem
}

.form-control.is-valid, .was-validated .form-control:valid {
    border-color: #28a745;
    padding-right: calc(1.5em + .75rem);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: center right calc(.375em + .1875rem);
    background-size: calc(.75em + .375rem) calc(.75em + .375rem)
}

.form-control.is-valid:focus, .was-validated .form-control:valid:focus {
    border-color: #28a745;
    box-shadow: 0 0 0 .2rem rgba(40, 167, 69, .25)
}

.form-control.is-valid ~ .valid-feedback, .form-control.is-valid ~ .valid-tooltip, .was-validated .form-control:valid ~ .valid-feedback, .was-validated .form-control:valid ~ .valid-tooltip {
    display: block
}

.was-validated textarea.form-control:valid, textarea.form-control.is-valid {
    padding-right: calc(1.5em + .75rem);
    background-position: top calc(.375em + .1875rem) right calc(.375em + .1875rem)
}

.custom-select.is-valid, .was-validated .custom-select:valid {
    border-color: #28a745;
    padding-right: calc((1em + .75rem) * 3 / 4 + 1.75rem);
    background: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'%3e%3cpath fill='%23343a40' d='M2 0L0 2h4zm0 5L0 3h4z'/%3e%3c/svg%3e") no-repeat right .75rem center/8px 10px, url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e") #fff no-repeat center right 1.75rem/calc(.75em + .375rem) calc(.75em + .375rem)
}

.custom-select.is-valid:focus, .was-validated .custom-select:valid:focus {
    border-color: #28a745;
    box-shadow: 0 0 0 .2rem rgba(40, 167, 69, .25)
}

.custom-select.is-valid ~ .valid-feedback, .custom-select.is-valid ~ .valid-tooltip, .was-validated .custom-select:valid ~ .valid-feedback, .was-validated .custom-select:valid ~ .valid-tooltip {
    display: block
}

.form-control-file.is-valid ~ .valid-feedback, .form-control-file.is-valid ~ .valid-tooltip, .was-validated .form-control-file:valid ~ .valid-feedback, .was-validated .form-control-file:valid ~ .valid-tooltip {
    display: block
}

.form-check-input.is-valid ~ .form-check-label, .was-validated .form-check-input:valid ~ .form-check-label {
    color: #28a745
}

.form-check-input.is-valid ~ .valid-feedback, .form-check-input.is-valid ~ .valid-tooltip, .was-validated .form-check-input:valid ~ .valid-feedback, .was-validated .form-check-input:valid ~ .valid-tooltip {
    display: block
}

.custom-control-input.is-valid ~ .custom-control-label, .was-validated .custom-control-input:valid ~ .custom-control-label {
    color: #28a745
}

.custom-control-input.is-valid ~ .custom-control-label::before, .was-validated .custom-control-input:valid ~ .custom-control-label::before {
    border-color: #28a745
}

.custom-control-input.is-valid ~ .valid-feedback, .custom-control-input.is-valid ~ .valid-tooltip, .was-validated .custom-control-input:valid ~ .valid-feedback, .was-validated .custom-control-input:valid ~ .valid-tooltip {
    display: block
}

.custom-control-input.is-valid:checked ~ .custom-control-label::before, .was-validated .custom-control-input:valid:checked ~ .custom-control-label::before {
    border-color: #34ce57;
    background-color: #34ce57
}

.custom-control-input.is-valid:focus ~ .custom-control-label::before, .was-validated .custom-control-input:valid:focus ~ .custom-control-label::before {
    box-shadow: 0 0 0 .2rem rgba(40, 167, 69, .25)
}

.custom-control-input.is-valid:focus:not(:checked) ~ .custom-control-label::before, .was-validated .custom-control-input:valid:focus:not(:checked) ~ .custom-control-label::before {
    border-color: #28a745
}

.custom-file-input.is-valid ~ .custom-file-label, .was-validated .custom-file-input:valid ~ .custom-file-label {
    border-color: #28a745
}

.custom-file-input.is-valid ~ .valid-feedback, .custom-file-input.is-valid ~ .valid-tooltip, .was-validated .custom-file-input:valid ~ .valid-feedback, .was-validated .custom-file-input:valid ~ .valid-tooltip {
    display: block
}

.custom-file-input.is-valid:focus ~ .custom-file-label, .was-validated .custom-file-input:valid:focus ~ .custom-file-label {
    border-color: #28a745;
    box-shadow: 0 0 0 .2rem rgba(40, 167, 69, .25)
}

.invalid-feedback {
    display: none;
    width: 100%;
    margin-top: .25rem;
    font-size: 80%;
    color: #dc3545
}

.invalid-tooltip {
    position: absolute;
    top: 100%;
    z-index: 5;
    display: none;
    max-width: 100%;
    padding: .25rem .5rem;
    margin-top: .1rem;
    font-size: .875rem;
    line-height: 1.5;
    color: #fff;
    background-color: rgba(220, 53, 69, .9);
    border-radius: .25rem
}

.form-control.is-invalid, .was-validated .form-control:invalid {
    border-color: #dc3545;
    padding-right: calc(1.5em + .75rem);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23dc3545' viewBox='-2 -2 7 7'%3e%3cpath stroke='%23dc3545' d='M0 0l3 3m0-3L0 3'/%3e%3ccircle r='.5'/%3e%3ccircle cx='3' r='.5'/%3e%3ccircle cy='3' r='.5'/%3e%3ccircle cx='3' cy='3' r='.5'/%3e%3c/svg%3E");
    background-repeat: no-repeat;
    background-position: center right calc(.375em + .1875rem);
    background-size: calc(.75em + .375rem) calc(.75em + .375rem)
}

.form-control.is-invalid:focus, .was-validated .form-control:invalid:focus {
    border-color: #dc3545;
    box-shadow: 0 0 0 .2rem rgba(220, 53, 69, .25)
}

.form-control.is-invalid ~ .invalid-feedback, .form-control.is-invalid ~ .invalid-tooltip, .was-validated .form-control:invalid ~ .invalid-feedback, .was-validated .form-control:invalid ~ .invalid-tooltip {
    display: block
}

.was-validated textarea.form-control:invalid, textarea.form-control.is-invalid {
    padding-right: calc(1.5em + .75rem);
    background-position: top calc(.375em + .1875rem) right calc(.375em + .1875rem)
}

.custom-select.is-invalid, .was-validated .custom-select:invalid {
    border-color: #dc3545;
    padding-right: calc((1em + .75rem) * 3 / 4 + 1.75rem);
    background: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'%3e%3cpath fill='%23343a40' d='M2 0L0 2h4zm0 5L0 3h4z'/%3e%3c/svg%3e") no-repeat right .75rem center/8px 10px, url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23dc3545' viewBox='-2 -2 7 7'%3e%3cpath stroke='%23dc3545' d='M0 0l3 3m0-3L0 3'/%3e%3ccircle r='.5'/%3e%3ccircle cx='3' r='.5'/%3e%3ccircle cy='3' r='.5'/%3e%3ccircle cx='3' cy='3' r='.5'/%3e%3c/svg%3E") #fff no-repeat center right 1.75rem/calc(.75em + .375rem) calc(.75em + .375rem)
}

.custom-select.is-invalid:focus, .was-validated .custom-select:invalid:focus {
    border-color: #dc3545;
    box-shadow: 0 0 0 .2rem rgba(220, 53, 69, .25)
}

.custom-select.is-invalid ~ .invalid-feedback, .custom-select.is-invalid ~ .invalid-tooltip, .was-validated .custom-select:invalid ~ .invalid-feedback, .was-validated .custom-select:invalid ~ .invalid-tooltip {
    display: block
}

.form-control-file.is-invalid ~ .invalid-feedback, .form-control-file.is-invalid ~ .invalid-tooltip, .was-validated .form-control-file:invalid ~ .invalid-feedback, .was-validated .form-control-file:invalid ~ .invalid-tooltip {
    display: block
}

.form-check-input.is-invalid ~ .form-check-label, .was-validated .form-check-input:invalid ~ .form-check-label {
    color: #dc3545
}

.form-check-input.is-invalid ~ .invalid-feedback, .form-check-input.is-invalid ~ .invalid-tooltip, .was-validated .form-check-input:invalid ~ .invalid-feedback, .was-validated .form-check-input:invalid ~ .invalid-tooltip {
    display: block
}

.custom-control-input.is-invalid ~ .custom-control-label, .was-validated .custom-control-input:invalid ~ .custom-control-label {
    color: #dc3545
}

.custom-control-input.is-invalid ~ .custom-control-label::before, .was-validated .custom-control-input:invalid ~ .custom-control-label::before {
    border-color: #dc3545
}

.custom-control-input.is-invalid ~ .invalid-feedback, .custom-control-input.is-invalid ~ .invalid-tooltip, .was-validated .custom-control-input:invalid ~ .invalid-feedback, .was-validated .custom-control-input:invalid ~ .invalid-tooltip {
    display: block
}

.custom-control-input.is-invalid:checked ~ .custom-control-label::before, .was-validated .custom-control-input:invalid:checked ~ .custom-control-label::before {
    border-color: #e4606d;
    background-color: #e4606d
}

.custom-control-input.is-invalid:focus ~ .custom-control-label::before, .was-validated .custom-control-input:invalid:focus ~ .custom-control-label::before {
    box-shadow: 0 0 0 .2rem rgba(220, 53, 69, .25)
}

.custom-control-input.is-invalid:focus:not(:checked) ~ .custom-control-label::before, .was-validated .custom-control-input:invalid:focus:not(:checked) ~ .custom-control-label::before {
    border-color: #dc3545
}

.custom-file-input.is-invalid ~ .custom-file-label, .was-validated .custom-file-input:invalid ~ .custom-file-label {
    border-color: #dc3545
}

.custom-file-input.is-invalid ~ .invalid-feedback, .custom-file-input.is-invalid ~ .invalid-tooltip, .was-validated .custom-file-input:invalid ~ .invalid-feedback, .was-validated .custom-file-input:invalid ~ .invalid-tooltip {
    display: block
}

.custom-file-input.is-invalid:focus ~ .custom-file-label, .was-validated .custom-file-input:invalid:focus ~ .custom-file-label {
    border-color: #dc3545;
    box-shadow: 0 0 0 .2rem rgba(220, 53, 69, .25)
}

.form-inline {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-flow: row wrap;
    flex-flow: row wrap;
    -ms-flex-align: center;
    align-items: center
}

.form-inline .form-check {
    width: 100%
}

@media (min-width: 576px) {
    .form-inline label {
        display: -ms-flexbox;
        display: flex;
        -ms-flex-align: center;
        align-items: center;
        -ms-flex-pack: center;
        justify-content: center;
        margin-bottom: 0
    }

    .form-inline .form-group {
        display: -ms-flexbox;
        display: flex;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        -ms-flex-flow: row wrap;
        flex-flow: row wrap;
        -ms-flex-align: center;
        align-items: center;
        margin-bottom: 0
    }

    .form-inline .form-control {
        display: inline-block;
        width: auto;
        vertical-align: middle
    }

    .form-inline .form-control-plaintext {
        display: inline-block
    }

    .form-inline .custom-select, .form-inline .input-group {
        width: auto
    }

    .form-inline .form-check {
        display: -ms-flexbox;
        display: flex;
        -ms-flex-align: center;
        align-items: center;
        -ms-flex-pack: center;
        justify-content: center;
        width: auto;
        padding-left: 0
    }

    .form-inline .form-check-input {
        position: relative;
        -ms-flex-negative: 0;
        flex-shrink: 0;
        margin-top: 0;
        margin-right: .25rem;
        margin-left: 0
    }

    .form-inline .custom-control {
        -ms-flex-align: center;
        align-items: center;
        -ms-flex-pack: center;
        justify-content: center
    }

    .form-inline .custom-control-label {
        margin-bottom: 0
    }
}

.btn {
    display: inline-block;
    font-weight: 400;
    color: #212529;
    text-align: center;
    vertical-align: middle;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    background-color: transparent;
    border: 1px solid transparent;
    padding: .375rem .75rem;
    font-size: 1rem;
    line-height: 1.5;
    border-radius: .25rem;
    transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out
}

@media (prefers-reduced-motion: reduce) {
    .btn {
        transition: none
    }
}

.btn:hover {
    color: #212529;
    text-decoration: none
}

.btn.focus, .btn:focus {
    outline: 0;
    box-shadow: 0 0 0 .2rem rgba(0, 123, 255, .25)
}

.btn.disabled, .btn:disabled {
    opacity: .65
}

a.btn.disabled, fieldset:disabled a.btn {
    pointer-events: none
}

.btn-primary {
    color: #fff;
    background-color: #007bff;
    border-color: #007bff
}

.btn-primary:hover {
    color: #fff;
    background-color: #0069d9;
    border-color: #0062cc
}

.btn-primary.focus, .btn-primary:focus {
    box-shadow: 0 0 0 .2rem rgba(38, 143, 255, .5)
}

.btn-primary.disabled, .btn-primary:disabled {
    color: #fff;
    background-color: #007bff;
    border-color: #007bff
}

.btn-primary:not(:disabled):not(.disabled).active, .btn-primary:not(:disabled):not(.disabled):active, .show > .btn-primary.dropdown-toggle {
    color: #fff;
    background-color: #0062cc;
    border-color: #005cbf
}

.btn-primary:not(:disabled):not(.disabled).active:focus, .btn-primary:not(:disabled):not(.disabled):active:focus, .show > .btn-primary.dropdown-toggle:focus {
    box-shadow: 0 0 0 .2rem rgba(38, 143, 255, .5)
}

.btn-secondary {
    color: #fff;
    background-color: #6c757d;
    border-color: #6c757d
}

.btn-secondary:hover {
    color: #fff;
    background-color: #5a6268;
    border-color: #545b62
}

.btn-secondary.focus, .btn-secondary:focus {
    box-shadow: 0 0 0 .2rem rgba(130, 138, 145, .5)
}

.btn-secondary.disabled, .btn-secondary:disabled {
    color: #fff;
    background-color: #6c757d;
    border-color: #6c757d
}

.btn-secondary:not(:disabled):not(.disabled).active, .btn-secondary:not(:disabled):not(.disabled):active, .show > .btn-secondary.dropdown-toggle {
    color: #fff;
    background-color: #545b62;
    border-color: #4e555b
}

.btn-secondary:not(:disabled):not(.disabled).active:focus, .btn-secondary:not(:disabled):not(.disabled):active:focus, .show > .btn-secondary.dropdown-toggle:focus {
    box-shadow: 0 0 0 .2rem rgba(130, 138, 145, .5)
}

.btn-success {
    color: #fff;
    background-color: #28a745;
    border-color: #28a745
}

.btn-success:hover {
    color: #fff;
    background-color: #218838;
    border-color: #1e7e34
}

.btn-success.focus, .btn-success:focus {
    box-shadow: 0 0 0 .2rem rgba(72, 180, 97, .5)
}

.btn-success.disabled, .btn-success:disabled {
    color: #fff;
    background-color: #28a745;
    border-color: #28a745
}

.btn-success:not(:disabled):not(.disabled).active, .btn-success:not(:disabled):not(.disabled):active, .show > .btn-success.dropdown-toggle {
    color: #fff;
    background-color: #1e7e34;
    border-color: #1c7430
}

.btn-success:not(:disabled):not(.disabled).active:focus, .btn-success:not(:disabled):not(.disabled):active:focus, .show > .btn-success.dropdown-toggle:focus {
    box-shadow: 0 0 0 .2rem rgba(72, 180, 97, .5)
}

.btn-info {
    color: #fff;
    background-color: #17a2b8;
    border-color: #17a2b8
}

.btn-info:hover {
    color: #fff;
    background-color: #138496;
    border-color: #117a8b
}

.btn-info.focus, .btn-info:focus {
    box-shadow: 0 0 0 .2rem rgba(58, 176, 195, .5)
}

.btn-info.disabled, .btn-info:disabled {
    color: #fff;
    background-color: #17a2b8;
    border-color: #17a2b8
}

.btn-info:not(:disabled):not(.disabled).active, .btn-info:not(:disabled):not(.disabled):active, .show > .btn-info.dropdown-toggle {
    color: #fff;
    background-color: #117a8b;
    border-color: #10707f
}

.btn-info:not(:disabled):not(.disabled).active:focus, .btn-info:not(:disabled):not(.disabled):active:focus, .show > .btn-info.dropdown-toggle:focus {
    box-shadow: 0 0 0 .2rem rgba(58, 176, 195, .5)
}

.btn-warning {
    color: #212529;
    background-color: #ffc107;
    border-color: #ffc107
}

.btn-warning:hover {
    color: #212529;
    background-color: #e0a800;
    border-color: #d39e00
}

.btn-warning.focus, .btn-warning:focus {
    box-shadow: 0 0 0 .2rem rgba(222, 170, 12, .5)
}

.btn-warning.disabled, .btn-warning:disabled {
    color: #212529;
    background-color: #ffc107;
    border-color: #ffc107
}

.btn-warning:not(:disabled):not(.disabled).active, .btn-warning:not(:disabled):not(.disabled):active, .show > .btn-warning.dropdown-toggle {
    color: #212529;
    background-color: #d39e00;
    border-color: #c69500
}

.btn-warning:not(:disabled):not(.disabled).active:focus, .btn-warning:not(:disabled):not(.disabled):active:focus, .show > .btn-warning.dropdown-toggle:focus {
    box-shadow: 0 0 0 .2rem rgba(222, 170, 12, .5)
}

.btn-danger {
    color: #fff;
    background-color: #dc3545;
    border-color: #dc3545
}

.btn-danger:hover {
    color: #fff;
    background-color: #c82333;
    border-color: #bd2130
}

.btn-danger.focus, .btn-danger:focus {
    box-shadow: 0 0 0 .2rem rgba(225, 83, 97, .5)
}

.btn-danger.disabled, .btn-danger:disabled {
    color: #fff;
    background-color: #dc3545;
    border-color: #dc3545
}

.btn-danger:not(:disabled):not(.disabled).active, .btn-danger:not(:disabled):not(.disabled):active, .show > .btn-danger.dropdown-toggle {
    color: #fff;
    background-color: #bd2130;
    border-color: #b21f2d
}

.btn-danger:not(:disabled):not(.disabled).active:focus, .btn-danger:not(:disabled):not(.disabled):active:focus, .show > .btn-danger.dropdown-toggle:focus {
    box-shadow: 0 0 0 .2rem rgba(225, 83, 97, .5)
}

.btn-light {
    color: #212529;
    background-color: #f8f9fa;
    border-color: #f8f9fa
}

.btn-light:hover {
    color: #212529;
    background-color: #e2e6ea;
    border-color: #dae0e5
}

.btn-light.focus, .btn-light:focus {
    box-shadow: 0 0 0 .2rem rgba(216, 217, 219, .5)
}

.btn-light.disabled, .btn-light:disabled {
    color: #212529;
    background-color: #f8f9fa;
    border-color: #f8f9fa
}

.btn-light:not(:disabled):not(.disabled).active, .btn-light:not(:disabled):not(.disabled):active, .show > .btn-light.dropdown-toggle {
    color: #212529;
    background-color: #dae0e5;
    border-color: #d3d9df
}

.btn-light:not(:disabled):not(.disabled).active:focus, .btn-light:not(:disabled):not(.disabled):active:focus, .show > .btn-light.dropdown-toggle:focus {
    box-shadow: 0 0 0 .2rem rgba(216, 217, 219, .5)
}

.btn-dark {
    color: #fff;
    background-color: #343a40;
    border-color: #343a40
}

.btn-dark:hover {
    color: #fff;
    background-color: #23272b;
    border-color: #1d2124
}

.btn-dark.focus, .btn-dark:focus {
    box-shadow: 0 0 0 .2rem rgba(82, 88, 93, .5)
}

.btn-dark.disabled, .btn-dark:disabled {
    color: #fff;
    background-color: #343a40;
    border-color: #343a40
}

.btn-dark:not(:disabled):not(.disabled).active, .btn-dark:not(:disabled):not(.disabled):active, .show > .btn-dark.dropdown-toggle {
    color: #fff;
    background-color: #1d2124;
    border-color: #171a1d
}

.btn-dark:not(:disabled):not(.disabled).active:focus, .btn-dark:not(:disabled):not(.disabled):active:focus, .show > .btn-dark.dropdown-toggle:focus {
    box-shadow: 0 0 0 .2rem rgba(82, 88, 93, .5)
}

.btn-outline-primary {
    color: #007bff;
    border-color: #007bff
}

.btn-outline-primary:hover {
    color: #fff;
    background-color: #007bff;
    border-color: #007bff
}

.btn-outline-primary.focus, .btn-outline-primary:focus {
    box-shadow: 0 0 0 .2rem rgba(0, 123, 255, .5)
}

.btn-outline-primary.disabled, .btn-outline-primary:disabled {
    color: #007bff;
    background-color: transparent
}

.btn-outline-primary:not(:disabled):not(.disabled).active, .btn-outline-primary:not(:disabled):not(.disabled):active, .show > .btn-outline-primary.dropdown-toggle {
    color: #fff;
    background-color: #007bff;
    border-color: #007bff
}

.btn-outline-primary:not(:disabled):not(.disabled).active:focus, .btn-outline-primary:not(:disabled):not(.disabled):active:focus, .show > .btn-outline-primary.dropdown-toggle:focus {
    box-shadow: 0 0 0 .2rem rgba(0, 123, 255, .5)
}

.btn-outline-secondary {
    color: #6c757d;
    border-color: #6c757d
}

.btn-outline-secondary:hover {
    color: #fff;
    background-color: #6c757d;
    border-color: #6c757d
}

.btn-outline-secondary.focus, .btn-outline-secondary:focus {
    box-shadow: 0 0 0 .2rem rgba(108, 117, 125, .5)
}

.btn-outline-secondary.disabled, .btn-outline-secondary:disabled {
    color: #6c757d;
    background-color: transparent
}

.btn-outline-secondary:not(:disabled):not(.disabled).active, .btn-outline-secondary:not(:disabled):not(.disabled):active, .show > .btn-outline-secondary.dropdown-toggle {
    color: #fff;
    background-color: #6c757d;
    border-color: #6c757d
}

.btn-outline-secondary:not(:disabled):not(.disabled).active:focus, .btn-outline-secondary:not(:disabled):not(.disabled):active:focus, .show > .btn-outline-secondary.dropdown-toggle:focus {
    box-shadow: 0 0 0 .2rem rgba(108, 117, 125, .5)
}

.btn-outline-success {
    color: #28a745;
    border-color: #28a745
}

.btn-outline-success:hover {
    color: #fff;
    background-color: #28a745;
    border-color: #28a745
}

.btn-outline-success.focus, .btn-outline-success:focus {
    box-shadow: 0 0 0 .2rem rgba(40, 167, 69, .5)
}

.btn-outline-success.disabled, .btn-outline-success:disabled {
    color: #28a745;
    background-color: transparent
}

.btn-outline-success:not(:disabled):not(.disabled).active, .btn-outline-success:not(:disabled):not(.disabled):active, .show > .btn-outline-success.dropdown-toggle {
    color: #fff;
    background-color: #28a745;
    border-color: #28a745
}

.btn-outline-success:not(:disabled):not(.disabled).active:focus, .btn-outline-success:not(:disabled):not(.disabled):active:focus, .show > .btn-outline-success.dropdown-toggle:focus {
    box-shadow: 0 0 0 .2rem rgba(40, 167, 69, .5)
}

.btn-outline-info {
    color: #17a2b8;
    border-color: #17a2b8
}

.btn-outline-info:hover {
    color: #fff;
    background-color: #17a2b8;
    border-color: #17a2b8
}

.btn-outline-info.focus, .btn-outline-info:focus {
    box-shadow: 0 0 0 .2rem rgba(23, 162, 184, .5)
}

.btn-outline-info.disabled, .btn-outline-info:disabled {
    color: #17a2b8;
    background-color: transparent
}

.btn-outline-info:not(:disabled):not(.disabled).active, .btn-outline-info:not(:disabled):not(.disabled):active, .show > .btn-outline-info.dropdown-toggle {
    color: #fff;
    background-color: #17a2b8;
    border-color: #17a2b8
}

.btn-outline-info:not(:disabled):not(.disabled).active:focus, .btn-outline-info:not(:disabled):not(.disabled):active:focus, .show > .btn-outline-info.dropdown-toggle:focus {
    box-shadow: 0 0 0 .2rem rgba(23, 162, 184, .5)
}

.btn-outline-warning {
    color: #ffc107;
    border-color: #ffc107
}

.btn-outline-warning:hover {
    color: #212529;
    background-color: #ffc107;
    border-color: #ffc107
}

.btn-outline-warning.focus, .btn-outline-warning:focus {
    box-shadow: 0 0 0 .2rem rgba(255, 193, 7, .5)
}

.btn-outline-warning.disabled, .btn-outline-warning:disabled {
    color: #ffc107;
    background-color: transparent
}

.btn-outline-warning:not(:disabled):not(.disabled).active, .btn-outline-warning:not(:disabled):not(.disabled):active, .show > .btn-outline-warning.dropdown-toggle {
    color: #212529;
    background-color: #ffc107;
    border-color: #ffc107
}

.btn-outline-warning:not(:disabled):not(.disabled).active:focus, .btn-outline-warning:not(:disabled):not(.disabled):active:focus, .show > .btn-outline-warning.dropdown-toggle:focus {
    box-shadow: 0 0 0 .2rem rgba(255, 193, 7, .5)
}

.btn-outline-danger {
    color: #dc3545;
    border-color: #dc3545
}

.btn-outline-danger:hover {
    color: #fff;
    background-color: #dc3545;
    border-color: #dc3545
}

.btn-outline-danger.focus, .btn-outline-danger:focus {
    box-shadow: 0 0 0 .2rem rgba(220, 53, 69, .5)
}

.btn-outline-danger.disabled, .btn-outline-danger:disabled {
    color: #dc3545;
    background-color: transparent
}

.btn-outline-danger:not(:disabled):not(.disabled).active, .btn-outline-danger:not(:disabled):not(.disabled):active, .show > .btn-outline-danger.dropdown-toggle {
    color: #fff;
    background-color: #dc3545;
    border-color: #dc3545
}

.btn-outline-danger:not(:disabled):not(.disabled).active:focus, .btn-outline-danger:not(:disabled):not(.disabled):active:focus, .show > .btn-outline-danger.dropdown-toggle:focus {
    box-shadow: 0 0 0 .2rem rgba(220, 53, 69, .5)
}

.btn-outline-light {
    color: #f8f9fa;
    border-color: #f8f9fa
}

.btn-outline-light:hover {
    color: #212529;
    background-color: #f8f9fa;
    border-color: #f8f9fa
}

.btn-outline-light.focus, .btn-outline-light:focus {
    box-shadow: 0 0 0 .2rem rgba(248, 249, 250, .5)
}

.btn-outline-light.disabled, .btn-outline-light:disabled {
    color: #f8f9fa;
    background-color: transparent
}

.btn-outline-light:not(:disabled):not(.disabled).active, .btn-outline-light:not(:disabled):not(.disabled):active, .show > .btn-outline-light.dropdown-toggle {
    color: #212529;
    background-color: #f8f9fa;
    border-color: #f8f9fa
}

.btn-outline-light:not(:disabled):not(.disabled).active:focus, .btn-outline-light:not(:disabled):not(.disabled):active:focus, .show > .btn-outline-light.dropdown-toggle:focus {
    box-shadow: 0 0 0 .2rem rgba(248, 249, 250, .5)
}

.btn-outline-dark {
    color: #343a40;
    border-color: #343a40
}

.btn-outline-dark:hover {
    color: #fff;
    background-color: #343a40;
    border-color: #343a40
}

.btn-outline-dark.focus, .btn-outline-dark:focus {
    box-shadow: 0 0 0 .2rem rgba(52, 58, 64, .5)
}

.btn-outline-dark.disabled, .btn-outline-dark:disabled {
    color: #343a40;
    background-color: transparent
}

.btn-outline-dark:not(:disabled):not(.disabled).active, .btn-outline-dark:not(:disabled):not(.disabled):active, .show > .btn-outline-dark.dropdown-toggle {
    color: #fff;
    background-color: #343a40;
    border-color: #343a40
}

.btn-outline-dark:not(:disabled):not(.disabled).active:focus, .btn-outline-dark:not(:disabled):not(.disabled):active:focus, .show > .btn-outline-dark.dropdown-toggle:focus {
    box-shadow: 0 0 0 .2rem rgba(52, 58, 64, .5)
}

.btn-link {
    font-weight: 400;
    color: #007bff;
    text-decoration: none
}

.btn-link:hover {
    color: #0056b3;
    text-decoration: underline
}

.btn-link.focus, .btn-link:focus {
    text-decoration: underline;
    box-shadow: none
}

.btn-link.disabled, .btn-link:disabled {
    color: #6c757d;
    pointer-events: none
}

.btn-group-lg > .btn, .btn-lg {
    padding: .5rem 1rem;
    font-size: 1.25rem;
    line-height: 1.5;
    border-radius: .3rem
}

.btn-group-sm > .btn, .btn-sm {
    padding: .25rem .5rem;
    font-size: .875rem;
    line-height: 1.5;
    border-radius: .2rem
}

.btn-block {
    display: block;
    width: 100%
}

.btn-block + .btn-block {
    margin-top: .5rem
}

input[type=button].btn-block, input[type=reset].btn-block, input[type=submit].btn-block {
    width: 100%
}

.fade {
    transition: opacity .15s linear
}

@media (prefers-reduced-motion: reduce) {
    .fade {
        transition: none
    }
}

.fade:not(.show) {
    opacity: 0
}

.collapse:not(.show) {
    display: none
}

.collapsing {
    position: relative;
    height: 0;
    overflow: hidden;
    transition: height .35s ease
}

@media (prefers-reduced-motion: reduce) {
    .collapsing {
        transition: none
    }
}

.dropdown, .dropleft, .dropright, .dropup {
    position: relative
}

.dropdown-toggle {
    white-space: nowrap
}

.dropdown-toggle::after {
    display: inline-block;
    margin-left: .255em;
    vertical-align: .255em;
    content: "";
    border-top: .3em solid;
    border-right: .3em solid transparent;
    border-bottom: 0;
    border-left: .3em solid transparent
}

.dropdown-toggle:empty::after {
    margin-left: 0
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
    display: none;
    float: left;
    min-width: 10rem;
    padding: .5rem 0;
    margin: .125rem 0 0;
    font-size: 1rem;
    color: #212529;
    text-align: left;
    list-style: none;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid rgba(0, 0, 0, .15);
    border-radius: .25rem
}

.dropdown-menu-left {
    right: auto;
    left: 0
}

.dropdown-menu-right {
    right: 0;
    left: auto
}

@media (min-width: 576px) {
    .dropdown-menu-sm-left {
        right: auto;
        left: 0
    }

    .dropdown-menu-sm-right {
        right: 0;
        left: auto
    }
}

@media (min-width: 768px) {
    .dropdown-menu-md-left {
        right: auto;
        left: 0
    }

    .dropdown-menu-md-right {
        right: 0;
        left: auto
    }
}

@media (min-width: 992px) {
    .dropdown-menu-lg-left {
        right: auto;
        left: 0
    }

    .dropdown-menu-lg-right {
        right: 0;
        left: auto
    }
}

@media (min-width: 1200px) {
    .dropdown-menu-xl-left {
        right: auto;
        left: 0
    }

    .dropdown-menu-xl-right {
        right: 0;
        left: auto
    }
}

.dropup .dropdown-menu {
    top: auto;
    bottom: 100%;
    margin-top: 0;
    margin-bottom: .125rem
}

.dropup .dropdown-toggle::after {
    display: inline-block;
    margin-left: .255em;
    vertical-align: .255em;
    content: "";
    border-top: 0;
    border-right: .3em solid transparent;
    border-bottom: .3em solid;
    border-left: .3em solid transparent
}

.dropup .dropdown-toggle:empty::after {
    margin-left: 0
}

.dropright .dropdown-menu {
    top: 0;
    right: auto;
    left: 100%;
    margin-top: 0;
    margin-left: .125rem
}

.dropright .dropdown-toggle::after {
    display: inline-block;
    margin-left: .255em;
    vertical-align: .255em;
    content: "";
    border-top: .3em solid transparent;
    border-right: 0;
    border-bottom: .3em solid transparent;
    border-left: .3em solid
}

.dropright .dropdown-toggle:empty::after {
    margin-left: 0
}

.dropright .dropdown-toggle::after {
    vertical-align: 0
}

.dropleft .dropdown-menu {
    top: 0;
    right: 100%;
    left: auto;
    margin-top: 0;
    margin-right: .125rem
}

.dropleft .dropdown-toggle::after {
    display: inline-block;
    margin-left: .255em;
    vertical-align: .255em;
    content: ""
}

.dropleft .dropdown-toggle::after {
    display: none
}

.dropleft .dropdown-toggle::before {
    display: inline-block;
    margin-right: .255em;
    vertical-align: .255em;
    content: "";
    border-top: .3em solid transparent;
    border-right: .3em solid;
    border-bottom: .3em solid transparent
}

.dropleft .dropdown-toggle:empty::after {
    margin-left: 0
}

.dropleft .dropdown-toggle::before {
    vertical-align: 0
}

.dropdown-menu[x-placement^=bottom], .dropdown-menu[x-placement^=left], .dropdown-menu[x-placement^=right], .dropdown-menu[x-placement^=top] {
    right: auto;
    bottom: auto
}

.dropdown-divider {
    height: 0;
    margin: .5rem 0;
    overflow: hidden;
    border-top: 1px solid #e9ecef
}

.dropdown-item {
    display: block;
    width: 100%;
    padding: .25rem 1.5rem;
    clear: both;
    font-weight: 400;
    color: #212529;
    text-align: inherit;
    white-space: nowrap;
    background-color: transparent;
    border: 0
}

.dropdown-item:focus, .dropdown-item:hover {
    color: #16181b;
    text-decoration: none;
    background-color: #f8f9fa
}

.dropdown-item.active, .dropdown-item:active {
    color: #fff;
    text-decoration: none;
    background-color: #007bff
}

.dropdown-item.disabled, .dropdown-item:disabled {
    color: #6c757d;
    pointer-events: none;
    background-color: transparent
}

.dropdown-menu.show {
    display: block
}

.dropdown-header {
    display: block;
    padding: .5rem 1.5rem;
    margin-bottom: 0;
    font-size: .875rem;
    color: #6c757d;
    white-space: nowrap
}

.dropdown-item-text {
    display: block;
    padding: .25rem 1.5rem;
    color: #212529
}

.btn-group, .btn-group-vertical {
    position: relative;
    display: -ms-inline-flexbox;
    display: inline-flex;
    vertical-align: middle
}

.btn-group-vertical > .btn, .btn-group > .btn {
    position: relative;
    -ms-flex: 1 1 auto;
    flex: 1 1 auto
}

.btn-group-vertical > .btn:hover, .btn-group > .btn:hover {
    z-index: 1
}

.btn-group-vertical > .btn.active, .btn-group-vertical > .btn:active, .btn-group-vertical > .btn:focus, .btn-group > .btn.active, .btn-group > .btn:active, .btn-group > .btn:focus {
    z-index: 1
}

.btn-toolbar {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -ms-flex-pack: start;
    justify-content: flex-start
}

.btn-toolbar .input-group {
    width: auto
}

.btn-group > .btn-group:not(:first-child), .btn-group > .btn:not(:first-child) {
    margin-left: -1px
}

.btn-group > .btn-group:not(:last-child) > .btn, .btn-group > .btn:not(:last-child):not(.dropdown-toggle) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0
}

.btn-group > .btn-group:not(:first-child) > .btn, .btn-group > .btn:not(:first-child) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0
}

.dropdown-toggle-split {
    padding-right: .5625rem;
    padding-left: .5625rem
}

.dropdown-toggle-split::after, .dropright .dropdown-toggle-split::after, .dropup .dropdown-toggle-split::after {
    margin-left: 0
}

.dropleft .dropdown-toggle-split::before {
    margin-right: 0
}

.btn-group-sm > .btn + .dropdown-toggle-split, .btn-sm + .dropdown-toggle-split {
    padding-right: .375rem;
    padding-left: .375rem
}

.btn-group-lg > .btn + .dropdown-toggle-split, .btn-lg + .dropdown-toggle-split {
    padding-right: .75rem;
    padding-left: .75rem
}

.btn-group-vertical {
    -ms-flex-direction: column;
    flex-direction: column;
    -ms-flex-align: start;
    align-items: flex-start;
    -ms-flex-pack: center;
    justify-content: center
}

.btn-group-vertical > .btn, .btn-group-vertical > .btn-group {
    width: 100%
}

.btn-group-vertical > .btn-group:not(:first-child), .btn-group-vertical > .btn:not(:first-child) {
    margin-top: -1px
}

.btn-group-vertical > .btn-group:not(:last-child) > .btn, .btn-group-vertical > .btn:not(:last-child):not(.dropdown-toggle) {
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0
}

.btn-group-vertical > .btn-group:not(:first-child) > .btn, .btn-group-vertical > .btn:not(:first-child) {
    border-top-left-radius: 0;
    border-top-right-radius: 0
}

.btn-group-toggle > .btn, .btn-group-toggle > .btn-group > .btn {
    margin-bottom: 0
}

.btn-group-toggle > .btn input[type=checkbox], .btn-group-toggle > .btn input[type=radio], .btn-group-toggle > .btn-group > .btn input[type=checkbox], .btn-group-toggle > .btn-group > .btn input[type=radio] {
    position: absolute;
    clip: rect(0, 0, 0, 0);
    pointer-events: none
}

.input-group {
    position: relative;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -ms-flex-align: stretch;
    align-items: stretch;
    width: 100%
}

.input-group > .custom-file, .input-group > .custom-select, .input-group > .form-control, .input-group > .form-control-plaintext {
    position: relative;
    -ms-flex: 1 1 auto;
    flex: 1 1 auto;
    width: 1%;
    margin-bottom: 0
}

.input-group > .custom-file + .custom-file, .input-group > .custom-file + .custom-select, .input-group > .custom-file + .form-control, .input-group > .custom-select + .custom-file, .input-group > .custom-select + .custom-select, .input-group > .custom-select + .form-control, .input-group > .form-control + .custom-file, .input-group > .form-control + .custom-select, .input-group > .form-control + .form-control, .input-group > .form-control-plaintext + .custom-file, .input-group > .form-control-plaintext + .custom-select, .input-group > .form-control-plaintext + .form-control {
    margin-left: -1px
}

.input-group > .custom-file .custom-file-input:focus ~ .custom-file-label, .input-group > .custom-select:focus, .input-group > .form-control:focus {
    z-index: 3
}

.input-group > .custom-file .custom-file-input:focus {
    z-index: 4
}

.input-group > .custom-select:not(:last-child), .input-group > .form-control:not(:last-child) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0
}

.input-group > .custom-select:not(:first-child), .input-group > .form-control:not(:first-child) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0
}

.input-group > .custom-file {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: center;
    align-items: center
}

.input-group > .custom-file:not(:last-child) .custom-file-label, .input-group > .custom-file:not(:last-child) .custom-file-label::after {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0
}

.input-group > .custom-file:not(:first-child) .custom-file-label {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0
}

.input-group-append, .input-group-prepend {
    display: -ms-flexbox;
    display: flex
}

.input-group-append .btn, .input-group-prepend .btn {
    position: relative;
    z-index: 2
}

.input-group-append .btn:focus, .input-group-prepend .btn:focus {
    z-index: 3
}

.input-group-append .btn + .btn, .input-group-append .btn + .input-group-text, .input-group-append .input-group-text + .btn, .input-group-append .input-group-text + .input-group-text, .input-group-prepend .btn + .btn, .input-group-prepend .btn + .input-group-text, .input-group-prepend .input-group-text + .btn, .input-group-prepend .input-group-text + .input-group-text {
    margin-left: -1px
}

.input-group-prepend {
    margin-right: -1px
}

.input-group-append {
    margin-left: -1px
}

.input-group-text {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: center;
    align-items: center;
    padding: .375rem .75rem;
    margin-bottom: 0;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #495057;
    text-align: center;
    white-space: nowrap;
    background-color: #e9ecef;
    border: 1px solid #ced4da;
    border-radius: .25rem
}

.input-group-text input[type=checkbox], .input-group-text input[type=radio] {
    margin-top: 0
}

.input-group-lg > .custom-select, .input-group-lg > .form-control:not(textarea) {
    height: calc(1.5em + 1rem + 2px)
}

.input-group-lg > .custom-select, .input-group-lg > .form-control, .input-group-lg > .input-group-append > .btn, .input-group-lg > .input-group-append > .input-group-text, .input-group-lg > .input-group-prepend > .btn, .input-group-lg > .input-group-prepend > .input-group-text {
    padding: .5rem 1rem;
    font-size: 1.25rem;
    line-height: 1.5;
    border-radius: .3rem
}

.input-group-sm > .custom-select, .input-group-sm > .form-control:not(textarea) {
    height: calc(1.5em + .5rem + 2px)
}

.input-group-sm > .custom-select, .input-group-sm > .form-control, .input-group-sm > .input-group-append > .btn, .input-group-sm > .input-group-append > .input-group-text, .input-group-sm > .input-group-prepend > .btn, .input-group-sm > .input-group-prepend > .input-group-text {
    padding: .25rem .5rem;
    font-size: .875rem;
    line-height: 1.5;
    border-radius: .2rem
}

.input-group-lg > .custom-select, .input-group-sm > .custom-select {
    padding-right: 1.75rem
}

.input-group > .input-group-append:last-child > .btn:not(:last-child):not(.dropdown-toggle), .input-group > .input-group-append:last-child > .input-group-text:not(:last-child), .input-group > .input-group-append:not(:last-child) > .btn, .input-group > .input-group-append:not(:last-child) > .input-group-text, .input-group > .input-group-prepend > .btn, .input-group > .input-group-prepend > .input-group-text {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0
}

.input-group > .input-group-append > .btn, .input-group > .input-group-append > .input-group-text, .input-group > .input-group-prepend:first-child > .btn:not(:first-child), .input-group > .input-group-prepend:first-child > .input-group-text:not(:first-child), .input-group > .input-group-prepend:not(:first-child) > .btn, .input-group > .input-group-prepend:not(:first-child) > .input-group-text {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0
}

.custom-control {
    position: relative;
    display: block;
    min-height: 1.5rem;
    padding-left: 1.5rem
}

.custom-control-inline {
    display: -ms-inline-flexbox;
    display: inline-flex;
    margin-right: 1rem
}

.custom-control-input {
    position: absolute;
    z-index: -1;
    opacity: 0
}

.custom-control-input:checked ~ .custom-control-label::before {
    color: #fff;
    border-color: #007bff;
    background-color: #007bff
}

.custom-control-input:focus ~ .custom-control-label::before {
    box-shadow: 0 0 0 .2rem rgba(0, 123, 255, .25)
}

.custom-control-input:focus:not(:checked) ~ .custom-control-label::before {
    border-color: #80bdff
}

.custom-control-input:not(:disabled):active ~ .custom-control-label::before {
    color: #fff;
    background-color: #b3d7ff;
    border-color: #b3d7ff
}

.custom-control-input:disabled ~ .custom-control-label {
    color: #6c757d
}

.custom-control-input:disabled ~ .custom-control-label::before {
    background-color: #e9ecef
}

.custom-control-label {
    position: relative;
    margin-bottom: 0;
    vertical-align: top
}

.custom-control-label::before {
    position: absolute;
    top: .25rem;
    left: -1.5rem;
    display: block;
    width: 1rem;
    height: 1rem;
    pointer-events: none;
    content: "";
    background-color: #fff;
    border: #adb5bd solid 1px
}

.custom-control-label::after {
    position: absolute;
    top: .25rem;
    left: -1.5rem;
    display: block;
    width: 1rem;
    height: 1rem;
    content: "";
    background: no-repeat 50%/50% 50%
}

.custom-checkbox .custom-control-label::before {
    border-radius: .25rem
}

.custom-checkbox .custom-control-input:checked ~ .custom-control-label::after {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23fff' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3e%3c/svg%3e")
}

.custom-checkbox .custom-control-input:indeterminate ~ .custom-control-label::before {
    border-color: #007bff;
    background-color: #007bff
}

.custom-checkbox .custom-control-input:indeterminate ~ .custom-control-label::after {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 4'%3e%3cpath stroke='%23fff' d='M0 2h4'/%3e%3c/svg%3e")
}

.custom-checkbox .custom-control-input:disabled:checked ~ .custom-control-label::before {
    background-color: rgba(0, 123, 255, .5)
}

.custom-checkbox .custom-control-input:disabled:indeterminate ~ .custom-control-label::before {
    background-color: rgba(0, 123, 255, .5)
}

.custom-radio .custom-control-label::before {
    border-radius: 50%
}

.custom-radio .custom-control-input:checked ~ .custom-control-label::after {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23fff'/%3e%3c/svg%3e")
}

.custom-radio .custom-control-input:disabled:checked ~ .custom-control-label::before {
    background-color: rgba(0, 123, 255, .5)
}

.custom-switch {
    padding-left: 2.25rem
}

.custom-switch .custom-control-label::before {
    left: -2.25rem;
    width: 1.75rem;
    pointer-events: all;
    border-radius: .5rem
}

.custom-switch .custom-control-label::after {
    top: calc(.25rem + 2px);
    left: calc(-2.25rem + 2px);
    width: calc(1rem - 4px);
    height: calc(1rem - 4px);
    background-color: #adb5bd;
    border-radius: .5rem;
    transition: background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out, -webkit-transform .15s ease-in-out;
    transition: transform .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
    transition: transform .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out, -webkit-transform .15s ease-in-out
}

@media (prefers-reduced-motion: reduce) {
    .custom-switch .custom-control-label::after {
        transition: none
    }
}

.custom-switch .custom-control-input:checked ~ .custom-control-label::after {
    background-color: #fff;
    -webkit-transform: translateX(.75rem);
    transform: translateX(.75rem)
}

.custom-switch .custom-control-input:disabled:checked ~ .custom-control-label::before {
    background-color: rgba(0, 123, 255, .5)
}

.custom-select {
    display: inline-block;
    width: 100%;
    height: calc(1.5em + .75rem + 2px);
    padding: .375rem 1.75rem .375rem .75rem;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #495057;
    vertical-align: middle;
    background: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'%3e%3cpath fill='%23343a40' d='M2 0L0 2h4zm0 5L0 3h4z'/%3e%3c/svg%3e") no-repeat right .75rem center/8px 10px;
    background-color: #fff;
    border: 1px solid #ced4da;
    border-radius: .25rem;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none
}

.custom-select:focus {
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 .2rem rgba(0, 123, 255, .25)
}

.custom-select:focus::-ms-value {
    color: #495057;
    background-color: #fff
}

.custom-select[multiple], .custom-select[size]:not([size="1"]) {
    height: auto;
    padding-right: .75rem;
    background-image: none
}

.custom-select:disabled {
    color: #6c757d;
    background-color: #e9ecef
}

.custom-select::-ms-expand {
    display: none
}

.custom-select-sm {
    height: calc(1.5em + .5rem + 2px);
    padding-top: .25rem;
    padding-bottom: .25rem;
    padding-left: .5rem;
    font-size: .875rem
}

.custom-select-lg {
    height: calc(1.5em + 1rem + 2px);
    padding-top: .5rem;
    padding-bottom: .5rem;
    padding-left: 1rem;
    font-size: 1.25rem
}

.custom-file {
    position: relative;
    display: inline-block;
    width: 100%;
    height: calc(1.5em + .75rem + 2px);
    margin-bottom: 0
}

.custom-file-input {
    position: relative;
    z-index: 2;
    width: 100%;
    height: calc(1.5em + .75rem + 2px);
    margin: 0;
    opacity: 0
}

.custom-file-input:focus ~ .custom-file-label {
    border-color: #80bdff;
    box-shadow: 0 0 0 .2rem rgba(0, 123, 255, .25)
}

.custom-file-input:disabled ~ .custom-file-label {
    background-color: #e9ecef
}

.custom-file-input:lang(en) ~ .custom-file-label::after {
    content: "Browse"
}

.custom-file-input ~ .custom-file-label[data-browse]::after {
    content: attr(data-browse)
}

.custom-file-label {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    z-index: 1;
    height: calc(1.5em + .75rem + 2px);
    padding: .375rem .75rem;
    font-weight: 400;
    line-height: 1.5;
    color: #495057;
    background-color: #fff;
    border: 1px solid #ced4da;
    border-radius: .25rem
}

.custom-file-label::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    z-index: 3;
    display: block;
    height: calc(1.5em + .75rem);
    padding: .375rem .75rem;
    line-height: 1.5;
    color: #495057;
    content: "Browse";
    background-color: #e9ecef;
    border-left: inherit;
    border-radius: 0 .25rem .25rem 0
}

.custom-range {
    width: 100%;
    height: calc(1rem + .4rem);
    padding: 0;
    background-color: transparent;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none
}

.custom-range:focus {
    outline: 0
}

.custom-range:focus::-webkit-slider-thumb {
    box-shadow: 0 0 0 1px #fff, 0 0 0 .2rem rgba(0, 123, 255, .25)
}

.custom-range:focus::-moz-range-thumb {
    box-shadow: 0 0 0 1px #fff, 0 0 0 .2rem rgba(0, 123, 255, .25)
}

.custom-range:focus::-ms-thumb {
    box-shadow: 0 0 0 1px #fff, 0 0 0 .2rem rgba(0, 123, 255, .25)
}

.custom-range::-moz-focus-outer {
    border: 0
}

.custom-range::-webkit-slider-thumb {
    width: 1rem;
    height: 1rem;
    margin-top: -.25rem;
    background-color: #007bff;
    border: 0;
    border-radius: 1rem;
    transition: background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
    -webkit-appearance: none;
    appearance: none
}

@media (prefers-reduced-motion: reduce) {
    .custom-range::-webkit-slider-thumb {
        transition: none
    }
}

.custom-range::-webkit-slider-thumb:active {
    background-color: #b3d7ff
}

.custom-range::-webkit-slider-runnable-track {
    width: 100%;
    height: .5rem;
    color: transparent;
    cursor: pointer;
    background-color: #dee2e6;
    border-color: transparent;
    border-radius: 1rem
}

.custom-range::-moz-range-thumb {
    width: 1rem;
    height: 1rem;
    background-color: #007bff;
    border: 0;
    border-radius: 1rem;
    transition: background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
    -moz-appearance: none;
    appearance: none
}

@media (prefers-reduced-motion: reduce) {
    .custom-range::-moz-range-thumb {
        transition: none
    }
}

.custom-range::-moz-range-thumb:active {
    background-color: #b3d7ff
}

.custom-range::-moz-range-track {
    width: 100%;
    height: .5rem;
    color: transparent;
    cursor: pointer;
    background-color: #dee2e6;
    border-color: transparent;
    border-radius: 1rem
}

.custom-range::-ms-thumb {
    width: 1rem;
    height: 1rem;
    margin-top: 0;
    margin-right: .2rem;
    margin-left: .2rem;
    background-color: #007bff;
    border: 0;
    border-radius: 1rem;
    transition: background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
    appearance: none
}

@media (prefers-reduced-motion: reduce) {
    .custom-range::-ms-thumb {
        transition: none
    }
}

.custom-range::-ms-thumb:active {
    background-color: #b3d7ff
}

.custom-range::-ms-track {
    width: 100%;
    height: .5rem;
    color: transparent;
    cursor: pointer;
    background-color: transparent;
    border-color: transparent;
    border-width: .5rem
}

.custom-range::-ms-fill-lower {
    background-color: #dee2e6;
    border-radius: 1rem
}

.custom-range::-ms-fill-upper {
    margin-right: 15px;
    background-color: #dee2e6;
    border-radius: 1rem
}

.custom-range:disabled::-webkit-slider-thumb {
    background-color: #adb5bd
}

.custom-range:disabled::-webkit-slider-runnable-track {
    cursor: default
}

.custom-range:disabled::-moz-range-thumb {
    background-color: #adb5bd
}

.custom-range:disabled::-moz-range-track {
    cursor: default
}

.custom-range:disabled::-ms-thumb {
    background-color: #adb5bd
}

.custom-control-label::before, .custom-file-label, .custom-select {
    transition: background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out
}

@media (prefers-reduced-motion: reduce) {
    .custom-control-label::before, .custom-file-label, .custom-select {
        transition: none
    }
}

.nav {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    padding-left: 0;
    margin-bottom: 0;
    list-style: none
}

.nav-link {
    display: block;
    padding: .5rem 1rem
}

.nav-link:focus, .nav-link:hover {
    text-decoration: none
}

.nav-link.disabled {
    color: #6c757d;
    pointer-events: none;
    cursor: default
}

.nav-tabs {
    border-bottom: 1px solid #dee2e6
}

.nav-tabs .nav-item {
    margin-bottom: -1px
}

.nav-tabs .nav-link {
    border: 1px solid transparent;
    border-top-left-radius: .25rem;
    border-top-right-radius: .25rem
}

.nav-tabs .nav-link:focus, .nav-tabs .nav-link:hover {
    border-color: #e9ecef #e9ecef #dee2e6
}

.nav-tabs .nav-link.disabled {
    color: #6c757d;
    background-color: transparent;
    border-color: transparent
}

.nav-tabs .nav-item.show .nav-link, .nav-tabs .nav-link.active {
    color: #495057;
    background-color: #fff;
    border-color: #dee2e6 #dee2e6 #fff
}

.nav-tabs .dropdown-menu {
    margin-top: -1px;
    border-top-left-radius: 0;
    border-top-right-radius: 0
}

.nav-pills .nav-link {
    border-radius: .25rem
}

.nav-pills .nav-link.active, .nav-pills .show > .nav-link {
    color: #fff;
    background-color: #007bff
}

.nav-fill .nav-item {
    -ms-flex: 1 1 auto;
    flex: 1 1 auto;
    text-align: center
}

.nav-justified .nav-item {
    -ms-flex-preferred-size: 0;
    flex-basis: 0;
    -ms-flex-positive: 1;
    flex-grow: 1;
    text-align: center
}

.tab-content > .tab-pane {
    display: none
}

.tab-content > .active {
    display: block
}

.navbar {
    position: relative;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -ms-flex-align: center;
    align-items: center;
    -ms-flex-pack: justify;
    justify-content: space-between;
    padding: .5rem 1rem
}

.navbar > .container, .navbar > .container-fluid {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -ms-flex-align: center;
    align-items: center;
    -ms-flex-pack: justify;
    justify-content: space-between
}

.navbar-brand {
    display: inline-block;
    padding-top: .3125rem;
    padding-bottom: .3125rem;
    margin-right: 1rem;
    font-size: 1.25rem;
    line-height: inherit;
    white-space: nowrap
}

.navbar-brand:focus, .navbar-brand:hover {
    text-decoration: none
}

.navbar-nav {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: column;
    flex-direction: column;
    padding-left: 0;
    margin-bottom: 0;
    list-style: none
}

.navbar-nav .nav-link {
    padding-right: 0;
    padding-left: 0
}

.navbar-nav .dropdown-menu {
    position: static;
    float: none
}

.navbar-text {
    display: inline-block;
    padding-top: .5rem;
    padding-bottom: .5rem
}

.navbar-collapse {
    -ms-flex-preferred-size: 100%;
    flex-basis: 100%;
    -ms-flex-positive: 1;
    flex-grow: 1;
    -ms-flex-align: center;
    align-items: center
}

.navbar-toggler {
    padding: .25rem .75rem;
    font-size: 1.25rem;
    line-height: 1;
    background-color: transparent;
    border: 1px solid transparent;
    border-radius: .25rem
}

.navbar-toggler:focus, .navbar-toggler:hover {
    text-decoration: none
}

.navbar-toggler-icon {
    display: inline-block;
    width: 1.5em;
    height: 1.5em;
    vertical-align: middle;
    content: "";
    background: no-repeat center center;
    background-size: 100% 100%
}

@media (max-width: 575.98px) {
    .navbar-expand-sm > .container, .navbar-expand-sm > .container-fluid {
        padding-right: 0;
        padding-left: 0
    }
}

@media (min-width: 576px) {
    .navbar-expand-sm {
        -ms-flex-flow: row nowrap;
        flex-flow: row nowrap;
        -ms-flex-pack: start;
        justify-content: flex-start
    }

    .navbar-expand-sm .navbar-nav {
        -ms-flex-direction: row;
        flex-direction: row
    }

    .navbar-expand-sm .navbar-nav .dropdown-menu {
        position: absolute
    }

    .navbar-expand-sm .navbar-nav .nav-link {
        padding-right: .5rem;
        padding-left: .5rem
    }

    .navbar-expand-sm > .container, .navbar-expand-sm > .container-fluid {
        -ms-flex-wrap: nowrap;
        flex-wrap: nowrap
    }

    .navbar-expand-sm .navbar-collapse {
        display: -ms-flexbox !important;
        display: flex !important;
        -ms-flex-preferred-size: auto;
        flex-basis: auto
    }

    .navbar-expand-sm .navbar-toggler {
        display: none
    }
}

@media (max-width: 767.98px) {
    .navbar-expand-md > .container, .navbar-expand-md > .container-fluid {
        padding-right: 0;
        padding-left: 0
    }
}

@media (min-width: 768px) {
    .navbar-expand-md {
        -ms-flex-flow: row nowrap;
        flex-flow: row nowrap;
        -ms-flex-pack: start;
        justify-content: flex-start
    }

    .navbar-expand-md .navbar-nav {
        -ms-flex-direction: row;
        flex-direction: row
    }

    .navbar-expand-md .navbar-nav .dropdown-menu {
        position: absolute
    }

    .navbar-expand-md .navbar-nav .nav-link {
        padding-right: .5rem;
        padding-left: .5rem
    }

    .navbar-expand-md > .container, .navbar-expand-md > .container-fluid {
        -ms-flex-wrap: nowrap;
        flex-wrap: nowrap
    }

    .navbar-expand-md .navbar-collapse {
        display: -ms-flexbox !important;
        display: flex !important;
        -ms-flex-preferred-size: auto;
        flex-basis: auto
    }

    .navbar-expand-md .navbar-toggler {
        display: none
    }
}

@media (max-width: 991.98px) {
    .navbar-expand-lg > .container, .navbar-expand-lg > .container-fluid {
        padding-right: 0;
        padding-left: 0
    }
}

@media (min-width: 992px) {
    .navbar-expand-lg {
        -ms-flex-flow: row nowrap;
        flex-flow: row nowrap;
        -ms-flex-pack: start;
        justify-content: flex-start
    }

    .navbar-expand-lg .navbar-nav {
        -ms-flex-direction: row;
        flex-direction: row
    }

    .navbar-expand-lg .navbar-nav .dropdown-menu {
        position: absolute
    }

    .navbar-expand-lg .navbar-nav .nav-link {
        padding-right: .5rem;
        padding-left: .5rem
    }

    .navbar-expand-lg > .container, .navbar-expand-lg > .container-fluid {
        -ms-flex-wrap: nowrap;
        flex-wrap: nowrap
    }

    .navbar-expand-lg .navbar-collapse {
        display: -ms-flexbox !important;
        display: flex !important;
        -ms-flex-preferred-size: auto;
        flex-basis: auto
    }

    .navbar-expand-lg .navbar-toggler {
        display: none
    }
}

@media (max-width: 1199.98px) {
    .navbar-expand-xl > .container, .navbar-expand-xl > .container-fluid {
        padding-right: 0;
        padding-left: 0
    }
}

@media (min-width: 1200px) {
    .navbar-expand-xl {
        -ms-flex-flow: row nowrap;
        flex-flow: row nowrap;
        -ms-flex-pack: start;
        justify-content: flex-start
    }

    .navbar-expand-xl .navbar-nav {
        -ms-flex-direction: row;
        flex-direction: row
    }

    .navbar-expand-xl .navbar-nav .dropdown-menu {
        position: absolute
    }

    .navbar-expand-xl .navbar-nav .nav-link {
        padding-right: .5rem;
        padding-left: .5rem
    }

    .navbar-expand-xl > .container, .navbar-expand-xl > .container-fluid {
        -ms-flex-wrap: nowrap;
        flex-wrap: nowrap
    }

    .navbar-expand-xl .navbar-collapse {
        display: -ms-flexbox !important;
        display: flex !important;
        -ms-flex-preferred-size: auto;
        flex-basis: auto
    }

    .navbar-expand-xl .navbar-toggler {
        display: none
    }
}

.navbar-expand {
    -ms-flex-flow: row nowrap;
    flex-flow: row nowrap;
    -ms-flex-pack: start;
    justify-content: flex-start
}

.navbar-expand > .container, .navbar-expand > .container-fluid {
    padding-right: 0;
    padding-left: 0
}

.navbar-expand .navbar-nav {
    -ms-flex-direction: row;
    flex-direction: row
}

.navbar-expand .navbar-nav .dropdown-menu {
    position: absolute
}

.navbar-expand .navbar-nav .nav-link {
    padding-right: .5rem;
    padding-left: .5rem
}

.navbar-expand > .container, .navbar-expand > .container-fluid {
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap
}

.navbar-expand .navbar-collapse {
    display: -ms-flexbox !important;
    display: flex !important;
    -ms-flex-preferred-size: auto;
    flex-basis: auto
}

.navbar-expand .navbar-toggler {
    display: none
}

.navbar-light .navbar-brand {
    color: rgba(0, 0, 0, .9)
}

.navbar-light .navbar-brand:focus, .navbar-light .navbar-brand:hover {
    color: rgba(0, 0, 0, .9)
}

.navbar-light .navbar-nav .nav-link {
    color: rgba(0, 0, 0, .5)
}

.navbar-light .navbar-nav .nav-link:focus, .navbar-light .navbar-nav .nav-link:hover {
    color: rgba(0, 0, 0, .7)
}

.navbar-light .navbar-nav .nav-link.disabled {
    color: rgba(0, 0, 0, .3)
}

.navbar-light .navbar-nav .active > .nav-link, .navbar-light .navbar-nav .nav-link.active, .navbar-light .navbar-nav .nav-link.show, .navbar-light .navbar-nav .show > .nav-link {
    color: rgba(0, 0, 0, .9)
}

.navbar-light .navbar-toggler {
    color: rgba(0, 0, 0, .5);
    border-color: rgba(0, 0, 0, .1)
}

.navbar-light .navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3e%3cpath stroke='rgba(0, 0, 0, 0.5)' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e")
}

.navbar-light .navbar-text {
    color: rgba(0, 0, 0, .5)
}

.navbar-light .navbar-text a {
    color: rgba(0, 0, 0, .9)
}

.navbar-light .navbar-text a:focus, .navbar-light .navbar-text a:hover {
    color: rgba(0, 0, 0, .9)
}

.navbar-dark .navbar-brand {
    color: #fff
}

.navbar-dark .navbar-brand:focus, .navbar-dark .navbar-brand:hover {
    color: #fff
}

.navbar-dark .navbar-nav .nav-link {
    color: rgba(255, 255, 255, .5)
}

.navbar-dark .navbar-nav .nav-link:focus, .navbar-dark .navbar-nav .nav-link:hover {
    color: rgba(255, 255, 255, .75)
}

.navbar-dark .navbar-nav .nav-link.disabled {
    color: rgba(255, 255, 255, .25)
}

.navbar-dark .navbar-nav .active > .nav-link, .navbar-dark .navbar-nav .nav-link.active, .navbar-dark .navbar-nav .nav-link.show, .navbar-dark .navbar-nav .show > .nav-link {
    color: #fff
}

.navbar-dark .navbar-toggler {
    color: rgba(255, 255, 255, .5);
    border-color: rgba(255, 255, 255, .1)
}

.navbar-dark .navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3e%3cpath stroke='rgba(255, 255, 255, 0.5)' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e")
}

.navbar-dark .navbar-text {
    color: rgba(255, 255, 255, .5)
}

.navbar-dark .navbar-text a {
    color: #fff
}

.navbar-dark .navbar-text a:focus, .navbar-dark .navbar-text a:hover {
    color: #fff
}

.card {
    position: relative;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: column;
    flex-direction: column;
    min-width: 0;
    word-wrap: break-word;
    background-color: #fff;
    background-clip: border-box;
    border: 1px solid rgba(0, 0, 0, .125);
    border-radius: .25rem
}

.card > hr {
    margin-right: 0;
    margin-left: 0
}

.card > .list-group:first-child .list-group-item:first-child {
    border-top-left-radius: .25rem;
    border-top-right-radius: .25rem
}

.card > .list-group:last-child .list-group-item:last-child {
    border-bottom-right-radius: .25rem;
    border-bottom-left-radius: .25rem
}

.card-body {
    -ms-flex: 1 1 auto;
    flex: 1 1 auto;
    padding: 1.25rem
}

.card-title {
    margin-bottom: .75rem
}

.card-subtitle {
    margin-top: -.375rem;
    margin-bottom: 0
}

.card-text:last-child {
    margin-bottom: 0
}

.card-link:hover {
    text-decoration: none
}

.card-link + .card-link {
    margin-left: 1.25rem
}

.card-header {
    padding: .75rem 1.25rem;
    margin-bottom: 0;
    background-color: rgba(0, 0, 0, .03);
    border-bottom: 1px solid rgba(0, 0, 0, .125)
}

.card-header:first-child {
    border-radius: calc(.25rem - 1px) calc(.25rem - 1px) 0 0
}

.card-header + .list-group .list-group-item:first-child {
    border-top: 0
}

.card-footer {
    padding: .75rem 1.25rem;
    background-color: rgba(0, 0, 0, .03);
    border-top: 1px solid rgba(0, 0, 0, .125)
}

.card-footer:last-child {
    border-radius: 0 0 calc(.25rem - 1px) calc(.25rem - 1px)
}

.card-header-tabs {
    margin-right: -.625rem;
    margin-bottom: -.75rem;
    margin-left: -.625rem;
    border-bottom: 0
}

.card-header-pills {
    margin-right: -.625rem;
    margin-left: -.625rem
}

.card-img-overlay {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    padding: 1.25rem
}

.card-img {
    width: 100%;
    border-radius: calc(.25rem - 1px)
}

.card-img-top {
    width: 100%;
    border-top-left-radius: calc(.25rem - 1px);
    border-top-right-radius: calc(.25rem - 1px)
}

.card-img-bottom {
    width: 100%;
    border-bottom-right-radius: calc(.25rem - 1px);
    border-bottom-left-radius: calc(.25rem - 1px)
}

.card-deck {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: column;
    flex-direction: column
}

.card-deck .card {
    margin-bottom: 15px
}

@media (min-width: 576px) {
    .card-deck {
        -ms-flex-flow: row wrap;
        flex-flow: row wrap;
        margin-right: -15px;
        margin-left: -15px
    }

    .card-deck .card {
        display: -ms-flexbox;
        display: flex;
        -ms-flex: 1 0 0%;
        flex: 1 0 0%;
        -ms-flex-direction: column;
        flex-direction: column;
        margin-right: 15px;
        margin-bottom: 0;
        margin-left: 15px
    }
}

.card-group {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: column;
    flex-direction: column
}

.card-group > .card {
    margin-bottom: 15px
}

@media (min-width: 576px) {
    .card-group {
        -ms-flex-flow: row wrap;
        flex-flow: row wrap
    }

    .card-group > .card {
        -ms-flex: 1 0 0%;
        flex: 1 0 0%;
        margin-bottom: 0
    }

    .card-group > .card + .card {
        margin-left: 0;
        border-left: 0
    }

    .card-group > .card:not(:last-child) {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0
    }

    .card-group > .card:not(:last-child) .card-header, .card-group > .card:not(:last-child) .card-img-top {
        border-top-right-radius: 0
    }

    .card-group > .card:not(:last-child) .card-footer, .card-group > .card:not(:last-child) .card-img-bottom {
        border-bottom-right-radius: 0
    }

    .card-group > .card:not(:first-child) {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0
    }

    .card-group > .card:not(:first-child) .card-header, .card-group > .card:not(:first-child) .card-img-top {
        border-top-left-radius: 0
    }

    .card-group > .card:not(:first-child) .card-footer, .card-group > .card:not(:first-child) .card-img-bottom {
        border-bottom-left-radius: 0
    }
}

.card-columns .card {
    margin-bottom: .75rem
}

@media (min-width: 576px) {
    .card-columns {
        -webkit-column-count: 3;
        -moz-column-count: 3;
        column-count: 3;
        -webkit-column-gap: 1.25rem;
        -moz-column-gap: 1.25rem;
        column-gap: 1.25rem;
        orphans: 1;
        widows: 1
    }

    .card-columns .card {
        display: inline-block;
        width: 100%
    }
}

.accordion > .card {
    overflow: hidden
}

.accordion > .card:not(:first-of-type) .card-header:first-child {
    border-radius: 0
}

.accordion > .card:not(:first-of-type):not(:last-of-type) {
    border-bottom: 0;
    border-radius: 0
}

.accordion > .card:first-of-type {
    border-bottom: 0;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0
}

.accordion > .card:last-of-type {
    border-top-left-radius: 0;
    border-top-right-radius: 0
}

.accordion > .card .card-header {
    margin-bottom: -1px
}

.breadcrumb {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    padding: .75rem 1rem;
    margin-bottom: 1rem;
    list-style: none;
    background-color: #e9ecef;
    border-radius: .25rem
}

.breadcrumb-item + .breadcrumb-item {
    padding-left: .5rem
}

.breadcrumb-item + .breadcrumb-item::before {
    display: inline-block;
    padding-right: .5rem;
    color: #6c757d;
    content: "/"
}

.breadcrumb-item + .breadcrumb-item:hover::before {
    text-decoration: underline
}

.breadcrumb-item + .breadcrumb-item:hover::before {
    text-decoration: none
}

.breadcrumb-item.active {
    color: #6c757d
}

.pagination {
    display: -ms-flexbox;
    display: flex;
    padding-left: 0;
    list-style: none;
    border-radius: .25rem
}

.page-link {
    position: relative;
    display: block;
    padding: .5rem .75rem;
    margin-left: -1px;
    line-height: 1.25;
    color: #007bff;
    background-color: #fff;
    border: 1px solid #dee2e6
}

.page-link:hover {
    z-index: 2;
    color: #0056b3;
    text-decoration: none;
    background-color: #e9ecef;
    border-color: #dee2e6
}

.page-link:focus {
    z-index: 2;
    outline: 0;
    box-shadow: 0 0 0 .2rem rgba(0, 123, 255, .25)
}

.page-item:first-child .page-link {
    margin-left: 0;
    border-top-left-radius: .25rem;
    border-bottom-left-radius: .25rem
}

.page-item:last-child .page-link {
    border-top-right-radius: .25rem;
    border-bottom-right-radius: .25rem
}

.page-item.active .page-link {
    z-index: 1;
    color: #fff;
    background-color: #007bff;
    border-color: #007bff
}

.page-item.disabled .page-link {
    color: #6c757d;
    pointer-events: none;
    cursor: auto;
    background-color: #fff;
    border-color: #dee2e6
}

.pagination-lg .page-link {
    padding: .75rem 1.5rem;
    font-size: 1.25rem;
    line-height: 1.5
}

.pagination-lg .page-item:first-child .page-link {
    border-top-left-radius: .3rem;
    border-bottom-left-radius: .3rem
}

.pagination-lg .page-item:last-child .page-link {
    border-top-right-radius: .3rem;
    border-bottom-right-radius: .3rem
}

.pagination-sm .page-link {
    padding: .25rem .5rem;
    font-size: .875rem;
    line-height: 1.5
}

.pagination-sm .page-item:first-child .page-link {
    border-top-left-radius: .2rem;
    border-bottom-left-radius: .2rem
}

.pagination-sm .page-item:last-child .page-link {
    border-top-right-radius: .2rem;
    border-bottom-right-radius: .2rem
}

.badge {
    display: inline-block;
    padding: .25em .4em;
    font-size: 75%;
    font-weight: 700;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: .25rem;
    transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out
}

@media (prefers-reduced-motion: reduce) {
    .badge {
        transition: none
    }
}

a.badge:focus, a.badge:hover {
    text-decoration: none
}

.badge:empty {
    display: none
}

.btn .badge {
    position: relative;
    top: -1px
}

.badge-pill {
    padding-right: .6em;
    padding-left: .6em;
    border-radius: 10rem
}

.badge-primary {
    color: #fff;
    background-color: #007bff
}

a.badge-primary:focus, a.badge-primary:hover {
    color: #fff;
    background-color: #0062cc
}

a.badge-primary.focus, a.badge-primary:focus {
    outline: 0;
    box-shadow: 0 0 0 .2rem rgba(0, 123, 255, .5)
}

.badge-secondary {
    color: #fff;
    background-color: #6c757d
}

a.badge-secondary:focus, a.badge-secondary:hover {
    color: #fff;
    background-color: #545b62
}

a.badge-secondary.focus, a.badge-secondary:focus {
    outline: 0;
    box-shadow: 0 0 0 .2rem rgba(108, 117, 125, .5)
}

.badge-success {
    color: #fff;
    background-color: #28a745
}

a.badge-success:focus, a.badge-success:hover {
    color: #fff;
    background-color: #1e7e34
}

a.badge-success.focus, a.badge-success:focus {
    outline: 0;
    box-shadow: 0 0 0 .2rem rgba(40, 167, 69, .5)
}

.badge-info {
    color: #fff;
    background-color: #17a2b8
}

a.badge-info:focus, a.badge-info:hover {
    color: #fff;
    background-color: #117a8b
}

a.badge-info.focus, a.badge-info:focus {
    outline: 0;
    box-shadow: 0 0 0 .2rem rgba(23, 162, 184, .5)
}

.badge-warning {
    color: #212529;
    background-color: #ffc107
}

a.badge-warning:focus, a.badge-warning:hover {
    color: #212529;
    background-color: #d39e00
}

a.badge-warning.focus, a.badge-warning:focus {
    outline: 0;
    box-shadow: 0 0 0 .2rem rgba(255, 193, 7, .5)
}

.badge-danger {
    color: #fff;
    background-color: #dc3545
}

a.badge-danger:focus, a.badge-danger:hover {
    color: #fff;
    background-color: #bd2130
}

a.badge-danger.focus, a.badge-danger:focus {
    outline: 0;
    box-shadow: 0 0 0 .2rem rgba(220, 53, 69, .5)
}

.badge-light {
    color: #212529;
    background-color: #f8f9fa
}

a.badge-light:focus, a.badge-light:hover {
    color: #212529;
    background-color: #dae0e5
}

a.badge-light.focus, a.badge-light:focus {
    outline: 0;
    box-shadow: 0 0 0 .2rem rgba(248, 249, 250, .5)
}

.badge-dark {
    color: #fff;
    background-color: #343a40
}

a.badge-dark:focus, a.badge-dark:hover {
    color: #fff;
    background-color: #1d2124
}

a.badge-dark.focus, a.badge-dark:focus {
    outline: 0;
    box-shadow: 0 0 0 .2rem rgba(52, 58, 64, .5)
}

.jumbotron {
    padding: 2rem 1rem;
    margin-bottom: 2rem;
    background-color: #e9ecef;
    border-radius: .3rem
}

@media (min-width: 576px) {
    .jumbotron {
        padding: 4rem 2rem
    }
}

.jumbotron-fluid {
    padding-right: 0;
    padding-left: 0;
    border-radius: 0
}

.alert {
    position: relative;
    padding: .75rem 1.25rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
    border-radius: .25rem
}

.alert-heading {
    color: inherit
}

.alert-link {
    font-weight: 700
}

.alert-dismissible {
    padding-right: 4rem
}

.alert-dismissible .close {
    position: absolute;
    top: 0;
    right: 0;
    padding: .75rem 1.25rem;
    color: inherit
}

.alert-primary {
    color: #004085;
    background-color: #cce5ff;
    border-color: #b8daff
}

.alert-primary hr {
    border-top-color: #9fcdff
}

.alert-primary .alert-link {
    color: #002752
}

.alert-secondary {
    color: #383d41;
    background-color: #e2e3e5;
    border-color: #d6d8db
}

.alert-secondary hr {
    border-top-color: #c8cbcf
}

.alert-secondary .alert-link {
    color: #202326
}

.alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb
}

.alert-success hr {
    border-top-color: #b1dfbb
}

.alert-success .alert-link {
    color: #0b2e13
}

.alert-info {
    color: #0c5460;
    background-color: #d1ecf1;
    border-color: #bee5eb
}

.alert-info hr {
    border-top-color: #abdde5
}

.alert-info .alert-link {
    color: #062c33
}

.alert-warning {
    color: #856404;
    background-color: #fff3cd;
    border-color: #ffeeba
}

.alert-warning hr {
    border-top-color: #ffe8a1
}

.alert-warning .alert-link {
    color: #533f03
}

.alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb
}

.alert-danger hr {
    border-top-color: #f1b0b7
}

.alert-danger .alert-link {
    color: #491217
}

.alert-light {
    color: #818182;
    background-color: #fefefe;
    border-color: #fdfdfe
}

.alert-light hr {
    border-top-color: #ececf6
}

.alert-light .alert-link {
    color: #686868
}

.alert-dark {
    color: #1b1e21;
    background-color: #d6d8d9;
    border-color: #c6c8ca
}

.alert-dark hr {
    border-top-color: #b9bbbe
}

.alert-dark .alert-link {
    color: #040505
}

@-webkit-keyframes progress-bar-stripes {
    from {
        background-position: 1rem 0
    }
    to {
        background-position: 0 0
    }
}

@keyframes progress-bar-stripes {
    from {
        background-position: 1rem 0
    }
    to {
        background-position: 0 0
    }
}

.progress {
    display: -ms-flexbox;
    display: flex;
    height: 1rem;
    overflow: hidden;
    font-size: .75rem;
    background-color: #e9ecef;
    border-radius: .25rem
}

.progress-bar {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: column;
    flex-direction: column;
    -ms-flex-pack: center;
    justify-content: center;
    color: #fff;
    text-align: center;
    white-space: nowrap;
    background-color: #007bff;
    transition: width .6s ease
}

@media (prefers-reduced-motion: reduce) {
    .progress-bar {
        transition: none
    }
}

.progress-bar-striped {
    background-image: linear-gradient(45deg, rgba(255, 255, 255, .15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .15) 50%, rgba(255, 255, 255, .15) 75%, transparent 75%, transparent);
    background-size: 1rem 1rem
}

.progress-bar-animated {
    -webkit-animation: progress-bar-stripes 1s linear infinite;
    animation: progress-bar-stripes 1s linear infinite
}

@media (prefers-reduced-motion: reduce) {
    .progress-bar-animated {
        -webkit-animation: none;
        animation: none
    }
}

.media {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: start;
    align-items: flex-start
}

.media-body {
    -ms-flex: 1;
    flex: 1
}

.list-group {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: column;
    flex-direction: column;
    padding-left: 0;
    margin-bottom: 0
}

.list-group-item-action {
    width: 100%;
    color: #495057;
    text-align: inherit
}

.list-group-item-action:focus, .list-group-item-action:hover {
    z-index: 1;
    color: #495057;
    text-decoration: none;
    background-color: #f8f9fa
}

.list-group-item-action:active {
    color: #212529;
    background-color: #e9ecef
}

.list-group-item {
    position: relative;
    display: block;
    padding: .75rem 1.25rem;
    margin-bottom: -1px;
    background-color: #fff;
    border: 1px solid rgba(0, 0, 0, .125)
}

.list-group-item:first-child {
    border-top-left-radius: .25rem;
    border-top-right-radius: .25rem
}

.list-group-item:last-child {
    margin-bottom: 0;
    border-bottom-right-radius: .25rem;
    border-bottom-left-radius: .25rem
}

.list-group-item.disabled, .list-group-item:disabled {
    color: #6c757d;
    pointer-events: none;
    background-color: #fff
}

.list-group-item.active {
    z-index: 2;
    color: #fff;
    background-color: #007bff;
    border-color: #007bff
}

.list-group-horizontal {
    -ms-flex-direction: row;
    flex-direction: row
}

.list-group-horizontal .list-group-item {
    margin-right: -1px;
    margin-bottom: 0
}

.list-group-horizontal .list-group-item:first-child {
    border-top-left-radius: .25rem;
    border-bottom-left-radius: .25rem;
    border-top-right-radius: 0
}

.list-group-horizontal .list-group-item:last-child {
    margin-right: 0;
    border-top-right-radius: .25rem;
    border-bottom-right-radius: .25rem;
    border-bottom-left-radius: 0
}

@media (min-width: 576px) {
    .list-group-horizontal-sm {
        -ms-flex-direction: row;
        flex-direction: row
    }

    .list-group-horizontal-sm .list-group-item {
        margin-right: -1px;
        margin-bottom: 0
    }

    .list-group-horizontal-sm .list-group-item:first-child {
        border-top-left-radius: .25rem;
        border-bottom-left-radius: .25rem;
        border-top-right-radius: 0
    }

    .list-group-horizontal-sm .list-group-item:last-child {
        margin-right: 0;
        border-top-right-radius: .25rem;
        border-bottom-right-radius: .25rem;
        border-bottom-left-radius: 0
    }
}

@media (min-width: 768px) {
    .list-group-horizontal-md {
        -ms-flex-direction: row;
        flex-direction: row
    }

    .list-group-horizontal-md .list-group-item {
        margin-right: -1px;
        margin-bottom: 0
    }

    .list-group-horizontal-md .list-group-item:first-child {
        border-top-left-radius: .25rem;
        border-bottom-left-radius: .25rem;
        border-top-right-radius: 0
    }

    .list-group-horizontal-md .list-group-item:last-child {
        margin-right: 0;
        border-top-right-radius: .25rem;
        border-bottom-right-radius: .25rem;
        border-bottom-left-radius: 0
    }
}

@media (min-width: 992px) {
    .list-group-horizontal-lg {
        -ms-flex-direction: row;
        flex-direction: row
    }

    .list-group-horizontal-lg .list-group-item {
        margin-right: -1px;
        margin-bottom: 0
    }

    .list-group-horizontal-lg .list-group-item:first-child {
        border-top-left-radius: .25rem;
        border-bottom-left-radius: .25rem;
        border-top-right-radius: 0
    }

    .list-group-horizontal-lg .list-group-item:last-child {
        margin-right: 0;
        border-top-right-radius: .25rem;
        border-bottom-right-radius: .25rem;
        border-bottom-left-radius: 0
    }
}

@media (min-width: 1200px) {
    .list-group-horizontal-xl {
        -ms-flex-direction: row;
        flex-direction: row
    }

    .list-group-horizontal-xl .list-group-item {
        margin-right: -1px;
        margin-bottom: 0
    }

    .list-group-horizontal-xl .list-group-item:first-child {
        border-top-left-radius: .25rem;
        border-bottom-left-radius: .25rem;
        border-top-right-radius: 0
    }

    .list-group-horizontal-xl .list-group-item:last-child {
        margin-right: 0;
        border-top-right-radius: .25rem;
        border-bottom-right-radius: .25rem;
        border-bottom-left-radius: 0
    }
}

.list-group-flush .list-group-item {
    border-right: 0;
    border-left: 0;
    border-radius: 0
}

.list-group-flush .list-group-item:last-child {
    margin-bottom: -1px
}

.list-group-flush:first-child .list-group-item:first-child {
    border-top: 0
}

.list-group-flush:last-child .list-group-item:last-child {
    margin-bottom: 0;
    border-bottom: 0
}

.list-group-item-primary {
    color: #004085;
    background-color: #b8daff
}

.list-group-item-primary.list-group-item-action:focus, .list-group-item-primary.list-group-item-action:hover {
    color: #004085;
    background-color: #9fcdff
}

.list-group-item-primary.list-group-item-action.active {
    color: #fff;
    background-color: #004085;
    border-color: #004085
}

.list-group-item-secondary {
    color: #383d41;
    background-color: #d6d8db
}

.list-group-item-secondary.list-group-item-action:focus, .list-group-item-secondary.list-group-item-action:hover {
    color: #383d41;
    background-color: #c8cbcf
}

.list-group-item-secondary.list-group-item-action.active {
    color: #fff;
    background-color: #383d41;
    border-color: #383d41
}

.list-group-item-success {
    color: #155724;
    background-color: #c3e6cb
}

.list-group-item-success.list-group-item-action:focus, .list-group-item-success.list-group-item-action:hover {
    color: #155724;
    background-color: #b1dfbb
}

.list-group-item-success.list-group-item-action.active {
    color: #fff;
    background-color: #155724;
    border-color: #155724
}

.list-group-item-info {
    color: #0c5460;
    background-color: #bee5eb
}

.list-group-item-info.list-group-item-action:focus, .list-group-item-info.list-group-item-action:hover {
    color: #0c5460;
    background-color: #abdde5
}

.list-group-item-info.list-group-item-action.active {
    color: #fff;
    background-color: #0c5460;
    border-color: #0c5460
}

.list-group-item-warning {
    color: #856404;
    background-color: #ffeeba
}

.list-group-item-warning.list-group-item-action:focus, .list-group-item-warning.list-group-item-action:hover {
    color: #856404;
    background-color: #ffe8a1
}

.list-group-item-warning.list-group-item-action.active {
    color: #fff;
    background-color: #856404;
    border-color: #856404
}

.list-group-item-danger {
    color: #721c24;
    background-color: #f5c6cb
}

.list-group-item-danger.list-group-item-action:focus, .list-group-item-danger.list-group-item-action:hover {
    color: #721c24;
    background-color: #f1b0b7
}

.list-group-item-danger.list-group-item-action.active {
    color: #fff;
    background-color: #721c24;
    border-color: #721c24
}

.list-group-item-light {
    color: #818182;
    background-color: #fdfdfe
}

.list-group-item-light.list-group-item-action:focus, .list-group-item-light.list-group-item-action:hover {
    color: #818182;
    background-color: #ececf6
}

.list-group-item-light.list-group-item-action.active {
    color: #fff;
    background-color: #818182;
    border-color: #818182
}

.list-group-item-dark {
    color: #1b1e21;
    background-color: #c6c8ca
}

.list-group-item-dark.list-group-item-action:focus, .list-group-item-dark.list-group-item-action:hover {
    color: #1b1e21;
    background-color: #b9bbbe
}

.list-group-item-dark.list-group-item-action.active {
    color: #fff;
    background-color: #1b1e21;
    border-color: #1b1e21
}

.close {
    float: right;
    font-size: 1.5rem;
    font-weight: 700;
    line-height: 1;
    color: #000;
    text-shadow: 0 1px 0 #fff;
    opacity: .5
}

.close:hover {
    color: #000;
    text-decoration: none
}

.close:not(:disabled):not(.disabled):focus, .close:not(:disabled):not(.disabled):hover {
    opacity: .75
}

button.close {
    padding: 0;
    background-color: transparent;
    border: 0;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none
}

a.close.disabled {
    pointer-events: none
}

.toast {
    max-width: 350px;
    overflow: hidden;
    font-size: .875rem;
    background-color: rgba(255, 255, 255, .85);
    background-clip: padding-box;
    border: 1px solid rgba(0, 0, 0, .1);
    box-shadow: 0 .25rem .75rem rgba(0, 0, 0, .1);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    opacity: 0;
    border-radius: .25rem
}

.toast:not(:last-child) {
    margin-bottom: .75rem
}

.toast.showing {
    opacity: 1
}

.toast.show {
    display: block;
    opacity: 1
}

.toast.hide {
    display: none
}

.toast-header {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: center;
    align-items: center;
    padding: .25rem .75rem;
    color: #6c757d;
    background-color: rgba(255, 255, 255, .85);
    background-clip: padding-box;
    border-bottom: 1px solid rgba(0, 0, 0, .05)
}

.toast-body {
    padding: .75rem
}

.modal-open {
    overflow: hidden
}

.modal-open .modal {
    overflow-x: hidden;
    overflow-y: auto
}

.modal {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1050;
    display: none;
    width: 100%;
    height: 100%;
    overflow: hidden;
    outline: 0
}

.modal-dialog {
    position: relative;
    width: auto;
    margin: .5rem;
    pointer-events: none
}

.modal.fade .modal-dialog {
    transition: -webkit-transform .3s ease-out;
    transition: transform .3s ease-out;
    transition: transform .3s ease-out, -webkit-transform .3s ease-out;
    -webkit-transform: translate(0, -50px);
    transform: translate(0, -50px)
}

@media (prefers-reduced-motion: reduce) {
    .modal.fade .modal-dialog {
        transition: none
    }
}

.modal.show .modal-dialog {
    -webkit-transform: none;
    transform: none
}

.modal-dialog-scrollable {
    display: -ms-flexbox;
    display: flex;
    max-height: calc(100% - 1rem)
}

.modal-dialog-scrollable .modal-content {
    max-height: calc(100vh - 1rem);
    overflow: hidden
}

.modal-dialog-scrollable .modal-footer, .modal-dialog-scrollable .modal-header {
    -ms-flex-negative: 0;
    flex-shrink: 0
}

.modal-dialog-scrollable .modal-body {
    overflow-y: auto
}

.modal-dialog-centered {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: center;
    align-items: center;
    min-height: calc(100% - 1rem)
}

.modal-dialog-centered::before {
    display: block;
    height: calc(100vh - 1rem);
    content: ""
}

.modal-dialog-centered.modal-dialog-scrollable {
    -ms-flex-direction: column;
    flex-direction: column;
    -ms-flex-pack: center;
    justify-content: center;
    height: 100%
}

.modal-dialog-centered.modal-dialog-scrollable .modal-content {
    max-height: none
}

.modal-dialog-centered.modal-dialog-scrollable::before {
    content: none
}

.modal-content {
    position: relative;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: column;
    flex-direction: column;
    width: 100%;
    pointer-events: auto;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid rgba(0, 0, 0, .2);
    border-radius: .3rem;
    outline: 0
}

.modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1040;
    width: 100vw;
    height: 100vh;
    background-color: #000
}

.modal-backdrop.fade {
    opacity: 0
}

.modal-backdrop.show {
    opacity: .5
}

.modal-header {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: start;
    align-items: flex-start;
    -ms-flex-pack: justify;
    justify-content: space-between;
    padding: 1rem 1rem;
    border-bottom: 1px solid #dee2e6;
    border-top-left-radius: .3rem;
    border-top-right-radius: .3rem
}

.modal-header .close {
    padding: 1rem 1rem;
    margin: -1rem -1rem -1rem auto
}

.modal-title {
    margin-bottom: 0;
    line-height: 1.5
}

.modal-body {
    position: relative;
    -ms-flex: 1 1 auto;
    flex: 1 1 auto;
    padding: 1rem
}

.modal-footer {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: center;
    align-items: center;
    -ms-flex-pack: end;
    justify-content: flex-end;
    padding: 1rem;
    border-top: 1px solid #dee2e6;
    border-bottom-right-radius: .3rem;
    border-bottom-left-radius: .3rem
}

.modal-footer > :not(:first-child) {
    margin-left: .25rem
}

.modal-footer > :not(:last-child) {
    margin-right: .25rem
}

.modal-scrollbar-measure {
    position: absolute;
    top: -9999px;
    width: 50px;
    height: 50px;
    overflow: scroll
}

@media (min-width: 576px) {
    .modal-dialog {
        max-width: 500px;
        margin: 1.75rem auto
    }

    .modal-dialog-scrollable {
        max-height: calc(100% - 3.5rem)
    }

    .modal-dialog-scrollable .modal-content {
        max-height: calc(100vh - 3.5rem)
    }

    .modal-dialog-centered {
        min-height: calc(100% - 3.5rem)
    }

    .modal-dialog-centered::before {
        height: calc(100vh - 3.5rem)
    }

    .modal-sm {
        max-width: 300px
    }
}

@media (min-width: 992px) {
    .modal-lg, .modal-xl {
        max-width: 800px
    }
}

@media (min-width: 1200px) {
    .modal-xl {
        max-width: 1140px
    }
}

.tooltip {
    position: absolute;
    z-index: 1070;
    display: block;
    margin: 0;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    font-style: normal;
    font-weight: 400;
    line-height: 1.5;
    text-align: left;
    text-align: start;
    text-decoration: none;
    text-shadow: none;
    text-transform: none;
    letter-spacing: normal;
    word-break: normal;
    word-spacing: normal;
    white-space: normal;
    line-break: auto;
    font-size: .875rem;
    word-wrap: break-word;
    opacity: 0
}

.tooltip.show {
    opacity: .9
}

.tooltip .arrow {
    position: absolute;
    display: block;
    width: .8rem;
    height: .4rem
}

.tooltip .arrow::before {
    position: absolute;
    content: "";
    border-color: transparent;
    border-style: solid
}

.bs-tooltip-auto[x-placement^=top], .bs-tooltip-top {
    padding: .4rem 0
}

.bs-tooltip-auto[x-placement^=top] .arrow, .bs-tooltip-top .arrow {
    bottom: 0
}

.bs-tooltip-auto[x-placement^=top] .arrow::before, .bs-tooltip-top .arrow::before {
    top: 0;
    border-width: .4rem .4rem 0;
    border-top-color: #000
}

.bs-tooltip-auto[x-placement^=right], .bs-tooltip-right {
    padding: 0 .4rem
}

.bs-tooltip-auto[x-placement^=right] .arrow, .bs-tooltip-right .arrow {
    left: 0;
    width: .4rem;
    height: .8rem
}

.bs-tooltip-auto[x-placement^=right] .arrow::before, .bs-tooltip-right .arrow::before {
    right: 0;
    border-width: .4rem .4rem .4rem 0;
    border-right-color: #000
}

.bs-tooltip-auto[x-placement^=bottom], .bs-tooltip-bottom {
    padding: .4rem 0
}

.bs-tooltip-auto[x-placement^=bottom] .arrow, .bs-tooltip-bottom .arrow {
    top: 0
}

.bs-tooltip-auto[x-placement^=bottom] .arrow::before, .bs-tooltip-bottom .arrow::before {
    bottom: 0;
    border-width: 0 .4rem .4rem;
    border-bottom-color: #000
}

.bs-tooltip-auto[x-placement^=left], .bs-tooltip-left {
    padding: 0 .4rem
}

.bs-tooltip-auto[x-placement^=left] .arrow, .bs-tooltip-left .arrow {
    right: 0;
    width: .4rem;
    height: .8rem
}

.bs-tooltip-auto[x-placement^=left] .arrow::before, .bs-tooltip-left .arrow::before {
    left: 0;
    border-width: .4rem 0 .4rem .4rem;
    border-left-color: #000
}

.tooltip-inner {
    max-width: 200px;
    padding: .25rem .5rem;
    color: #fff;
    text-align: center;
    background-color: #000;
    border-radius: .25rem
}

.popover {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1060;
    display: block;
    max-width: 276px;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    font-style: normal;
    font-weight: 400;
    line-height: 1.5;
    text-align: left;
    text-align: start;
    text-decoration: none;
    text-shadow: none;
    text-transform: none;
    letter-spacing: normal;
    word-break: normal;
    word-spacing: normal;
    white-space: normal;
    line-break: auto;
    font-size: .875rem;
    word-wrap: break-word;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid rgba(0, 0, 0, .2);
    border-radius: .3rem
}

.popover .arrow {
    position: absolute;
    display: block;
    width: 1rem;
    height: .5rem;
    margin: 0 .3rem
}

.popover .arrow::after, .popover .arrow::before {
    position: absolute;
    display: block;
    content: "";
    border-color: transparent;
    border-style: solid
}

.bs-popover-auto[x-placement^=top], .bs-popover-top {
    margin-bottom: .5rem
}

.bs-popover-auto[x-placement^=top] > .arrow, .bs-popover-top > .arrow {
    bottom: calc((.5rem + 1px) * -1)
}

.bs-popover-auto[x-placement^=top] > .arrow::before, .bs-popover-top > .arrow::before {
    bottom: 0;
    border-width: .5rem .5rem 0;
    border-top-color: rgba(0, 0, 0, .25)
}

.bs-popover-auto[x-placement^=top] > .arrow::after, .bs-popover-top > .arrow::after {
    bottom: 1px;
    border-width: .5rem .5rem 0;
    border-top-color: #fff
}

.bs-popover-auto[x-placement^=right], .bs-popover-right {
    margin-left: .5rem
}

.bs-popover-auto[x-placement^=right] > .arrow, .bs-popover-right > .arrow {
    left: calc((.5rem + 1px) * -1);
    width: .5rem;
    height: 1rem;
    margin: .3rem 0
}

.bs-popover-auto[x-placement^=right] > .arrow::before, .bs-popover-right > .arrow::before {
    left: 0;
    border-width: .5rem .5rem .5rem 0;
    border-right-color: rgba(0, 0, 0, .25)
}

.bs-popover-auto[x-placement^=right] > .arrow::after, .bs-popover-right > .arrow::after {
    left: 1px;
    border-width: .5rem .5rem .5rem 0;
    border-right-color: #fff
}

.bs-popover-auto[x-placement^=bottom], .bs-popover-bottom {
    margin-top: .5rem
}

.bs-popover-auto[x-placement^=bottom] > .arrow, .bs-popover-bottom > .arrow {
    top: calc((.5rem + 1px) * -1)
}

.bs-popover-auto[x-placement^=bottom] > .arrow::before, .bs-popover-bottom > .arrow::before {
    top: 0;
    border-width: 0 .5rem .5rem .5rem;
    border-bottom-color: rgba(0, 0, 0, .25)
}

.bs-popover-auto[x-placement^=bottom] > .arrow::after, .bs-popover-bottom > .arrow::after {
    top: 1px;
    border-width: 0 .5rem .5rem .5rem;
    border-bottom-color: #fff
}

.bs-popover-auto[x-placement^=bottom] .popover-header::before, .bs-popover-bottom .popover-header::before {
    position: absolute;
    top: 0;
    left: 50%;
    display: block;
    width: 1rem;
    margin-left: -.5rem;
    content: "";
    border-bottom: 1px solid #f7f7f7
}

.bs-popover-auto[x-placement^=left], .bs-popover-left {
    margin-right: .5rem
}

.bs-popover-auto[x-placement^=left] > .arrow, .bs-popover-left > .arrow {
    right: calc((.5rem + 1px) * -1);
    width: .5rem;
    height: 1rem;
    margin: .3rem 0
}

.bs-popover-auto[x-placement^=left] > .arrow::before, .bs-popover-left > .arrow::before {
    right: 0;
    border-width: .5rem 0 .5rem .5rem;
    border-left-color: rgba(0, 0, 0, .25)
}

.bs-popover-auto[x-placement^=left] > .arrow::after, .bs-popover-left > .arrow::after {
    right: 1px;
    border-width: .5rem 0 .5rem .5rem;
    border-left-color: #fff
}

.popover-header {
    padding: .5rem .75rem;
    margin-bottom: 0;
    font-size: 1rem;
    background-color: #f7f7f7;
    border-bottom: 1px solid #ebebeb;
    border-top-left-radius: calc(.3rem - 1px);
    border-top-right-radius: calc(.3rem - 1px)
}

.popover-header:empty {
    display: none
}

.popover-body {
    padding: .5rem .75rem;
    color: #212529
}

.carousel {
    position: relative
}

.carousel.pointer-event {
    -ms-touch-action: pan-y;
    touch-action: pan-y
}

.carousel-inner {
    position: relative;
    width: 100%;
    overflow: hidden
}

.carousel-inner::after {
    display: block;
    clear: both;
    content: ""
}

.carousel-item {
    position: relative;
    display: none;
    float: left;
    width: 100%;
    margin-right: -100%;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    transition: -webkit-transform .6s ease-in-out;
    transition: transform .6s ease-in-out;
    transition: transform .6s ease-in-out, -webkit-transform .6s ease-in-out
}

@media (prefers-reduced-motion: reduce) {
    .carousel-item {
        transition: none
    }
}

.carousel-item-next, .carousel-item-prev, .carousel-item.active {
    display: block
}

.active.carousel-item-right, .carousel-item-next:not(.carousel-item-left) {
    -webkit-transform: translateX(100%);
    transform: translateX(100%)
}

.active.carousel-item-left, .carousel-item-prev:not(.carousel-item-right) {
    -webkit-transform: translateX(-100%);
    transform: translateX(-100%)
}

.carousel-fade .carousel-item {
    opacity: 0;
    transition-property: opacity;
    -webkit-transform: none;
    transform: none
}

.carousel-fade .carousel-item-next.carousel-item-left, .carousel-fade .carousel-item-prev.carousel-item-right, .carousel-fade .carousel-item.active {
    z-index: 1;
    opacity: 1
}

.carousel-fade .active.carousel-item-left, .carousel-fade .active.carousel-item-right {
    z-index: 0;
    opacity: 0;
    transition: 0s .6s opacity
}

@media (prefers-reduced-motion: reduce) {
    .carousel-fade .active.carousel-item-left, .carousel-fade .active.carousel-item-right {
        transition: none
    }
}

.carousel-control-next, .carousel-control-prev {
    position: absolute;
    top: 0;
    bottom: 0;
    z-index: 1;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: center;
    align-items: center;
    -ms-flex-pack: center;
    justify-content: center;
    width: 15%;
    color: #fff;
    text-align: center;
    opacity: .5;
    transition: opacity .15s ease
}

@media (prefers-reduced-motion: reduce) {
    .carousel-control-next, .carousel-control-prev {
        transition: none
    }
}

.carousel-control-next:focus, .carousel-control-next:hover, .carousel-control-prev:focus, .carousel-control-prev:hover {
    color: #fff;
    text-decoration: none;
    outline: 0;
    opacity: .9
}

.carousel-control-prev {
    left: 0
}

.carousel-control-next {
    right: 0
}

.carousel-control-next-icon, .carousel-control-prev-icon {
    display: inline-block;
    width: 20px;
    height: 20px;
    background: no-repeat 50%/100% 100%
}

.carousel-control-prev-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23fff' viewBox='0 0 8 8'%3e%3cpath d='M5.25 0l-4 4 4 4 1.5-1.5-2.5-2.5 2.5-2.5-1.5-1.5z'/%3e%3c/svg%3e")
}

.carousel-control-next-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23fff' viewBox='0 0 8 8'%3e%3cpath d='M2.75 0l-1.5 1.5 2.5 2.5-2.5 2.5 1.5 1.5 4-4-4-4z'/%3e%3c/svg%3e")
}

.carousel-indicators {
    position: absolute;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 15;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-pack: center;
    justify-content: center;
    padding-left: 0;
    margin-right: 15%;
    margin-left: 15%;
    list-style: none
}

.carousel-indicators li {
    box-sizing: content-box;
    -ms-flex: 0 1 auto;
    flex: 0 1 auto;
    width: 30px;
    height: 3px;
    margin-right: 3px;
    margin-left: 3px;
    text-indent: -999px;
    cursor: pointer;
    background-color: #fff;
    background-clip: padding-box;
    border-top: 10px solid transparent;
    border-bottom: 10px solid transparent;
    opacity: .5;
    transition: opacity .6s ease
}

@media (prefers-reduced-motion: reduce) {
    .carousel-indicators li {
        transition: none
    }
}

.carousel-indicators .active {
    opacity: 1
}

.carousel-caption {
    position: absolute;
    right: 15%;
    bottom: 20px;
    left: 15%;
    z-index: 10;
    padding-top: 20px;
    padding-bottom: 20px;
    color: #fff;
    text-align: center
}

@-webkit-keyframes spinner-border {
    to {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg)
    }
}

@keyframes spinner-border {
    to {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg)
    }
}

.spinner-border {
    display: inline-block;
    width: 2rem;
    height: 2rem;
    vertical-align: text-bottom;
    border: .25em solid currentColor;
    border-right-color: transparent;
    border-radius: 50%;
    -webkit-animation: spinner-border .75s linear infinite;
    animation: spinner-border .75s linear infinite
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
    border-width: .2em
}

@-webkit-keyframes spinner-grow {
    0% {
        -webkit-transform: scale(0);
        transform: scale(0)
    }
    50% {
        opacity: 1
    }
}

@keyframes spinner-grow {
    0% {
        -webkit-transform: scale(0);
        transform: scale(0)
    }
    50% {
        opacity: 1
    }
}

.spinner-grow {
    display: inline-block;
    width: 2rem;
    height: 2rem;
    vertical-align: text-bottom;
    background-color: currentColor;
    border-radius: 50%;
    opacity: 0;
    -webkit-animation: spinner-grow .75s linear infinite;
    animation: spinner-grow .75s linear infinite
}

.spinner-grow-sm {
    width: 1rem;
    height: 1rem
}

.align-baseline {
    vertical-align: baseline !important
}

.align-top {
    vertical-align: top !important
}

.align-middle {
    vertical-align: middle !important
}

.align-bottom {
    vertical-align: bottom !important
}

.align-text-bottom {
    vertical-align: text-bottom !important
}

.align-text-top {
    vertical-align: text-top !important
}

.bg-primary {
    background-color: #007bff !important
}

a.bg-primary:focus, a.bg-primary:hover, button.bg-primary:focus, button.bg-primary:hover {
    background-color: #0062cc !important
}

.bg-secondary {
    background-color: #6c757d !important
}

a.bg-secondary:focus, a.bg-secondary:hover, button.bg-secondary:focus, button.bg-secondary:hover {
    background-color: #545b62 !important
}

.bg-success {
    background-color: #28a745 !important
}

a.bg-success:focus, a.bg-success:hover, button.bg-success:focus, button.bg-success:hover {
    background-color: #1e7e34 !important
}

.bg-info {
    background-color: #17a2b8 !important
}

a.bg-info:focus, a.bg-info:hover, button.bg-info:focus, button.bg-info:hover {
    background-color: #117a8b !important
}

.bg-warning {
    background-color: #ffc107 !important
}

a.bg-warning:focus, a.bg-warning:hover, button.bg-warning:focus, button.bg-warning:hover {
    background-color: #d39e00 !important
}

.bg-danger {
    background-color: #dc3545 !important
}

a.bg-danger:focus, a.bg-danger:hover, button.bg-danger:focus, button.bg-danger:hover {
    background-color: #bd2130 !important
}

.bg-light {
    background-color: #f8f9fa !important
}

a.bg-light:focus, a.bg-light:hover, button.bg-light:focus, button.bg-light:hover {
    background-color: #dae0e5 !important
}

.bg-dark {
    background-color: #343a40 !important
}

a.bg-dark:focus, a.bg-dark:hover, button.bg-dark:focus, button.bg-dark:hover {
    background-color: #1d2124 !important
}

.bg-white {
    background-color: #fff !important
}

.bg-transparent {
    background-color: transparent !important
}

.border {
    border: 1px solid #dee2e6 !important
}

.border-top {
    border-top: 1px solid #dee2e6 !important
}

.border-right {
    border-right: 1px solid #dee2e6 !important
}

.border-bottom {
    border-bottom: 1px solid #dee2e6 !important
}

.border-left {
    border-left: 1px solid #dee2e6 !important
}

.border-0 {
    border: 0 !important
}

.border-top-0 {
    border-top: 0 !important
}

.border-right-0 {
    border-right: 0 !important
}

.border-bottom-0 {
    border-bottom: 0 !important
}

.border-left-0 {
    border-left: 0 !important
}

.border-primary {
    border-color: #007bff !important
}

.border-secondary {
    border-color: #6c757d !important
}

.border-success {
    border-color: #28a745 !important
}

.border-info {
    border-color: #17a2b8 !important
}

.border-warning {
    border-color: #ffc107 !important
}

.border-danger {
    border-color: #dc3545 !important
}

.border-light {
    border-color: #f8f9fa !important
}

.border-dark {
    border-color: #343a40 !important
}

.border-white {
    border-color: #fff !important
}

.rounded-sm {
    border-radius: .2rem !important
}

.rounded {
    border-radius: .25rem !important
}

.rounded-top {
    border-top-left-radius: .25rem !important;
    border-top-right-radius: .25rem !important
}

.rounded-right {
    border-top-right-radius: .25rem !important;
    border-bottom-right-radius: .25rem !important
}

.rounded-bottom {
    border-bottom-right-radius: .25rem !important;
    border-bottom-left-radius: .25rem !important
}

.rounded-left {
    border-top-left-radius: .25rem !important;
    border-bottom-left-radius: .25rem !important
}

.rounded-lg {
    border-radius: .3rem !important
}

.rounded-circle {
    border-radius: 50% !important
}

.rounded-pill {
    border-radius: 50rem !important
}

.rounded-0 {
    border-radius: 0 !important
}

.clearfix::after {
    display: block;
    clear: both;
    content: ""
}

.d-none {
    display: none !important
}

.d-inline {
    display: inline !important
}

.d-inline-block {
    display: inline-block !important
}

.d-block {
    display: block !important
}

.d-table {
    display: table !important
}

.d-table-row {
    display: table-row !important
}

.d-table-cell {
    display: table-cell !important
}

.d-flex {
    display: -ms-flexbox !important;
    display: flex !important
}

.d-inline-flex {
    display: -ms-inline-flexbox !important;
    display: inline-flex !important
}

@media (min-width: 576px) {
    .d-sm-none {
        display: none !important
    }

    .d-sm-inline {
        display: inline !important
    }

    .d-sm-inline-block {
        display: inline-block !important
    }

    .d-sm-block {
        display: block !important
    }

    .d-sm-table {
        display: table !important
    }

    .d-sm-table-row {
        display: table-row !important
    }

    .d-sm-table-cell {
        display: table-cell !important
    }

    .d-sm-flex {
        display: -ms-flexbox !important;
        display: flex !important
    }

    .d-sm-inline-flex {
        display: -ms-inline-flexbox !important;
        display: inline-flex !important
    }
}

@media (min-width: 768px) {
    .d-md-none {
        display: none !important
    }

    .d-md-inline {
        display: inline !important
    }

    .d-md-inline-block {
        display: inline-block !important
    }

    .d-md-block {
        display: block !important
    }

    .d-md-table {
        display: table !important
    }

    .d-md-table-row {
        display: table-row !important
    }

    .d-md-table-cell {
        display: table-cell !important
    }

    .d-md-flex {
        display: -ms-flexbox !important;
        display: flex !important
    }

    .d-md-inline-flex {
        display: -ms-inline-flexbox !important;
        display: inline-flex !important
    }
}

@media (min-width: 992px) {
    .d-lg-none {
        display: none !important
    }

    .d-lg-inline {
        display: inline !important
    }

    .d-lg-inline-block {
        display: inline-block !important
    }

    .d-lg-block {
        display: block !important
    }

    .d-lg-table {
        display: table !important
    }

    .d-lg-table-row {
        display: table-row !important
    }

    .d-lg-table-cell {
        display: table-cell !important
    }

    .d-lg-flex {
        display: -ms-flexbox !important;
        display: flex !important
    }

    .d-lg-inline-flex {
        display: -ms-inline-flexbox !important;
        display: inline-flex !important
    }
}

@media (min-width: 1200px) {
    .d-xl-none {
        display: none !important
    }

    .d-xl-inline {
        display: inline !important
    }

    .d-xl-inline-block {
        display: inline-block !important
    }

    .d-xl-block {
        display: block !important
    }

    .d-xl-table {
        display: table !important
    }

    .d-xl-table-row {
        display: table-row !important
    }

    .d-xl-table-cell {
        display: table-cell !important
    }

    .d-xl-flex {
        display: -ms-flexbox !important;
        display: flex !important
    }

    .d-xl-inline-flex {
        display: -ms-inline-flexbox !important;
        display: inline-flex !important
    }
}

@media print {
    .d-print-none {
        display: none !important
    }

    .d-print-inline {
        display: inline !important
    }

    .d-print-inline-block {
        display: inline-block !important
    }

    .d-print-block {
        display: block !important
    }

    .d-print-table {
        display: table !important
    }

    .d-print-table-row {
        display: table-row !important
    }

    .d-print-table-cell {
        display: table-cell !important
    }

    .d-print-flex {
        display: -ms-flexbox !important;
        display: flex !important
    }

    .d-print-inline-flex {
        display: -ms-inline-flexbox !important;
        display: inline-flex !important
    }
}

.embed-responsive {
    position: relative;
    display: block;
    width: 100%;
    padding: 0;
    overflow: hidden
}

.embed-responsive::before {
    display: block;
    content: ""
}

.embed-responsive .embed-responsive-item, .embed-responsive embed, .embed-responsive iframe, .embed-responsive object, .embed-responsive video {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 0
}

.embed-responsive-21by9::before {
    padding-top: 42.857143%
}

.embed-responsive-16by9::before {
    padding-top: 56.25%
}

.embed-responsive-4by3::before {
    padding-top: 75%
}

.embed-responsive-1by1::before {
    padding-top: 100%
}

.flex-row {
    -ms-flex-direction: row !important;
    flex-direction: row !important
}

.flex-column {
    -ms-flex-direction: column !important;
    flex-direction: column !important
}

.flex-row-reverse {
    -ms-flex-direction: row-reverse !important;
    flex-direction: row-reverse !important
}

.flex-column-reverse {
    -ms-flex-direction: column-reverse !important;
    flex-direction: column-reverse !important
}

.flex-wrap {
    -ms-flex-wrap: wrap !important;
    flex-wrap: wrap !important
}

.flex-nowrap {
    -ms-flex-wrap: nowrap !important;
    flex-wrap: nowrap !important
}

.flex-wrap-reverse {
    -ms-flex-wrap: wrap-reverse !important;
    flex-wrap: wrap-reverse !important
}

.flex-fill {
    -ms-flex: 1 1 auto !important;
    flex: 1 1 auto !important
}

.flex-grow-0 {
    -ms-flex-positive: 0 !important;
    flex-grow: 0 !important
}

.flex-grow-1 {
    -ms-flex-positive: 1 !important;
    flex-grow: 1 !important
}

.flex-shrink-0 {
    -ms-flex-negative: 0 !important;
    flex-shrink: 0 !important
}

.flex-shrink-1 {
    -ms-flex-negative: 1 !important;
    flex-shrink: 1 !important
}

.justify-content-start {
    -ms-flex-pack: start !important;
    justify-content: flex-start !important
}

.justify-content-end {
    -ms-flex-pack: end !important;
    justify-content: flex-end !important
}

.justify-content-center {
    -ms-flex-pack: center !important;
    justify-content: center !important
}

.justify-content-between {
    -ms-flex-pack: justify !important;
    justify-content: space-between !important
}

.justify-content-around {
    -ms-flex-pack: distribute !important;
    justify-content: space-around !important
}

.align-items-start {
    -ms-flex-align: start !important;
    align-items: flex-start !important
}

.align-items-end {
    -ms-flex-align: end !important;
    align-items: flex-end !important
}

.align-items-center {
    -ms-flex-align: center !important;
    align-items: center !important
}

.align-items-baseline {
    -ms-flex-align: baseline !important;
    align-items: baseline !important
}

.align-items-stretch {
    -ms-flex-align: stretch !important;
    align-items: stretch !important
}

.align-content-start {
    -ms-flex-line-pack: start !important;
    align-content: flex-start !important
}

.align-content-end {
    -ms-flex-line-pack: end !important;
    align-content: flex-end !important
}

.align-content-center {
    -ms-flex-line-pack: center !important;
    align-content: center !important
}

.align-content-between {
    -ms-flex-line-pack: justify !important;
    align-content: space-between !important
}

.align-content-around {
    -ms-flex-line-pack: distribute !important;
    align-content: space-around !important
}

.align-content-stretch {
    -ms-flex-line-pack: stretch !important;
    align-content: stretch !important
}

.align-self-auto {
    -ms-flex-item-align: auto !important;
    align-self: auto !important
}

.align-self-start {
    -ms-flex-item-align: start !important;
    align-self: flex-start !important
}

.align-self-end {
    -ms-flex-item-align: end !important;
    align-self: flex-end !important
}

.align-self-center {
    -ms-flex-item-align: center !important;
    align-self: center !important
}

.align-self-baseline {
    -ms-flex-item-align: baseline !important;
    align-self: baseline !important
}

.align-self-stretch {
    -ms-flex-item-align: stretch !important;
    align-self: stretch !important
}

@media (min-width: 576px) {
    .flex-sm-row {
        -ms-flex-direction: row !important;
        flex-direction: row !important
    }

    .flex-sm-column {
        -ms-flex-direction: column !important;
        flex-direction: column !important
    }

    .flex-sm-row-reverse {
        -ms-flex-direction: row-reverse !important;
        flex-direction: row-reverse !important
    }

    .flex-sm-column-reverse {
        -ms-flex-direction: column-reverse !important;
        flex-direction: column-reverse !important
    }

    .flex-sm-wrap {
        -ms-flex-wrap: wrap !important;
        flex-wrap: wrap !important
    }

    .flex-sm-nowrap {
        -ms-flex-wrap: nowrap !important;
        flex-wrap: nowrap !important
    }

    .flex-sm-wrap-reverse {
        -ms-flex-wrap: wrap-reverse !important;
        flex-wrap: wrap-reverse !important
    }

    .flex-sm-fill {
        -ms-flex: 1 1 auto !important;
        flex: 1 1 auto !important
    }

    .flex-sm-grow-0 {
        -ms-flex-positive: 0 !important;
        flex-grow: 0 !important
    }

    .flex-sm-grow-1 {
        -ms-flex-positive: 1 !important;
        flex-grow: 1 !important
    }

    .flex-sm-shrink-0 {
        -ms-flex-negative: 0 !important;
        flex-shrink: 0 !important
    }

    .flex-sm-shrink-1 {
        -ms-flex-negative: 1 !important;
        flex-shrink: 1 !important
    }

    .justify-content-sm-start {
        -ms-flex-pack: start !important;
        justify-content: flex-start !important
    }

    .justify-content-sm-end {
        -ms-flex-pack: end !important;
        justify-content: flex-end !important
    }

    .justify-content-sm-center {
        -ms-flex-pack: center !important;
        justify-content: center !important
    }

    .justify-content-sm-between {
        -ms-flex-pack: justify !important;
        justify-content: space-between !important
    }

    .justify-content-sm-around {
        -ms-flex-pack: distribute !important;
        justify-content: space-around !important
    }

    .align-items-sm-start {
        -ms-flex-align: start !important;
        align-items: flex-start !important
    }

    .align-items-sm-end {
        -ms-flex-align: end !important;
        align-items: flex-end !important
    }

    .align-items-sm-center {
        -ms-flex-align: center !important;
        align-items: center !important
    }

    .align-items-sm-baseline {
        -ms-flex-align: baseline !important;
        align-items: baseline !important
    }

    .align-items-sm-stretch {
        -ms-flex-align: stretch !important;
        align-items: stretch !important
    }

    .align-content-sm-start {
        -ms-flex-line-pack: start !important;
        align-content: flex-start !important
    }

    .align-content-sm-end {
        -ms-flex-line-pack: end !important;
        align-content: flex-end !important
    }

    .align-content-sm-center {
        -ms-flex-line-pack: center !important;
        align-content: center !important
    }

    .align-content-sm-between {
        -ms-flex-line-pack: justify !important;
        align-content: space-between !important
    }

    .align-content-sm-around {
        -ms-flex-line-pack: distribute !important;
        align-content: space-around !important
    }

    .align-content-sm-stretch {
        -ms-flex-line-pack: stretch !important;
        align-content: stretch !important
    }

    .align-self-sm-auto {
        -ms-flex-item-align: auto !important;
        align-self: auto !important
    }

    .align-self-sm-start {
        -ms-flex-item-align: start !important;
        align-self: flex-start !important
    }

    .align-self-sm-end {
        -ms-flex-item-align: end !important;
        align-self: flex-end !important
    }

    .align-self-sm-center {
        -ms-flex-item-align: center !important;
        align-self: center !important
    }

    .align-self-sm-baseline {
        -ms-flex-item-align: baseline !important;
        align-self: baseline !important
    }

    .align-self-sm-stretch {
        -ms-flex-item-align: stretch !important;
        align-self: stretch !important
    }
}

@media (min-width: 768px) {
    .flex-md-row {
        -ms-flex-direction: row !important;
        flex-direction: row !important
    }

    .flex-md-column {
        -ms-flex-direction: column !important;
        flex-direction: column !important
    }

    .flex-md-row-reverse {
        -ms-flex-direction: row-reverse !important;
        flex-direction: row-reverse !important
    }

    .flex-md-column-reverse {
        -ms-flex-direction: column-reverse !important;
        flex-direction: column-reverse !important
    }

    .flex-md-wrap {
        -ms-flex-wrap: wrap !important;
        flex-wrap: wrap !important
    }

    .flex-md-nowrap {
        -ms-flex-wrap: nowrap !important;
        flex-wrap: nowrap !important
    }

    .flex-md-wrap-reverse {
        -ms-flex-wrap: wrap-reverse !important;
        flex-wrap: wrap-reverse !important
    }

    .flex-md-fill {
        -ms-flex: 1 1 auto !important;
        flex: 1 1 auto !important
    }

    .flex-md-grow-0 {
        -ms-flex-positive: 0 !important;
        flex-grow: 0 !important
    }

    .flex-md-grow-1 {
        -ms-flex-positive: 1 !important;
        flex-grow: 1 !important
    }

    .flex-md-shrink-0 {
        -ms-flex-negative: 0 !important;
        flex-shrink: 0 !important
    }

    .flex-md-shrink-1 {
        -ms-flex-negative: 1 !important;
        flex-shrink: 1 !important
    }

    .justify-content-md-start {
        -ms-flex-pack: start !important;
        justify-content: flex-start !important
    }

    .justify-content-md-end {
        -ms-flex-pack: end !important;
        justify-content: flex-end !important
    }

    .justify-content-md-center {
        -ms-flex-pack: center !important;
        justify-content: center !important
    }

    .justify-content-md-between {
        -ms-flex-pack: justify !important;
        justify-content: space-between !important
    }

    .justify-content-md-around {
        -ms-flex-pack: distribute !important;
        justify-content: space-around !important
    }

    .align-items-md-start {
        -ms-flex-align: start !important;
        align-items: flex-start !important
    }

    .align-items-md-end {
        -ms-flex-align: end !important;
        align-items: flex-end !important
    }

    .align-items-md-center {
        -ms-flex-align: center !important;
        align-items: center !important
    }

    .align-items-md-baseline {
        -ms-flex-align: baseline !important;
        align-items: baseline !important
    }

    .align-items-md-stretch {
        -ms-flex-align: stretch !important;
        align-items: stretch !important
    }

    .align-content-md-start {
        -ms-flex-line-pack: start !important;
        align-content: flex-start !important
    }

    .align-content-md-end {
        -ms-flex-line-pack: end !important;
        align-content: flex-end !important
    }

    .align-content-md-center {
        -ms-flex-line-pack: center !important;
        align-content: center !important
    }

    .align-content-md-between {
        -ms-flex-line-pack: justify !important;
        align-content: space-between !important
    }

    .align-content-md-around {
        -ms-flex-line-pack: distribute !important;
        align-content: space-around !important
    }

    .align-content-md-stretch {
        -ms-flex-line-pack: stretch !important;
        align-content: stretch !important
    }

    .align-self-md-auto {
        -ms-flex-item-align: auto !important;
        align-self: auto !important
    }

    .align-self-md-start {
        -ms-flex-item-align: start !important;
        align-self: flex-start !important
    }

    .align-self-md-end {
        -ms-flex-item-align: end !important;
        align-self: flex-end !important
    }

    .align-self-md-center {
        -ms-flex-item-align: center !important;
        align-self: center !important
    }

    .align-self-md-baseline {
        -ms-flex-item-align: baseline !important;
        align-self: baseline !important
    }

    .align-self-md-stretch {
        -ms-flex-item-align: stretch !important;
        align-self: stretch !important
    }
}

@media (min-width: 992px) {
    .flex-lg-row {
        -ms-flex-direction: row !important;
        flex-direction: row !important
    }

    .flex-lg-column {
        -ms-flex-direction: column !important;
        flex-direction: column !important
    }

    .flex-lg-row-reverse {
        -ms-flex-direction: row-reverse !important;
        flex-direction: row-reverse !important
    }

    .flex-lg-column-reverse {
        -ms-flex-direction: column-reverse !important;
        flex-direction: column-reverse !important
    }

    .flex-lg-wrap {
        -ms-flex-wrap: wrap !important;
        flex-wrap: wrap !important
    }

    .flex-lg-nowrap {
        -ms-flex-wrap: nowrap !important;
        flex-wrap: nowrap !important
    }

    .flex-lg-wrap-reverse {
        -ms-flex-wrap: wrap-reverse !important;
        flex-wrap: wrap-reverse !important
    }

    .flex-lg-fill {
        -ms-flex: 1 1 auto !important;
        flex: 1 1 auto !important
    }

    .flex-lg-grow-0 {
        -ms-flex-positive: 0 !important;
        flex-grow: 0 !important
    }

    .flex-lg-grow-1 {
        -ms-flex-positive: 1 !important;
        flex-grow: 1 !important
    }

    .flex-lg-shrink-0 {
        -ms-flex-negative: 0 !important;
        flex-shrink: 0 !important
    }

    .flex-lg-shrink-1 {
        -ms-flex-negative: 1 !important;
        flex-shrink: 1 !important
    }

    .justify-content-lg-start {
        -ms-flex-pack: start !important;
        justify-content: flex-start !important
    }

    .justify-content-lg-end {
        -ms-flex-pack: end !important;
        justify-content: flex-end !important
    }

    .justify-content-lg-center {
        -ms-flex-pack: center !important;
        justify-content: center !important
    }

    .justify-content-lg-between {
        -ms-flex-pack: justify !important;
        justify-content: space-between !important
    }

    .justify-content-lg-around {
        -ms-flex-pack: distribute !important;
        justify-content: space-around !important
    }

    .align-items-lg-start {
        -ms-flex-align: start !important;
        align-items: flex-start !important
    }

    .align-items-lg-end {
        -ms-flex-align: end !important;
        align-items: flex-end !important
    }

    .align-items-lg-center {
        -ms-flex-align: center !important;
        align-items: center !important
    }

    .align-items-lg-baseline {
        -ms-flex-align: baseline !important;
        align-items: baseline !important
    }

    .align-items-lg-stretch {
        -ms-flex-align: stretch !important;
        align-items: stretch !important
    }

    .align-content-lg-start {
        -ms-flex-line-pack: start !important;
        align-content: flex-start !important
    }

    .align-content-lg-end {
        -ms-flex-line-pack: end !important;
        align-content: flex-end !important
    }

    .align-content-lg-center {
        -ms-flex-line-pack: center !important;
        align-content: center !important
    }

    .align-content-lg-between {
        -ms-flex-line-pack: justify !important;
        align-content: space-between !important
    }

    .align-content-lg-around {
        -ms-flex-line-pack: distribute !important;
        align-content: space-around !important
    }

    .align-content-lg-stretch {
        -ms-flex-line-pack: stretch !important;
        align-content: stretch !important
    }

    .align-self-lg-auto {
        -ms-flex-item-align: auto !important;
        align-self: auto !important
    }

    .align-self-lg-start {
        -ms-flex-item-align: start !important;
        align-self: flex-start !important
    }

    .align-self-lg-end {
        -ms-flex-item-align: end !important;
        align-self: flex-end !important
    }

    .align-self-lg-center {
        -ms-flex-item-align: center !important;
        align-self: center !important
    }

    .align-self-lg-baseline {
        -ms-flex-item-align: baseline !important;
        align-self: baseline !important
    }

    .align-self-lg-stretch {
        -ms-flex-item-align: stretch !important;
        align-self: stretch !important
    }
}

@media (min-width: 1200px) {
    .flex-xl-row {
        -ms-flex-direction: row !important;
        flex-direction: row !important
    }

    .flex-xl-column {
        -ms-flex-direction: column !important;
        flex-direction: column !important
    }

    .flex-xl-row-reverse {
        -ms-flex-direction: row-reverse !important;
        flex-direction: row-reverse !important
    }

    .flex-xl-column-reverse {
        -ms-flex-direction: column-reverse !important;
        flex-direction: column-reverse !important
    }

    .flex-xl-wrap {
        -ms-flex-wrap: wrap !important;
        flex-wrap: wrap !important
    }

    .flex-xl-nowrap {
        -ms-flex-wrap: nowrap !important;
        flex-wrap: nowrap !important
    }

    .flex-xl-wrap-reverse {
        -ms-flex-wrap: wrap-reverse !important;
        flex-wrap: wrap-reverse !important
    }

    .flex-xl-fill {
        -ms-flex: 1 1 auto !important;
        flex: 1 1 auto !important
    }

    .flex-xl-grow-0 {
        -ms-flex-positive: 0 !important;
        flex-grow: 0 !important
    }

    .flex-xl-grow-1 {
        -ms-flex-positive: 1 !important;
        flex-grow: 1 !important
    }

    .flex-xl-shrink-0 {
        -ms-flex-negative: 0 !important;
        flex-shrink: 0 !important
    }

    .flex-xl-shrink-1 {
        -ms-flex-negative: 1 !important;
        flex-shrink: 1 !important
    }

    .justify-content-xl-start {
        -ms-flex-pack: start !important;
        justify-content: flex-start !important
    }

    .justify-content-xl-end {
        -ms-flex-pack: end !important;
        justify-content: flex-end !important
    }

    .justify-content-xl-center {
        -ms-flex-pack: center !important;
        justify-content: center !important
    }

    .justify-content-xl-between {
        -ms-flex-pack: justify !important;
        justify-content: space-between !important
    }

    .justify-content-xl-around {
        -ms-flex-pack: distribute !important;
        justify-content: space-around !important
    }

    .align-items-xl-start {
        -ms-flex-align: start !important;
        align-items: flex-start !important
    }

    .align-items-xl-end {
        -ms-flex-align: end !important;
        align-items: flex-end !important
    }

    .align-items-xl-center {
        -ms-flex-align: center !important;
        align-items: center !important
    }

    .align-items-xl-baseline {
        -ms-flex-align: baseline !important;
        align-items: baseline !important
    }

    .align-items-xl-stretch {
        -ms-flex-align: stretch !important;
        align-items: stretch !important
    }

    .align-content-xl-start {
        -ms-flex-line-pack: start !important;
        align-content: flex-start !important
    }

    .align-content-xl-end {
        -ms-flex-line-pack: end !important;
        align-content: flex-end !important
    }

    .align-content-xl-center {
        -ms-flex-line-pack: center !important;
        align-content: center !important
    }

    .align-content-xl-between {
        -ms-flex-line-pack: justify !important;
        align-content: space-between !important
    }

    .align-content-xl-around {
        -ms-flex-line-pack: distribute !important;
        align-content: space-around !important
    }

    .align-content-xl-stretch {
        -ms-flex-line-pack: stretch !important;
        align-content: stretch !important
    }

    .align-self-xl-auto {
        -ms-flex-item-align: auto !important;
        align-self: auto !important
    }

    .align-self-xl-start {
        -ms-flex-item-align: start !important;
        align-self: flex-start !important
    }

    .align-self-xl-end {
        -ms-flex-item-align: end !important;
        align-self: flex-end !important
    }

    .align-self-xl-center {
        -ms-flex-item-align: center !important;
        align-self: center !important
    }

    .align-self-xl-baseline {
        -ms-flex-item-align: baseline !important;
        align-self: baseline !important
    }

    .align-self-xl-stretch {
        -ms-flex-item-align: stretch !important;
        align-self: stretch !important
    }
}

.float-left {
    float: left !important
}

.float-right {
    float: right !important
}

.float-none {
    float: none !important
}

@media (min-width: 576px) {
    .float-sm-left {
        float: left !important
    }

    .float-sm-right {
        float: right !important
    }

    .float-sm-none {
        float: none !important
    }
}

@media (min-width: 768px) {
    .float-md-left {
        float: left !important
    }

    .float-md-right {
        float: right !important
    }

    .float-md-none {
        float: none !important
    }
}

@media (min-width: 992px) {
    .float-lg-left {
        float: left !important
    }

    .float-lg-right {
        float: right !important
    }

    .float-lg-none {
        float: none !important
    }
}

@media (min-width: 1200px) {
    .float-xl-left {
        float: left !important
    }

    .float-xl-right {
        float: right !important
    }

    .float-xl-none {
        float: none !important
    }
}

.overflow-auto {
    overflow: auto !important
}

.overflow-hidden {
    overflow: hidden !important
}

.position-static {
    position: static !important
}

.position-relative {
    position: relative !important
}

.position-absolute {
    position: absolute !important
}

.position-fixed {
    position: fixed !important
}

.position-sticky {
    position: -webkit-sticky !important;
    position: sticky !important
}

.fixed-top {
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    z-index: 1030
}

.fixed-bottom {
    position: fixed;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1030
}

@supports ((position:-webkit-sticky) or (position:sticky)) {
    .sticky-top {
        position: -webkit-sticky;
        position: sticky;
        top: 0;
        z-index: 1020
    }
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0
}

.sr-only-focusable:active, .sr-only-focusable:focus {
    position: static;
    width: auto;
    height: auto;
    overflow: visible;
    clip: auto;
    white-space: normal
}

.shadow-sm {
    box-shadow: 0 .125rem .25rem rgba(0, 0, 0, .075) !important
}

.shadow {
    box-shadow: 0 .5rem 1rem rgba(0, 0, 0, .15) !important
}

.shadow-lg {
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, .175) !important
}

.shadow-none {
    box-shadow: none !important
}

.w-25 {
    width: 25% !important
}

.w-50 {
    width: 50% !important
}

.w-75 {
    width: 75% !important
}

.w-100 {
    width: 100% !important
}

.w-auto {
    width: auto !important
}

.h-25 {
    height: 25% !important
}

.h-50 {
    height: 50% !important
}

.h-75 {
    height: 75% !important
}

.h-100 {
    height: 100% !important
}

.h-auto {
    height: auto !important
}

.mw-100 {
    max-width: 100% !important
}

.mh-100 {
    max-height: 100% !important
}

.min-vw-100 {
    min-width: 100vw !important
}

.min-vh-100 {
    min-height: 100vh !important
}

.vw-100 {
    width: 100vw !important
}

.vh-100 {
    height: 100vh !important
}

.stretched-link::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1;
    pointer-events: auto;
    content: "";
    background-color: rgba(0, 0, 0, 0)
}

.m-0 {
    margin: 0 !important
}

.mt-0, .my-0 {
    margin-top: 0 !important
}

.mr-0, .mx-0 {
    margin-right: 0 !important
}

.mb-0, .my-0 {
    margin-bottom: 0 !important
}

.ml-0, .mx-0 {
    margin-left: 0 !important
}

.m-1 {
    margin: .25rem !important
}

.mt-1, .my-1 {
    margin-top: .25rem !important
}

.mr-1, .mx-1 {
    margin-right: .25rem !important
}

.mb-1, .my-1 {
    margin-bottom: .25rem !important
}

.ml-1, .mx-1 {
    margin-left: .25rem !important
}

.m-2 {
    margin: .5rem !important
}

.mt-2, .my-2 {
    margin-top: .5rem !important
}

.mr-2, .mx-2 {
    margin-right: .5rem !important
}

.mb-2, .my-2 {
    margin-bottom: .5rem !important
}

.ml-2, .mx-2 {
    margin-left: .5rem !important
}

.m-3 {
    margin: 1rem !important
}

.mt-3, .my-3 {
    margin-top: 1rem !important
}

.mr-3, .mx-3 {
    margin-right: 1rem !important
}

.mb-3, .my-3 {
    margin-bottom: 1rem !important
}

.ml-3, .mx-3 {
    margin-left: 1rem !important
}

.m-4 {
    margin: 1.5rem !important
}

.mt-4, .my-4 {
    margin-top: 1.5rem !important
}

.mr-4, .mx-4 {
    margin-right: 1.5rem !important
}

.mb-4, .my-4 {
    margin-bottom: 1.5rem !important
}

.ml-4, .mx-4 {
    margin-left: 1.5rem !important
}

.m-5 {
    margin: 3rem !important
}

.mt-5, .my-5 {
    margin-top: 3rem !important
}

.mr-5, .mx-5 {
    margin-right: 3rem !important
}

.mb-5, .my-5 {
    margin-bottom: 3rem !important
}

.ml-5, .mx-5 {
    margin-left: 3rem !important
}

.p-0 {
    padding: 0 !important
}

.pt-0, .py-0 {
    padding-top: 0 !important
}

.pr-0, .px-0 {
    padding-right: 0 !important
}

.pb-0, .py-0 {
    padding-bottom: 0 !important
}

.pl-0, .px-0 {
    padding-left: 0 !important
}

.p-1 {
    padding: .25rem !important
}

.pt-1, .py-1 {
    padding-top: .25rem !important
}

.pr-1, .px-1 {
    padding-right: .25rem !important
}

.pb-1, .py-1 {
    padding-bottom: .25rem !important
}

.pl-1, .px-1 {
    padding-left: .25rem !important
}

.p-2 {
    padding: .5rem !important
}

.pt-2, .py-2 {
    padding-top: .5rem !important
}

.pr-2, .px-2 {
    padding-right: .5rem !important
}

.pb-2, .py-2 {
    padding-bottom: .5rem !important
}

.pl-2, .px-2 {
    padding-left: .5rem !important
}

.p-3 {
    padding: 1rem !important
}

.pt-3, .py-3 {
    padding-top: 1rem !important
}

.pr-3, .px-3 {
    padding-right: 1rem !important
}

.pb-3, .py-3 {
    padding-bottom: 1rem !important
}

.pl-3, .px-3 {
    padding-left: 1rem !important
}

.p-4 {
    padding: 1.5rem !important
}

.pt-4, .py-4 {
    padding-top: 1.5rem !important
}

.pr-4, .px-4 {
    padding-right: 1.5rem !important
}

.pb-4, .py-4 {
    padding-bottom: 1.5rem !important
}

.pl-4, .px-4 {
    padding-left: 1.5rem !important
}

.p-5 {
    padding: 3rem !important
}

.pt-5, .py-5 {
    padding-top: 3rem !important
}

.pr-5, .px-5 {
    padding-right: 3rem !important
}

.pb-5, .py-5 {
    padding-bottom: 3rem !important
}

.pl-5, .px-5 {
    padding-left: 3rem !important
}

.m-n1 {
    margin: -.25rem !important
}

.mt-n1, .my-n1 {
    margin-top: -.25rem !important
}

.mr-n1, .mx-n1 {
    margin-right: -.25rem !important
}

.mb-n1, .my-n1 {
    margin-bottom: -.25rem !important
}

.ml-n1, .mx-n1 {
    margin-left: -.25rem !important
}

.m-n2 {
    margin: -.5rem !important
}

.mt-n2, .my-n2 {
    margin-top: -.5rem !important
}

.mr-n2, .mx-n2 {
    margin-right: -.5rem !important
}

.mb-n2, .my-n2 {
    margin-bottom: -.5rem !important
}

.ml-n2, .mx-n2 {
    margin-left: -.5rem !important
}

.m-n3 {
    margin: -1rem !important
}

.mt-n3, .my-n3 {
    margin-top: -1rem !important
}

.mr-n3, .mx-n3 {
    margin-right: -1rem !important
}

.mb-n3, .my-n3 {
    margin-bottom: -1rem !important
}

.ml-n3, .mx-n3 {
    margin-left: -1rem !important
}

.m-n4 {
    margin: -1.5rem !important
}

.mt-n4, .my-n4 {
    margin-top: -1.5rem !important
}

.mr-n4, .mx-n4 {
    margin-right: -1.5rem !important
}

.mb-n4, .my-n4 {
    margin-bottom: -1.5rem !important
}

.ml-n4, .mx-n4 {
    margin-left: -1.5rem !important
}

.m-n5 {
    margin: -3rem !important
}

.mt-n5, .my-n5 {
    margin-top: -3rem !important
}

.mr-n5, .mx-n5 {
    margin-right: -3rem !important
}

.mb-n5, .my-n5 {
    margin-bottom: -3rem !important
}

.ml-n5, .mx-n5 {
    margin-left: -3rem !important
}

.m-auto {
    margin: auto !important
}

.mt-auto, .my-auto {
    margin-top: auto !important
}

.mr-auto, .mx-auto {
    margin-right: auto !important
}

.mb-auto, .my-auto {
    margin-bottom: auto !important
}

.ml-auto, .mx-auto {
    margin-left: auto !important
}

@media (min-width: 576px) {
    .m-sm-0 {
        margin: 0 !important
    }

    .mt-sm-0, .my-sm-0 {
        margin-top: 0 !important
    }

    .mr-sm-0, .mx-sm-0 {
        margin-right: 0 !important
    }

    .mb-sm-0, .my-sm-0 {
        margin-bottom: 0 !important
    }

    .ml-sm-0, .mx-sm-0 {
        margin-left: 0 !important
    }

    .m-sm-1 {
        margin: .25rem !important
    }

    .mt-sm-1, .my-sm-1 {
        margin-top: .25rem !important
    }

    .mr-sm-1, .mx-sm-1 {
        margin-right: .25rem !important
    }

    .mb-sm-1, .my-sm-1 {
        margin-bottom: .25rem !important
    }

    .ml-sm-1, .mx-sm-1 {
        margin-left: .25rem !important
    }

    .m-sm-2 {
        margin: .5rem !important
    }

    .mt-sm-2, .my-sm-2 {
        margin-top: .5rem !important
    }

    .mr-sm-2, .mx-sm-2 {
        margin-right: .5rem !important
    }

    .mb-sm-2, .my-sm-2 {
        margin-bottom: .5rem !important
    }

    .ml-sm-2, .mx-sm-2 {
        margin-left: .5rem !important
    }

    .m-sm-3 {
        margin: 1rem !important
    }

    .mt-sm-3, .my-sm-3 {
        margin-top: 1rem !important
    }

    .mr-sm-3, .mx-sm-3 {
        margin-right: 1rem !important
    }

    .mb-sm-3, .my-sm-3 {
        margin-bottom: 1rem !important
    }

    .ml-sm-3, .mx-sm-3 {
        margin-left: 1rem !important
    }

    .m-sm-4 {
        margin: 1.5rem !important
    }

    .mt-sm-4, .my-sm-4 {
        margin-top: 1.5rem !important
    }

    .mr-sm-4, .mx-sm-4 {
        margin-right: 1.5rem !important
    }

    .mb-sm-4, .my-sm-4 {
        margin-bottom: 1.5rem !important
    }

    .ml-sm-4, .mx-sm-4 {
        margin-left: 1.5rem !important
    }

    .m-sm-5 {
        margin: 3rem !important
    }

    .mt-sm-5, .my-sm-5 {
        margin-top: 3rem !important
    }

    .mr-sm-5, .mx-sm-5 {
        margin-right: 3rem !important
    }

    .mb-sm-5, .my-sm-5 {
        margin-bottom: 3rem !important
    }

    .ml-sm-5, .mx-sm-5 {
        margin-left: 3rem !important
    }

    .p-sm-0 {
        padding: 0 !important
    }

    .pt-sm-0, .py-sm-0 {
        padding-top: 0 !important
    }

    .pr-sm-0, .px-sm-0 {
        padding-right: 0 !important
    }

    .pb-sm-0, .py-sm-0 {
        padding-bottom: 0 !important
    }

    .pl-sm-0, .px-sm-0 {
        padding-left: 0 !important
    }

    .p-sm-1 {
        padding: .25rem !important
    }

    .pt-sm-1, .py-sm-1 {
        padding-top: .25rem !important
    }

    .pr-sm-1, .px-sm-1 {
        padding-right: .25rem !important
    }

    .pb-sm-1, .py-sm-1 {
        padding-bottom: .25rem !important
    }

    .pl-sm-1, .px-sm-1 {
        padding-left: .25rem !important
    }

    .p-sm-2 {
        padding: .5rem !important
    }

    .pt-sm-2, .py-sm-2 {
        padding-top: .5rem !important
    }

    .pr-sm-2, .px-sm-2 {
        padding-right: .5rem !important
    }

    .pb-sm-2, .py-sm-2 {
        padding-bottom: .5rem !important
    }

    .pl-sm-2, .px-sm-2 {
        padding-left: .5rem !important
    }

    .p-sm-3 {
        padding: 1rem !important
    }

    .pt-sm-3, .py-sm-3 {
        padding-top: 1rem !important
    }

    .pr-sm-3, .px-sm-3 {
        padding-right: 1rem !important
    }

    .pb-sm-3, .py-sm-3 {
        padding-bottom: 1rem !important
    }

    .pl-sm-3, .px-sm-3 {
        padding-left: 1rem !important
    }

    .p-sm-4 {
        padding: 1.5rem !important
    }

    .pt-sm-4, .py-sm-4 {
        padding-top: 1.5rem !important
    }

    .pr-sm-4, .px-sm-4 {
        padding-right: 1.5rem !important
    }

    .pb-sm-4, .py-sm-4 {
        padding-bottom: 1.5rem !important
    }

    .pl-sm-4, .px-sm-4 {
        padding-left: 1.5rem !important
    }

    .p-sm-5 {
        padding: 3rem !important
    }

    .pt-sm-5, .py-sm-5 {
        padding-top: 3rem !important
    }

    .pr-sm-5, .px-sm-5 {
        padding-right: 3rem !important
    }

    .pb-sm-5, .py-sm-5 {
        padding-bottom: 3rem !important
    }

    .pl-sm-5, .px-sm-5 {
        padding-left: 3rem !important
    }

    .m-sm-n1 {
        margin: -.25rem !important
    }

    .mt-sm-n1, .my-sm-n1 {
        margin-top: -.25rem !important
    }

    .mr-sm-n1, .mx-sm-n1 {
        margin-right: -.25rem !important
    }

    .mb-sm-n1, .my-sm-n1 {
        margin-bottom: -.25rem !important
    }

    .ml-sm-n1, .mx-sm-n1 {
        margin-left: -.25rem !important
    }

    .m-sm-n2 {
        margin: -.5rem !important
    }

    .mt-sm-n2, .my-sm-n2 {
        margin-top: -.5rem !important
    }

    .mr-sm-n2, .mx-sm-n2 {
        margin-right: -.5rem !important
    }

    .mb-sm-n2, .my-sm-n2 {
        margin-bottom: -.5rem !important
    }

    .ml-sm-n2, .mx-sm-n2 {
        margin-left: -.5rem !important
    }

    .m-sm-n3 {
        margin: -1rem !important
    }

    .mt-sm-n3, .my-sm-n3 {
        margin-top: -1rem !important
    }

    .mr-sm-n3, .mx-sm-n3 {
        margin-right: -1rem !important
    }

    .mb-sm-n3, .my-sm-n3 {
        margin-bottom: -1rem !important
    }

    .ml-sm-n3, .mx-sm-n3 {
        margin-left: -1rem !important
    }

    .m-sm-n4 {
        margin: -1.5rem !important
    }

    .mt-sm-n4, .my-sm-n4 {
        margin-top: -1.5rem !important
    }

    .mr-sm-n4, .mx-sm-n4 {
        margin-right: -1.5rem !important
    }

    .mb-sm-n4, .my-sm-n4 {
        margin-bottom: -1.5rem !important
    }

    .ml-sm-n4, .mx-sm-n4 {
        margin-left: -1.5rem !important
    }

    .m-sm-n5 {
        margin: -3rem !important
    }

    .mt-sm-n5, .my-sm-n5 {
        margin-top: -3rem !important
    }

    .mr-sm-n5, .mx-sm-n5 {
        margin-right: -3rem !important
    }

    .mb-sm-n5, .my-sm-n5 {
        margin-bottom: -3rem !important
    }

    .ml-sm-n5, .mx-sm-n5 {
        margin-left: -3rem !important
    }

    .m-sm-auto {
        margin: auto !important
    }

    .mt-sm-auto, .my-sm-auto {
        margin-top: auto !important
    }

    .mr-sm-auto, .mx-sm-auto {
        margin-right: auto !important
    }

    .mb-sm-auto, .my-sm-auto {
        margin-bottom: auto !important
    }

    .ml-sm-auto, .mx-sm-auto {
        margin-left: auto !important
    }
}

@media (min-width: 768px) {
    .m-md-0 {
        margin: 0 !important
    }

    .mt-md-0, .my-md-0 {
        margin-top: 0 !important
    }

    .mr-md-0, .mx-md-0 {
        margin-right: 0 !important
    }

    .mb-md-0, .my-md-0 {
        margin-bottom: 0 !important
    }

    .ml-md-0, .mx-md-0 {
        margin-left: 0 !important
    }

    .m-md-1 {
        margin: .25rem !important
    }

    .mt-md-1, .my-md-1 {
        margin-top: .25rem !important
    }

    .mr-md-1, .mx-md-1 {
        margin-right: .25rem !important
    }

    .mb-md-1, .my-md-1 {
        margin-bottom: .25rem !important
    }

    .ml-md-1, .mx-md-1 {
        margin-left: .25rem !important
    }

    .m-md-2 {
        margin: .5rem !important
    }

    .mt-md-2, .my-md-2 {
        margin-top: .5rem !important
    }

    .mr-md-2, .mx-md-2 {
        margin-right: .5rem !important
    }

    .mb-md-2, .my-md-2 {
        margin-bottom: .5rem !important
    }

    .ml-md-2, .mx-md-2 {
        margin-left: .5rem !important
    }

    .m-md-3 {
        margin: 1rem !important
    }

    .mt-md-3, .my-md-3 {
        margin-top: 1rem !important
    }

    .mr-md-3, .mx-md-3 {
        margin-right: 1rem !important
    }

    .mb-md-3, .my-md-3 {
        margin-bottom: 1rem !important
    }

    .ml-md-3, .mx-md-3 {
        margin-left: 1rem !important
    }

    .m-md-4 {
        margin: 1.5rem !important
    }

    .mt-md-4, .my-md-4 {
        margin-top: 1.5rem !important
    }

    .mr-md-4, .mx-md-4 {
        margin-right: 1.5rem !important
    }

    .mb-md-4, .my-md-4 {
        margin-bottom: 1.5rem !important
    }

    .ml-md-4, .mx-md-4 {
        margin-left: 1.5rem !important
    }

    .m-md-5 {
        margin: 3rem !important
    }

    .mt-md-5, .my-md-5 {
        margin-top: 3rem !important
    }

    .mr-md-5, .mx-md-5 {
        margin-right: 3rem !important
    }

    .mb-md-5, .my-md-5 {
        margin-bottom: 3rem !important
    }

    .ml-md-5, .mx-md-5 {
        margin-left: 3rem !important
    }

    .p-md-0 {
        padding: 0 !important
    }

    .pt-md-0, .py-md-0 {
        padding-top: 0 !important
    }

    .pr-md-0, .px-md-0 {
        padding-right: 0 !important
    }

    .pb-md-0, .py-md-0 {
        padding-bottom: 0 !important
    }

    .pl-md-0, .px-md-0 {
        padding-left: 0 !important
    }

    .p-md-1 {
        padding: .25rem !important
    }

    .pt-md-1, .py-md-1 {
        padding-top: .25rem !important
    }

    .pr-md-1, .px-md-1 {
        padding-right: .25rem !important
    }

    .pb-md-1, .py-md-1 {
        padding-bottom: .25rem !important
    }

    .pl-md-1, .px-md-1 {
        padding-left: .25rem !important
    }

    .p-md-2 {
        padding: .5rem !important
    }

    .pt-md-2, .py-md-2 {
        padding-top: .5rem !important
    }

    .pr-md-2, .px-md-2 {
        padding-right: .5rem !important
    }

    .pb-md-2, .py-md-2 {
        padding-bottom: .5rem !important
    }

    .pl-md-2, .px-md-2 {
        padding-left: .5rem !important
    }

    .p-md-3 {
        padding: 1rem !important
    }

    .pt-md-3, .py-md-3 {
        padding-top: 1rem !important
    }

    .pr-md-3, .px-md-3 {
        padding-right: 1rem !important
    }

    .pb-md-3, .py-md-3 {
        padding-bottom: 1rem !important
    }

    .pl-md-3, .px-md-3 {
        padding-left: 1rem !important
    }

    .p-md-4 {
        padding: 1.5rem !important
    }

    .pt-md-4, .py-md-4 {
        padding-top: 1.5rem !important
    }

    .pr-md-4, .px-md-4 {
        padding-right: 1.5rem !important
    }

    .pb-md-4, .py-md-4 {
        padding-bottom: 1.5rem !important
    }

    .pl-md-4, .px-md-4 {
        padding-left: 1.5rem !important
    }

    .p-md-5 {
        padding: 3rem !important
    }

    .pt-md-5, .py-md-5 {
        padding-top: 3rem !important
    }

    .pr-md-5, .px-md-5 {
        padding-right: 3rem !important
    }

    .pb-md-5, .py-md-5 {
        padding-bottom: 3rem !important
    }

    .pl-md-5, .px-md-5 {
        padding-left: 3rem !important
    }

    .m-md-n1 {
        margin: -.25rem !important
    }

    .mt-md-n1, .my-md-n1 {
        margin-top: -.25rem !important
    }

    .mr-md-n1, .mx-md-n1 {
        margin-right: -.25rem !important
    }

    .mb-md-n1, .my-md-n1 {
        margin-bottom: -.25rem !important
    }

    .ml-md-n1, .mx-md-n1 {
        margin-left: -.25rem !important
    }

    .m-md-n2 {
        margin: -.5rem !important
    }

    .mt-md-n2, .my-md-n2 {
        margin-top: -.5rem !important
    }

    .mr-md-n2, .mx-md-n2 {
        margin-right: -.5rem !important
    }

    .mb-md-n2, .my-md-n2 {
        margin-bottom: -.5rem !important
    }

    .ml-md-n2, .mx-md-n2 {
        margin-left: -.5rem !important
    }

    .m-md-n3 {
        margin: -1rem !important
    }

    .mt-md-n3, .my-md-n3 {
        margin-top: -1rem !important
    }

    .mr-md-n3, .mx-md-n3 {
        margin-right: -1rem !important
    }

    .mb-md-n3, .my-md-n3 {
        margin-bottom: -1rem !important
    }

    .ml-md-n3, .mx-md-n3 {
        margin-left: -1rem !important
    }

    .m-md-n4 {
        margin: -1.5rem !important
    }

    .mt-md-n4, .my-md-n4 {
        margin-top: -1.5rem !important
    }

    .mr-md-n4, .mx-md-n4 {
        margin-right: -1.5rem !important
    }

    .mb-md-n4, .my-md-n4 {
        margin-bottom: -1.5rem !important
    }

    .ml-md-n4, .mx-md-n4 {
        margin-left: -1.5rem !important
    }

    .m-md-n5 {
        margin: -3rem !important
    }

    .mt-md-n5, .my-md-n5 {
        margin-top: -3rem !important
    }

    .mr-md-n5, .mx-md-n5 {
        margin-right: -3rem !important
    }

    .mb-md-n5, .my-md-n5 {
        margin-bottom: -3rem !important
    }

    .ml-md-n5, .mx-md-n5 {
        margin-left: -3rem !important
    }

    .m-md-auto {
        margin: auto !important
    }

    .mt-md-auto, .my-md-auto {
        margin-top: auto !important
    }

    .mr-md-auto, .mx-md-auto {
        margin-right: auto !important
    }

    .mb-md-auto, .my-md-auto {
        margin-bottom: auto !important
    }

    .ml-md-auto, .mx-md-auto {
        margin-left: auto !important
    }
}

@media (min-width: 992px) {
    .m-lg-0 {
        margin: 0 !important
    }

    .mt-lg-0, .my-lg-0 {
        margin-top: 0 !important
    }

    .mr-lg-0, .mx-lg-0 {
        margin-right: 0 !important
    }

    .mb-lg-0, .my-lg-0 {
        margin-bottom: 0 !important
    }

    .ml-lg-0, .mx-lg-0 {
        margin-left: 0 !important
    }

    .m-lg-1 {
        margin: .25rem !important
    }

    .mt-lg-1, .my-lg-1 {
        margin-top: .25rem !important
    }

    .mr-lg-1, .mx-lg-1 {
        margin-right: .25rem !important
    }

    .mb-lg-1, .my-lg-1 {
        margin-bottom: .25rem !important
    }

    .ml-lg-1, .mx-lg-1 {
        margin-left: .25rem !important
    }

    .m-lg-2 {
        margin: .5rem !important
    }

    .mt-lg-2, .my-lg-2 {
        margin-top: .5rem !important
    }

    .mr-lg-2, .mx-lg-2 {
        margin-right: .5rem !important
    }

    .mb-lg-2, .my-lg-2 {
        margin-bottom: .5rem !important
    }

    .ml-lg-2, .mx-lg-2 {
        margin-left: .5rem !important
    }

    .m-lg-3 {
        margin: 1rem !important
    }

    .mt-lg-3, .my-lg-3 {
        margin-top: 1rem !important
    }

    .mr-lg-3, .mx-lg-3 {
        margin-right: 1rem !important
    }

    .mb-lg-3, .my-lg-3 {
        margin-bottom: 1rem !important
    }

    .ml-lg-3, .mx-lg-3 {
        margin-left: 1rem !important
    }

    .m-lg-4 {
        margin: 1.5rem !important
    }

    .mt-lg-4, .my-lg-4 {
        margin-top: 1.5rem !important
    }

    .mr-lg-4, .mx-lg-4 {
        margin-right: 1.5rem !important
    }

    .mb-lg-4, .my-lg-4 {
        margin-bottom: 1.5rem !important
    }

    .ml-lg-4, .mx-lg-4 {
        margin-left: 1.5rem !important
    }

    .m-lg-5 {
        margin: 3rem !important
    }

    .mt-lg-5, .my-lg-5 {
        margin-top: 3rem !important
    }

    .mr-lg-5, .mx-lg-5 {
        margin-right: 3rem !important
    }

    .mb-lg-5, .my-lg-5 {
        margin-bottom: 3rem !important
    }

    .ml-lg-5, .mx-lg-5 {
        margin-left: 3rem !important
    }

    .p-lg-0 {
        padding: 0 !important
    }

    .pt-lg-0, .py-lg-0 {
        padding-top: 0 !important
    }

    .pr-lg-0, .px-lg-0 {
        padding-right: 0 !important
    }

    .pb-lg-0, .py-lg-0 {
        padding-bottom: 0 !important
    }

    .pl-lg-0, .px-lg-0 {
        padding-left: 0 !important
    }

    .p-lg-1 {
        padding: .25rem !important
    }

    .pt-lg-1, .py-lg-1 {
        padding-top: .25rem !important
    }

    .pr-lg-1, .px-lg-1 {
        padding-right: .25rem !important
    }

    .pb-lg-1, .py-lg-1 {
        padding-bottom: .25rem !important
    }

    .pl-lg-1, .px-lg-1 {
        padding-left: .25rem !important
    }

    .p-lg-2 {
        padding: .5rem !important
    }

    .pt-lg-2, .py-lg-2 {
        padding-top: .5rem !important
    }

    .pr-lg-2, .px-lg-2 {
        padding-right: .5rem !important
    }

    .pb-lg-2, .py-lg-2 {
        padding-bottom: .5rem !important
    }

    .pl-lg-2, .px-lg-2 {
        padding-left: .5rem !important
    }

    .p-lg-3 {
        padding: 1rem !important
    }

    .pt-lg-3, .py-lg-3 {
        padding-top: 1rem !important
    }

    .pr-lg-3, .px-lg-3 {
        padding-right: 1rem !important
    }

    .pb-lg-3, .py-lg-3 {
        padding-bottom: 1rem !important
    }

    .pl-lg-3, .px-lg-3 {
        padding-left: 1rem !important
    }

    .p-lg-4 {
        padding: 1.5rem !important
    }

    .pt-lg-4, .py-lg-4 {
        padding-top: 1.5rem !important
    }

    .pr-lg-4, .px-lg-4 {
        padding-right: 1.5rem !important
    }

    .pb-lg-4, .py-lg-4 {
        padding-bottom: 1.5rem !important
    }

    .pl-lg-4, .px-lg-4 {
        padding-left: 1.5rem !important
    }

    .p-lg-5 {
        padding: 3rem !important
    }

    .pt-lg-5, .py-lg-5 {
        padding-top: 3rem !important
    }

    .pr-lg-5, .px-lg-5 {
        padding-right: 3rem !important
    }

    .pb-lg-5, .py-lg-5 {
        padding-bottom: 3rem !important
    }

    .pl-lg-5, .px-lg-5 {
        padding-left: 3rem !important
    }

    .m-lg-n1 {
        margin: -.25rem !important
    }

    .mt-lg-n1, .my-lg-n1 {
        margin-top: -.25rem !important
    }

    .mr-lg-n1, .mx-lg-n1 {
        margin-right: -.25rem !important
    }

    .mb-lg-n1, .my-lg-n1 {
        margin-bottom: -.25rem !important
    }

    .ml-lg-n1, .mx-lg-n1 {
        margin-left: -.25rem !important
    }

    .m-lg-n2 {
        margin: -.5rem !important
    }

    .mt-lg-n2, .my-lg-n2 {
        margin-top: -.5rem !important
    }

    .mr-lg-n2, .mx-lg-n2 {
        margin-right: -.5rem !important
    }

    .mb-lg-n2, .my-lg-n2 {
        margin-bottom: -.5rem !important
    }

    .ml-lg-n2, .mx-lg-n2 {
        margin-left: -.5rem !important
    }

    .m-lg-n3 {
        margin: -1rem !important
    }

    .mt-lg-n3, .my-lg-n3 {
        margin-top: -1rem !important
    }

    .mr-lg-n3, .mx-lg-n3 {
        margin-right: -1rem !important
    }

    .mb-lg-n3, .my-lg-n3 {
        margin-bottom: -1rem !important
    }

    .ml-lg-n3, .mx-lg-n3 {
        margin-left: -1rem !important
    }

    .m-lg-n4 {
        margin: -1.5rem !important
    }

    .mt-lg-n4, .my-lg-n4 {
        margin-top: -1.5rem !important
    }

    .mr-lg-n4, .mx-lg-n4 {
        margin-right: -1.5rem !important
    }

    .mb-lg-n4, .my-lg-n4 {
        margin-bottom: -1.5rem !important
    }

    .ml-lg-n4, .mx-lg-n4 {
        margin-left: -1.5rem !important
    }

    .m-lg-n5 {
        margin: -3rem !important
    }

    .mt-lg-n5, .my-lg-n5 {
        margin-top: -3rem !important
    }

    .mr-lg-n5, .mx-lg-n5 {
        margin-right: -3rem !important
    }

    .mb-lg-n5, .my-lg-n5 {
        margin-bottom: -3rem !important
    }

    .ml-lg-n5, .mx-lg-n5 {
        margin-left: -3rem !important
    }

    .m-lg-auto {
        margin: auto !important
    }

    .mt-lg-auto, .my-lg-auto {
        margin-top: auto !important
    }

    .mr-lg-auto, .mx-lg-auto {
        margin-right: auto !important
    }

    .mb-lg-auto, .my-lg-auto {
        margin-bottom: auto !important
    }

    .ml-lg-auto, .mx-lg-auto {
        margin-left: auto !important
    }
}

@media (min-width: 1200px) {
    .m-xl-0 {
        margin: 0 !important
    }

    .mt-xl-0, .my-xl-0 {
        margin-top: 0 !important
    }

    .mr-xl-0, .mx-xl-0 {
        margin-right: 0 !important
    }

    .mb-xl-0, .my-xl-0 {
        margin-bottom: 0 !important
    }

    .ml-xl-0, .mx-xl-0 {
        margin-left: 0 !important
    }

    .m-xl-1 {
        margin: .25rem !important
    }

    .mt-xl-1, .my-xl-1 {
        margin-top: .25rem !important
    }

    .mr-xl-1, .mx-xl-1 {
        margin-right: .25rem !important
    }

    .mb-xl-1, .my-xl-1 {
        margin-bottom: .25rem !important
    }

    .ml-xl-1, .mx-xl-1 {
        margin-left: .25rem !important
    }

    .m-xl-2 {
        margin: .5rem !important
    }

    .mt-xl-2, .my-xl-2 {
        margin-top: .5rem !important
    }

    .mr-xl-2, .mx-xl-2 {
        margin-right: .5rem !important
    }

    .mb-xl-2, .my-xl-2 {
        margin-bottom: .5rem !important
    }

    .ml-xl-2, .mx-xl-2 {
        margin-left: .5rem !important
    }

    .m-xl-3 {
        margin: 1rem !important
    }

    .mt-xl-3, .my-xl-3 {
        margin-top: 1rem !important
    }

    .mr-xl-3, .mx-xl-3 {
        margin-right: 1rem !important
    }

    .mb-xl-3, .my-xl-3 {
        margin-bottom: 1rem !important
    }

    .ml-xl-3, .mx-xl-3 {
        margin-left: 1rem !important
    }

    .m-xl-4 {
        margin: 1.5rem !important
    }

    .mt-xl-4, .my-xl-4 {
        margin-top: 1.5rem !important
    }

    .mr-xl-4, .mx-xl-4 {
        margin-right: 1.5rem !important
    }

    .mb-xl-4, .my-xl-4 {
        margin-bottom: 1.5rem !important
    }

    .ml-xl-4, .mx-xl-4 {
        margin-left: 1.5rem !important
    }

    .m-xl-5 {
        margin: 3rem !important
    }

    .mt-xl-5, .my-xl-5 {
        margin-top: 3rem !important
    }

    .mr-xl-5, .mx-xl-5 {
        margin-right: 3rem !important
    }

    .mb-xl-5, .my-xl-5 {
        margin-bottom: 3rem !important
    }

    .ml-xl-5, .mx-xl-5 {
        margin-left: 3rem !important
    }

    .p-xl-0 {
        padding: 0 !important
    }

    .pt-xl-0, .py-xl-0 {
        padding-top: 0 !important
    }

    .pr-xl-0, .px-xl-0 {
        padding-right: 0 !important
    }

    .pb-xl-0, .py-xl-0 {
        padding-bottom: 0 !important
    }

    .pl-xl-0, .px-xl-0 {
        padding-left: 0 !important
    }

    .p-xl-1 {
        padding: .25rem !important
    }

    .pt-xl-1, .py-xl-1 {
        padding-top: .25rem !important
    }

    .pr-xl-1, .px-xl-1 {
        padding-right: .25rem !important
    }

    .pb-xl-1, .py-xl-1 {
        padding-bottom: .25rem !important
    }

    .pl-xl-1, .px-xl-1 {
        padding-left: .25rem !important
    }

    .p-xl-2 {
        padding: .5rem !important
    }

    .pt-xl-2, .py-xl-2 {
        padding-top: .5rem !important
    }

    .pr-xl-2, .px-xl-2 {
        padding-right: .5rem !important
    }

    .pb-xl-2, .py-xl-2 {
        padding-bottom: .5rem !important
    }

    .pl-xl-2, .px-xl-2 {
        padding-left: .5rem !important
    }

    .p-xl-3 {
        padding: 1rem !important
    }

    .pt-xl-3, .py-xl-3 {
        padding-top: 1rem !important
    }

    .pr-xl-3, .px-xl-3 {
        padding-right: 1rem !important
    }

    .pb-xl-3, .py-xl-3 {
        padding-bottom: 1rem !important
    }

    .pl-xl-3, .px-xl-3 {
        padding-left: 1rem !important
    }

    .p-xl-4 {
        padding: 1.5rem !important
    }

    .pt-xl-4, .py-xl-4 {
        padding-top: 1.5rem !important
    }

    .pr-xl-4, .px-xl-4 {
        padding-right: 1.5rem !important
    }

    .pb-xl-4, .py-xl-4 {
        padding-bottom: 1.5rem !important
    }

    .pl-xl-4, .px-xl-4 {
        padding-left: 1.5rem !important
    }

    .p-xl-5 {
        padding: 3rem !important
    }

    .pt-xl-5, .py-xl-5 {
        padding-top: 3rem !important
    }

    .pr-xl-5, .px-xl-5 {
        padding-right: 3rem !important
    }

    .pb-xl-5, .py-xl-5 {
        padding-bottom: 3rem !important
    }

    .pl-xl-5, .px-xl-5 {
        padding-left: 3rem !important
    }

    .m-xl-n1 {
        margin: -.25rem !important
    }

    .mt-xl-n1, .my-xl-n1 {
        margin-top: -.25rem !important
    }

    .mr-xl-n1, .mx-xl-n1 {
        margin-right: -.25rem !important
    }

    .mb-xl-n1, .my-xl-n1 {
        margin-bottom: -.25rem !important
    }

    .ml-xl-n1, .mx-xl-n1 {
        margin-left: -.25rem !important
    }

    .m-xl-n2 {
        margin: -.5rem !important
    }

    .mt-xl-n2, .my-xl-n2 {
        margin-top: -.5rem !important
    }

    .mr-xl-n2, .mx-xl-n2 {
        margin-right: -.5rem !important
    }

    .mb-xl-n2, .my-xl-n2 {
        margin-bottom: -.5rem !important
    }

    .ml-xl-n2, .mx-xl-n2 {
        margin-left: -.5rem !important
    }

    .m-xl-n3 {
        margin: -1rem !important
    }

    .mt-xl-n3, .my-xl-n3 {
        margin-top: -1rem !important
    }

    .mr-xl-n3, .mx-xl-n3 {
        margin-right: -1rem !important
    }

    .mb-xl-n3, .my-xl-n3 {
        margin-bottom: -1rem !important
    }

    .ml-xl-n3, .mx-xl-n3 {
        margin-left: -1rem !important
    }

    .m-xl-n4 {
        margin: -1.5rem !important
    }

    .mt-xl-n4, .my-xl-n4 {
        margin-top: -1.5rem !important
    }

    .mr-xl-n4, .mx-xl-n4 {
        margin-right: -1.5rem !important
    }

    .mb-xl-n4, .my-xl-n4 {
        margin-bottom: -1.5rem !important
    }

    .ml-xl-n4, .mx-xl-n4 {
        margin-left: -1.5rem !important
    }

    .m-xl-n5 {
        margin: -3rem !important
    }

    .mt-xl-n5, .my-xl-n5 {
        margin-top: -3rem !important
    }

    .mr-xl-n5, .mx-xl-n5 {
        margin-right: -3rem !important
    }

    .mb-xl-n5, .my-xl-n5 {
        margin-bottom: -3rem !important
    }

    .ml-xl-n5, .mx-xl-n5 {
        margin-left: -3rem !important
    }

    .m-xl-auto {
        margin: auto !important
    }

    .mt-xl-auto, .my-xl-auto {
        margin-top: auto !important
    }

    .mr-xl-auto, .mx-xl-auto {
        margin-right: auto !important
    }

    .mb-xl-auto, .my-xl-auto {
        margin-bottom: auto !important
    }

    .ml-xl-auto, .mx-xl-auto {
        margin-left: auto !important
    }
}

.text-monospace {
    font-family: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace !important
}

.text-justify {
    text-align: justify !important
}

.text-wrap {
    white-space: normal !important
}

.text-nowrap {
    white-space: nowrap !important
}

.text-truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.text-left {
    text-align: left !important
}

.text-right {
    text-align: right !important
}

.text-center {
    text-align: center !important
}

@media (min-width: 576px) {
    .text-sm-left {
        text-align: left !important
    }

    .text-sm-right {
        text-align: right !important
    }

    .text-sm-center {
        text-align: center !important
    }
}

@media (min-width: 768px) {
    .text-md-left {
        text-align: left !important
    }

    .text-md-right {
        text-align: right !important
    }

    .text-md-center {
        text-align: center !important
    }
}

@media (min-width: 992px) {
    .text-lg-left {
        text-align: left !important
    }

    .text-lg-right {
        text-align: right !important
    }

    .text-lg-center {
        text-align: center !important
    }
}

@media (min-width: 1200px) {
    .text-xl-left {
        text-align: left !important
    }

    .text-xl-right {
        text-align: right !important
    }

    .text-xl-center {
        text-align: center !important
    }
}

.text-lowercase {
    text-transform: lowercase !important
}

.text-uppercase {
    text-transform: uppercase !important
}

.text-capitalize {
    text-transform: capitalize !important
}

.font-weight-light {
    font-weight: 300 !important
}

.font-weight-lighter {
    font-weight: lighter !important
}

.font-weight-normal {
    font-weight: 400 !important
}

.font-weight-bold {
    font-weight: 700 !important
}

.font-weight-bolder {
    font-weight: bolder !important
}

.font-italic {
    font-style: italic !important
}

.text-white {
    color: #fff !important
}

.text-primary {
    color: #007bff !important
}

a.text-primary:focus, a.text-primary:hover {
    color: #0056b3 !important
}

.text-secondary {
    color: #6c757d !important
}

a.text-secondary:focus, a.text-secondary:hover {
    color: #494f54 !important
}

.text-success {
    color: #28a745 !important
}

a.text-success:focus, a.text-success:hover {
    color: #19692c !important
}

.text-info {
    color: #17a2b8 !important
}

a.text-info:focus, a.text-info:hover {
    color: #0f6674 !important
}

.text-warning {
    color: #ffc107 !important
}

a.text-warning:focus, a.text-warning:hover {
    color: #ba8b00 !important
}

.text-danger {
    color: #dc3545 !important
}

a.text-danger:focus, a.text-danger:hover {
    color: #a71d2a !important
}

.text-light {
    color: #f8f9fa !important
}

a.text-light:focus, a.text-light:hover {
    color: #cbd3da !important
}

.text-dark {
    color: #343a40 !important
}

a.text-dark:focus, a.text-dark:hover {
    color: #121416 !important
}

.text-body {
    color: #212529 !important
}

.text-muted {
    color: #6c757d !important
}

.text-black-50 {
    color: rgba(0, 0, 0, .5) !important
}

.text-white-50 {
    color: rgba(255, 255, 255, .5) !important
}

.text-hide {
    font: 0/0 a;
    color: transparent;
    text-shadow: none;
    background-color: transparent;
    border: 0
}

.text-decoration-none {
    text-decoration: none !important
}

.text-break {
    word-break: break-word !important;
    overflow-wrap: break-word !important
}

.text-reset {
    color: inherit !important
}

.visible {
    visibility: visible !important
}

.invisible {
    visibility: hidden !important
}

@media print {
    *, ::after, ::before {
        text-shadow: none !important;
        box-shadow: none !important
    }

    a:not(.btn) {
        text-decoration: underline
    }

    abbr[title]::after {
        content: " (" attr(title) ")"
    }

    pre {
        white-space: pre-wrap !important
    }

    blockquote, pre {
        border: 1px solid #adb5bd;
        page-break-inside: avoid
    }

    thead {
        display: table-header-group
    }

    img, tr {
        page-break-inside: avoid
    }

    h2, h3, p {
        orphans: 3;
        widows: 3
    }

    h2, h3 {
        page-break-after: avoid
    }

    @page {
        size: a3
    }

    body {
        min-width: 992px !important
    }

    /* .container {
        min-width: 992px !important
    } */

    .navbar {
        display: none
    }

    .badge {
        border: 1px solid #000
    }

    .table {
        border-collapse: collapse !important
    }

    .table td, .table th {
        background-color: #fff !important
    }

    .table-bordered td, .table-bordered th {
        border: 1px solid #dee2e6 !important
    }

    .table-dark {
        color: inherit
    }

    .table-dark tbody + tbody, .table-dark td, .table-dark th, .table-dark thead th {
        border-color: #dee2e6
    }

    .table .thead-dark th {
        color: inherit;
        border-color: #dee2e6
    }
}
/*----------------------------------------------------
@File: Default Styles

This file contains the styling for the actual template, this
is the file you need to edit to change the look of the
template.
---------------------------------------------------- */
/*=====================================================================
@Template Name: KbDoc
@Version: 1.1.0

@Default Styles

Table of Content:
01/ Variables
02/ predefine
03/ header
04/ banner
05/ about
06/ portfolio
07/ team
08/ forum
09/ blog
10/ service
11/ blog
12/ contact
13/ footer


=====================================================================*/
/*----------------------------------------------------*/
/*font Variables*/
/*=================== fonts ====================*/
/* cyrillic-ext */
@font-face {
    font-family: 'Great Vibes';
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: url(https://fonts.gstatic.com/s/greatvibes/v19/RWmMoKWR9v4ksMfaWd_JN9XIiaQ6DQ.woff2) format('woff2');
    unicode-range: U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
  }
  /* cyrillic */
  @font-face {
    font-family: 'Great Vibes';
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: url(https://fonts.gstatic.com/s/greatvibes/v19/RWmMoKWR9v4ksMfaWd_JN9XBiaQ6DQ.woff2) format('woff2');
    unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
  }
  /* greek-ext */
  @font-face {
    font-family: 'Great Vibes';
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: url(https://fonts.gstatic.com/s/greatvibes/v19/RWmMoKWR9v4ksMfaWd_JN9XJiaQ6DQ.woff2) format('woff2');
    unicode-range: U+1F00-1FFF;
  }
  /* vietnamese */
  @font-face {
    font-family: 'Great Vibes';
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: url(https://fonts.gstatic.com/s/greatvibes/v19/RWmMoKWR9v4ksMfaWd_JN9XKiaQ6DQ.woff2) format('woff2');
    unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
  }
  /* latin-ext */
  @font-face {
    font-family: 'Great Vibes';
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: url(https://fonts.gstatic.com/s/greatvibes/v19/RWmMoKWR9v4ksMfaWd_JN9XLiaQ6DQ.woff2) format('woff2');
    unicode-range: U+0100-02AF, U+0304, U+0308, U+0329, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
  }
  /* latin */
  @font-face {
    font-family: 'Great Vibes';
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: url(https://fonts.gstatic.com/s/greatvibes/v19/RWmMoKWR9v4ksMfaWd_JN9XFiaQ.woff2) format('woff2');
    unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
  }
  /* cyrillic-ext */
  @font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 300;
    font-display: swap;
    src: url(https://fonts.gstatic.com/s/roboto/v32/KFOlCnqEu92Fr1MmSU5fCRc4EsA.woff2) format('woff2');
    unicode-range: U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
  }
  /* cyrillic */
  @font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 300;
    font-display: swap;
    src: url(https://fonts.gstatic.com/s/roboto/v32/KFOlCnqEu92Fr1MmSU5fABc4EsA.woff2) format('woff2');
    unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
  }
  /* greek-ext */
  @font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 300;
    font-display: swap;
    src: url(https://fonts.gstatic.com/s/roboto/v32/KFOlCnqEu92Fr1MmSU5fCBc4EsA.woff2) format('woff2');
    unicode-range: U+1F00-1FFF;
  }
  /* greek */
  @font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 300;
    font-display: swap;
    src: url(https://fonts.gstatic.com/s/roboto/v32/KFOlCnqEu92Fr1MmSU5fBxc4EsA.woff2) format('woff2');
    unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
  }
  /* vietnamese */
  @font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 300;
    font-display: swap;
    src: url(https://fonts.gstatic.com/s/roboto/v32/KFOlCnqEu92Fr1MmSU5fCxc4EsA.woff2) format('woff2');
    unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
  }
  /* latin-ext */
  @font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 300;
    font-display: swap;
    src: url(https://fonts.gstatic.com/s/roboto/v32/KFOlCnqEu92Fr1MmSU5fChc4EsA.woff2) format('woff2');
    unicode-range: U+0100-02AF, U+0304, U+0308, U+0329, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
  }
  /* latin */
  @font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 300;
    font-display: swap;
    src: url(https://fonts.gstatic.com/s/roboto/v32/KFOlCnqEu92Fr1MmSU5fBBc4.woff2) format('woff2');
    unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
  }
  /* cyrillic-ext */
  @font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: url(https://fonts.gstatic.com/s/roboto/v32/KFOmCnqEu92Fr1Mu72xKOzY.woff2) format('woff2');
    unicode-range: U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
  }
  /* cyrillic */
  @font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: url(https://fonts.gstatic.com/s/roboto/v32/KFOmCnqEu92Fr1Mu5mxKOzY.woff2) format('woff2');
    unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
  }
  /* greek-ext */
  @font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: url(https://fonts.gstatic.com/s/roboto/v32/KFOmCnqEu92Fr1Mu7mxKOzY.woff2) format('woff2');
    unicode-range: U+1F00-1FFF;
  }
  /* greek */
  @font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: url(https://fonts.gstatic.com/s/roboto/v32/KFOmCnqEu92Fr1Mu4WxKOzY.woff2) format('woff2');
    unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
  }
  /* vietnamese */
  @font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: url(https://fonts.gstatic.com/s/roboto/v32/KFOmCnqEu92Fr1Mu7WxKOzY.woff2) format('woff2');
    unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
  }
  /* latin-ext */
  @font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: url(https://fonts.gstatic.com/s/roboto/v32/KFOmCnqEu92Fr1Mu7GxKOzY.woff2) format('woff2');
    unicode-range: U+0100-02AF, U+0304, U+0308, U+0329, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
  }
  /* latin */
  @font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: url(https://fonts.gstatic.com/s/roboto/v32/KFOmCnqEu92Fr1Mu4mxK.woff2) format('woff2');
    unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
  }
  /* cyrillic-ext */
  @font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 500;
    font-display: swap;
    src: url(https://fonts.gstatic.com/s/roboto/v32/KFOlCnqEu92Fr1MmEU9fCRc4EsA.woff2) format('woff2');
    unicode-range: U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
  }
  /* cyrillic */
  @font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 500;
    font-display: swap;
    src: url(https://fonts.gstatic.com/s/roboto/v32/KFOlCnqEu92Fr1MmEU9fABc4EsA.woff2) format('woff2');
    unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
  }
  /* greek-ext */
  @font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 500;
    font-display: swap;
    src: url(https://fonts.gstatic.com/s/roboto/v32/KFOlCnqEu92Fr1MmEU9fCBc4EsA.woff2) format('woff2');
    unicode-range: U+1F00-1FFF;
  }
  /* greek */
  @font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 500;
    font-display: swap;
    src: url(https://fonts.gstatic.com/s/roboto/v32/KFOlCnqEu92Fr1MmEU9fBxc4EsA.woff2) format('woff2');
    unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
  }
  /* vietnamese */
  @font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 500;
    font-display: swap;
    src: url(https://fonts.gstatic.com/s/roboto/v32/KFOlCnqEu92Fr1MmEU9fCxc4EsA.woff2) format('woff2');
    unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
  }
  /* latin-ext */
  @font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 500;
    font-display: swap;
    src: url(https://fonts.gstatic.com/s/roboto/v32/KFOlCnqEu92Fr1MmEU9fChc4EsA.woff2) format('woff2');
    unicode-range: U+0100-02AF, U+0304, U+0308, U+0329, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
  }
  /* latin */
  @font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 500;
    font-display: swap;
    src: url(https://fonts.gstatic.com/s/roboto/v32/KFOlCnqEu92Fr1MmEU9fBBc4.woff2) format('woff2');
    unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
  }
  /* cyrillic-ext */
  @font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 700;
    font-display: swap;
    src: url(https://fonts.gstatic.com/s/roboto/v32/KFOlCnqEu92Fr1MmWUlfCRc4EsA.woff2) format('woff2');
    unicode-range: U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
  }
  /* cyrillic */
  @font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 700;
    font-display: swap;
    src: url(https://fonts.gstatic.com/s/roboto/v32/KFOlCnqEu92Fr1MmWUlfABc4EsA.woff2) format('woff2');
    unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
  }
  /* greek-ext */
  @font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 700;
    font-display: swap;
    src: url(https://fonts.gstatic.com/s/roboto/v32/KFOlCnqEu92Fr1MmWUlfCBc4EsA.woff2) format('woff2');
    unicode-range: U+1F00-1FFF;
  }
  /* greek */
  @font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 700;
    font-display: swap;
    src: url(https://fonts.gstatic.com/s/roboto/v32/KFOlCnqEu92Fr1MmWUlfBxc4EsA.woff2) format('woff2');
    unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
  }
  /* vietnamese */
  @font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 700;
    font-display: swap;
    src: url(https://fonts.gstatic.com/s/roboto/v32/KFOlCnqEu92Fr1MmWUlfCxc4EsA.woff2) format('woff2');
    unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
  }
  /* latin-ext */
  @font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 700;
    font-display: swap;
    src: url(https://fonts.gstatic.com/s/roboto/v32/KFOlCnqEu92Fr1MmWUlfChc4EsA.woff2) format('woff2');
    unicode-range: U+0100-02AF, U+0304, U+0308, U+0329, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
  }
  /* latin */
  @font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 700;
    font-display: swap;
    src: url(https://fonts.gstatic.com/s/roboto/v32/KFOlCnqEu92Fr1MmWUlfBBc4.woff2) format('woff2');
    unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
  }
.col-md-offset-right-1, .col-lg-offset-right-1 {
  margin-right: 8.33333333%;
}

/*---------------------------------------------------- */
/*----------------------------------------------------*/
html {
  height: 100%;
}

a {
  text-decoration: none;
  transition: all 0.3s ease-in-out;
}
a:hover, a:focus {
  text-decoration: none;
  outline: none;
  color: #10b3d6;
}

body {
  line-height: 26px;
  font-size: 16px;
  height: 100%;
  font-family: "Roboto", sans-serif;
  font-weight: 400;
  color: #6b707f;
}

:focus {
  outline: none;
}

h1, h2, h3, h4, h5, h6, .h1, .h2, .h3, .h4, .h5, .h6 {
  font-weight: 700;
  font-family: "Roboto", sans-serif;
  color: #1d2746;
}

figure {
  padding-top: 5px;
  padding-bottom: 5px;
  clear: both;
  border: none;
}

figure figcaption {
  color: rgba(0, 0, 0, 0.54);
  line-height: 20px;
  font-size: 14px;
  margin-top: 10px;
  text-align: center;
  max-width: 728px;
  margin-right: auto;
}

video {
  max-width: 100%;
}

table tr td p:last-child {
  margin-bottom: 0;
}

/** === Scrollbar === **/
::-webkit-scrollbar {
  width: 10px;
}

::-webkit-scrollbar-track {
  background: #fff;
}

::-webkit-scrollbar-thumb {
  background: #999999;
  border-radius: 30px;
}

/** === Predefined Global Classes === **/
.h1 {
  font-size: 50px;
}

.h2 {
  font-size: 44px;
}

.h3 {
  font-size: 36px;
}

.h4 {
  font-size: 30px;
}

.h5 {
  font-size: 24px;
}

.h6 {
  font-size: 18px;
}

.bold {
  font-weight: 700;
}

.medium {
  font-weight: 500;
}

.regular {
  font-weight: 400;
}

.shortcode_info h1, .shortcode_info h2, .shortcode_info h3, .shortcode_info h4, .shortcode_info h5, .shortcode_info h6,
.blog_single_item h1, .blog_single_item h2, .blog_single_item h3, .blog_single_item h4, .blog_single_item h5, .blog_single_item h6 {
  margin-bottom: 30px;
}

button:focus {
  outline: none;
}

/** === Container === **/
.body_wrapper {
  overflow: hidden;
}
/* 
@media (min-width: 1200px) {
  .container {
    max-width: 1200px;
  }
} */

@media (min-width: 1500px) {
  .custom_container {
    max-width: 1470px;
  }
}

.sec_pad {
  padding: 30px 0;
}

.pl-40 {
  padding-left: 40px;
}

.pl-60 {
  padding-left: 60px;
}

.pr-60 {
  padding-right: 60px;
}

.mt-40 {
  margin-top: 40px;
}

/** === List style === **/
ul.list-style {
  margin-bottom: 2rem;
}

ul.list-style li {
  margin-bottom: 0.5rem;
  list-style-type: circle;
}

ul.list-style li:last-child {
  margin-bottom: 0;
}

/** === Key === **/
.key, kbd {
  line-height: 0.95rem;
  border: 1px solid #ddd;
  color: #6b707f;
}

.key, kbd, p .key, p kbd {
  display: inline;
  display: inline-block;
  min-width: 1em;
  padding: 0.5em 0.6em 0.4em;
  margin: 2px;
  font-weight: 400;
  font-size: 0.85rem;
  font-family: "Roboto", sans-serif;
  text-align: center;
  text-decoration: none;
  line-height: 0.6rem;
  -moz-border-radius: 0.3em;
  -webkit-border-radius: 0.3em;
  border-radius: 0.3em;
  cursor: default;
  -moz-user-select: none;
  -webkit-user-select: none;
  user-select: none;
  background: #fff;
  border: 1px solid #ddd;
}

/** ===== section title scss ====== **/
.section_title {
  margin-bottom: 56px;
}
.section_title p {
  max-width: 530px;
  margin: 10px auto 0;
}
.section_title.text-left p {
  max-width: inherit;
}
.section_title.title-img {
  margin-bottom: 76px;
}
.section_title.title-img .title-lg {
  font-size: 44px;
  line-height: 1.1;
  color: #1d2746;
  margin-bottom: 30px;
  font-weight: 400;
}
.section_title.title-img .action_btn {
  padding: 9px 34px;
  box-shadow: none;
  border: 2px solid #10b3d6;
  font-weight: 500;
}
.section_title.title-img .action_btn:hover {
  background: transparent;
  color: #10b3d6;
}

.h_title {
  font-size: 30px;
  line-height: 40px;
  margin-bottom: 0;
  font-weight: 700;
  position: relative;
}
@media (max-width: 440px) {
  .h_title br {
    display: none;
  }
}

.c_head {
  font-size: 20px;
  line-height: 26px;
}

/*===== section title scss ======*/
.bg_color {
  background: #fafcfd;
}

.f_bg_color {
  background: #f6fbfc;
}

.local-video-container {
  display: -webkit-flex;
  display: flex;
  position: relative;
  -webkit-align-items: center;
  align-items: center;
  -webkit-justify-content: center;
  justify-content: center;
  overflow: hidden;
  border-radius: 2px;
  border: 1px solid rgba(0, 0, 0, 0.05);
  box-sizing: content-box;
}

/** === Back to Top Button === **/
#back-to-top {
  display: inline-block;
  background-color: rgba(220, 220, 220, 0.8);
  width: 50px;
  height: 50px;
  text-align: center;
  border-radius: 4px;
  position: fixed;
  bottom: 30px;
  right: 30px;
  transition: background-color 0.3s, opacity 0.5s, visibility 0.5s;
  opacity: 0;
  visibility: hidden;
  z-index: 1000;
}

#back-to-top::after {
  content: "2";
  font-family: eleganticons;
  font-weight: normal;
  font-style: normal;
  font-size: 2em;
  line-height: 50px;
  color: #000;
}

#back-to-top:hover {
  cursor: pointer;
  background-color: #333;
}

#back-to-top:hover::after {
  color: #fff;
}

#back-to-top:active {
  background-color: #555;
}

#back-to-top.show {
  opacity: 1;
  visibility: visible;
}

.search-focused .click_capture {
  opacity: 1;
  visibility: visible;
}
.search-focused .banner_search_form {
  position: relative;
  z-index: 999 !important;
}

/*---------------------------------------------------- */
/*----------------------------------------------------*/
.action_btn {
  font-size: 16px;
  font-weight: 400;
  color: #fff;
  box-shadow: 0 20px 30px 0 rgba(12, 118, 142, 0.24);
  border-radius: 4px;
  background: #10b3d6;
  display: inline-block;
  padding: 14px 28px;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.18s ease-in-out;
}
.action_btn i {
  vertical-align: middle;
  font-size: 18px;
  padding-left: 5px;
}
.action_btn:hover {
  box-shadow: none;
  color: #fff;
}

.error_area .action_btn {
  font-size: 16px;
}
.error_area .action_btn i {
  padding-right: 5px;
}

.doc_border_btn {
  border: 2px solid #79ccde;
  border-radius: 4px;
  color: #10b3d6;
  font-size: 14px;
  font-weight: 500;
  background: #f8fdfe;
  display: inline-block;
  padding: 6px 34px;
  transition: all 0.3s linear, color 0.2s;
}
.doc_border_btn.btn-round {
  border-radius: 30px;
}
@media (max-width: 420px) {
  .doc_border_btn.btn-round {
    font-size: 12px;
  }
}
.doc_border_btn i {
  margin-left: 6px;
  font-size: 16px;
  vertical-align: middle;
  display: inline-block;
  transition: all 0.2s linear, color 0s linear;
}
.doc_border_btn:hover {
  box-shadow: 0 10px 20px 0 rgba(12, 118, 142, 0.2);
  background: #10b3d6;
  border-color: #10b3d6;
  color: #fff;
}
.doc_border_btn:hover i {
  transform: translateX(8px);
}

.download-btn {
  background-image: -moz-linear-gradient(90deg, #192030 0%, #4c5977 100%);
  background-image: -webkit-linear-gradient(90deg, #192030 0%, #4c5977 100%);
  background-image: -ms-linear-gradient(90deg, #192030 0%, #4c5977 100%);
  color: #242424;
  text-align: right;
  width: 300px;
  height: 80px;
  padding-right: 40px;
  display: inline-block;
  font-size: 18px;
  border-radius: 5px;
  position: relative;
  overflow: hidden;
  z-index: 2;
}
.download-btn::before {
  content: "";
  background-image: -moz-linear-gradient(90deg, #afbcd0 0%, #edf1ff 100%);
  background-image: -webkit-linear-gradient(90deg, #afbcd0 0%, #edf1ff 100%);
  background-image: -ms-linear-gradient(90deg, #afbcd0 0%, #edf1ff 100%);
  position: absolute;
  right: -20px;
  top: 0px;
  width: 225px;
  height: 80px;
  border-radius: 5px;
  border-top-left-radius: 0px;
  border-bottom-left-radius: 25px;
  transform: skewX(-25deg);
  z-index: -1;
}
.download-btn .btn-icon {
  display: inline-block;
  padding-left: 20px;
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
}
.download-btn .btn-icon img {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 0 auto;
}
.download-btn .btn-txt {
  position: absolute;
  right: 45px;
  top: 50%;
  transform: translateY(-50%);
}
.download-btn .btn-txt .title {
  font-size: 24px;
  font-weight: 600;
  background: -webkit-linear-gradient(-90deg, #20273a, #4e5c7a);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.download-btn .btn-txt .prop span {
  font-size: 14px;
  color: #5b6483;
  font-weight: 400;
}
.download-btn .btn-txt .prop span + span {
  color: #0066fe;
}
.download-btn .ribbon {
  width: 150px;
  height: 150px;
  overflow: hidden;
  position: absolute;
}
.download-btn .ribbon span {
  position: absolute;
  display: block;
  width: 225px;
  padding: 15px 0;
  background-color: #3498db;
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
  color: #fff;
  font: 700 18px/1 "Lato", sans-serif;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
  text-transform: uppercase;
  text-align: center;
}
.download-btn .ribbon::before, .download-btn .ribbon::after {
  position: absolute;
  z-index: -1;
  content: "";
  display: block;
  border: 5px solid #2980b9;
}
.download-btn .ribbon.ribbon-top-right::before, .download-btn .ribbon.ribbon-top-right::after {
  border-top-color: transparent;
  border-right-color: transparent;
}
.download-btn .ribbon.ribbon-top-right::before {
  top: 0;
  left: 0;
}
.download-btn .ribbon.ribbon-top-right::after {
  bottom: 0;
  right: 0;
}
.download-btn .ribbon.ribbon-top-right {
  top: -10px;
  right: -10px;
}
.download-btn .ribbon.ribbon-top-right span {
  left: -25px;
  top: 30px;
  transform: rotate(45deg);
}

/*---------------------------------------------------- */
/*----------------------------------------------------*/
/*================= Top header area css ===============*/
.header_top {
  background: #192341;
  color: #8c93a8;
  height: 40px;
}
.header_top .menu {
  display: flex;
  align-items: center;
  height: 40px;
}
.header_top .menu li {
  margin-right: 50px;
}
.header_top .menu li:last-child {
  margin-right: 0;
}
.header_top .menu li a {
  color: #8c93a8;
  font-size: 14px;
}
.header_top .menu li a:hover {
  color: #10b3d6;
}
.header_top .right-menu {
  justify-content: flex-end;
}
.header_top .right-menu li {
  position: relative;
  margin-right: 0;
  padding-right: 15px;
}
.header_top .right-menu li::after {
  position: absolute;
  content: "";
  background: #5a6279;
  height: 13px;
  width: 1px;
  margin: 0 15px;
  top: 5px;
}
.header_top .right-menu li:last-child {
  padding-right: 0;
  padding-left: 15px;
}
.header_top .right-menu li:last-child::after {
  display: none;
}

/*================= header area css ===============*/
.navbar {
  border: 0;
  border-radius: 0;
  padding: 0;
  width: 100%;
  left: 0;
  top: 0;
  z-index: 20;
  position: absolute;
}

.menu_one {
  background: transparent;
  transition: all 0.2s linear;
}

.sticky_logo img + img {
  display: none;
}

.menu > .nav-item {
  padding-bottom: 37px;
  padding-top: 37px;
  transition: all 0.3s linear;
}
.menu > .nav-item > .nav-link {
  font-size: 14px;
  font-weight: 500;
  color: #fff;
  padding: 0;
}
.menu > .nav-item > .nav-link:after {
  display: none;
}
.menu > .nav-item.submenu .dropdown-menu {
  margin: 0;
  border-radius: 4px;
  left: -38px;
  min-width: max-content;
  border: 1px solid #e6eeef;
  background: #fff;
  padding: 20px 0;
  box-shadow: 0 30px 40px 0 rgba(4, 73, 89, 0.1);
}
.menu > .nav-item.submenu .dropdown-menu:before {
  content: "";
  width: 12px;
  height: 12px;
  background: #fff;
  position: absolute;
  top: -6px;
  transform: rotate(45deg);
  left: 40px;
  border-left: 1px solid #e6eeef;
  border-top: 1px solid #e6eeef;
}
@media (min-width: 992px) {
  .menu > .nav-item.submenu .dropdown-menu {
    transform: translateY(20px);
    transition: all 0.3s ease-in;
    opacity: 0;
    visibility: hidden;
    display: block;
  }
}
.menu > .nav-item.submenu .dropdown-menu.dropdown_menu_two .nav-item {
  padding: 0 30px;
}
.menu > .nav-item.submenu .dropdown-menu.dropdown_menu_two .nav-item + .nav-item {
  margin-top: 8px;
}
.menu > .nav-item.submenu .dropdown-menu.dropdown_menu_two .nav-item .nav-link {
  display: flex;
  align-items: center;
}
.menu > .nav-item.submenu .dropdown-menu.dropdown_menu_two .nav-item .nav-link:before {
  display: none;
}
.menu > .nav-item.submenu .dropdown-menu.dropdown_menu_two .nav-item .nav-link img {
  margin-right: 20px;
  width: 32px;
}
.menu > .nav-item.submenu .dropdown-menu .nav-item {
  padding: 0 40px 0 30px;
  transition: all 0.3s linear;
  position: relative;
}
.menu > .nav-item.submenu .dropdown-menu .nav-item + .nav-item {
  margin-top: 18px;
}
.menu > .nav-item.submenu .dropdown-menu .nav-item .nav-link {
  padding: 0;
  white-space: nowrap;
  font: 400 14px/16px "Roboto", sans-serif;
  color: #6b707f;
  transition: color 0.2s linear;
  position: relative;
  display: inline-block;
}
.menu > .nav-item.submenu .dropdown-menu .nav-item .nav-link:after {
  display: none;
}
.menu > .nav-item.submenu .dropdown-menu .nav-item .nav-link:before {
  content: "";
  width: 0;
  height: 1px;
  position: absolute;
  left: 0;
  bottom: 0;
  background: #10b3d6;
  transition: all 0.2s linear;
}
.menu > .nav-item.submenu .dropdown-menu .nav-item .nav-link h5 {
  padding-top: 9px;
  margin-bottom: 0;
  font-size: 16px;
  text-transform: capitalize;
  font-weight: 500;
  transition: all 0.2s linear;
}
.menu > .nav-item.submenu .dropdown-menu .nav-item .nav-link p {
  margin-bottom: 0;
  line-height: 29px;
  font-size: 14px;
  padding-bottom: 0;
}
.menu > .nav-item.submenu .dropdown-menu .nav-item:hover > .nav-link, .menu > .nav-item.submenu .dropdown-menu .nav-item:focus > .nav-link, .menu > .nav-item.submenu .dropdown-menu .nav-item.active > .nav-link {
  color: #10b3d6;
}
.menu > .nav-item.submenu .dropdown-menu .nav-item:hover > .nav-link:before, .menu > .nav-item.submenu .dropdown-menu .nav-item:focus > .nav-link:before, .menu > .nav-item.submenu .dropdown-menu .nav-item.active > .nav-link:before {
  width: 100%;
}
.menu > .nav-item.submenu .dropdown-menu .nav-item:hover > .nav-link h5, .menu > .nav-item.submenu .dropdown-menu .nav-item:focus > .nav-link h5, .menu > .nav-item.submenu .dropdown-menu .nav-item.active > .nav-link h5 {
  color: #10b3d6;
}
.menu > .nav-item.submenu .dropdown-menu .nav-item:hover > i, .menu > .nav-item.submenu .dropdown-menu .nav-item:focus > i, .menu > .nav-item.submenu .dropdown-menu .nav-item.active > i {
  color: #10b3d6;
}
.menu > .nav-item.submenu .dropdown-menu .nav-item > .dropdown-menu {
  transition: all 0.3s ease-in;
}
.menu > .nav-item.submenu .dropdown-menu .nav-item > .dropdown-menu:before {
  display: none;
}
@media (min-width: 992px) {
  .menu > .nav-item.submenu .dropdown-menu .nav-item > .dropdown-menu {
    position: absolute;
    left: 100%;
    top: -25px;
    opacity: 0;
    display: block;
    visibility: hidden;
    transform: translateY(20px);
    transition: all 0.3s ease-in;
  }
}
.menu > .nav-item.submenu.mega_menu .dropdown-menu {
  min-width: 540px;
  padding: 0;
  background: #4464a1;
  border: 0;
  max-height: 309px;
  min-height: 309px;
}
.menu > .nav-item.submenu.mega_menu .dropdown-menu li > div {
  min-height: 309px;
}
.menu > .nav-item.submenu.mega_menu .dropdown-menu .nav-pills {
  padding: 30px 0;
  background: #fff;
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
  height: 100%;
  margin-right: -10px;
}
.menu > .nav-item.submenu.mega_menu .dropdown-menu .tabHeader .nav-item {
  padding: 0 30px;
}
.menu > .nav-item.submenu.mega_menu .dropdown-menu .tabHeader .nav-item:after {
  content: "5";
  position: absolute;
  right: 25px;
  top: -1px;
  font-family: "ElegantIcons";
  display: block;
  color: #10b3d6;
  transition: all 0.2s linear;
  opacity: 0;
}
.menu > .nav-item.submenu.mega_menu .dropdown-menu .tabHeader .nav-item .nav-link {
  font-size: 14px;
  color: #6b707f;
  display: inline-block;
  border-radius: 0;
}
.menu > .nav-item.submenu.mega_menu .dropdown-menu .tabHeader .nav-item .nav-link.active {
  background: transparent;
}
.menu > .nav-item.submenu.mega_menu .dropdown-menu .tabHeader .nav-item + .nav-item {
  margin-top: 12px;
}
.menu > .nav-item.submenu.mega_menu .dropdown-menu .tabHeader .nav-item:hover:after, .menu > .nav-item.submenu.mega_menu .dropdown-menu .tabHeader .nav-item.active:after {
  opacity: 1;
  right: 20px;
}
.menu > .nav-item.submenu.mega_menu .dropdown-menu .tabHeader .nav-item:hover .nav-link, .menu > .nav-item.submenu.mega_menu .dropdown-menu .tabHeader .nav-item.active .nav-link {
  color: #10b3d6;
}
.menu > .nav-item.submenu.mega_menu .dropdown-menu .tabContent .tab-pane {
  padding: 26px 10px;
}
.menu > .nav-item.submenu.mega_menu .dropdown-menu .tabContent .tab-pane .tab_list {
  width: 50%;
  padding-bottom: 12px;
}
.menu > .nav-item.submenu.mega_menu .dropdown-menu .tabContent .tab-pane .tab_list.w_100 {
  width: 100%;
}
.menu > .nav-item.submenu.mega_menu .dropdown-menu .tabContent .tab-pane .tab_list li {
  margin-bottom: 12px;
}
.menu > .nav-item.submenu.mega_menu .dropdown-menu .tabContent .tab-pane .tab_list li a {
  font-size: 14px;
  font-weight: 400;
  color: #fff;
  position: relative;
  font-family: "Roboto", sans-serif;
}
.menu > .nav-item.submenu.mega_menu .dropdown-menu .tabContent .tab-pane .tab_list li a:before {
  content: "";
  width: 0;
  height: 1px;
  background: #fff;
  position: absolute;
  left: 0;
  bottom: 0;
  transition: all 0.2s linear;
}
.menu > .nav-item.submenu.mega_menu .dropdown-menu .tabContent .tab-pane .tab_list li a:hover:before, .menu > .nav-item.submenu.mega_menu .dropdown-menu .tabContent .tab-pane .tab_list li a.active:before {
  width: 100%;
}
.menu > .nav-item.submenu.mega_menu .dropdown-menu .tabContent .tab-pane p {
  font-size: 14px;
  line-height: 30px;
  color: rgba(255, 255, 255, 0.5);
  margin-bottom: 0;
}
.menu > .nav-item.active > i {
  color: #10b3d6;
}
.menu > .nav-item.active .nav-link {
  color: #10b3d6;
}
.menu > .nav-item.active .nav-link:before {
  transform: scaleX(1);
  opacity: 1;
  background: #10b3d6;
}
.menu > .nav-item:hover > i {
  color: #10b3d6;
}
.menu > .nav-item:hover .nav-link {
  color: #10b3d6;
}
.menu > .nav-item:hover .nav-link:before {
  transform: scaleX(1);
  opacity: 1;
  background: #10b3d6;
}
@media (min-width: 992px) {
  .menu > .nav-item:hover .dropdown-menu {
    opacity: 1;
    transform: translateY(0);
    visibility: visible;
  }
}
@media (min-width: 992px) and (min-width: 992px) {
  .menu > .nav-item:hover .dropdown-menu > .nav-item:hover .dropdown-menu {
    transform: scaleY(1);
    opacity: 1;
    visibility: visible;
  }
}
.menu > .nav-item + .nav-item {
  margin-left: 55px;
}

.nav_btn {
  font-size: 14px;
  font-weight: 500;
  padding: 8px 25px;
  text-align: center;
  border-radius: 4px;
  border: 2px solid #fff;
  transition: all 0.3s linear;
  margin-left: 80px;
  background: #fff;
  color: #10b3d6;
}
.nav_btn.round-btn {
  border-radius: 50px;
  background-color: rgba(16, 179, 214, 0.031);
}
.nav_btn.icon-btn {
  border-color: #79ccde;
}
.nav_btn.icon-btn i {
  color: #79ccde;
  padding-right: 10px;
}
.nav_btn:hover {
  background: #10b3d6;
  border-color: #10b3d6;
  color: #fff;
}
.nav_btn:hover i {
  color: #fff;
}

.mCSB_inside > .mCSB_container {
  margin-right: 0;
}

/*================= header area css ===============*/
/*=========== dk_menu css ===========*/
.menu_two {
  box-shadow: 0 4px 10px 0 rgba(12, 118, 142, 0.06);
  z-index: 10;
  background: #fff;
}
.menu_two .nav_btn {
  border: 2px solid #79ccde;
  color: #10b3d6;
  background: #f8fdfe;
}
.menu_two .nav_btn i {
  padding-right: 8px;
  font-size: 12px;
}
.menu_two .nav_btn:hover {
  background: #10b3d6;
  color: #fff;
  border-color: #10b3d6;
}
.menu_two .menu_toggle .hamburger span, .menu_two .menu_toggle .hamburger-cross span {
  background: #1d2746;
}
.menu_two + div, .menu_two + section {
  margin-top: 98px;
}

.dk_menu > .nav-item > .nav-link {
  color: #6b707f;
}
.dk_menu > .nav-item > .nav-link i {
  padding-right: 5px;
  font-size: 14px;
}

.mobile_dropdown_icon {
  display: none;
}

/*=========== dk_menu css ===========*/
@media (max-width: 991px) {
  .display_none {
    display: none;
  }
}

.sticky-nav-doc .navbar-brand img + img {
  display: none;
}
.sticky-nav-doc .mobile_main_menu .menu_toggle .hamburger span {
  background: #1d2746;
}

.mobile_main_menu {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 10;
  padding: 13px 0;
}
@media (min-width: 992px) {
  .mobile_main_menu {
    display: none;
  }
}
.mobile_main_menu .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.mobile_main_menu .menu_toggle {
  margin-right: 18px;
}
.mobile_main_menu .menu_toggle .hamburger span {
  width: 100%;
}
.mobile_main_menu .mobile_menu_left {
  display: flex;
}
.mobile_main_menu .nav_btn {
  margin: 0;
}
.mobile_main_menu.navbar_fixed {
  box-shadow: 0 4px 10px 0 rgba(12, 118, 142, 0.06);
  background: #fff;
  transition: all 0.2s, top 0.4s linear;
}
.mobile_main_menu.navbar_fixed .nav_btn {
  border-color: #6b707f;
}
.mobile_main_menu.navbar_fixed .nav_btn:hover {
  border-color: #10b3d6;
}

/*=========== menu_four css ===========*/
.sticky_menu .menu_one {
  background: #fff;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.15);
  position: fixed;
  width: 100%;
  top: 0;
  left: 0;
}
.sticky_menu .menu_one + div, .sticky_menu .menu_one + section {
  margin-top: 100px;
}
.sticky_menu .menu_one .menu > .nav-item .nav-link {
  position: relative;
  color: #6b707f;
  font-weight: 400;
}
.sticky_menu .menu_one .menu > .nav-item:hover > .nav-link, .sticky_menu .menu_one .menu > .nav-item.active > .nav-link {
  color: #10b3d6;
}

.menu_social {
  margin-bottom: 0;
  margin-left: 70px;
}

.search_form {
  width: 250px;
  list-style: none;
  position: relative;
}
.search_form .form-control {
  font-size: 14px;
  font-weight: 400;
  padding: 15px;
  height: 45px;
  margin-top: -2px;
  padding-left: 20px;
  background: #fafcfd;
  border: 1px solid #e1e4e5;
  border-radius: 4px;
  box-shadow: none;
}
.search_form .form-control.placeholder {
  color: #8f94a6;
}
.search_form .form-control:-moz-placeholder {
  color: #8f94a6;
}
.search_form .form-control::-moz-placeholder {
  color: #8f94a6;
}
.search_form .form-control::-webkit-input-placeholder {
  color: #8f94a6;
}
.search_form button {
  position: absolute;
  top: 11px;
  right: 15px;
  border: 0;
  padding: 0;
  color: #1d2746;
  background: transparent;
  font-size: 14px;
  z-index: 3;
}

#stickyTwo .menu > .nav-item.submenu.mega_menu .dropdown-menu, .menu_two .menu > .nav-item.submenu.mega_menu .dropdown-menu {
  border-top: 1px solid #e6eeef;
}

/*=========== menu_four css ===========*/
.nav_btn_two {
  border-color: #79ccde;
  background: #f1f8fc;
}
.nav_btn_two:hover {
  border-color: #10b3d6;
}

.navbar_fixed {
  position: fixed;
}
.navbar_fixed.menu_one {
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.15);
  background: #fff;
  transition: all 0.2s, top 0.4s linear;
  margin-top: 0;
}
.navbar_fixed.menu_one .menu > .nav-item {
  padding-bottom: 25px;
  padding-top: 25px;
}
.navbar_fixed.menu_one .menu > .nav-item > .nav-link {
  color: #6b707f;
}
.navbar_fixed.menu_one .menu > .nav-item:hover > .nav-link, .navbar_fixed.menu_one .menu > .nav-item.active > .nav-link {
  color: #10b3d6;
}
.navbar_fixed.menu_one .menu > .nav-item.submenu.mega_menu .dropdown-menu {
  border-top: 1px solid #e6eeef;
}
.navbar_fixed.menu_one .nav_btn {
  color: #10b3d6;
  border: 2px solid #79ccde;
}
.navbar_fixed.menu_one .nav_btn:hover {
  background: #10b3d6;
  color: #fff;
  border-color: #10b3d6;
}
.navbar_fixed .sticky_logo img {
  display: none;
}
.navbar_fixed .sticky_logo img + img {
  display: block;
}
.navbar_fixed.menu_two {
  box-shadow: 0 4px 10px 0 rgba(12, 118, 142, 0.06);
  width: 100%;
  background: #fff;
  left: 0;
  top: 0;
  position: fixed;
  transition: top 0.4s linear;
}
@media (min-width: 992px) {
  .navbar_fixed.menu_two .menu > .nav-item {
    padding-bottom: 30px;
    padding-top: 30px;
  }
}
.navbar_fixed.menu_two + div, .navbar_fixed.menu_two + section {
  margin-top: 86px;
}
.navbar_fixed .menu_toggle .hamburger span, .navbar_fixed .menu_toggle .hamburger-cross span {
  background: #1d2746;
}

.navbar-toggler {
  padding-right: 0;
  padding-left: 0;
}

.hamburger {
  height: 100%;
  width: 100%;
  display: block;
}
.hamburger span {
  width: 0%;
  height: 2px;
  position: relative;
  top: 0;
  left: 0;
  margin: 4px 0;
  display: block;
  background: #fff;
  border-radius: 3px;
  -webkit-transition: 0.2s ease-in-out;
  transition: 0.2s ease-in-out;
}
.hamburger span:nth-child(1) {
  transition-delay: 0s;
}
.hamburger span:nth-child(2) {
  transition-delay: 0.125s;
}
.hamburger span:nth-child(3) {
  transition-delay: 0.2s;
}

.menu_toggle {
  width: 22px;
  height: 22px;
  position: relative;
  cursor: pointer;
  display: block;
}
.menu_toggle .hamburger {
  position: absolute;
}
.menu_toggle .hamburger-cross {
  position: absolute;
  height: 100%;
  width: 100%;
  transform: rotate(45deg);
  display: block;
}
.menu_toggle .hamburger-cross span {
  display: block;
  background: #fff;
  border-radius: 3px;
  -webkit-transition: 0.2s ease-in-out;
  transition: 0.2s ease-in-out;
}
.menu_toggle .hamburger-cross span:nth-child(1) {
  height: 100%;
  width: 2px;
  position: absolute;
  top: 0;
  left: 10px;
  transition-delay: 0.3s;
}
.menu_toggle .hamburger-cross span:nth-child(2) {
  width: 100%;
  height: 2px;
  position: absolute;
  left: 0;
  top: 10px;
  transition-delay: 0.4s;
}

.collapsed .menu_toggle .hamburger span {
  width: 100%;
}
.collapsed .menu_toggle .hamburger span:nth-child(1) {
  transition-delay: 0.3s;
}
.collapsed .menu_toggle .hamburger span:nth-child(2) {
  transition-delay: 0.4s;
}
.collapsed .menu_toggle .hamburger span:nth-child(3) {
  transition-delay: 0.5s;
}
.collapsed .menu_toggle .hamburger-cross span:nth-child(1) {
  height: 0%;
  transition-delay: 0s;
}
.collapsed .menu_toggle .hamburger-cross span:nth-child(2) {
  width: 0%;
  transition-delay: 0.2s;
}

/*=========== side_menu css ===========*/
.side_menu {
  width: 300px;
  height: 100vh;
  max-height: 100vh;
  top: 0;
  position: fixed;
  transform: translate3d(-300px, 0, 0);
  transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1013;
  background: #fff;
  padding: 20px 0;
  overflow-x: hidden;
}
.side_menu .mobile_menu_header {
  display: flex;
  align-items: center;
  padding-left: 15px;
  padding-right: 15px;
}
.side_menu .mobile_menu_header .close_nav {
  font-size: 25px;
  color: #1d2746;
  margin-right: 15px;
}
.side_menu .mobile_menu_header .close_nav i + i {
  display: none;
}
.side_menu .doc_left_sidebarlist {
  border: 0;
}
.side_menu .doc_left_sidebarlist:before {
  display: none;
}
.side_menu .nav-sidebar .nav-item {
  padding-right: 0;
}
.side_menu .mobile_nav_wrapper {
  display: flex;
  transform: translate3d(-300px, 0, 0);
  transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}
.side_menu .mobile_nav_top, .side_menu .mobile_nav_bottom {
  height: calc(100vh - 65px);
  flex-shrink: 0;
  display: block;
  width: 300px;
  padding: 0 15px;
}
.side_menu .mobile_nav_top {
  padding-top: 30px;
}
.side_menu .mobile_nav_top .menu {
  padding-left: 0;
  padding-right: 0;
}
.side_menu.menu-opened .mobile_nav_wrapper {
  transform: translate3d(0, 0, 0);
}
.side_menu.menu-opened .close_nav i {
  display: none;
}
.side_menu.menu-opened .close_nav i + i {
  display: block;
}

.click_capture {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  visibility: hidden;
  z-index: 99;
  opacity: 0;
  background: rgba(0, 0, 0, 0.4);
  transition: all 0.3s linear;
}

@media (max-width: 992px) {
  .menu-is-opened {
    overflow: hidden;
    height: 100%;
  }
  .menu-is-opened .click_capture {
    opacity: 1;
    visibility: visible;
  }
  .menu-is-opened .side_menu {
    transform: translate3d(0, 0, 0);
  }
}
/*---------------------------------------------------- */
/*----------------------------------------------------*/
/*=========== doc_banner_area_one css ============*/
.doc_banner_area_one {
  height: 750px;
  display: flex;
  align-items: center;
  background-image: linear-gradient(60deg, #10b3d6 0%, #1d2746 100%);
  position: relative;
  overflow: hidden;
}
.doc_banner_area_one .star {
  z-index: -1;
}
.doc_banner_area_one .star_one {
  right: 170px;
  top: 50%;
  animation: star 9s both infinite alternate 5s;
}
.doc_banner_area_one .star_two {
  top: 33px;
  right: 147px;
  animation: star2 4s ease-in-out infinite alternate;
}
.doc_banner_area_one .star_three {
  top: 50px;
  left: 347px;
  animation: star 7s ease-in-out infinite alternate;
}
.doc_banner_area_one .one {
  bottom: 0;
  left: 300px;
}
.doc_banner_area_one .two {
  bottom: 0;
  right: 320px;
}
.doc_banner_area_one .three {
  bottom: 0;
  left: 520px;
}
.doc_banner_area_one .four {
  bottom: 0;
  left: 58%;
  margin-left: -20px;
}
.doc_banner_area_one .five {
  bottom: 155px;
  left: 74%;
  transform: translateX(-50%);
}

.bl_left {
  width: 150px;
  height: 145px;
  bottom: -7px;
  left: 20px;
  z-index: 1;
}

.bl_right {
  width: 135px;
  height: 110px;
  right: 30px;
  bottom: -7px;
  z-index: 1;
}

.dark {
  position: absolute;
  width: 100%;
  left: 0;
  height: auto;
  bottom: 0;
  z-index: -1;
}

.dark_two {
  position: absolute;
  width: 100%;
  left: 0;
  height: auto;
  bottom: 0;
  z-index: -1;
}

.star {
  top: 0;
  width: 100%;
  left: 0;
  z-index: 0;
  right: 0;
  height: auto;
}

.doc_banner_text {
  text-align: center;
}
.doc_banner_text h2,
.doc_banner_text p,
.doc_banner_text h6 {
  color: #fff;
}
.doc_banner_text h2 {
  font-size: 46px;
  margin-bottom: 14px;
}
.doc_banner_text p {
  font-size: 18px;
  line-height: 30px;
  margin-bottom: 0;
  font-weight: 300;
}
.doc_banner_text h6 {
  font-size: 14px;
  font-weight: 400;
  margin-bottom: 0;
  margin-top: 40px;
}
.doc_banner_text h6 span {
  color: #e1e1e1;
  margin-right: 10px;
}
.doc_banner_text h6 a {
  color: #fff;
  margin-right: 10px;
  text-decoration: underline;
}
.doc_banner_text h6 a:last-child {
  margin-right: 0;
}
.doc_banner_text .banner_search_form {
  margin-top: 65px;
}

.banner_search_form {
  max-width: 570px;
  margin: 0 auto;
  display: flex;
}
.banner_search_form .form-control {
  height: 70px;
  border-radius: 4px;
  padding-left: 30px;
  font-size: 14px;
  border: 0;
  border-radius: 4px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  box-shadow: none;
  color: #1d2746;
}
.banner_search_form .form-control.placeholder {
  color: #848996;
}
.banner_search_form .form-control:-moz-placeholder {
  color: #848996;
}
.banner_search_form .form-control::-moz-placeholder {
  color: #848996;
}
.banner_search_form .form-control::-webkit-input-placeholder {
  color: #848996;
}
.banner_search_form button {
  padding: 5px 25px;
  font-size: 20px;
  background: #10b3d6;
  color: #fff;
  text-align: center;
  border: 0;
  border-radius: 4px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  transition: all 0.4s linear;
}

/*=========== doc_banner_area_one css ============*/
/*=========== Start doc_banner_area_two css ============*/
.doc_banner_area_two {
  height: 650px;
  background: #daf6fc;
  position: relative;
  z-index: 1;
  padding-top: 160px;
}
.doc_banner_area_two .table_img {
  left: 200px;
  bottom: -6px;
  width: 250px;
}
.doc_banner_area_two .flower {
  bottom: 0;
  left: 80px;
}
.doc_banner_area_two .girl {
  right: 130px;
  bottom: 0;
}
.doc_banner_area_two .bord {
  bottom: -14px;
  right: 320px;
}

.b_plus,
.b_round,
.p_absolute {
  position: absolute;
}

.b_plus.one {
  left: 120px;
  top: 120px;
}
.b_plus.two {
  right: 25%;
  top: 210px;
}

.b_round {
  border-radius: 50%;
}
.b_round.r_one {
  right: 135px;
  top: 85px;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: #83daed;
  animation: spin1 2s infinite alternate;
}
.b_round.r_two {
  left: 30%;
  top: 160px;
  width: 8px;
  height: 8px;
  background: #10b3d6;
  animation: spin2 2s infinite alternate;
}
.b_round.r_three {
  background: #ffa3be;
  width: 16px;
  height: 16px;
  left: 24%;
  top: 310px;
  animation: spin1 2.5s infinite alternate;
}
.b_round.r_four {
  background: #ffa3be;
  width: 8px;
  height: 8px;
  right: 18%;
  bottom: 380px;
  animation: spin1 1s infinite alternate;
}

.doc_banner_text_two h2 {
  font-size: 46px;
  margin-bottom: 22px;
  font-weight: 700;
}
.doc_banner_text_two p {
  font-size: 18px;
  line-height: 30px;
  font-weight: 400;
  max-width: 440px;
  margin: 0 auto;
}
.doc_banner_text_two .banner_search_form {
  max-width: 770px;
  margin: 60px auto 0;
}
.doc_banner_text_two .banner_search_form .form-control {
  margin-right: 20px;
  border-radius: 4px;
  box-shadow: 0 4px 10px 0 rgba(12, 118, 142, 0.06);
}
.doc_banner_text_two .banner_search_form .search_btn {
  border-radius: 4px;
  font-size: 18px;
  font-weight: 400;
  box-shadow: 0 10px 20px 0 rgba(12, 118, 142, 0.1);
  background: #10b3d6;
  padding: 5px 42px;
}

.building_img {
  left: 50%;
  transform: translateX(-50%);
  bottom: 0;
  z-index: -1;
}

/*=========== End doc_banner_area_two css ============*/
/*=========== Start Banner Support ============*/
.kbDoc-banner-support {
  height: 945px;
  background-image: linear-gradient(30deg, #feefec 0%, #f5fdff 100%);
  padding: 200px 0;
  position: relative;
}
@media (max-width: 991px) {
  .kbDoc-banner-support {
    padding: 150px 0;
  }
}
@media (max-width: 768px) {
  .kbDoc-banner-support {
    padding: 100px 0;
    height: 820px;
  }
}
.kbDoc-banner-support .banner-content-wrapper {
  max-width: 770px;
  margin: 0 auto;
  position: relative;
  z-index: 2;
}
@media (max-width: 991px) {
  .kbDoc-banner-support .banner-content-wrapper {
    max-width: 90%;
  }
}
.kbDoc-banner-support .banner-content-wrapper .banner-title {
  font-size: 44px;
  font-weight: 500;
  color: #1d2746;
  margin-bottom: 12px;
}
@media (max-width: 768px) {
  .kbDoc-banner-support .banner-content-wrapper .banner-title {
    font-size: 34px;
  }
}
.kbDoc-banner-support .banner-content-wrapper p {
  font-size: 18px;
  color: #6b707f;
  line-height: 28px;
  margin-bottom: 40px;
}
@media (max-width: 991px) {
  .kbDoc-banner-support .banner-content-wrapper p br {
    display: none;
  }
}
.kbDoc-banner-support .banner-content-wrapper .banner-search-form {
  box-shadow: 0px 4px 10px 0px rgba(49, 12, 2, 0.06);
  max-width: 770px;
  margin: 0 auto;
  display: flex;
  background: #fff;
  border-radius: 30px;
}
.kbDoc-banner-support .banner-content-wrapper .banner-search-form input {
  width: 100%;
  background: transparent;
  border: 0;
  padding: 15px 30px;
  height: 60px;
}
.kbDoc-banner-support .banner-content-wrapper .banner-search-form input::placeholder {
  color: #6b707f;
}
.kbDoc-banner-support .banner-content-wrapper .banner-search-form button {
  background: transparent;
  border: 0;
  padding-right: 20px;
  color: #10b3d6;
  font-size: 20px;
}
.kbDoc-banner-support .banner-content-wrapper .banner-search-form button i {
  transform: rotate(-90deg);
  display: inline-block;
}
.kbDoc-banner-support .bottom-shape {
  position: absolute;
  width: 100%;
  left: 0;
  bottom: -1px;
}
.kbDoc-banner-support .bottom-shape img {
  width: 100%;
}

.people-image {
  margin: 0;
  padding: 0;
  list-style: none;
  position: absolute;
  width: 100%;
  height: 320px;
  max-width: 1250px;
  left: 50%;
  transform: translateX(-50%);
  bottom: 165px;
  z-index: 3;
}
@media (max-width: 767px) {
  .people-image {
    height: 280px;
  }
}
@media (max-width: 576px) {
  .people-image {
    bottom: 80px;
  }
}
.people-image li {
  position: absolute;
  box-shadow: 0px 40px 60px 0px rgba(59, 14, 3, 0.12);
  border-radius: 50%;
  border: 4px solid #fff;
  overflow: hidden;
}
.people-image li img {
  width: 100%;
}
.people-image li:nth-child(1) {
  left: 50%;
  top: 15px;
  transform: translateX(-50%);
  border: 6px solid #fff;
  height: 130px;
  width: 130px;
  box-shadow: 0px 50px 80px 0px rgba(59, 14, 3, 0.12);
}
.people-image li:nth-child(2) {
  left: 29.4%;
  top: 0;
  height: 80px;
  width: 80px;
}
.people-image li:nth-child(3) {
  right: 26.4%;
  top: -5px;
  height: 70px;
  width: 70px;
}
.people-image li:nth-child(4) {
  left: 160px;
  top: 75px;
  height: 80px;
  width: 80px;
}
.people-image li:nth-child(5) {
  left: 26.4%;
  bottom: 40px;
  height: 80px;
  width: 80px;
}
.people-image li:nth-child(6) {
  right: 38%;
  bottom: -20px;
  height: 100px;
  width: 100px;
}
.people-image li:nth-child(7) {
  right: 85px;
  bottom: 5px;
  width: 90px;
  height: 90px;
}
.people-image li:nth-child(8) {
  left: 0;
  bottom: 0;
  border: 2px solid #fff;
}
.people-image li:nth-child(9) {
  right: 22.3%;
  bottom: 35%;
  height: 50px;
  width: 50px;
  border: 2px solid #fff;
}
.people-image li:nth-child(10) {
  right: 0;
  bottom: 145px;
  height: 60px;
  width: 60px;
  border: 2px solid #fff;
}
@media (max-width: 991px) {
  .people-image li:nth-child(1) {
    height: 110px;
    width: 110px;
  }
  .people-image li:nth-child(2) {
    height: 65px;
    width: 65px;
  }
  .people-image li:nth-child(3) {
    height: 60px;
    width: 60px;
  }
  .people-image li:nth-child(4) {
    height: 65px;
    width: 65px;
  }
  .people-image li:nth-child(5) {
    height: 65px;
    width: 65px;
  }
  .people-image li:nth-child(6) {
    height: 80px;
    width: 80px;
  }
  .people-image li:nth-child(7) {
    height: 70px;
    width: 70px;
  }
  .people-image li:nth-child(8) {
    left: 50px;
  }
  .people-image li:nth-child(9) {
    right: 27%;
  }
  .people-image li:nth-child(10) {
    right: 50px;
  }
}
@media (max-width: 768px) {
  .people-image li:nth-child(4) {
    left: 95px;
  }
}
@media (max-width: 576px) {
  .people-image li:nth-child(2) {
    left: 13%;
  }
  .people-image li:nth-child(3) {
    right: 18%;
  }
}
@media (max-width: 440px) {
  .people-image li:nth-child(1) {
    height: 100px;
    width: 100px;
  }
  .people-image li:nth-child(4) {
    left: 20px;
  }
  .people-image li:nth-child(5) {
    left: 22.4%;
    bottom: 55px;
  }
  .people-image li:nth-child(7) {
    right: 25px;
  }
  .people-image li:nth-child(8) {
    left: 10px;
  }
  .people-image li:nth-child(10) {
    right: 20px;
  }
}

.partical-animation {
  margin: 0;
  padding: 0;
  list-style: none;
  position: absolute;
  width: 100%;
  height: 550px;
  bottom: 0;
  left: 0;
  z-index: 1;
}
.partical-animation li {
  position: absolute;
  border-radius: 50%;
}
.partical-animation li:nth-child(1) {
  left: 165px;
  top: 120px;
}
.partical-animation li:nth-child(2) {
  left: 19%;
  top: 80px;
  height: 8px;
  width: 8px;
  background: #fd9382;
}
.partical-animation li:nth-child(3) {
  left: 19%;
  bottom: 30%;
  height: 10px;
  width: 10px;
  background: #fe97f3;
}
.partical-animation li:nth-child(3) {
  left: 28%;
  top: 35%;
  height: 35px;
  width: 35px;
  background: #f3d5de;
}
.partical-animation li:nth-child(4) {
  left: 27%;
  bottom: 30%;
  height: 12px;
  width: 12px;
  background: #83daed;
}
.partical-animation li:nth-child(5) {
  left: 33%;
  bottom: 50%;
  height: 10px;
  width: 10px;
  background: #fdc43a;
}
.partical-animation li:nth-child(6) {
  left: 51%;
  top: 32%;
  height: 48px;
  width: 48px;
  background: #4edfda;
}
.partical-animation li:nth-child(7) {
  right: 46%;
  bottom: 27%;
  height: 22px;
  width: 22px;
  background: #f39ab2;
}
.partical-animation li:nth-child(8) {
  right: 46%;
  bottom: 27%;
  height: 22px;
  width: 22px;
  background: #f39ab2;
}
.partical-animation li:nth-child(9) {
  right: 40%;
  bottom: 60%;
  height: 8px;
  width: 8px;
  background: #ffa3be;
}
.partical-animation li:nth-child(10) {
  right: 22%;
  bottom: 30%;
  height: 28px;
  width: 28px;
  background: #bff8c6;
}
.partical-animation li:nth-child(11) {
  right: 10%;
  bottom: 55%;
  height: 15px;
  width: 15px;
  background: #ffa3be;
}
.partical-animation li:nth-child(12) {
  right: 65px;
  bottom: 160px;
}

/*=========== End Banner Support ============*/
/*=========== Start breadcrumb_area css ============*/
.breadcrumb_area {
  background-image: linear-gradient(60deg, #10b3d6 0%, #1d2746 100%);
  padding: 190px 0 95px;
  position: relative;
}
.breadcrumb_area .one {
  right: 20%;
  bottom: 25px;
}
.breadcrumb_area .two {
  left: 20%;
  bottom: 0;
}
.breadcrumb_area .banner_search_form {
  max-width: 770px;
  position: relative;
  z-index: 5;
}
.breadcrumb_area .banner_search_form .form-control {
  height: 65px;
}
.breadcrumb_area .banner_search_form button {
  background: #10b3d6;
}
.breadcrumb_area .custom-select {
  line-height: 65px;
  z-index: 3;
}
.breadcrumb_area .custom-select:before {
  width: 1px;
  content: "";
  position: absolute;
  left: 0;
  top: 15px;
  bottom: 15px;
  background: #e2e7e9;
  z-index: 1;
}

.wave_shap_one,
.wave_shap_two {
  bottom: 0;
  z-index: 0;
}

.custom-select {
  border-radius: 0;
  font-size: 14px;
  box-shadow: none;
  padding: 0 80px 0 20px;
  height: 100%;
  border: 0;
  background: #fff;
  color: #6b707f;
}
.custom-select:after {
  border-bottom: 2px solid #6b707f;
  border-right: 2px solid #6b707f;
  height: 8px;
  width: 8px;
  right: 28px;
  margin-top: -6px;
}
.custom-select ul {
  width: 100%;
  padding: 10px 0;
  border-radius: 4px;
}
.custom-select ul li {
  font-size: 14px;
  color: #6b707f;
  position: relative;
  min-height: 36px;
  line-height: 36px;
}
.custom-select ul li:before {
  content: "";
  width: 2px;
  height: 0;
  position: absolute;
  left: 0;
  background: #10b3d6;
  transition: all 0.2s linear;
}
.custom-select ul li:hover, .custom-select ul li.selected {
  background: #f4f9fa;
  color: #10b3d6;
  font-weight: 500;
}
.custom-select ul li:hover:before, .custom-select ul li.selected:before {
  height: 100%;
}

/*=========== End breadcrumb_area css ============*/
/*=========== Start breadcrumb_area_two css ============*/
.breadcrumb_area_two {
  background: #edfbfe;
  padding: 50px 0 60px;
}

.breadcrumb_content {
  max-width: 630px;
}
.breadcrumb_content h2 {
  font-size: 36px;
  line-height: 1.4;
  font-weight: 500;
  margin-bottom: 0;
}
.breadcrumb_content .single_post_author {
  display: flex;
  align-items: flex-start;
  align-items: center;
  padding-top: 10px;
}
.breadcrumb_content .single_post_author img {
  border-radius: 50%;
  width: 34px;
  height: 34px;
  margin-right: 20px;
}
.breadcrumb_content .single_post_author .text {
  flex: 1;
}
.breadcrumb_content .single_post_author .text h4 {
  font-size: 16px;
  display: inline-block;
  font-weight: 400;
  margin-bottom: 0;
}
.breadcrumb_content .single_post_author .text .post_tag {
  display: inline-block;
  padding-left: 12px;
}
.breadcrumb_content .single_post_author .text .post_tag:before {
  content: "";
  display: inline-block;
  height: 13px;
  width: 1px;
  background: #B7B9BE;
  margin-right: 12px;
}
.breadcrumb_content .single_post_author .text .c_blue {
  color: #2a74fd;
}

/*=========== End breadcrumb_area_two css ============*/
/*=========== Start breadcrumb_area_three css ============*/
.breadcrumb_area_three {
  background: #f8fafd;
  padding: 157px 0 70px;
  position: relative;
  z-index: 1;
}
.breadcrumb_area_three .one {
  top: 0;
  left: 35px;
}
.breadcrumb_area_three .two {
  left: 280px;
  bottom: 55px;
}
.breadcrumb_area_three .three {
  right: 178px;
  bottom: 55px;
}
.breadcrumb_area_three .four {
  right: 0;
  bottom: 0;
}

.breadcrumb_text {
  text-align: center;
  max-width: 660px;
  margin: 0 auto;
}
.breadcrumb_text h2 {
  font-size: 50px;
  line-height: 66px;
  font-weight: 300;
  margin-bottom: 15px;
}
.breadcrumb_text h2 span {
  font-weight: 700;
}
.breadcrumb_text p {
  margin-bottom: 0;
}

/*=========== End breadcrumb_area_three css ============*/
/*=========== Start page_breadcrumb css ============*/
.page_breadcrumb {
  background: #f0f5f7;
  padding: 16px 0;
  position: relative;
  z-index: 1;
}
.page_breadcrumb .row {
  align-items: center;
}
.page_breadcrumb .date {
  color: #878EA6;
  float: right;
  font-size: 14px;
  padding-right: 10px;
}
.page_breadcrumb .date i {
  padding-right: 10px;
}

.breadcrumb {
  margin-bottom: 0;
  background: transparent;
  border-radius: 0;
  padding: 0;
}
.breadcrumb .breadcrumb-item {
  color: #878EA6;
  font-size: 14px;
  line-height: 21px;
}
.breadcrumb .breadcrumb-item a {
  color: #878EA6;
}
.breadcrumb .breadcrumb-item a:hover {
  color: #10b3d6;
}
.breadcrumb .breadcrumb-item + .breadcrumb-item:before {
  content: "5";
  font-family: eleganticons;
  color: #B7B9BE;
}
.breadcrumb .active {
  color: #1d2746;
}

.breadcrumb_content_two {
  position: relative;
  z-index: 1;
}
.breadcrumb_content_two h2 {
  font-size: 50px;
  color: #fff;
  font-weight: 700;
}
.breadcrumb_content_two .breadcrumb {
  justify-content: center;
}
.breadcrumb_content_two .breadcrumb .breadcrumb-item {
  font-size: 16px;
  color: #fff;
}
.breadcrumb_content_two .breadcrumb .breadcrumb-item:before {
  color: #fff;
}
.breadcrumb_content_two .breadcrumb .breadcrumb-item a {
  color: #fff;
}

.breadcrumb_area_four {
  overflow: hidden;
}

/*=========== End page_breadcrumb css ============*/
/*---------------------------------------------------- */
/*----------------------------------------------------*/
/*=============== doc_features_area_one css ===========*/
.doc_features_area_one {
  position: relative;
  z-index: 1;
  padding: 100px 0 140px;
}
.doc_features_area_one:before {
  content: "";
  width: 100%;
  height: 460px;
  top: 0;
  left: 0;
  background: url("../img/home_one/dow_bg.png") no-repeat scroll center top/cover;
  position: absolute;
  z-index: -1;
}

.doc_features_item_one {
  border-radius: 6px;
  background-color: white;
  box-shadow: 0 4px 8px 0 rgba(4, 73, 89, 0.05);
  padding: 30px;
  transition: all 0.3s linear;
  cursor: pointer;
}
.doc_features_item_one h3 {
  font-size: 20px;
  line-height: 24px;
  margin-bottom: 15px;
  transition: color 0.2s linear;
}
.doc_features_item_one h3:hover {
  color: #10b3d6;
}
.doc_features_item_one p {
  line-height: 24px;
}
.doc_features_item_one img {
  margin-right: 30px;
}
.doc_features_item_one .learn_btn {
  font-size: 14px;
}
.doc_features_item_one .learn_btn i {
  font-size: 20px;
  margin-left: 4px;
}
.doc_features_item_one:hover {
  box-shadow: 0 30px 40px 0 rgba(4, 73, 89, 0.08);
}

.learn_btn {
  font-size: 14px;
  font-weight: 500;
  color: #1d2746;
  padding-top: 5px;
  display: inline-block;
  transition: color 0.2s linear;
}
.learn_btn i {
  vertical-align: middle;
  display: inline-block;
  font-size: 20px;
  margin-left: 6px;
  transition: all 0.2s linear, color 0s linear;
}
.learn_btn.c_blue {
  color: #10b3d6;
}
.learn_btn:hover {
  color: #10b3d6;
}
.learn_btn:hover i {
  transform: translateX(8px);
}
.learn_btn:hover.c_blue {
  color: #1d2746;
}

/*=============== doc_features_area_one css ===========*/
/*---------------------------------------------------- */
/*----------------------------------------------------*/
.ycp .belah .luhur span.about {
  display: none;
}
.ycp .belah .luhur {
  height: 74px;
}
.ycp .belah .luhur span.tombol {
  font-size: 14px;
}
.ycp .belah .luhur span.tombol.vid-next {
  float: right;
  margin-right: 0;
}

/*---------------------------------------------------- */
/*----------------------------------------------------*/
/*============== Start doc_tag_area css =============*/
.doc_tag_area {
  padding: 0 0 110px;
}

.doc_tag {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  border: 0;
  margin-bottom: 75px;
}
.doc_tag .nav-item {
  margin-bottom: 0;
}
.doc_tag .nav-item .nav-link {
  font-size: 14px;
  font-weight: 400;
  color: #10b3d6;
  border: 1px solid #79ccde;
  border-radius: 4px;
  background-color: rgba(16, 179, 214, 0.031);
  padding: 0 20px;
  line-height: 28px;
  margin: 0 5px;
  transition: all 0.4s linear;
}
.doc_tag .nav-item .nav-link:hover, .doc_tag .nav-item .nav-link.active {
  background-color: #10b3d6;
  border-color: #10b3d6;
  box-shadow: 0 10px 20px 0 rgba(12, 118, 142, 0.2);
  color: #fff;
}

.doc_tag_title {
  margin-bottom: 45px;
}
.doc_tag_title h4 {
  font-size: 20px;
  line-height: 26px;
  margin-bottom: 20px;
}
.doc_tag_title .line {
  height: 2px;
  width: 100%;
  background: #e1e9eb;
  display: block;
}

.doc_tab_pane .row {
  margin-bottom: -85px;
}

.doc_tag_item {
  margin-bottom: 85px;
}

.tag_list {
  margin-bottom: 32px;
}
.tag_list li {
  margin-top: 15px;
}
.tag_list li a {
  font-size: 16px;
  color: #6b707f;
  line-height: 22px;
  transition: color 0.2s linear;
  display: flex;
}
.tag_list li a i {
  margin-right: 15px;
}
.tag_list li a:hover {
  color: #10b3d6;
}

/*============== End doc_tag_area css =============*/
/*============== Start documentation_area css =============*/
.documentation_tab {
  border-bottom: 2px solid #e1e9eb;
}
.documentation_tab .nav-item {
  margin-bottom: 0;
}
.documentation_tab .nav-item .nav-link {
  border-radius: 0;
  border: 0;
  color: #6b707f;
  font-size: 14px;
  font-weight: 500;
  padding: 0 20px 10px;
  position: relative;
  transition: color 0.2s linear;
  background: transparent;
}
.documentation_tab .nav-item .nav-link:before {
  content: "";
  width: 0;
  height: 2px;
  position: absolute;
  bottom: -2px;
  left: auto;
  right: 0;
  background: #10b3d6;
  transition: width 0.3s linear;
}
.documentation_tab .nav-item .nav-link.active, .documentation_tab .nav-item .nav-link:hover {
  color: #10b3d6;
}
.documentation_tab .nav-item .nav-link.active:before, .documentation_tab .nav-item .nav-link:hover:before {
  width: 100%;
  left: 0;
  right: auto;
}

.documentation_tab_pane {
  padding-top: 84px;
}
.documentation_tab_pane .row {
  margin-bottom: -26px;
}

.documentation_text .round {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background-color: #0fccd0;
  box-shadow: 0 20px 30px 0 rgba(24, 211, 214, 0.2);
  display: block;
  text-align: center;
  line-height: 100px;
  margin-bottom: 25px;
}
.documentation_text h4 {
  font-size: 24px;
  font-weight: 500;
  margin-bottom: 18px;
}
.documentation_text p {
  margin-bottom: 0;
}
.documentation_text .learn_btn {
  padding-top: 20px;
}

.documentation_item {
  padding-right: 40px;
  margin-bottom: 58px;
}
.documentation_item .icon {
  border-radius: 50%;
  background-color: white;
  box-shadow: 0 10px 30px 0 rgba(4, 73, 89, 0.12);
  width: 70px;
  height: 70px;
  text-align: center;
  line-height: 66px;
  margin-right: 30px;
}
.documentation_item .media-body h5 {
  font-weight: 500;
  font-size: 18px;
  margin-bottom: 12px;
  transition: color 0.2s linear;
}
.documentation_item .media-body h5:hover {
  color: #10b3d6;
}
.documentation_item .media-body p {
  margin-bottom: 0;
  line-height: 24px;
}

/*============== End documentation_area css =============*/
/*============== Start doc_faq_area css =============*/
.doc_faq_area .doc_tag {
  margin-bottom: 50px;
}

.doc_faq_info .card {
  border-radius: 4px;
  background: #fff;
  box-shadow: 0 20px 50px 0 rgba(4, 73, 89, 0.1);
  border: 0;
  margin-bottom: 30px;
}
.doc_faq_info .card .card-header {
  padding: 0;
  margin: 0;
  border-radius: 0;
  background: transparent;
  border: 0;
}
.doc_faq_info .card .card-header h2 button {
  display: block;
  text-align: left;
  width: 100%;
  font-size: 18px;
  line-height: 30px;
  font-weight: 500;
  color: #10b3d6;
  text-decoration: none;
  padding: 19px 40px 19px 30px;
  position: relative;
}
.doc_faq_info .card .card-header h2 button i {
  position: absolute;
  right: 30px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 20px;
  display: none;
}
.doc_faq_info .card .card-header h2 button i + i {
  display: block;
}
.doc_faq_info .card .card-header h2 button.collapsed {
  color: #1d2746;
}
.doc_faq_info .card .card-header h2 button.collapsed i {
  display: block;
}
.doc_faq_info .card .card-header h2 button.collapsed i + i {
  display: none;
}
.doc_faq_info .card .card-body {
  border: 0;
  line-height: 24px;
  padding: 0 30px 35px;
}
.doc_faq_info .card.active {
  position: relative;
  z-index: 1;
}

/*============== End doc_faq_area css =============*/
/*============== Start doc_categories_guide_area css =============*/
.doc_categories_guide_area {
  position: relative;
  z-index: 1;
}
.doc_categories_guide_area .shap {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 490px;
  z-index: -1;
  background-size: cover;
}

.categories_guide_item {
  border-radius: 6px;
  background: #fff;
  box-shadow: 0 50px 120px 0 rgba(4, 73, 89, 0.08);
  padding: 40px 40px 50px;
  margin-bottom: 30px;
}
.categories_guide_item img {
  margin-bottom: 34px;
}
.categories_guide_item .doc_tag_title {
  margin-bottom: 24px;
}
.categories_guide_item .doc_tag_title h4 {
  margin-bottom: 0;
}
.categories_guide_item .tag_list {
  margin-bottom: 42px;
}

.all_doc_btn {
  padding: 10px 40px;
  margin-top: 30px;
}

/*============== End doc_categories_guide_area css =============*/
/*============== Start doc_solution_area css =============*/
.doc_solution_area .all_doc_btn {
  padding: 20px 73px;
  font-size: 18px;
}
.doc_solution_area .all_doc_btn i {
  font-size: 25px;
  margin-left: 10px;
}

.doc_solution_item {
  text-align: center;
  padding: 60px;
  margin-bottom: 30px;
}
.doc_solution_item img {
  margin-bottom: 45px;
}
.doc_solution_item h4 {
  font-size: 20px;
  margin-bottom: 15px;
}
.doc_solution_item p {
  margin-bottom: 20px;
}
.doc_solution_item.online_doc {
  background: #fcf3da;
}
.doc_solution_item.premium_doc {
  background: #daecfc;
}

/*============== End doc_solution_area css =============*/
/*============== Start doc_fun_fact_area css =============*/
.doc_fun_fact_area {
  background: #10b3d6;
  padding: 105px 0;
  position: relative;
  z-index: 1;
}

.animated-wave {
  position: absolute;
  height: 200px;
  width: 100%;
  bottom: 0;
  left: 0;
  z-index: -1;
}

.doc_fact_item {
  text-align: center;
}
.doc_fact_item .counter {
  color: #fff;
  font-size: 50px;
  line-height: 60px;
  font-weight: 700;
}
.doc_fact_item p {
  margin-bottom: 0;
  font-size: 18px;
  color: #fff;
  padding-top: 5px;
}

/* Fun Fact Style Two */
.active-animation .path {
  stroke-dasharray: 1000;
  stroke-dashoffset: 0;
  animation: dash 5s ease-in-out forwards;
}

@keyframes dash {
  from {
    stroke-dashoffset: 1000;
  }
  to {
    stroke-dashoffset: 0;
  }
}
.funfact-area {
  padding: 115px 0 90px;
  background-image: linear-gradient(45deg, #f5fdff 0%, #feefec 100%);
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  position: relative;
  overflow: hidden;
}
.funfact-area .smile {
  position: absolute;
  width: 200px;
  height: 200px;
  right: 95px;
  top: -25px;
}

.funfact-boxes {
  display: flex;
  justify-content: space-between;
}
@media (max-width: 992px) {
  .funfact-boxes {
    flex-wrap: wrap;
    justify-content: center;
    width: calc(100% + 30px);
  }
}
@media (max-width: 420px) {
  .funfact-boxes {
    width: calc(100% + 15px);
  }
}
.funfact-boxes .funfact-box {
  border-radius: 10px;
  background: #fff;
  box-shadow: 0px 50px 80px 0px rgba(2, 47, 57, 0.12);
  min-width: 140px;
  padding: 30px 25px 20px;
  margin-bottom: 30px;
}
@media (max-width: 992px) {
  .funfact-boxes .funfact-box {
    width: calc(33.33% - 30px);
    margin-right: 30px;
  }
}
@media (max-width: 420px) {
  .funfact-boxes .funfact-box {
    width: calc(50% - 15px);
    margin-right: 15px;
  }
}
.funfact-boxes .funfact-box .fanfact-icon {
  margin-bottom: 20px;
}
.funfact-boxes .funfact-box .fanfact-icon img {
  -webkit-filter: drop-shadow(0px 20px 40px #086174);
  filter: drop-shadow(0px 20px 40px #086174);
}
.funfact-boxes .funfact-box .counter {
  font-size: 34px;
  font-weight: 500;
  color: #10b3d6;
  margin-bottom: 10px;
}
.funfact-boxes .funfact-box .title {
  color: #6b707f;
  font-size: 16px;
  margin: 0;
  line-height: 26px;
}
.funfact-boxes .funfact-box.color-two .fanfact-icon img {
  -webkit-filter: drop-shadow(0px 20px 40px #734F08);
  filter: drop-shadow(0px 20px 40px #734F08);
}
.funfact-boxes .funfact-box.color-two .counter {
  color: #fbb631;
}
.funfact-boxes .funfact-box.color-three .fanfact-icon img {
  -webkit-filter: drop-shadow(0px 20px 40px #73083F);
  filter: drop-shadow(0px 20px 40px #73083F);
}
.funfact-boxes .funfact-box.color-three .counter {
  color: #fd5baf;
}
.funfact-boxes .funfact-box.color-four .fanfact-icon img {
  -webkit-filter: drop-shadow(0px 20px 40px #087358);
  filter: drop-shadow(0px 20px 40px #087358);
}
.funfact-boxes .funfact-box.color-four .counter {
  color: #11c296;
}
.funfact-boxes .funfact-box.color-five .fanfact-icon img {
  -webkit-filter: drop-shadow(0px 20px 40px #2C0873);
  filter: drop-shadow(0px 20px 40px #2C0873);
}
.funfact-boxes .funfact-box.color-five .counter {
  color: #9360fc;
}

/*============== End doc_fun_fact_area css =============*/
/*============== Start theme_doc_area css =============*/
.theme_doc_area .row {
  margin-bottom: -30px;
}

.theme_doc_item {
  box-shadow: 0 4px 8px 0 rgba(4, 73, 89, 0.05);
  border-radius: 6px;
  background: #fff;
  padding: 30px;
  margin-bottom: 30px;
  transition: all 0.3s linear;
  cursor: pointer;
}
.theme_doc_item img {
  margin-right: 30px;
}
.theme_doc_item .c_head {
  margin-bottom: 14px;
  transition: color 0.2s linear;
}
.theme_doc_item .c_head:hover {
  color: #10b3d6;
}
.theme_doc_item p {
  margin-bottom: 0;
}
.theme_doc_item:hover {
  box-shadow: 0 30px 40px 0 rgba(4, 73, 89, 0.08);
}

/*============== End theme_doc_area css =============*/
/*============== End theme_doc_area css =============*/
.doc_action_info {
  border-radius: 4px;
  background: #fff;
  box-shadow: 0 50px 150px 0 rgba(4, 73, 89, 0.1);
  padding: 88px 70px;
  position: relative;
  z-index: 2;
}
.doc_action_info.mt_130 {
  margin-bottom: -130px;
}
.doc_action_info .media img {
  margin-right: 30px;
}
.doc_action_info .media .media-body h2 {
  margin-bottom: 13px;
}
.doc_action_info .media .media-body p {
  margin-bottom: 0;
  font-size: 18px;
}
.doc_action_info .action_btn {
  box-shadow: none;
}
.doc_action_info .action_btn:hover {
  box-shadow: 0 20px 30px 0 rgba(12, 118, 142, 0.24);
  color: #fff;
}

/*============== End theme_doc_area css =============*/
/*============== Start doc_action_area_two css =============*/
.doc_action_area_three {
  background: #1d2746;
  padding: 140px 0;
  position: relative;
  z-index: 1;
}
.doc_action_area_three .shadows {
  left: 40%;
  transform: translateX(-50%);
  top: 0;
  z-index: -1;
}
.doc_action_area_three .b_man {
  right: 100px;
  bottom: 0;
}
@media (max-width: 1400px) {
  .doc_action_area_three .b_man {
    right: 0;
  }
}

.action_content h2 {
  font-size: 36px;
  line-height: 48px;
  font-weight: 300;
  color: #fff;
  margin-bottom: 15px;
  margin-top: -10px;
}
.action_content h2 span {
  font-weight: 700;
}
.action_content p {
  font-size: 18px;
  color: #aeb5ca;
  margin-bottom: 0;
}

.action_subscribe_form .form-group {
  position: relative;
}
.action_subscribe_form .form-group .form-control {
  height: 70px;
  line-height: 70px;
}
.action_subscribe_form .form-group .form-control.placeholder {
  color: #a0a1a1;
}
.action_subscribe_form .form-group .form-control:-moz-placeholder {
  color: #a0a1a1;
}
.action_subscribe_form .form-group .form-control::-moz-placeholder {
  color: #a0a1a1;
}
.action_subscribe_form .form-group .form-control::-webkit-input-placeholder {
  color: #a0a1a1;
}
.action_subscribe_form .form-group .s_btn {
  padding: 7px 29px;
  transition: all 0.2s linear;
}
.action_subscribe_form .form-group .s_btn i {
  vertical-align: middle;
  font-size: 20px;
  transition: all 0.2s linear;
  display: inline-block;
}
.action_subscribe_form .form-group .s_btn:hover {
  background: #1d2746;
}
.action_subscribe_form .form-group .s_btn:hover i {
  transform: translateX(5px);
}
.action_subscribe_form .form-check {
  padding-left: 25px;
}
.action_subscribe_form .form-check input[type=checkbox] {
  position: relative;
  appearance: none;
  outline: none;
  border: none;
  cursor: pointer;
  outline-width: 0;
  height: auto;
  position: absolute;
  margin: 0;
  left: 0;
  top: 5px;
}
.action_subscribe_form .form-check input[type=checkbox]:before, .action_subscribe_form .form-check input[type=checkbox]:after {
  content: "";
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  border: 1px solid #aeb5ca;
  border-radius: 2px;
}
.action_subscribe_form .form-check input[type=checkbox]:before {
  width: 14px;
  height: 14px;
}
.action_subscribe_form .form-check input[type=checkbox]:after {
  content: "";
  display: block;
  width: 6px;
  height: 10px;
  border: solid #10b3d6;
  border-width: 0 2px 2px 0;
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
  position: absolute;
  top: 0;
  left: 4px;
  opacity: 0;
}
.action_subscribe_form .form-check input[type=checkbox]:checked:before {
  border-color: #10b3d6;
}
.action_subscribe_form .form-check input[type=checkbox]:checked:after {
  opacity: 1;
}
.action_subscribe_form .form-check label {
  position: relative;
  font-size: 15px;
  color: #aeb5ca;
  font-weight: 500;
}

/*============== End doc_action_area_two css =============*/
/*=========== Start doc_faq_area_two ============*/
.fact_navigation_info {
  border-radius: 4px;
  background-color: white;
  box-shadow: 0 20px 40px 0 rgba(4, 73, 89, 0.1);
  padding: 40px 40px 28px;
}

.fact_navigation {
  border: 0;
  padding-top: 8px;
  margin: 0;
}
.fact_navigation .nav-item {
  display: block;
  width: 100%;
  padding: 12px 0;
}
.fact_navigation .nav-item .nav-link {
  display: block;
  padding: 0;
  font-size: 16px;
  font-weight: 500;
  color: rgba(16, 179, 214, 0.5);
  position: relative;
  padding-left: 28px;
  border: 0;
  border-radius: 0;
  transition: all 0.2s linear;
}
.fact_navigation .nav-item .nav-link:before {
  content: "5";
  font-family: eleganticons;
  font-size: 20px;
  position: absolute;
  right: 0;
}
.fact_navigation .nav-item .nav-link i {
  position: absolute;
  left: 0;
  top: 2px;
}
.fact_navigation .nav-item .nav-link.active, .fact_navigation .nav-item .nav-link:hover {
  color: #10b3d6;
}
.fact_navigation .nav-item .nav-link.active:before, .fact_navigation .nav-item .nav-link:hover:before {
  color: #10b3d6;
}
.fact_navigation .nav-item + li {
  border-top: 1px solid #e6eeef;
}

/*=========== End doc_faq_area_two ============*/
/*---------------------------------------------------- */
/*----------------------------------------------------*/
/*============ Start doc_feedback_area css ==========*/
.doc_feedback_area {
  position: relative;
  z-index: 1;
  padding: 110px 0;
}

.overlay_bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(9, 19, 50, 0.8);
}

.doc_feedback_info {
  position: relative;
}

.slider_nav .arrow {
  width: 25px;
  height: 2px;
  background: #989dad;
  transition: all 0.3s ease-in-out;
  display: block;
  margin-top: 12px;
}
.slider_nav .arrow:before, .slider_nav .arrow:after {
  content: "";
  position: absolute;
  width: 15px;
  height: 2px;
  left: -2px;
  background-color: #bdb4b4;
  transition: all 0.3s ease-in-out;
}
.slider_nav .prev, .slider_nav .next {
  height: 25px;
  width: 27px;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  transition: all 0.3s ease-in-out;
  cursor: pointer;
}
.slider_nav .prev:hover, .slider_nav .next:hover {
  width: 40px;
}
.slider_nav .prev:hover .arrow, .slider_nav .next:hover .arrow {
  width: 40px;
  background: #10b3d6;
}
.slider_nav .prev:hover .arrow:before, .slider_nav .prev:hover .arrow:after, .slider_nav .next:hover .arrow:before, .slider_nav .next:hover .arrow:after {
  background: #10b3d6;
}
.slider_nav .prev {
  left: 0;
}
.slider_nav .prev .arrow:before {
  top: 17px;
  transform: rotate(45deg);
}
.slider_nav .prev .arrow:after {
  top: 7px;
  transform: rotate(-45deg);
}
.slider_nav .prev:hover {
  left: -20px;
}
.slider_nav .next {
  right: 0;
}
.slider_nav .next .arrow:before {
  transform: rotate(-45deg);
  right: -1px;
  left: auto;
  bottom: 6px;
}
.slider_nav .next .arrow:after {
  transform: rotate(45deg);
  right: -1px;
  left: auto;
  top: 6px;
}
.slider_nav .next:hover {
  right: -20px;
}

.doc_feedback_slider {
  max-width: 770px;
  margin: 0 auto 0 !important;
  text-align: center;
}
.doc_feedback_slider .item .author_img {
  border-radius: 50%;
  width: 60px;
  height: 60px;
  overflow: hidden;
  margin: 0 auto 43px;
}
.doc_feedback_slider .item p {
  font-size: 18px;
  color: #fff;
  line-height: 30px;
  margin-bottom: 38px;
}
.doc_feedback_slider .item h5 {
  font-size: 20px;
  font-weight: 500;
  color: #fff;
}
.doc_feedback_slider .item h6 {
  font-size: 16px;
  color: #989dad;
  font-weight: 400;
  margin-bottom: 0;
}

/*============ End doc_feedback_area css ==========*/
/*============ Start doc_action_area css ==========*/
.doc_action_area {
  position: relative;
  z-index: 1;
  padding: 93px 0;
}
.doc_action_area .overlay_bg {
  background-image: linear-gradient(180deg, rgba(4, 133, 161, 0.6) 0%, rgba(29, 39, 70, 0.6) 100%);
}
.doc_action_area .action_btn {
  border: 1px solid #10b3d6;
}
.doc_action_area .action_btn:hover {
  background: transparent;
  border-color: #fff;
  color: #fff;
}

.action_text h2 {
  color: #fff;
  font-size: 40px;
  font-weight: 500;
}
.action_text p {
  font-size: 18px;
  line-height: 28px;
  color: #b4b9c8;
  margin-bottom: 0;
}

/*============ End doc_action_area css ==========*/
/*---------------------------------------------------- */
/*----------------------------------------------------*/
@keyframes wave2 {
  25% {
    transform-origin: 0 100%;
  }
  50% {
    transform: scale(1.8, 1.3);
  }
  75% {
    transform-origin: 100% 100%;
  }
  100% {
    transform: scale(1, 1.3);
  }
}
@keyframes wave4 {
  25% {
    transform: scaleY(0.9);
  }
  75% {
    transform: scaleY(1.1) scaleX(1.02);
  }
}
@keyframes star {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(0.8);
    opacity: 1;
  }
  75% {
    transform: scale(1) rotate(45deg);
    opacity: 1;
  }
  100% {
    transform: scale(1.01) rotate(-5deg);
    opacity: 1;
  }
}
@keyframes star2 {
  0% {
    transform: scale(0.3);
  }
  50% {
    transform: scale(0.8);
  }
  75% {
    transform: scale(1);
  }
  100% {
    transform: scale(1.01);
  }
}
@-webkit-keyframes spin {
  0% {
    transform: translateX(-10px) scale(0.9);
  }
  100% {
    transform: translateX(30px) scale(1.3) translateY(10px);
  }
}
@keyframes spin {
  0% {
    transform: translateX(-10px) scale(0.9);
  }
  100% {
    transform: translateX(30px) scale(1.3) translateY(10px);
  }
}
@-webkit-keyframes spin1 {
  0% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1.5);
  }
}
@keyframes spin1 {
  0% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1.5);
  }
}
@-webkit-keyframes spin2 {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(40px);
  }
}
@keyframes spin2 {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(40px);
  }
}
@-webkit-keyframes pulse1 {
  0% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(0.5);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(4.5);
    border-radius: 50%;
  }
}
@keyframes pulse1 {
  0% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(0.5);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(4.5);
    border-radius: 50%;
  }
}
@keyframes customUp {
  0% {
    transform: translateY(8px);
  }
  100% {
    transform: translateY(0);
  }
}
@keyframes clipInDown {
  0% {
    clip-path: polygon(-10% -10%, -10% -10%, 110% -10%, 110% -10%);
  }
  100% {
    clip-path: polygon(-10% 110%, -10% -10%, 110% -10%, 110% 110%);
  }
}
.clipInDown {
  animation-name: clipInDown;
}

/*================ preloader css ====================*/
#preloader {
  background: #fff;
  height: 100%;
  bottom: 0;
  left: 0;
  position: fixed;
  right: 0;
  top: 0;
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}
#preloader .loading {
  display: flex;
}
#preloader .ctn-preloader {
  padding-left: 15px;
  padding-right: 15px;
}
#preloader .spinner {
  animation: spinner 3s infinite linear;
  border-radius: 50%;
  border: 3px solid #f1f6f8;
  border-left-color: #10b3d6;
  border-top-color: #10b3d6;
  margin: 0 auto 0em auto;
  position: absolute;
  left: -40px;
  right: -40px;
  bottom: -40px;
  top: -40px;
}
#preloader .spinner:before {
  content: "";
  width: 20px;
  height: 20px;
  border: 6px solid #fff;
  box-shadow: 0 0 20px 0 rgba(4, 46, 56, 0.2);
  background: #10b3d6;
  position: absolute;
  right: 31px;
  top: 41px;
  border-radius: 50%;
}
@media (max-width: 576px) {
  #preloader .spinner:before {
    top: 18px;
  }
}
#preloader .round_spinner {
  border-width: 1px;
  border-color: #eef3f4;
  border-style: solid;
  border-radius: 50%;
  background-color: #fdfdfd;
  box-shadow: 0 0 100px 0 rgba(4, 46, 56, 0.14);
  width: 248px;
  height: 248px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  margin: 40px auto 80px;
}
@media (max-width: 576px) {
  #preloader .round_spinner {
    width: 155px;
    height: 155px;
  }
}
#preloader .round_spinner h4 {
  font-size: 30px;
  font-weight: 400;
  margin-bottom: 0;
  color: #1d2746;
  margin-top: 10px;
}
#preloader .round_spinner h4 span {
  font-weight: 700;
}

#preloader .head {
  color: #1d2746;
  display: block;
  font-size: 26px;
  font-weight: 700;
  letter-spacing: 5.2px;
  text-transform: uppercase;
  text-align: center;
  font-family: "Roboto", sans-serif;
  margin: 5% 0 1% 0;
  padding: 0;
}

#preloader p {
  color: #6b707f;
  display: block;
  font-size: 14px;
  font-weight: 400;
  text-align: center;
  border: none;
  margin: 0;
  padding: 0;
}

@keyframes spinner {
  to {
    transform: rotateZ(360deg);
  }
}
/*---------------------------------------------------- */
/*----------------------------------------------------*/
.doc_documentation_area {
  padding-bottom: 110px;
  z-index: 0;
}
@media (min-width: 1400px) {
  .doc_documentation_area {
    min-height: 1000px;
  }
}
.doc_documentation_area .overlay_bg {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  background: #1d2746;
  opacity: 0;
  z-index: 1;
  visibility: hidden;
  transition: all 0.4s linear;
}
.doc_documentation_area.body_fixed .doc_left_sidebarlist {
  position: fixed;
  top: 0;
  width: 270px;
}
@media (min-width: 1500px) {
  .doc_documentation_area.body_fixed .doc_left_sidebarlist {
    width: 307px;
  }
}
@media (max-height: 630px) {
  .doc_documentation_area.body_fixed .doc_left_sidebarlist {
    padding-top: 20px;
  }
}
.doc_documentation_area.body_fixed .doc_rightsidebar {
  position: fixed;
  top: 0;
}
@media (max-height: 630px) {
  .doc_documentation_area.body_fixed .doc_rightsidebar {
    padding-top: 20px;
  }
}
.doc_documentation_area.overlay .overlay_bg {
  opacity: 0.5;
  visibility: visible;
}

.full-width-doc .body_fixed .doc_rightsidebar, .full-width-doc .body_fixed .doc_left_sidebarlist {
  padding-top: 30px;
}

/*============ doc_left_sidebarlist css =========*/
.doc_left_sidebarlist {
  padding-top: 50px;
  padding-bottom: 30px;
  z-index: 1;
  margin-right: 30px;
  position: relative;
  height: 100%;
}
.doc_left_sidebarlist:before {
  content: "";
  width: 200%;
  right: 0;
  height: 100%;
  background: #fafcfd;
  position: absolute;
  border-bottom: 1px solid #e5e9eb;
  top: 0;
  z-index: -1;
}
.doc_left_sidebarlist .scroll {
  max-height: 800px;
}
.doc_left_sidebarlist .scroll .mCSB_inside > .mCSB_container {
  margin-right: 0 !important;
}
@media (max-height: 630px) {
  .doc_left_sidebarlist .scroll {
    padding-bottom: 0;
    max-height: 450px;
  }
}
.doc_left_sidebarlist h2 {
  margin-bottom: 25px;
}

.nav-sidebar {
  margin-bottom: 0;
}
.nav-sidebar .nav-item {
  margin-bottom: 20px;
  padding-right: 35px;
  position: relative;
  cursor: pointer;
}
.nav-sidebar .nav-item .nav-link {
  padding: 0;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  font-size: 14px;
  line-height: 20px;
  font-weight: 500;
  color: #1d2746;
  position: relative;
}
.nav-sidebar .nav-item .nav-link img {
  padding-right: 15px;
}
.nav-sidebar .nav-item .nav-link + .icon {
  font-size: 20px;
  color: #6b707f;
  float: right;
  position: absolute;
  right: 30px;
  top: -2px;
  transition: all 0.3s linear;
}
.nav-sidebar .nav-item:hover .nav-link, .nav-sidebar .nav-item.active .nav-link {
  color: #10b3d6;
}
.nav-sidebar .nav-item:hover .icon, .nav-sidebar .nav-item.active .icon {
  color: #10b3d6;
}
.nav-sidebar .nav-item.active .icon {
  transform: rotate(-180deg);
}
.nav-sidebar .nav-item .dropdown_nav {
  padding-left: 32px !important;
  margin-bottom: 0;
  padding-top: 12px;
  width: 100% !important;
  display: none;
}
.nav-sidebar .nav-item .dropdown_nav li a {
  font-size: 14px;
  font-weight: 400;
  line-height: 30px;
  color: #6b707f;
  padding: 0;
}
.nav-sidebar .nav-item .dropdown_nav li a:hover, .nav-sidebar .nav-item .dropdown_nav li a.active {
  color: #10b3d6;
}
.nav-sidebar .nav-item .dropdown_nav li a:hover i, .nav-sidebar .nav-item .dropdown_nav li a.active i {
  color: #10b3d6;
}
.nav-sidebar + .nav-sidebar {
  border-top: 1px solid #e5e9eb;
  padding-top: 20px;
}

.coding_nav {
  padding-top: 15px;
  padding-bottom: 20px;
}

.bottom_nav .nav-item .nav-link i {
  position: relative;
  color: #1d2746;
  padding-left: 4px;
  transition: all 0.2s linear;
}
.bottom_nav .nav-item .nav-link:hover i {
  transform: translateX(5px);
}

/*============ doc_left_sidebarlist css =========*/
.documentation_info {
  padding-right: 59px;
  padding-left: 15px;
  position: relative;
}
.documentation_info .c_head {
  font-weight: 500;
  margin-bottom: 12px;
}
.documentation_info .c_head .anchorjs-link {
  font-size: 22px !important;
}
.documentation_info .slideshow {
  width: auto;
  height: 600px;
}
.documentation_info .slideshow .slide {
  width: inherit;
  height: inherit;
}
.documentation_info .slideshow .slide .item--horse {
  top: 124px;
  left: 78px;
}
.documentation_info .slideshow .nav {
  display: none;
}
.documentation_info .slideshow .title {
  color: #10b3d6;
}

.sticky-nav-doc #documentation {
  padding-top: 174px;
}

.documentation_body {
  padding-top: 60px;
}
.documentation_body footer .border_bottom {
  margin-top: 100px;
}

.doc_documentation_area .shortcode_title {
  margin-bottom: 40px;
}
.doc_documentation_area .shortcode_title h1 {
  font-size: 30px;
  font-weight: 500;
  margin-bottom: 12px;
}
.doc_documentation_area .shortcode_title h4 {
  margin-bottom: 15px;
}
.doc_documentation_area .shortcode_title p span {
  font-weight: 500;
}
.doc_documentation_area .shortcode_title .s_title {
  margin-bottom: 10px;
}
.doc_documentation_area .shortcode_title + ul {
  margin-top: -0.8rem;
}

.get_started {
  padding-top: 45px;
  padding-bottom: 50px;
}
.get_started .c_head i {
  color: #10b3d6;
  font-size: 16px;
  margin-left: 8px;
}
.get_started p {
  color: #6b707f;
}
.get_started p a {
  color: #10b3d6;
}

.link {
  background: #f1fdf3;
  padding: 14px 10px 14px 30px;
  border-radius: 4px;
}
.link p {
  position: relative;
  margin-bottom: 0;
  padding-left: 40px;
}
.link p:before {
  content: "R";
  position: absolute;
  font-family: eleganticons;
  left: 0;
  top: 4px;
  font-size: 24px;
  color: #10d631;
}

.test_version {
  padding-top: 45px;
}
.test_version p {
  font-size: 16px;
}
.test_version p span {
  color: #1d2746;
}

.v_menu {
  border-bottom: 1px solid #e8ecee;
  padding-bottom: 20px;
  padding-top: 5px;
  margin-bottom: 20px;
}
.v_menu .nav-item {
  display: inline-block;
}
.v_menu .nav-item .nav-link {
  font-size: 16px;
  color: #6b707f;
  transition: all 0.2s linear;
  padding: 0;
  border: 0;
  border-radius: 0;
}
.v_menu .nav-item .nav-link span {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #e1e3eb;
  display: inline-block;
  text-align: center;
  line-height: 20px;
  font-size: 12px;
  margin-right: 10px;
  transition: all 0.2s linear;
}
.v_menu .nav-item .nav-link.active, .v_menu .nav-item .nav-link:hover {
  color: #10b3d6;
  border: 0;
  background: transparent;
}
.v_menu .nav-item .nav-link.active span, .v_menu .nav-item .nav-link:hover span {
  background: #10b3d6;
  color: #fff;
}
.v_menu .nav-item + li {
  margin-left: 55px;
}

.process_tab_shortcode .version {
  background: #f8fafb;
  padding: 30px;
  font-size: 15px;
  margin-bottom: 20px;
}
.process_tab_shortcode .version p {
  margin-bottom: 20px;
}
.process_tab_shortcode .version p:last-child {
  margin-bottom: 0;
}
.process_tab_shortcode .version .v_head {
  padding-bottom: 24px;
}
.process_tab_shortcode .version .v_middle p {
  margin-bottom: 0;
  font-size: 15px;
}
.process_tab_shortcode .version .v_middle p .red {
  color: #f12249;
}
.process_tab_shortcode .version .v_middle p .green {
  color: #11c52f;
}
.process_tab_shortcode .version .v_footer {
  padding-top: 25px;
}
.process_tab_shortcode .version .v_footer p {
  margin-bottom: 0;
}

.developer {
  padding: 34px 0 20px;
}
.developer p a {
  color: #10b3d6;
}
.developer .tag_list li {
  margin-top: 10px;
}
.developer .tag_list li i {
  padding-right: 10px;
}

.help_text {
  padding: 40px 0 0;
}
.help_text .help_info {
  display: flex;
  flex-wrap: wrap;
  padding-top: 16px;
  margin-left: -30px;
  margin-right: -30px;
}
.help_text .help_info .help_item {
  width: 33.33%;
  margin-bottom: 15px;
  padding: 0 30px;
}
.help_text .help_info .help_item h4 {
  font-size: 16px;
  margin-bottom: 15px;
  transition: color 0.2s linear;
}
.help_text .help_info .help_item h4 i {
  font-size: 18px;
  top: 2px;
  position: relative;
}
.help_text .help_info .help_item h4:hover {
  color: #10b3d6;
}

.code_structure {
  padding-top: 50px;
}
.code_structure .c_head {
  margin-bottom: 8px;
}

.process_tab_shortcode {
  position: relative;
  overflow: hidden;
}
.process_tab_shortcode .previous, .process_tab_shortcode .next {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  padding: 0;
  border-radius: 0;
  background: #e7f7fb;
  border: 0;
  color: #10b3d6;
  outline: none;
  box-shadow: none;
  transition: all 0.4s linear;
}
.process_tab_shortcode .previous:focus, .process_tab_shortcode .next:focus {
  outline: none;
  box-shadow: none;
}
.process_tab_shortcode .previous:hover, .process_tab_shortcode .next:hover {
  outline: none;
  box-shadow: none;
  background: #10b3d6;
  color: #fff;
}
.process_tab_shortcode .next {
  right: -30px;
}
.process_tab_shortcode .previous {
  left: -30px;
}
.process_tab_shortcode:hover .next {
  right: 0;
}
.process_tab_shortcode:hover .previous {
  left: 0;
}

.question_box {
  padding-top: 35px;
  padding-bottom: 70px;
}
.question_box .question_text h4 {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 18px;
}
.question_box .question_text p a {
  color: #10b3d6;
}
.question_box .question_text_two {
  padding-left: 50px;
}
.question_box .signup_form {
  padding-top: 10px;
  padding-bottom: 20px;
}
.question_box .signup_form .input-group {
  border-radius: 4px;
  background-color: white;
  box-shadow: 0 2px 6px 0 rgba(2, 48, 58, 0.14);
}
.question_box .signup_form .input-group .form-control {
  background: transparent;
  border: 0;
  font-size: 16px;
  color: #1d2746;
  box-shadow: none;
  padding-left: 20px;
  height: 50px;
}
.question_box .signup_form .input-group .form-control.placeholder {
  color: #9c9fa9;
}
.question_box .signup_form .input-group .form-control:-moz-placeholder {
  color: #9c9fa9;
}
.question_box .signup_form .input-group .form-control::-moz-placeholder {
  color: #9c9fa9;
}
.question_box .signup_form .input-group .form-control::-webkit-input-placeholder {
  color: #9c9fa9;
}
.question_box .signup_form .input-group button {
  padding: 0;
  background: transparent;
  border: 0;
  font-size: 16px;
  font-weight: 500;
  color: #10b3d6;
  padding: 0 20px;
  position: relative;
}
.question_box .signup_form .input-group button:before {
  content: "";
  width: 1px;
  background: #e8ecee;
  top: 8px;
  bottom: 8px;
  left: 0;
  position: absolute;
}

.feedback_link {
  align-items: center;
  padding-top: 30px;
}
.feedback_link h6 {
  font-weight: 500;
  font-size: 16px;
  color: #6b707f;
  margin-bottom: 0;
}
.feedback_link h6 a {
  color: #10b3d6;
}
.feedback_link h6 i {
  padding-right: 8px;
}
.feedback_link p {
  margin-bottom: 0;
  text-align: right;
}
.feedback_link .h_btn {
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  color: #6b707f;
  display: inline-block;
  padding: 1px 14px 0;
  background-color: white;
  box-shadow: 0 3px 8px 0 rgba(2, 48, 58, 0.14);
  transition: all 0.2s linear;
  margin-left: 8px;
}
.feedback_link .h_btn:hover {
  color: #10b3d6;
}

.help_form {
  max-width: 730px;
  display: flex;
  align-items: center;
  height: 100vh;
  margin-top: 0;
  margin-bottom: 0;
}
.help_form .modal-content {
  border: 0;
}

.contact_form .form-group {
  margin-bottom: 30px;
}
.contact_form .form-group .form-control, .contact_form .form-group textarea {
  border: 1px solid #f4f8f8;
  height: 58px;
  border-radius: 4px;
  background-color: white;
  box-shadow: 0 4px 10px 0 rgba(4, 73, 89, 0.08);
  color: #878b99;
  font-weight: 400;
  padding-left: 30px;
}
.contact_form .form-group .form-control.placeholder, .contact_form .form-group textarea.placeholder {
  color: #878b99;
}
.contact_form .form-group .form-control:-moz-placeholder, .contact_form .form-group textarea:-moz-placeholder {
  color: #878b99;
}
.contact_form .form-group .form-control::-moz-placeholder, .contact_form .form-group textarea::-moz-placeholder {
  color: #878b99;
}
.contact_form .form-group .form-control::-webkit-input-placeholder, .contact_form .form-group textarea::-webkit-input-placeholder {
  color: #878b99;
}
.contact_form .form-group .form-control:focus, .contact_form .form-group textarea:focus {
  box-shadow: 0 20px 30px 0 rgba(4, 73, 89, 0.1);
}
.contact_form .form-group textarea {
  width: 100%;
  height: 160px;
  padding-top: 20px;
  display: block;
}
.contact_form .form-group .action_btn {
  border: 0;
  padding: 15px 40px;
  box-shadow: none;
  font-weight: 500;
}
.contact_form .form-group .action_btn:hover {
  box-shadow: 0 20px 30px 0 rgba(12, 118, 142, 0.24);
}

/*========== doc_rightsidebar css ============*/
.mCSB_scrollTools {
  width: 0;
}

.open_icon {
  width: 40px;
  height: 40px;
  background: #e8eeff;
  line-height: 40px;
  font-size: 32px;
  text-align: center;
  position: absolute;
  border-top-left-radius: 3px;
  border-bottom-left-radius: 3px;
  left: -40px;
  color: #10b3d6;
  display: none;
}
.open_icon i {
  display: inline-block;
}
.open_icon i + i {
  display: none;
}
.open_icon.overlay i {
  display: none;
}
.open_icon.overlay i + i {
  display: inline-block;
}

.full-width-doc .doc_rightsidebar {
  padding-right: 15px;
}
.full-width-doc .doc_rightsidebar .bootstrap-select:not([class*=col-]):not([class*=form-control]):not(.input-group-btn) {
  width: 225px;
}

.doc_rightsidebar {
  border-left: 1px solid #e8ecee;
  margin-left: -20px;
  padding-left: 30px;
  padding-top: 50px;
  height: 100%;
}
.doc_rightsidebar .bootstrap-select:not([class*=col-]):not([class*=form-control]):not(.input-group-btn) {
  width: 204px;
}
@media (max-height: 530px) {
  .doc_rightsidebar {
    padding-bottom: 0;
    max-height: 300px;
  }
}
.doc_rightsidebar.scroll .mCSB_inside > .mCSB_container {
  margin-right: 0;
}
.doc_rightsidebar h6 {
  font-size: 15px;
  font-weight: 500;
}
.doc_rightsidebar .doc_right_link li {
  margin-bottom: 10px;
  padding-left: 25px;
  position: relative;
}
.doc_rightsidebar .doc_right_link li a {
  font-size: 14px;
  font-weight: 500;
  color: #6b707f;
}
.doc_rightsidebar .doc_right_link li a i {
  padding-right: 12px;
  position: absolute;
  left: 0;
  font-size: 12px;
  top: 5px;
}
.doc_rightsidebar .doc_switch {
  display: flex;
  align-items: center;
  padding-bottom: 36px;
}
.doc_rightsidebar .doc_switch .tab-btn {
  font-size: 16px;
  color: #10b3d6;
  line-height: 22px;
  margin-bottom: 0;
  padding-top: 2px;
  cursor: pointer;
  transition: color 0.2s linear;
}
.doc_rightsidebar .doc_switch .tab-btn:hover {
  color: #10b3d6;
}
.doc_rightsidebar .doc_switch .fa-moon {
  top: -1px;
  position: relative;
}
.doc_rightsidebar .doc_switch input[type=checkbox] {
  width: 50px;
  height: 22px;
  border: 1px solid #91e1f2;
  background: #e7f7fb;
  display: block;
  border-radius: 25px;
  margin: 0 12px 0 15px;
  position: relative;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  box-sizing: content-box;
}
.doc_rightsidebar .doc_switch input[type=checkbox]:before {
  content: "";
  width: 16px;
  height: 16px;
  background: #10b3d6;
  position: absolute;
  border-radius: 50%;
  top: 50%;
  transform: translateY(-50%);
  transition: all 0.2s linear;
  left: 3px;
}
.doc_rightsidebar .doc_switch input[type=checkbox]:checked:before {
  left: calc(100% - 19px);
}
.doc_rightsidebar .doc_switch input[type=checkbox]:checked + .tab-btn {
  color: #10b3d6;
}
.doc_rightsidebar .doc_switch input[type=checkbox] + .tab-btn {
  color: #6b707f;
}

.doc_menu .nav-link {
  font-size: 14px;
  font-weight: 400;
  line-height: 30px;
  padding: 0;
  color: #6b707f;
}
.doc_menu .nav-link.active, .doc_menu .nav-link:hover {
  color: #1d2746;
}
.doc_menu .nav-link.active {
  font-weight: 500;
}
.doc_menu .nav {
  padding-left: 20px;
}
.doc_menu .nav .nav-link {
  font-size: 14px;
  font-weight: 400;
  line-height: 30px;
}

.mobile_menu {
  width: 300px;
  position: fixed;
  height: 100vh;
  max-height: 100vh !important;
  top: 0;
  background: #fff;
  right: -300px;
  z-index: 1050;
  transition: all 0.4s linear;
}
.mobile_menu .doc_left_sidebarlist {
  padding-left: 20px;
}
.mobile_menu .doc_left_sidebarlist:before {
  display: none;
}
.mobile_menu.open {
  right: 0;
}
.mobile_menu .close_nav {
  padding-top: 20px;
  padding-left: 30px;
  font-size: 28px;
}

/*=============== doc_documentation_full_area css =========*/
.full-width-doc .doc_left_sidebarlist {
  margin-right: 0;
  padding-left: 0;
}
.full-width-doc.body_fixed .doc_left_sidebarlist {
  width: 287px;
}
.full-width-doc .documentation_info {
  padding-left: 35px;
  padding-right: 35px;
}
.full-width-doc .doc_rightsidebar {
  margin-left: 0;
}

.sticky_menu .doc_documentation_area.body_fixed .doc_left_sidebarlist, .sticky_menu .doc_documentation_area.body_fixed .doc_rightsidebar {
  top: 78px;
}

.resource .c_head {
  margin-bottom: 5px;
}
.resource .tag_list {
  padding-top: 0;
}
.resource .tag_list li {
  margin-top: 4px;
}
.resource .tag_list li a {
  display: inline-block;
  font-weight: 500;
}

/*========== typography_content css ========*/
.typography_content .code-preview {
  border: 1px solid #e5ebef;
  padding: 20px;
  box-shadow: 0 3px 6px 0 rgba(4, 73, 89, 0.02);
  position: relative;
}
.typography_content .code-preview #header-name {
  margin-bottom: 0;
}
.typography_content .code-preview img {
  max-width: 100%;
}
.typography_content .code-preview.video_img {
  display: inline-block;
}
.typography_content .code-toolbar .snippets {
  margin-bottom: 0;
}
.typography_content .code-toolbar .snippets code {
  margin-bottom: 20px;
}
.typography_content .code_item {
  padding-top: 15px;
}
.typography_content .code_item p a {
  color: #10b3d6;
}

.vjs-iframe-blocker {
  display: none;
}

.video-js {
  margin: 0 auto;
  width: 100%;
  max-width: 640px;
  height: 360px;
}
.video-js .mfp-close {
  right: -55px;
  top: -10px;
}

/*============ Sticky Nav doc css ================*/
.sticky-nav-doc:not(.onepage-doc) .body_fixed .doc_rightsidebar, .sticky-nav-doc:not(.onepage-doc) .body_fixed .doc_left_sidebarlist {
  padding-top: 100px;
}
.sticky-nav-doc .body_fixed.body_navbar_fixed .doc_left_sidebarlist, .sticky-nav-doc .body_fixed.body_navbar_fixed .doc_rightsidebar {
  padding-top: 30px;
}

/*============ onepage doc css ================*/
.onepage-doc .shortcode_info {
  padding-left: 0;
  padding-right: 0;
}
.onepage-doc .bootstrap-select:not([class*=col-]):not([class*=form-control]):not(.input-group-btn) {
  width: 230px;
}
.onepage-doc.body_fixed .doc_left_sidebarlist {
  padding-top: 30px;
}
.onepage-doc .doc_left_sidebarlist {
  width: 270px;
  margin-right: 0;
  border: 0;
  padding-top: 0;
}
.onepage-doc .doc_left_sidebarlist:before {
  display: none;
}
.onepage-doc .nav-sidebar .nav-item {
  padding-right: 0;
  background: #f8fafb;
  margin-bottom: 0;
  margin-top: 3px;
  position: relative;
}
.onepage-doc .nav-sidebar .nav-item .docs-progress-bar {
  position: absolute;
  background: #ebf0f1;
  bottom: 0;
  left: 0;
  top: 0;
  max-height: 48px;
}
.onepage-doc .nav-sidebar .nav-item .nav-link {
  padding: 13px 20px;
}
.onepage-doc .nav-sidebar .nav-item .icon {
  top: 0;
  right: 20px;
  line-height: 48px;
  font-size: 18px;
  position: absolute;
  color: #10b3d6;
  transform: rotate(0deg);
}
.onepage-doc .nav-sidebar .nav-item .icon i {
  display: inline-block;
}
.onepage-doc .nav-sidebar .nav-item .icon i + i {
  display: none;
}
.onepage-doc .nav-sidebar .nav-item.active .icon i {
  display: none;
}
.onepage-doc .nav-sidebar .nav-item.active .icon i + i {
  display: inline-block;
}
.onepage-doc .nav-sidebar .nav-item.active .dropdown_nav {
  display: block;
}
.onepage-doc .nav-sidebar .nav-item .dropdown_nav {
  padding-top: 15px;
  padding-bottom: 15px;
  background: #fff;
  position: relative;
}
.onepage-doc .nav-sidebar .nav-item .dropdown_nav:before {
  content: "";
  width: 2px;
  bottom: 15px;
  background: #edf1f3;
  position: absolute;
  top: 15px;
  left: 20px;
}
.onepage-doc .nav-sidebar .nav-item .dropdown_nav .nav-item {
  margin-top: 0;
  background: transparent;
  position: relative;
  padding-left: 22px;
}
.onepage-doc .nav-sidebar .nav-item .dropdown_nav .nav-item:before {
  content: "5";
  position: absolute;
  left: 0;
  top: 0;
  font-family: "ElegantIcons";
  color: #10b3d6;
}
.onepage-doc .nav-sidebar .nav-item .dropdown_nav .nav-item .docs-progress-bar {
  display: none;
}
.onepage-doc .nav-sidebar .nav-item .dropdown_nav .nav-item .nav-link {
  padding: 0;
  color: #6b707f;
}
.onepage-doc .nav-sidebar .nav-item .dropdown_nav .nav-item.active .nav-link {
  color: #10b3d6;
}
.onepage-doc .doc_rightsidebar {
  margin-left: 0;
  padding-right: 10px;
  padding-top: 0;
}
.onepage-doc .doc-container {
  padding-top: 100px;
}

.doc_rightsidebar .bootstrap-select .dropdown-toggle .filter-option-inner-inner {
  font-size: 14px;
}
.doc_rightsidebar .dropdown-toggle {
  background: #fafcfd;
  border: 1px solid #e1e4e5;
  border-radius: 5px;
  font-size: 16px;
  color: #6b707f;
  padding: 9px 20px 0;
  box-shadow: none;
}
.doc_rightsidebar .dropdown-toggle:after {
  content: "3";
  border: 0;
  font-family: "ElegantIcons";
  top: -2px;
  position: relative;
}
.doc_rightsidebar .dropdown-toggle i {
  padding-right: 8px;
}
.doc_rightsidebar .dropdown-toggle:focus {
  outline: none !important;
  box-shadow: none !important;
}
.doc_rightsidebar .bootstrap-select {
  margin-bottom: 30px;
}
.doc_rightsidebar .bootstrap-select > .dropdown-menu {
  box-shadow: 0 0 0 1px rgba(68, 68, 68, 0.11);
  background: #fff;
  border: 0;
}
.doc_rightsidebar .bootstrap-select > .dropdown-menu .inner .dropdown-menu {
  border: 0;
}
.doc_rightsidebar .bootstrap-select > .dropdown-menu .inner .dropdown-menu li a {
  color: #6b707f;
  position: relative;
  font-size: 14px;
}
.doc_rightsidebar .bootstrap-select > .dropdown-menu .inner .dropdown-menu li a:before {
  content: "";
  width: 2px;
  height: 0;
  position: absolute;
  left: 0;
  top: 0;
  background: #10b3d6;
  transition: all 0.2s linear;
}
.doc_rightsidebar .bootstrap-select > .dropdown-menu .inner .dropdown-menu li a i {
  padding-right: 8px;
}
.doc_rightsidebar .bootstrap-select > .dropdown-menu .inner .dropdown-menu li a.active, .doc_rightsidebar .bootstrap-select > .dropdown-menu .inner .dropdown-menu li a:hover {
  background: #f6f6f6;
  color: #10b3d6;
}
.doc_rightsidebar .bootstrap-select > .dropdown-menu .inner .dropdown-menu li a.active:before, .doc_rightsidebar .bootstrap-select > .dropdown-menu .inner .dropdown-menu li a:hover:before {
  height: 100%;
}
.doc_rightsidebar .doc_switch {
  padding-top: 20px;
  margin-left: -40px;
  margin-top: 10px;
  padding-left: 40px;
}

.fontsize-controllers .btn-group {
  border: 1px solid #dfe2e4;
  background-color: white;
  box-shadow: 0 1px 0 0 rgba(3, 13, 37, 0.2), inset 0 -8px 14px 0 rgba(3, 13, 37, 0.1);
  border-radius: 4px;
}
.fontsize-controllers .btn-group .btn {
  font-size: 16px;
  font-weight: 400;
  color: #1d2746;
  line-height: 1.2;
  border: 0;
}
.fontsize-controllers .btn-group .btn:focus {
  outline: none;
  box-shadow: none;
  background: #eff0f1;
}
.fontsize-controllers .btn-group .btn.rvfs-reset {
  border-left: 1px solid #dfe2e4;
  border-right: 1px solid #dfe2e4;
}

.print {
  color: #6b707f;
  font-size: 18px;
}

.nav_title {
  font-size: 16px;
  text-transform: uppercase;
  color: #fff;
  background: #1d2746;
  border-radius: 4px;
  line-height: 22px;
  padding: 13px 30px;
  margin-bottom: 0;
}

body:not(.onepage-doc) .body_fixed #font-switcher {
  width: 204px;
}

/*---------------------------------------------------- */
/*----------------------------------------------------*/
.status-chart {
  padding: 62px 0 112px;
}
.status-chart .section_title {
  margin-bottom: 70px;
}

.kbDoc-chart-wrapper {
  position: relative;
}

.full-amount {
  position: absolute;
  height: 230px;
  width: 230px;
  padding: 100px 0;
  text-align: center;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  font-size: 30px;
  font-weight: 700;
  top: 50%;
  left: 50%;
  z-index: 99;
  transform: translate(-50%, -50%);
}
@media (max-width: 768px) {
  .full-amount {
    display: none;
  }
}
@media (max-width: 1200px) {
  .full-amount {
    height: 200px;
    width: 200px;
    padding: 85px 0;
  }
}
@media (max-width: 1200px) {
  .full-amount {
    height: 150px;
    width: 150px;
    padding: 58px 0;
  }
}
@media (max-width: 768px) {
  .full-amount {
    padding: 40px 0;
    height: 100px;
    width: 100px;
  }
}
.full-amount:before {
  content: "";
  position: absolute;
  left: 30px;
  top: 30px;
  background: #ffffff;
  border-radius: 50%;
  height: 170px;
  width: 170px;
}
@media (max-width: 1200px) {
  .full-amount:before {
    height: 110px;
    width: 110px;
    left: 20px;
    top: 20px;
  }
}
@media (max-width: 991px) {
  .full-amount:before {
    height: 110px;
    width: 110px;
    left: 20px;
    top: 20px;
  }
}
@media (max-width: 768px) {
  .full-amount:before {
    height: 80px;
    width: 80px;
    left: 10px;
    top: 10px;
  }
}
.full-amount .total-count {
  position: relative;
  z-index: 22;
  color: #a262f8;
  font-size: 30px;
  font-weight: 700;
}
@media (max-width: 768px) and (max-width: 768px) {
  .full-amount .total-count {
    font-size: 20px;
  }
}

@media (max-width: 768px) {
  .canvas {
    height: 100vh;
    width: 100vw;
  }
}
.chart-info {
  margin: 0;
  padding: 0;
  position: absolute;
  width: 100%;
  height: 100%;
  list-style: none;
}
@media (max-width: 1200px) {
  .chart-info {
    z-index: -1;
  }
}
@media (max-width: 768px) {
  .chart-info {
    display: none;
  }
}
.chart-info li {
  position: absolute;
  width: 370px;
}
.chart-info li .counterup {
  font-size: 34px;
  font-weight: 500;
  color: #42dabf;
  margin-bottom: 5px;
}
.chart-info li .border-image {
  position: absolute;
}
.chart-info li .border-image img {
  width: 100%;
}
.chart-info li .border-image svg {
  position: absolute;
}
.chart-info li .border-image:before {
  content: "";
  height: 8px;
  width: 8px;
  border-radius: 50%;
  background: #42dabf;
  position: absolute;
  left: 0px;
  top: -3px;
  z-index: 2;
}
.chart-info li.color-two .counterup {
  color: #f9327a;
}
.chart-info li.color-two .border-image:before {
  left: auto;
  right: 0;
  background: #fa5d96;
}
.chart-info li.color-three .counterup {
  color: #35bae9;
}
.chart-info li.color-three .border-image:before {
  top: 55px;
  background: #35bae9;
}
.chart-info li.color-four .counterup {
  color: #fcc103;
}
.chart-info li.color-four .border-image:before {
  top: 55px;
  left: auto;
  right: 0;
  background: #fcc103;
}
.chart-info li p {
  max-width: 210px;
  margin: 23px 0 0;
}
.chart-info li.info-left-top {
  top: 0;
  left: 30px;
}
.chart-info li.info-left-top svg {
  max-width: 370px;
}
@media (max-width: 1200px) {
  .chart-info li.info-left-top .border-image {
    max-width: 292px;
  }
  .chart-info li.info-left-top .border-image svg {
    max-width: 280px;
  }
  .chart-info li.info-left-top .border-image:before {
    top: 4px;
  }
}
@media (max-width: 991px) {
  .chart-info li.info-left-top {
    left: 0;
  }
  .chart-info li.info-left-top .border-image {
    max-width: 210px;
  }
  .chart-info li.info-left-top .border-image svg {
    max-width: 220px;
  }
  .chart-info li.info-left-top .border-image:before {
    top: 9px;
  }
}
.chart-info li.info-right-top {
  right: 125px;
  top: 35px;
  width: 330px;
  text-align: right;
}
@media (max-width: 991px) {
  .chart-info li.info-right-top {
    top: 20px;
  }
}
.chart-info li.info-right-top p {
  margin-left: auto;
}
.chart-info li.info-right-top svg {
  max-width: 330px;
  right: 0;
}
.chart-info li.info-right-top .border-image {
  right: 0;
}
@media (max-width: 1200px) {
  .chart-info li.info-right-top .border-image {
    width: 230px;
  }
  .chart-info li.info-right-top .border-image svg {
    max-width: 251px;
    right: 0;
    height: 40px;
  }
}
@media (max-width: 992px) {
  .chart-info li.info-right-top {
    right: 40px;
  }
  .chart-info li.info-right-top .border-image {
    width: 185px;
  }
}
.chart-info li.info-left-bottom {
  bottom: 0;
  left: 0;
  width: 350px;
  margin-left: 75px;
}
@media (max-width: 1200px) {
  .chart-info li.info-left-bottom {
    margin-left: 0;
  }
}
.chart-info li.info-left-bottom .border-image {
  bottom: 115px;
}
@media (max-width: 1200px) {
  .chart-info li.info-left-bottom .border-image {
    width: 330px;
  }
}
@media (max-width: 991px) {
  .chart-info li.info-left-bottom {
    margin-left: 0;
  }
  .chart-info li.info-left-bottom .border-image {
    width: 240px;
    overflow: hidden;
    height: 80px;
    bottom: 40px;
  }
  .chart-info li.info-left-bottom .border-image svg {
    max-width: 325px;
    left: -90px;
  }
}
.chart-info li.info-right-bottom {
  right: 50px;
  bottom: -9px;
  text-align: right;
}
.chart-info li.info-right-bottom p {
  margin-left: auto;
}
.chart-info li.info-right-bottom .border-image {
  right: 0;
  bottom: 122px;
}
.chart-info li.info-right-bottom .border-image svg {
  right: 0;
}
@media (max-width: 1200px) {
  .chart-info li.info-right-bottom .border-image {
    width: 271px;
  }
  .chart-info li.info-right-bottom .border-image svg {
    width: 320px;
  }
}
@media (max-width: 991px) {
  .chart-info li.info-right-bottom .border-image {
    width: 175px;
  }
  .chart-info li.info-right-bottom .border-image svg {
    width: 230px;
  }
}

/*---------------------------------------------------- */
/*----------------------------------------------------*/
.related-communities {
  padding: 55px 0 50px;
}
.related-communities .section_title {
  margin-bottom: 70px;
}

.dmt-4 {
  margin-top: 38px;
}

.communities-boxes {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  margin: 0 -15px;
}
@media (max-width: 992px) {
  .communities-boxes {
    flex-wrap: wrap;
    justify-content: center;
    width: calc(100% + 30px);
  }
}
@media (max-width: 420px) {
  .communities-boxes {
    width: calc(100% + 15px);
  }
}
.communities-boxes .kbDoc-com-box {
  text-align: center;
  margin-bottom: 57px;
  width: 20%;
  flex: 0 0 205px;
}
@media (max-width: 1200px) {
  .communities-boxes .kbDoc-com-box {
    flex: 0 0 165px;
  }
}
@media (max-width: 991px) {
  .communities-boxes .kbDoc-com-box {
    flex: 0 0 180px;
  }
}
@media (max-width: 768px) {
  .communities-boxes .kbDoc-com-box {
    flex: 0 0 185px;
  }
}
@media (max-width: 420px) {
  .communities-boxes .kbDoc-com-box {
    flex: 0 0 150px;
  }
}
.communities-boxes .kbDoc-com-box .icon-container {
  border-radius: 6px;
  background: #fff;
  box-shadow: 0px 40px 70px 0px rgba(2, 47, 57, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100px;
  width: 100px;
  margin: 0 auto 34px;
}
.communities-boxes .kbDoc-com-box .kbDoc-com-box-content .title {
  font-size: 20px;
  line-height: 26px;
  font-weight: 500;
  color: #1d2746;
  margin-bottom: 5px;
  transition: all 0.3s ease-in-out;
}
.communities-boxes .kbDoc-com-box .kbDoc-com-box-content .title a {
  color: #1d2746;
}
.communities-boxes .kbDoc-com-box .kbDoc-com-box-content .title a:hover {
  color: #10b3d6;
}
.communities-boxes .kbDoc-com-box .kbDoc-com-box-content .title:hover {
  color: #10b3d6;
}
.communities-boxes .kbDoc-com-box .kbDoc-com-box-content .total-post {
  color: #6b707f;
  font-size: 14px;
}

.more-communities {
  padding: 22px 0;
  border-top: 1px solid #e8ecee;
}
.more-communities .collapse-btn {
  text-align: center;
  display: block;
  color: #10b3d6;
  font-weight: 500;
}
.more-communities .collapse-btn i {
  vertical-align: -1px;
}
.more-communities .collapse-btn.active i:before {
  content: "K";
}
.more-communities .collapse-wrap {
  margin-top: 80px;
  display: none;
}

/*---------------------------------------------------- */
/*----------------------------------------------------*/
.about-journalism {
  background: #fafcfd;
  overflow: hidden;
  position: relative;
}

.light-header {
  position: absolute;
  bottom: -23px;
  left: 0;
  width: 100%;
  text-align: center;
}
@media (max-width: 768px) {
  .light-header {
    bottom: -10px;
  }
}
.light-header .title-light {
  color: #eff4f7;
  font-size: 60px;
  font-weight: 700;
  line-height: 1;
  margin: 0;
}
@media (max-width: 768px) {
  .light-header .title-light {
    font-size: 30px;
    bottom: -10px;
  }
}

.journalism-content-wrapper {
  max-width: 550px;
  padding: 113px 0 65px;
}
@media (max-width: 1480px) {
  .journalism-content-wrapper {
    padding: 83px 0 35px;
  }
}
.journalism-content-wrapper .quote {
  margin-left: -20px;
}
.journalism-content-wrapper .journalism-title {
  font-size: 30px;
  line-height: 40px;
  color: #1d2746;
  font-weight: 700;
  margin-bottom: 20px;
}
.journalism-content-wrapper p {
  font-size: 20px;
  color: #6b707f;
  line-height: 34px;
  margin-bottom: 40px;
}
.journalism-content-wrapper .journalism-info .name {
  color: #1d2746;
  font-size: 16px;
  margin: 0;
}
.journalism-content-wrapper .journalism-info .designation {
  font-size: 14px;
  color: #6b707f;
}

.journalism-feature-image {
  position: relative;
  margin-top: 20px;
}
@media (max-width: 992px) {
  .journalism-feature-image {
    margin-left: 20px;
  }
}
.journalism-feature-image:before {
  content: "";
  position: absolute;
  height: 600px;
  width: 600px;
  background-image: linear-gradient(45deg, #ecf9fc 0%, #feefec 100%);
  border-radius: 50%;
  z-index: 1;
  left: 20px;
  top: 85px;
}
@media (max-width: 992px) {
  .journalism-feature-image:before {
    left: -20px;
    top: 15px;
  }
}
.journalism-feature-image img {
  margin-left: -27px;
  position: relative;
  z-index: 2;
}
@media (max-width: 1480px) {
  .journalism-feature-image {
    margin-top: 80px;
  }
  .journalism-feature-image img {
    width: 500px;
  }
  .journalism-feature-image:before {
    height: 500px;
    width: 500px;
  }
}
@media (max-width: 992px) {
  .journalism-feature-image img {
    max-width: 500px;
  }
}
@media (max-width: 576px) {
  .journalism-feature-image img {
    max-width: 300px;
  }
}
@media (max-width: 576px) {
  .journalism-feature-image:before {
    width: 300px;
    height: 300px;
  }
}

@media (max-width: 992px) {
  .md-order-two {
    order: 2;
  }
}
/*---------------------------------------------------- */
/*----------------------------------------------------*/
.communities {
  padding: 115px 0 90px;
}

.community-box {
  border: 1px solid #f6f9fa;
  border-radius: 10px;
  background: #fff;
  box-shadow: 0px 2px 3px 0px rgba(2, 47, 57, 0.14);
  text-align: center;
  padding: 40px 70px;
  transition: all 0.3s ease-in-out;
  margin-bottom: 30px;
  overflow: hidden;
}
.community-box:before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  height: 5px;
  width: 100%;
  background: transparent;
  transition: all 0.3s ease-in-out;
}
.community-box .icon-container {
  margin-bottom: 33px;
}
.community-box .community-content .com-title {
  font-size: 18px;
  line-height: 28px;
  font-weight: 400;
  color: #6b707f;
  margin-bottom: 27px;
}
.community-box .community-content .details-link {
  color: #9296a3;
  font-size: 16px;
  font-weight: 500;
}
.community-box .community-content .details-link i {
  display: inline-block;
  vertical-align: -2px;
  transition: all 0.3s ease-in-out;
  color: #9296a3;
}
.community-box .community-content .details-link:hover {
  color: #26c6da;
}
.community-box .community-content .details-link:hover i {
  color: #26c6da;
  margin-left: 3px;
}
.community-box:hover {
  box-shadow: 0px 40px 30px 0px rgba(2, 47, 57, 0.1);
  transform: translateY(-5px);
}
.community-box:hover:before {
  background: #10b3d6;
}

/*---------------------------------------------------- */
/*----------------------------------------------------*/
.community-posts-area {
  padding: 110px 0 120px;
}
.community-posts-area .section_title {
  margin-bottom: 73px;
}

.community-post {
  border-radius: 6px;
  background-color: #fff;
  box-shadow: 0 2px 8px 0 rgba(2, 47, 57, 0.1);
  padding: 23px 30px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  transition: all 0.3s ease-in-out;
  border: 1px solid transparent;
}
.community-post.forum-item {
  padding: 23px 15px;
}
.community-post.forum-item .post-content p {
  font-size: 14px;
  line-height: 1.3;
}
@media (max-width: 667px) {
  .community-post {
    display: block;
  }
}
@media (max-width: 420px) {
  .community-post {
    padding: 20px 15px;
  }
}
.community-post:hover {
  box-shadow: 0 24px 40px 0 rgba(2, 47, 57, 0.12);
  border-color: #eef3f6;
  transform: translateX(10px);
}
.community-post .post-content {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.community-post .post-content .author-avatar {
  width: 40px;
  margin-right: 25px;
  border-radius: 50%;
  overflow: hidden;
  height: 40px;
}
.community-post .post-content .author-avatar.forum-icon {
  border-radius: 0;
}
.community-post .post-content .author-avatar.forum-icon img {
  padding: 2px;
}
.community-post .post-content .author-avatar img {
  max-width: 40px;
}
@media (max-width: 667px) {
  .community-post .post-content .author-avatar {
    margin-right: 20px;
  }
}
@media (max-width: 420px) {
  .community-post .post-content .author-avatar {
    margin-right: 15px;
  }
}
.community-post .post-content .entry-content {
  flex: 2;
}
.community-post .post-content .entry-content .post-title {
  font-size: 18px;
  font-weight: 500;
  color: #1d2746;
  margin-bottom: 0;
  line-height: 1.7;
}
.community-post .post-content .entry-content .post-title a {
  color: #1d2746;
}
.community-post .post-content .entry-content .post-title a:hover {
  color: #10b3d6;
}
.community-post .post-content .entry-content p {
  margin: 0;
}
@media (max-width: 667px) {
  .community-post .post-meta-wrapper {
    margin-top: 15px;
    margin-left: 70px;
  }
}
.community-post .post-meta-wrapper .post-meta-info {
  margin: 0;
  padding: 0;
  list-style: none;
}
.community-post .post-meta-wrapper .post-meta-info li {
  display: inline-block;
}
.community-post .post-meta-wrapper .post-meta-info li:not(:last-child) {
  margin-right: 50px;
}
@media (max-width: 991px) {
  .community-post .post-meta-wrapper .post-meta-info li:not(:last-child) {
    margin-right: 20px;
  }
}
.community-post .post-meta-wrapper .post-meta-info li a {
  display: block;
  color: #868b99;
}
.community-post .post-meta-wrapper .post-meta-info li a i {
  margin-right: 10px;
}
.community-post .post-meta-wrapper .post-meta-info li a:hover {
  color: #10b3d6;
}
.community-post.style-two {
  margin-bottom: 0;
  border-radius: 0;
  box-shadow: none;
  position: relative;
}
.community-post.style-two:not(:last-child) {
  border-bottom: 1px solid #f2f3f4;
}
.community-post.style-two .entry-content .post-title {
  font-size: 16px;
}
.community-post.style-two .post-meta-wrapper .post-meta-info li:not(:last-child) {
  margin-right: 30px;
}
.community-post.style-two .post-meta-wrapper .post-meta-info li i {
  transition: all 0.3s ease-in-out;
}
.community-post.style-two .post-meta-wrapper .post-meta-info li:nth-child(2):hover a {
  color: #b1b5c0;
}
.community-post.style-two .post-meta-wrapper .post-meta-info li:nth-child(2):hover i {
  color: #f9ae44;
}
.community-post.style-two .post-content .entry-content .post-title {
  display: inline-block;
  margin-right: 10px;
}
.community-post.style-two .post-content .com-featured {
  position: absolute;
  right: 0;
  top: -2px;
  height: 30px;
  width: 30px;
  z-index: 2;
  text-align: center;
  color: #fff;
  padding: 0 15px;
}
.community-post.style-two .post-content .com-featured i {
  z-index: 22;
  position: relative;
  font-size: 14px;
}
.community-post.style-two .post-content .com-featured:after {
  content: "";
  top: 0;
  position: absolute;
  right: 0;
  width: 0;
  height: 0;
  border-left: 30px solid transparent;
  border-right: 0 solid transparent;
  border-top: 30px solid #00ae69;
  z-index: 1;
}
.community-post.style-two .post-content .cat-wrap {
  display: inline-block;
  vertical-align: 3px;
}
.community-post.style-two .meta {
  margin: 0;
  padding: 0;
  list-style: none;
}
.community-post.style-two .meta li {
  display: inline-block;
  margin-right: 15px;
  color: #838793;
  font-size: 14px;
}
.community-post.style-two .meta li img {
  display: inline-block;
  vertical-align: -2px;
}
.community-post.style-two .meta li i,
.community-post.style-two .meta li img {
  margin-right: 10px;
  font-size: 14px;
}
.community-post.style-two .meta li a {
  font-size: 14px;
  color: #838793;
}
.community-post.style-two .meta li a:hover {
  color: #10b3d6;
}
.community-post.style-two:hover {
  background: #f7f9fa;
  transform: translateX(0);
  box-shadow: none;
  border-color: #f7f9fa;
}

.badge {
  font-size: 12px;
  background: #8152e0;
  padding: 2px 5px 0;
  color: #fff;
  border-radius: 3px;
  margin-right: 5px;
  display: inline-block;
  line-height: 1.45;
  border-bottom: 1px solid #6030c0;
  vertical-align: middle;
  font-weight: 500;
  height: 22px;
}
.badge:hover {
  color: #fff;
}
.badge.color-yellow {
  background: #fad05a;
  color: #1d2746;
  border-color: #dbaa20;
}
.badge.color-ass {
  background: #eeeeee;
  color: #1d2746;
  border-color: #d9d9d9;
}
.badge.color-green {
  background: #22936d;
  border-color: #07734f;
}
.badge.color-orange {
  background: #f88546;
  border-color: #d26428;
}
.badge.color-theme {
  background: #27b2da;
  border-color: #1697bc;
}
.badge.color-pink {
  background: #f053b3;
  border-color: #c91c86;
}
.badge.color-pink {
  background: #f053b3;
  border-color: #c91c86;
}

.bb-radius {
  border-bottom-left-radius: 6px;
  border-bottom-right-radius: 6px;
  overflow: hidden;
  box-shadow: 0 3px 6px 0 rgba(4, 73, 89, 0.06);
}

.button-container .all_doc_btn {
  margin-top: 40px;
}

@keyframes slideDown {
  0% {
    transform: translate3d(0, 15px, 0);
  }
  100% {
    transform: translate3d(0, 23px, 0);
  }
}
.doc_border_btn.border-light {
  border-color: #cfebf1 !important;
}
.doc_border_btn.border-light svg {
  margin-left: 8px;
  vertical-align: 0;
  transition: all 0.3s ease-in-out;
}
.doc_border_btn.border-light:hover {
  border-color: #10b3d6 !important;
}
.doc_border_btn.border-light:hover svg path {
  fill: #fff;
}

.pagination-wrapper {
  background: #fff;
  padding: 23px 30px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 10px;
  border-radius: 6px;
  box-shadow: 0 3px 6px 0 rgba(4, 73, 89, 0.06);
}
@media (max-width: 768px) {
  .pagination-wrapper {
    display: block;
    text-align: center;
  }
  .pagination-wrapper .view-post-of {
    margin-bottom: 20px;
  }
}
.pagination-wrapper p {
  margin: 0;
}
.pagination-wrapper .post-pagination {
  margin: 0;
  padding: 0;
  list-style: none;
}
@media (max-width: 768px) {
  .pagination-wrapper .post-pagination {
    margin-to: 30px;
  }
}
.pagination-wrapper .post-pagination li {
  display: inline-block;
}
.pagination-wrapper .post-pagination li.pegi-disable {
  display: none;
}
.pagination-wrapper .post-pagination li:not(:last-child) {
  margin-right: 3px;
}
.pagination-wrapper .post-pagination li a {
  display: block;
  color: #6b707f;
  height: 35px;
  width: 35px;
  text-align: center;
  line-height: 35px;
  background: #f2f5f6;
  border-radius: 3px;
}
.pagination-wrapper .post-pagination li a:hover, .pagination-wrapper .post-pagination li a.active {
  background: #10b3d6;
  color: #fff;
}

/*---------------------------------------------------- */
/*----------------------------------------------------*/
/*========== shortcode_text css ==============*/
.shortcode_text .shortcode_title p {
  margin-bottom: 20px;
}
.shortcode_text ul li {
  margin-bottom: 6px;
}
.shortcode_text ul li a {
  color: #6b707f;
}
.shortcode_text ul li a:hover {
  color: #10b3d6;
}
.shortcode_text .c_head {
  margin-top: 30px;
}

.article_list {
  margin-bottom: 30px;
}

/*============== shortcode_info css =========*/
.shortcode_info {
  padding-left: 15px;
  padding-right: 60px;
  padding-top: 60px;
}
.shortcode_info .shortcode_title p {
  margin-bottom: 0;
}
.shortcode_info h4 {
  font-size: 20px;
  font-weight: 500;
  margin-bottom: 25px;
  padding-top: 26px;
}
.shortcode_info h4:first-of-type {
  padding-top: 0;
}
.shortcode_info h6 {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 16px;
}
.shortcode_info p {
  margin-bottom: 30px;
}
.shortcode_info footer {
  padding-top: 60px;
}

.tab_shortcode {
  margin-bottom: 50px;
}
.tab_shortcode .nav-tabs {
  border: 0;
}
.tab_shortcode .nav-tabs .nav-item .nav-link {
  font-size: 16px;
  color: #6b707f;
  border-radius: 0;
  padding: 9px 20px;
  background: #f0f2f5;
  border: 0;
  border-left: 1px solid #dbe1e4;
  position: relative;
}
.tab_shortcode .nav-tabs .nav-item .nav-link:before {
  content: "";
  width: 0;
  height: 2.1px;
  background: #10b3d6;
  position: absolute;
  top: 0;
  left: 0;
  opacity: 0;
  transition: width 0.2s linear;
}
.tab_shortcode .nav-tabs .nav-item .nav-link.active {
  background: #fff;
  color: #1d2746;
}
.tab_shortcode .nav-tabs .nav-item .nav-link.active:before {
  opacity: 1;
  width: 100%;
}
.tab_shortcode .nav-tabs .nav-item:last-child .nav-link {
  border-right: 1px solid #dbe1e4;
}
.tab_shortcode .tab-content {
  color: #6b707f;
  padding: 25px 30px;
  border: 1px solid #dbe1e4;
  border-radius: 5px;
  border-top-left-radius: 0;
}

/*============== shortcode_info css =========*/
/*=========== toggle_shortcode css ========*/
.toggle_shortcode {
  padding-bottom: 60px;
}

.toggle_btn {
  display: block;
  font-size: 16px;
  font-weight: 500;
  color: #1d2746;
  background: #f0f2f5;
  text-align: left;
  border-radius: 4px;
  padding: 12px 30px;
  position: relative;
}
.toggle_btn:before {
  content: "C";
  position: absolute;
  font-size: 18px;
  font-family: eleganticons;
  right: 30px;
  transform: rotate(180deg);
  transition: all 0.2s linear;
  color: #10b3d6;
}
.toggle_btn.collapsed:before {
  transform: rotate(0deg);
  color: #1d2746;
}
.toggle_btn:focus {
  color: #1d2746;
}

.toggle_body {
  border: 1px solid #e8ecee;
  padding: 25px 30px;
  border-top: 0;
  border-radius: 4px;
}

/*=========== toggle_shortcode css ========*/
/*=========== doc_accordion Shortcode css ========*/
.doc_accordion {
  border: 0;
  margin-bottom: 5px;
}
.doc_accordion .card-header {
  border-radius: 0;
  border: 0;
  background: transparent;
  padding: 0;
}
.doc_accordion .card-header button {
  padding: 12px 30px;
  border-radius: 4px;
  text-align: left;
  width: 100%;
  font-size: 16px;
  color: #1d2746;
  font-weight: 500;
  background: #f0f2f5;
  text-decoration: none;
  position: relative;
}
.doc_accordion .card-header button i {
  position: absolute;
  right: 30px;
  color: #10b3d6;
  font-size: 22px;
  top: 50%;
  transform: translateY(-50%);
  display: none;
  transition: all 0.2s linear;
}
.doc_accordion .card-header button i + i {
  display: block;
}
.doc_accordion .card-header button.collapsed i {
  display: block;
  color: #6b707f;
}
.doc_accordion .card-header button.collapsed i + i {
  display: none;
}

/*=========== doc_accordion Shortcode css ========*/
/*=========== message_alert Shortcode css ========*/
.message_alert {
  border: 1px solid #e5e9eb;
  border-radius: 4px;
  background: #fafcfd;
  padding: 32px 40px 30px;
  margin-bottom: 20px;
}
.message_alert i {
  font-size: 24px;
  color: #abb0c0;
  margin-right: 25px;
}
.message_alert h5 {
  font-size: 16px;
  color: #1d2746;
  font-weight: 500;
  margin-bottom: 12px;
}
.message_alert p {
  margin-bottom: 0;
}
.message_alert .close {
  position: absolute;
  right: 14px;
  top: 12px;
  font-size: 24px;
  opacity: 1;
}
.message_alert .close i {
  margin-right: 0;
}

.alert-danger {
  background: #fff3f4;
  border-color: #fbadb3;
}
.alert-danger i, .alert-danger h5 {
  color: #fa303e;
}

.alert-success {
  background: #f1fdf3;
  border-color: #94e3a1;
}
.alert-success i, .alert-success h5 {
  color: #10d631;
}

.alert-warning {
  background: #fefbf1;
  border-color: #f2dca0;
}
.alert-warning i, .alert-warning h5 {
  color: #f6ba18;
}

.alert-info {
  background: #f1fbfd;
  border-color: #a0e3f2;
}
.alert-info i, .alert-info h5 {
  color: #10b3d6;
}

/*=========== message_alert Shortcode css ========*/
/*=========== notice Shortcode css ========*/
.notice_shortcode {
  padding-top: 25px;
}

.notice {
  border-left: 10px solid;
  padding: 30px 40px;
  margin-bottom: 20px;
  margin-top: 0;
}
.notice:before, .notice:after {
  display: none;
}
.notice i {
  font-size: 24px;
  margin-right: 25px;
}
.notice h5 {
  font-size: 20px;
  font-weight: 500;
  margin-bottom: 16px;
}
.notice p {
  font-size: 16px;
  font-weight: 400;
  margin-bottom: 0;
  color: #6b707f;
}
.notice p span {
  padding: 1px 10px;
  background: #fff;
  color: #ed2937;
}

.notice-success {
  background: #459e6d;
  border-color: #30845e;
}
.notice-success p, .notice-success i, .notice-success h5 {
  color: #fff;
}

.notice-warning {
  background: #fefaed;
  border-color: #f6ba18;
}
.notice-warning i {
  color: #f6ba18;
  padding-top: 4px;
}

.notice-danger {
  background: #ffeff0;
  border-color: #fa303e;
}
.notice-danger i {
  color: #fa303e;
}

.explanation {
  position: relative;
  background: linear-gradient(90deg, #fff, #75e3fb);
  border: 10px solid #fff;
  padding: 1rem 2rem;
  border-radius: 16px;
}
.explanation::before {
  content: "";
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  border: 1px solid #10b3d6;
  border-radius: 8px;
  pointer-events: none;
}
.explanation::after {
  font-family: "Roboto", sans-serif;
  content: "Hey!";
  text-transform: uppercase;
  font-weight: 700;
  top: -19px;
  left: 1rem;
  padding: 0 0.5rem;
  font-size: 0.6rem;
  position: absolute;
  z-index: 1;
  color: #000;
  background: #fff;
}

/*=========== lightbox Shortcode css ========*/
.lightbox_shortcode {
  display: inline-block;
  position: relative;
  z-index: 1;
}
.lightbox_shortcode img {
  background-color: #f0f2f5;
  box-shadow: 0 4px 14px 0 rgba(4, 73, 89, 0.08);
  max-width: 100%;
}

.img_popup {
  position: absolute;
  width: 50px;
  height: 50px;
  text-align: center;
  line-height: 52px;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  background: rgba(29, 39, 70, 0.5);
  border-radius: 50%;
  color: #fff;
  font-size: 26px;
}
.img_popup i {
  margin-left: -2px;
}
.img_popup:hover, .img_popup:focus {
  background: #fff;
  color: #1d2746;
  box-shadow: 4px 15px 34px 2px rgba(4, 73, 89, 0.2);
}

button.mfp-close {
  font-size: 30px;
  color: #6b707f;
  right: 10px;
  top: 5px;
}

.mfp-bg {
  background: #fff;
}

.mfp-with-zoom .mfp-container,
.mfp-with-zoom.mfp-bg {
  opacity: 0;
  transition: all 0.3s ease-out;
  padding: 0;
}

.mfp-with-zoom .mfp-container {
  transform: scale(0);
}

.mfp-with-zoom.mfp-ready .mfp-container {
  opacity: 1;
  transform: scale(1);
}

.mfp-with-zoom.mfp-ready.mfp-bg {
  opacity: 1;
}

.mfp-figure:after {
  background-color: #eceff4;
  box-shadow: 0 20px 80px 0 rgba(4, 73, 89, 0.12);
}

.mfp-with-zoom.mfp-removing .mfp-container,
.mfp-with-zoom.mfp-removing.mfp-bg {
  opacity: 0;
}

/*=========== lightbox Shortcode css ========*/
/*=========== tooltip Shortcode css ========*/
.tooltipster-sidetip .tooltipster-box {
  max-width: 300px;
  background: #fff;
  box-shadow: 0 30px 90px -20px rgba(0, 0, 0, 0.3), 0 0 1px 1px rgba(0, 0, 0, 0.05);
  border: 0;
  margin-top: 0;
}
.tooltipster-sidetip .tooltipster-box .tooltipster-content {
  padding: 0;
  background: #fff;
  border-radius: 5px;
  z-index: 1;
}
.tooltipster-sidetip .tooltipster-box .tooltipster-content .blog_grid_post {
  margin-bottom: 0;
}
.tooltipster-sidetip .tooltipster-box .tooltipster-content .blog_grid_post img {
  max-width: 100%;
}
.tooltipster-sidetip .tooltipster-box .tooltipster-content .blog_grid_post .grid_post_content {
  padding: 30px 20px 10px;
  box-shadow: none;
}
.tooltipster-sidetip .tooltipster-box .tooltipster-content .blog_grid_post .grid_post_content .post_tag a {
  font-size: 14px;
}
.tooltipster-sidetip .tooltipster-box .tooltipster-content .blog_grid_post .grid_post_content .b_title {
  font-size: 18px;
  line-height: 25px;
}
.tooltipster-sidetip .tooltipster-box .tooltipster-content .blog_grid_post .grid_post_content p {
  color: #6b707f;
  font-size: 14px;
  line-height: 22px;
}

.tooltip_content p {
  margin-bottom: 25px;
}
.tooltip_content p i {
  vertical-align: middle;
}
.tooltip_content p a {
  color: #10b3d6;
}
.tooltip_content .text-decoration {
  position: relative;
  display: inline-block;
}
.tooltip_content .text-decoration:before {
  content: "";
  width: 100%;
  height: 1px;
  background: #10b3d6;
  position: absolute;
  bottom: 5px;
  left: 0;
}

.direction_steps {
  display: inline-flex;
  align-items: center;
}

.direction_step {
  padding: 0 8px;
  font-size: 13px;
  border-radius: 4px;
  background: rgba(127, 130, 248, 0.8);
  color: #fff;
  position: relative;
  line-height: 20px;
}
.direction_step + .direction_step {
  margin-left: 28px;
}
.direction_step + .direction_step:before {
  content: "$";
  font-family: eleganticons;
  position: absolute;
  left: -22px;
  color: rgba(127, 130, 248, 0.8);
}

.tip_content {
  background: #fff;
  box-shadow: 0 20px 50px 0 rgba(4, 73, 89, 0.16);
  max-width: 320px;
  max-height: 100%;
  border-radius: 6px;
  text-align: left;
}
.tip_content .text {
  padding: 25px;
}
.tip_content p {
  font-size: 14px;
  line-height: 26px;
  color: #6b707f;
}
.tip_content h6 {
  font-size: 14px;
  color: #1d2746;
  font-weight: 500;
  padding-top: 10px;
  margin-bottom: 0;
}
.tip_content h6 span {
  display: block;
  font-weight: 400;
  color: #6b707f;
  font-size: 14px;
  padding-top: 5px;
}
.tip_content img {
  max-width: 100%;
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
}
.tip_content#tooltipTwo {
  min-height: 450px;
}

/*=========== tooltip Shortcode css ========*/
/*=========== image_pointer Shortcode css ========*/
.image_pointer .pointing_img {
  padding-bottom: 20px;
}
.image_pointer .pointing_img img {
  max-width: 100%;
  box-shadow: 0 20px 50px 0 rgba(4, 73, 89, 0.1);
}
.image_pointer .pointing_img h6 {
  margin-top: 35px;
}
.image_pointer .pointing_img h6 a {
  color: #10b3d6;
}

.modal-open {
  padding-right: 0 !important;
}

.modal-backdrop {
  background: #fff;
}

.img_modal {
  background: #fff;
  padding-right: 0 !important;
  box-shadow: 0 4px 14px 0 rgba(4, 73, 89, 0.08);
}
.img_modal img {
  box-shadow: 0 30px 80px 0 rgba(4, 73, 89, 0.12);
}
.img_modal .close {
  font-size: 30px;
  padding-right: 25px;
  padding-top: 25px;
  opacity: 1;
  color: #6b707f;
  position: absolute;
  right: 20px;
  top: 20px;
  padding: 0;
  z-index: 3;
}
.img_modal.fade .pointing_img_container {
  max-width: 1170px;
  margin: 0rem auto;
  padding: 80px 0;
  height: 100%;
  display: flex;
  align-items: center;
  transform: translate(0, 0);
}
.img_modal.fade .pointing_img_container .modal-content {
  border: 0;
  padding: 0;
}
.img_modal.fade .pointing_img_container .modal-content img {
  max-width: 100%;
}

.img_pointing {
  width: 10px;
  height: 10px;
  background: #a54ffe;
  border-radius: 50%;
  position: absolute;
  cursor: pointer;
}
.img_pointing:before, .img_pointing:after {
  content: "";
  width: 100%;
  height: 100%;
  position: absolute;
  border-radius: 50%;
  background: #a54ffe;
}
.img_pointing:before {
  transform: scale(2.1);
  opacity: 0.3;
}
.img_pointing:after {
  transform: scale(3);
  opacity: 0.2;
}
.img_pointing .dot {
  position: absolute;
  left: 50%;
  top: 50%;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: rgba(165, 79, 254, 0.9);
  animation: pulse1 3s infinite;
  transform: translate(-50%, -50%);
  animation-delay: 1.5s;
  will-change: transform;
}
.img_pointing.one {
  top: 60px;
  right: 340px;
}
.img_pointing.two {
  left: 175px;
  top: 20px;
}
.img_pointing.three {
  left: 50%;
  transform: translateX(-50%);
  top: 250px;
}
.img_pointing.four {
  left: 30%;
  transform: translateX(-50%);
  top: 50%;
}

.img_pointing_content {
  padding: 25px;
}
.img_pointing_content p {
  font-size: 14px;
  line-height: 22px;
  font-family: "Roboto", sans-serif;
  color: #6b707f;
  margin-bottom: 0;
}
.img_pointing_content p span {
  color: #1d2746;
}
.img_pointing_content p + p {
  margin-top: 15px;
}

.pointing_img_two {
  position: relative;
  margin-bottom: 60px;
}
.pointing_img_two img {
  box-shadow: 0 20px 50px 0 rgba(4, 73, 89, 0.1);
}
.pointing_img_two .img_pointing.one {
  right: 130px;
}
.pointing_img_two .img_pointing.three {
  left: 55%;
}
.pointing_img_two .img_pointing.four {
  left: 100px;
}

/*=========== image_pointer Shortcode css ========*/
/*=========== caniuse Shortcode css ========*/
.caniuse ul {
  display: flex;
  flex-wrap: wrap;
  margin: -6px;
}
.caniuse ul .caniuse_agents_item {
  text-align: center;
  border-radius: 5px;
  padding: 23px 30px 20px;
  margin: 6px;
}
.caniuse ul .caniuse_agents_item img {
  height: 40px;
  display: block;
}
.caniuse ul .caniuse_agents_item span {
  display: block;
  font-size: 20px;
  padding-top: 13px;
}

.caniuse_section + .caniuse_section {
  margin-top: 55px;
}

.light_blue {
  background: #ece6fc;
  color: #784bfb;
}

.light_red {
  background: #fce6e6;
  color: #f84343;
}

.blue {
  background: #e6f8fc;
  color: #10b3d6;
}

.caniuse_section_legend p {
  padding-top: 25px;
}
.caniuse_section_legend .caniuse_legend_list {
  padding: 15px 0 10px;
}
.caniuse_section_legend .caniuse_legend_list span {
  margin-right: 20px;
}
.caniuse_section_legend .caniuse_legend_list a {
  font-size: 14px;
  font-weight: 500;
  padding: 0 18px;
  display: inline-block;
  margin: 0 3px;
  border-radius: 2px;
}
.caniuse_section_legend .caniuse_legend_list a.blue:hover {
  background: #10b3d6;
  color: #fff;
}

.purpale_btn {
  background: #fad9ea;
  color: #e32a8b;
}
.purpale_btn:hover {
  background: #e32a8b;
  color: #fff;
}

.green_btn {
  background: #cef5d4;
  color: #11ca30;
}
.green_btn:hover {
  background: #11ca30;
  color: #fff;
}

.violate_btn {
  background: #e8d9fa;
  color: #9744fd;
}
.violate_btn:hover {
  background: #9744fd;
  color: #fff;
}

/*=========== caniuse Shortcode css ========*/
/*=========== table Shortcode css ========*/
.table_shortcode thead {
  background: #06a8cb;
}
.table_shortcode thead th {
  padding: 20px 25px;
  border: 0;
}
.table_shortcode th, .table_shortcode td {
  border: 0;
  font-size: 16px;
  font-weight: 400;
  color: #fff;
  vertical-align: middle;
}
.table_shortcode th {
  font-weight: 500;
}
.table_shortcode tbody tr {
  background: #f0f2f5;
}
.table_shortcode tbody tr th {
  background: #10b3d6;
}
.table_shortcode tbody tr td, .table_shortcode tbody tr th {
  padding: 12px 25px;
}
.table_shortcode tbody tr td {
  color: #6b707f;
}
.table_shortcode tbody tr:nth-child(odd) {
  background: #fafcfd;
}

.basic_table {
  padding-top: 45px;
}
.basic_table .s_title {
  margin-bottom: 10px;
}
.basic_table p {
  margin-bottom: 25px;
}

.basic_table_info {
  width: 100%;
  margin-bottom: 1rem;
  background-color: transparent;
  border: 1px solid #f1f2f3;
}
.basic_table_info thead th {
  border: 0;
}
.basic_table_info tbody tr th, .basic_table_info tbody tr td {
  border-color: #eaeff4;
}
.basic_table_info.table-hover tbody tr {
  transition: all 0.3s linear;
}
.basic_table_info.table-hover tbody tr:hover {
  background: #10b3d6;
  color: #fff;
}

/*=========== table Shortcode css ========*/
/*============ Footnote ===========*/
.text-component--has-footnotes {
  counter-reset: footnotes;
}

.footnotes {
  border-top: 1px solid var(--color-contrast-low);
  font-size: 0.875em;
}

.footnotes-link {
  counter-increment: footnotes;
  font-size: 0.75em;
  vertical-align: super;
}

.footnotes-link::before {
  content: "[" counter(footnotes) "]";
}

.footnotes-link:target, .footnotes_item:target {
  outline: 2px solid #10b3d6;
  outline-offset: 2px;
}

.footnotes_item {
  font-size: 14px;
}
.footnotes_item strong {
  color: #1d2746;
  padding-bottom: 7px;
  display: inline-block;
  font-size: 16px;
}
.footnotes_item + .footnotes_item {
  margin-top: 20px;
}

/*============ Footnote ===========*/
/*============ List Style ===========*/
.single_list {
  list-style: none;
  box-shadow: 0 3px 6px 0 rgba(4, 73, 89, 0.02);
  border: 1px solid #e5ebef;
  padding: 20px;
  margin-top: 10px;
  margin-bottom: 25px;
  border-radius: 2px;
}
.single_list li {
  font-size: 14px;
  font-weight: 500;
  line-height: 22px;
  position: relative;
  padding-left: 20px;
}
.single_list li:before {
  content: "";
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #10b3d6;
  position: absolute;
  left: 0;
  top: 9px;
}
.single_list li + li {
  margin-top: 15px;
}

/** === List Style === **/
.steps-panel {
  box-shadow: 0 3px 6px 0 rgba(4, 73, 89, 0.02);
  border: 1px solid #e5ebef;
  margin-top: 10px;
  margin-bottom: 25px;
  border-radius: 2px;
}

ul.ordered-list li::before {
  content: "h";
  font-family: "ElegantIcons";
  font-size: 10px;
}

.ordered-list {
  position: relative;
  list-style: none;
  padding-left: 20px;
  margin: 20px 0;
}
.ordered-list::before {
  content: "";
  position: absolute;
  left: 32px;
  top: 0;
  bottom: 0;
  width: 1px;
  background-color: #c4cdd5;
}
.ordered-list li {
  font-size: 16px;
  font-weight: 400;
  line-height: 25px;
  margin: 16px 0;
  position: relative;
  padding-left: 35px;
  counter-increment: a;
}
.ordered-list li::before {
  content: counter(a);
  position: absolute;
  color: #fff;
  height: 32px;
  width: 32px;
  border-radius: 50%;
  font-weight: 500;
  font-size: 12px;
  background-color: #10b3d6;
  text-align: center;
  line-height: 23px;
  top: -4px;
  left: -4px;
  border: 5px solid #fff;
}

/*============ changelog css ===========*/
.changelog_inner {
  padding-right: 30px;
  padding-bottom: 100px;
}

.changelog_info {
  padding-top: 15px;
}
.changelog_info .version_info {
  margin-right: 15px;
  position: relative;
  height: 100%;
}
.changelog_info .c_version {
  flex: 1;
  background: #1d2746;
  text-align: center;
  font-size: 14px;
  font-weight: 400;
  height: 26px;
  line-height: 27px;
  border-radius: 3px;
  color: #fff;
  min-width: 50px;
  max-width: 60px;
  display: block;
  margin: 0 auto;
}
.changelog_info .changelog_date {
  display: flex;
  flex-direction: row-reverse;
  text-align: right;
}
.changelog_info .changelog_date .c_date h6 {
  font-size: 20px;
  font-weight: 700;
  margin-bottom: 0;
}
.changelog_info .changelog_date .c_date p {
  font-size: 14px;
  margin-bottom: 0;
  color: #6b707f;
}
.changelog_info .line {
  height: 106%;
  width: 2px;
  background: #e2e5ee;
  position: absolute;
  left: 50%;
  top: 0;
  transform: translateX(-50%);
  z-index: -1;
}
.changelog_info:last-child .line {
  height: 100%;
}
.changelog_info:last-child .changelog_content {
  padding-bottom: 0;
}

.changelog_content {
  padding-bottom: 60px;
}
.changelog_content p {
  display: flex;
  margin-bottom: 15px;
  font-size: 14px;
}
.changelog_content span {
  font-weight: 400;
  font-size: 10px;
  color: #fff;
  padding: 0 10px;
  text-transform: uppercase;
  min-width: 110px;
  display: inline-block;
  text-align: center;
  line-height: 23px;
  border-radius: 3px;
  margin-right: 20px;
  height: 22px;
  flex: 0;
  letter-spacing: 0.8px;
}
.changelog_content span.improve {
  background: #10b3d6;
}
.changelog_content span.new {
  background: #0ed193;
}
.changelog_content span.update {
  background: #ffa100;
}
.changelog_content span.fixed {
  background: #ea3940;
}
.changelog_content .download-links {
  border-top: 1px dotted rgba(51, 51, 51, 0.2);
}
.changelog_content .changelog_btn {
  font-size: 14px;
  color: #6b707f;
  font-weight: 500;
  margin-top: 12px;
  display: inline-block;
  opacity: 0.5;
}
.changelog_content .changelog_btn i {
  margin-right: 10px;
}
.changelog_content .changelog_btn:hover {
  color: #10b3d6;
}
.changelog_content .changelog_btn + .changelog_btn {
  margin-left: 28px;
}
.changelog_content:hover .changelog_btn {
  opacity: 1;
}

.tour_info_content p {
  font-size: 14px;
  margin-bottom: 0;
}

.tour_item {
  padding: 70px 0;
}
.tour_item .arrow.text-right {
  margin-right: -80px;
  position: relative;
  z-index: 1;
}

.tour_intro_item .tour_info_content {
  position: relative;
}
.tour_intro_item .tour_info_content p {
  margin-bottom: 55px;
}
.tour_intro_item .tour_info_content .arrow {
  position: absolute;
  bottom: 0;
  left: -20px;
}
.tour_intro_item.flex-row-reverse .arrow {
  right: -20px;
  left: auto;
}
.tour_intro_item + .tour_intro_item {
  margin-top: 60px;
}

.last_tour_item {
  padding-bottom: 0;
}

.just_text {
  display: flex;
  align-items: flex-end;
  justify-content: flex-end;
}
.just_text p {
  margin-bottom: 0;
}
.just_text + .just_text {
  margin-top: 70px;
}

.answer_bottom {
  align-items: flex-start;
}

.tour_preview_img {
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

/*========== cheatsheet_info css =========*/
.cheatsheet_info + .cheatsheet_info {
  margin-top: 30px;
}

.cheatsheet_accordian .card {
  border: 0;
  padding: 0;
  overflow: visible;
  margin-top: 10px;
}
.cheatsheet_accordian .card .card-header {
  padding: 0;
  border: 0;
  background: transparent;
}
.cheatsheet_accordian .card .card-header button {
  padding: 0;
  font-size: 20px;
  font-weight: 500;
  margin-bottom: 30px;
  background: transparent;
  color: #1d2746;
  text-decoration: none;
}
.cheatsheet_accordian .card .card-header button .minus {
  display: inline-block;
}
.cheatsheet_accordian .card .card-header button .pluse {
  display: none;
}
.cheatsheet_accordian .card .card-header button.collapsed .pluse {
  display: inline-block;
}
.cheatsheet_accordian .card .card-header button.collapsed .minus {
  display: none;
}

.cheatsheet_item {
  text-align: center;
  box-shadow: 0 4px 8px 0 rgba(4, 73, 89, 0.05);
  padding: 20px 15px 30px;
  margin-bottom: 30px;
  transition: all 0.3s linear;
}
.cheatsheet_item:hover {
  box-shadow: 0 20px 27px 0 rgba(4, 73, 89, 0.08);
}
.cheatsheet_item .cheatsheet_num {
  text-align: right;
}
.cheatsheet_item p {
  margin-bottom: 0;
}
.cheatsheet_item h4 {
  margin-bottom: 0;
  color: #10b3d6;
}

/*---------------------------------------------------- */
/*----------------------------------------------------*/
/*==============typography_area css ================*/
.typography_area {
  padding: 115px 0 120px;
}
.typography_area p {
  line-height: 28px;
}

.heading_content .t_title {
  color: #6b707f;
  margin-bottom: 0;
  padding-bottom: 20px;
}
.heading_content .bold, .heading_content .medium, .heading_content .regular {
  margin-bottom: 22px;
  line-height: 1;
}

.typography_preview {
  margin-top: 30px;
}

.typography_underline h4 {
  margin-bottom: 35px;
}

.highlight_text, .typography_underline {
  padding-right: 100px;
  margin-top: 85px;
}

.highlight_text h5 {
  margin-bottom: 30px;
}
.highlight_text span {
  display: inline-block;
  padding: 0 6px;
}
.highlight_text .h_black {
  background: #222d39;
  color: #fff;
}
.highlight_text .h_green {
  background: #15e18d;
  color: #fff;
}
.highlight_text .h_blue {
  background: #2cabed;
  color: #fff;
}
.highlight_text p .tooltips_one {
  color: #4b5ffa;
}
.highlight_text p .tooltips_two {
  color: #ef971a;
}

.tooltip_blue .arrow:before {
  border-top-color: #4b5ffa;
}
.tooltip_blue.bs-tooltip-bottom .arrow:before {
  border-bottom-color: #4b5ffa;
}
.tooltip_blue .tooltip-inner {
  background: #4b5ffa;
}

.tooltip_danger.show {
  opacity: 1;
}

.tooltip_danger {
  opacity: 1;
}
.tooltip_danger .arrow:before {
  border-top-color: #ef971a;
}
.tooltip_danger.bs-tooltip-bottom .arrow:before {
  border-bottom-color: #ef971a;
}
.tooltip_danger .tooltip-inner {
  background: #ef971a;
}

.dropcap_content p span {
  float: left;
  font-size: 54px;
  margin-right: 10px;
  color: #fc5bc1;
  font-weight: 700;
  line-height: 55px;
  font-family: "Roboto", sans-serif;
}
.dropcap_content p .r_dropcap {
  background: #13c5bf;
  border-radius: 4px;
  color: #fff;
  padding: 0 6px;
  font-size: 40px;
  font-weight: 700;
  line-height: 42px;
  margin-top: 10px;
}

.typography_list h5, .dropcap_content h5 {
  margin-bottom: 30px;
}

.typography_list ul li a, .typography_list ol li a {
  font-size: 15px;
  line-height: 34px;
  color: #6b707f;
  font-weight: 400;
}
.typography_list ol {
  padding-left: 15px;
}
.typography_list ol li ol {
  padding-left: 30px;
}
.typography_list .unorderlist li {
  position: relative;
  padding-left: 14px;
}
.typography_list .unorderlist li:before {
  content: "";
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #a7acbd;
  position: absolute;
  left: 0;
  top: 14px;
}
.typography_list .unorderlist li ul {
  padding-left: 10px;
}

.blockquote_inner {
  margin-top: 85px;
}
.blockquote_inner h6 {
  color: #6b707f;
  margin-bottom: 0;
}

.blockquote {
  background: transparent;
  padding: 0 0 0 40px;
  border-color: #1d2746;
  border-width: 2px;
  margin-bottom: 55px;
}
.blockquote:before, .blockquote:after {
  display: none;
}
.blockquote p {
  font-size: 18px;
  font-style: italic;
  color: #4b505e;
  line-height: 30px;
}

.blockquote_two {
  background: #f9fafb;
  padding: 14px 40px 26px 140px;
  border: 0;
  position: relative;
  margin-top: 25px;
}
.blockquote_two:before, .blockquote_two:after {
  display: none;
}
.blockquote_two span {
  background: #d6dffa;
  padding: 0 4px;
}
.blockquote_two .quote_icon {
  font-size: 150px;
  color: #d3d6e1;
  position: absolute;
  transform: rotate(180deg);
  top: 18px;
  background: transparent;
  left: 40px;
  line-height: 155px;
}
.blockquote_two h5 {
  font-size: 22px;
  line-height: 40px;
  font-style: italic;
  font-weight: 400;
  color: #6b707f;
}

.button_inner {
  margin-top: 60px;
}
.button_inner h4 {
  margin-bottom: 50px;
  padding-top: 30px;
}
.button_inner h6 {
  color: #6b707f;
  font-weight: 400;
}
.button_inner .button_inner_one {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40px 0 50px;
  border-bottom: 1px solid #e6eeef;
}
.button_inner:last-child .button_inner_one {
  border: 0;
  padding-bottom: 0;
}

.btn_small {
  padding: 0 16px;
}

.btn_small_two {
  padding: 8px 29px;
}

.doc_border_btn_two, .btn_small_three {
  padding: 10px 47px;
}

.btn_bg {
  padding: 15px 78px;
}

.btn_radious_none {
  border-radius: 0;
  padding: 10px 68px;
}

.btn_radious_45 {
  border-radius: 45px;
  padding: 5px 28px;
}

.action_btn.btn_small, .action_btn.btn_small_two, .action_btn.btn_small_three, .action_btn.btn_bg, .action_btn.btn_radious_none, .action_btn.btn_radious_45 {
  box-shadow: none;
  border: 2px solid #10b3d6;
  transition: all 0.3s linear;
}
.action_btn.btn_small:hover, .action_btn.btn_small_two:hover, .action_btn.btn_small_three:hover, .action_btn.btn_bg:hover, .action_btn.btn_radious_none:hover, .action_btn.btn_radious_45:hover {
  background: #f8fdfe;
  border-color: #79ccde;
  color: #10b3d6;
}

.icon_btn {
  border: 2px solid #79ccde;
  margin-left: 0;
  background: #f8fdfe;
}
.icon_btn i {
  padding-right: 7px;
}
.icon_btn:hover {
  border-color: #10b3d6;
}

.arrow_btn_medium {
  padding: 13px 28px;
}

.arrow_btn_big {
  padding: 18px 44px;
  font-size: 18px;
}
.arrow_btn_big i {
  font-size: 28px;
  padding-left: 12px;
}

.arrow_btn_small {
  padding: 15px 37px;
}

.arrow_btn_small_two {
  padding: 10px 23px;
}

.dropcap_inner {
  padding: 50px 0;
}

/*==============typography_area css ================*/
/*==============action_area_three css ================*/
.action_area_three {
  background: url("../img/action_bg.jpg") no-repeat scroll center 0/cover;
  padding: 110px 0;
  background-attachment: fixed;
}

.action_content_three {
  max-width: 520px;
  margin: 0 auto;
}
.action_content_three h2 {
  font-size: 40px;
  line-height: 50px;
  font-weight: 700;
  color: #fff;
  margin-bottom: 30px;
}
.action_content_three .doc_border_btn {
  background: transparent;
  color: #fff;
  border-color: #b4b6da;
  padding: 13px 34px;
}
.action_content_three .doc_border_btn:hover {
  background: #ffffff;
  color: #10b3d6;
  border-color: #fff;
}

/*==============action_area_three css ================*/
/*---------------------------------------------------- */
/*----------------------------------------------------*/
/** === Forum heading bar === **/
.post-header {
  display: flex;
  justify-content: space-between;
  background: #f7f8f9;
  padding: 17px 30px;
  border: 1px solid #eaeeef;
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
}
.post-header.forums-header {
  padding: 17px 15px;
  display: flex;
  text-align: left;
}
@media (max-width: 768px) {
  .post-header {
    display: block;
    text-align: center;
  }
}
@media (max-width: 576px) {
  .post-header {
    padding: 15px;
  }
}
.post-header .support-total-info {
  margin: 0;
  padding: 0;
  list-style: none;
}
@media (max-width: 768px) {
  .post-header .support-total-info {
    margin-bottom: 20px;
  }
}
.post-header .support-total-info li {
  display: inline-block;
  font-size: 14px;
}
.post-header .support-total-info li.open-ticket {
  color: #1d2746;
  font-weight: 500;
}
.post-header .support-total-info li.close-ticket a {
  color: #6b707f;
}
.post-header .support-total-info li a {
  font-size: 14px;
  font-weight: 500;
}
.post-header .support-total-info li a:hover {
  color: #10b3d6;
}
.post-header .support-total-info li:not(:last-child) {
  margin-right: 20px;
}
.post-header .support-total-info li i {
  color: #00ae69;
  margin-right: 10px;
  vertical-align: -2px;
}
.post-header .support-total-info li:nth-child(1) i {
  color: #f06292;
}
.post-header .category-menu {
  margin: 0;
  padding: 0;
  list-style: none;
}
.post-header .category-menu li {
  display: inline-block;
}
.post-header .category-menu li .dropdown-menu {
  top: 12px !important;
  transform: translate3d(0, 23px, 0) !important;
}
.post-header .category-menu li:not(:last-child) {
  margin-right: 10px;
}
@media (max-width: 546px) {
  .post-header .category-menu li:not(:last-child) {
    margin-right: 0;
  }
}
.post-header .category-menu li:nth-child(4) .dropdown-menu, .post-header .category-menu li:nth-child(3) .dropdown-menu {
  right: -11px;
  left: auto !important;
}
@media (max-width: 380px) {
  .post-header .category-menu li:nth-child(4) .dropdown-menu {
    width: 190px;
  }
}
.post-header .category-menu li:nth-child(3) .title:before, .post-header .category-menu li:nth-child(3) .title:after {
  right: 40px;
}
.post-header .category-menu li:nth-child(1) .title:before, .post-header .category-menu li:nth-child(1) .title:after {
  right: auto;
  left: 30px;
}
.post-header .category-menu li:nth-child(2) .title:before, .post-header .category-menu li:nth-child(2) .title:after {
  right: auto;
  left: 25px;
}
.post-header .category-menu .dropdown-toggle {
  position: relative;
  background: transparent;
  box-shadow: none;
  border: 0;
  color: #6b707f;
  padding: 0 15px;
  font-size: 14px;
}
.post-header .category-menu .dropdown-toggle:after {
  content: "3";
  font-family: eleganticons;
  border: 0;
  position: absolute;
  top: 50%;
  right: -5px;
  transform: translateY(-58%);
}
.post-header .category-menu .btn-secondary:not(:disabled):not(.disabled).active,
.post-header .category-menu .btn-secondary:not(:disabled):not(.disabled):active,
.post-header .category-menu .show > .btn-secondary.dropdown-toggle {
  background-color: transparent;
  border: none;
  color: #6b707f;
  box-shadow: none;
}
.post-header .category-menu .dropdown-menu {
  border: 1px solid #e6eeef;
  background: #fff;
  box-shadow: 0 30px 40px 0 rgba(4, 73, 89, 0.1);
  padding: 0;
  width: 260px;
  animation: slideDown 0.12s cubic-bezier(0, 0.1, 0.1, 1) backwards;
}
@media (max-width: 576px) {
  .post-header .category-menu .dropdown-menu {
    width: 210px;
  }
}
.post-header .category-menu .title {
  font-size: 14px;
  font-weight: 500;
  margin: 0;
  background: #f7f8f9;
  border-bottom: 1px solid #e6eeef;
  padding: 14px 20px 11px;
  line-height: 1;
  color: #6b707f;
  position: relative;
}
.post-header .category-menu .title:before {
  content: "";
  height: 16px;
  width: 16px;
  background: #e6eeef;
  position: absolute;
  right: 30px;
  transform: rotate(45deg);
  top: -8px;
}
.post-header .category-menu .title:after {
  content: "";
  height: 16px;
  width: 16px;
  background: #f7f8f9;
  position: absolute;
  right: 30px;
  transform: rotate(45deg);
  top: -6px;
}
.post-header .category-menu .cate-search-form {
  padding: 0 20px;
  margin-top: 10px;
}
.post-header .category-menu .cate-search-form input {
  width: 100%;
  background: #fbfcfc;
  border: 2px solid #e2e7e8;
  border-radius: 4px;
  padding: 6px 20px;
  font-size: 14px;
}
.post-header .category-menu .cate-search-form input::placeholder {
  color: #a8acb6;
}
.post-header .category-menu .cate-search-form input:focus {
  background: #fff;
}
.post-header .category-menu .all-users {
  font-size: 14px;
  padding: 12px 22px;
  border-bottom: 1px solid #e6eeef;
  font-weight: 500;
  color: #6b707f;
}
.post-header .category-menu .all-users a {
  padding: 7px 0;
  color: #838793;
}
.post-header .category-menu .all-users a span {
  border-radius: 4px;
  width: 16px;
  height: 16px;
  background: #27b2da;
  display: inline-block;
  margin-right: 10px;
  vertical-align: -3px;
}
.post-header .category-menu .all-users a span.color-pupple {
  background: #8152e0;
}
.post-header .category-menu .all-users a span.color-yellow {
  background: #fad05a;
}
.post-header .category-menu .all-users a span.color-ass {
  background: #dfdfdf;
}
.post-header .category-menu .all-users a span.color-green {
  background: #22936d;
}
.post-header .category-menu .all-users a span.color-orange {
  background: #f88546;
}
.post-header .category-menu .all-users a span.color-light-green {
  background: #1eba17;
}
.post-header .category-menu .all-users a:not(:last-child) {
  border-bottom: 1px solid #e6eeef;
}
.post-header .category-menu .all-users a img {
  width: 20px;
  margin-right: 10px;
  display: inline-block;
}
.post-header .category-menu .all-users a:hover, .post-header .category-menu .all-users a:focus {
  background: transparent;
  color: #1d2746;
}
.post-header .category-menu .short-by {
  padding: 10px 20px;
}
.post-header .category-menu .short-by a {
  padding: 7px 0 7px 20px;
  position: relative;
  color: #838793;
  font-size: 14px;
}
.post-header .category-menu .short-by a.active-short {
  color: #1d2746;
}
.post-header .category-menu .short-by a.active-short:before {
  content: "N";
  position: absolute;
  font-family: eleganticons;
  left: 0;
  top: 7px;
  color: #0abe76;
  font-size: 14px;
}
.post-header .category-menu .short-by a:hover {
  background: transparent;
  color: #1d2746;
}

ul.forum-titles {
  list-style: none;
  display: flex;
  padding: 0;
  margin: 0;
  justify-content: flex-end;
}
ul.forum-titles li {
  text-align: right;
  font-size: 14px;
}
ul.forum-titles .forum-topic-count, ul.forum-titles .forum-reply-count {
  flex-basis: 25%;
}
ul.forum-titles .forum-freshness {
  flex-basis: 50%;
}
ul.forum-titles .forum-freshness .freshness-box {
  display: flex;
  font-size: 14px;
  line-height: 1.3;
  flex-direction: column;
}
ul.forum-titles .forum-freshness .freshness-box a {
  color: #6b707f;
}
ul.forum-titles .forum-freshness .freshness-box a:hover {
  color: #10b3d6;
}
ul.forum-titles .forum-freshness .freshness-box .freshness-btm {
  display: flex;
  justify-content: flex-end;
  padding-top: 5px;
  align-items: center;
}
ul.forum-titles .forum-freshness .freshness-box .freshness-btm a.bbp-author-link {
  padding-right: 10px;
}
ul.forum-titles .forum-freshness .freshness-box .freshness-btm .bbp-author-avatar img {
  max-width: 30px;
  border-radius: 50%;
}

.forum-page-content {
  background: #f9fafb;
}
.forum-page-content .communities-boxes {
  margin: 0 -30px;
}

.answer-action {
  border-radius: 6px;
  background: #fff;
  box-shadow: 0px 3px 6px 0px rgba(4, 73, 89, 0.06);
  padding: 12px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 70px;
}
@media (max-width: 768px) {
  .answer-action {
    display: block;
  }
}
.answer-action .action-content {
  display: flex;
  align-items: center;
}
.answer-action .action-content .image-wrap {
  margin-right: 30px;
}
.answer-action .action-content .ans-title {
  font-size: 20px;
  color: #1d2746;
  font-weight: 500;
  margin-bottom: 0;
}
.answer-action .action-content p {
  margin: 0;
}
.answer-action .btn-ans {
  font-size: 16px;
  font-weight: 500;
  padding: 10px 25px;
  box-shadow: none;
  border: 1px solid #10b3d6;
  transition: all 0.3s ease-in-out;
}
.answer-action .btn-ans:hover {
  background: transparent;
  color: #10b3d6;
}
@media (max-width: 768px) {
  .answer-action .btn-ans {
    margin-left: 130px;
    margin-top: 5px;
  }
}

.call-to-action {
  padding: 55px 0;
  background: #1d2746;
  position: relative;
}
.call-to-action .container {
  position: relative;
}
.call-to-action .overlay-bg {
  position: absolute;
  background-image: url(../img/home_support/overlay_bg.png);
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center center;
  top: 0;
  left: 0;
  background-repeat: no-repeat;
  opacity: 0.5;
}
.call-to-action .action-content-wrapper {
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
@media (max-width: 576px) {
  .call-to-action .action-content-wrapper {
    display: block;
    text-align: center;
  }
}
.call-to-action .action-content-wrapper .action-title-wrap {
  display: flex;
  align-items: center;
}
.call-to-action .action-content-wrapper .action-title-wrap img {
  padding-right: 45px;
}
.call-to-action .action-content-wrapper .action-title-wrap .action-title {
  color: #fff;
  font-size: 44px;
  line-height: 1.2;
  font-weight: 500;
}
@media (max-width: 991px) {
  .call-to-action .action-content-wrapper .action-title-wrap .action-title {
    font-size: 34px;
  }
}
.call-to-action .action-content-wrapper .action_btn {
  border: 1px solid #10b3d6;
  font-size: 16px;
  padding: 9px 25px;
  font-weight: 500;
}
.call-to-action .action-content-wrapper .action_btn:hover {
  background: transparent;
  border-color: #fff;
  color: #fff;
}

.status_widget .status {
  margin-bottom: 25px;
}
.status_widget .offline {
  color: #f4b130;
  border: 2px solid #f5d597;
  padding: 0px 13px;
  display: inline-block;
  border-radius: 4px;
  margin-left: 5px;
}
.status_widget .title-sm {
  font-size: 16px;
  color: #1d2746;
  font-weight: 500;
  margin-bottom: 10px;
}
.status_widget .open-hours {
  margin-bottom: 30px;
}
.status_widget .open-hours p {
  margin-bottom: 0;
}
@media (min-width: 991px) {
  .status_widget .open-hours p {
    max-width: 240px;
  }
}
.status_widget .current-time {
  margin-top: 28px;
}
.status_widget .current-time:after {
  content: "";
  display: block;
  clear: both;
}
.status_widget .current-time li {
  float: left;
  width: 50%;
}

.w_tag_list.style-light li a {
  background: #f0f2f5;
}
.w_tag_list.style-light li a:hover {
  background: #e1f3f7;
  color: #10b3d6;
  box-shadow: none;
}

.usefull-links {
  margin: 0;
  padding: 0;
  list-style: none;
}
.usefull-links li {
  padding: 7px 0;
}
.usefull-links li i {
  margin-right: 10px;
}
.usefull-links li a {
  color: #6b707f;
  font-size: 16px;
  font-weight: 400;
  display: inline-block;
}
.usefull-links li a:hover {
  color: #10b3d6;
}

.ticket_categories li {
  padding: 12px 0 10px;
  position: relative;
}
.ticket_categories li:not(:last-child) {
  border-bottom: 1px solid #e4e7e9;
}
.ticket_categories li img {
  margin-right: 10px;
  margin-bottom: 5px;
}
.ticket_categories li a {
  font-size: 16px;
  color: #1d2746;
}
.ticket_categories li a:hover {
  color: #10b3d6;
}
.ticket_categories li .count {
  position: absolute;
  right: 0;
  background: #ebedf0;
  padding: 4px 7px 2px;
  border-radius: 10px;
  color: #838793;
  font-size: 14px;
  display: inline-block;
  line-height: 1;
}
.ticket_categories li .count1 {
  position: absolute;
  right: 0;
  padding: 4px 7px 2px;
  border-radius: 10px;
  font-size: 14px;
  display: inline-block;
  line-height: 1;
}
.ticket_categories li .count.count-fill {
  right: 35px;
  background: #0abe76;
  color: #fff;
}

/*---------------------------------------------------- */
/*----------------------------------------------------*/
.forum-post-top {
  display: flex;
}
.forum-post-top .author-avatar img {
  border-radius: 50%;
  margin-right: 15px;
}
.forum-post-top .forum-post-author .author-name {
  font-size: 16px;
  font-weight: 500;
  color: #1d2746;
}
.forum-post-top .forum-post-author .author-name:hover {
  color: #10b3d6;
}
.forum-post-top .forum-post-author .forum-author-meta {
  display: flex;
}
.forum-post-top .forum-post-author .forum-author-meta span, .forum-post-top .forum-post-author .forum-author-meta a {
  color: #838793;
  font-size: 14px;
}
.forum-post-top .forum-post-author .forum-author-meta a:hover {
  color: #10b3d6;
}
.forum-post-top .forum-post-author .forum-author-meta .author-badge svg {
  margin-top: -5px;
  margin-right: 5px;
}
.forum-post-top .forum-post-author .forum-author-meta .author-badge i {
  margin-right: 5px;
  color: #838793;
}
.forum-post-top .forum-post-author .forum-author-meta .author-badge:first-child {
  margin-right: 20px;
}

.action-button-container {
  display: flex;
  justify-content: flex-end;
}
.action-button-container.action-btns {
  justify-content: flex-start;
  margin-top: 30px;
}
.action-button-container.action-btns .action_btn {
  height: 30px;
  font-size: 14px;
  line-height: 30px;
  padding: 0 15px;
  font-weight: 400;
}
.action-button-container .ask-btn {
  box-shadow: none;
  font-size: 16px;
  font-weight: 500;
  padding: 8px 28px;
  margin-top: 2px;
}
.action-button-container .ask-btn:hover {
  background: #16c9f0;
}
.action-button-container .reply-btn {
  margin-right: 10px;
}
.action-button-container .too-btn {
  background: #fff;
  border: 1px solid #d0d8dc;
  color: #1d2746;
}
.action-button-container .too-btn:hover {
  background: #fff;
  border: 1px solid #1d2746;
  color: #1d2746;
}

.q-title {
  display: flex;
  padding: 40px 15px 15px 0;
}
.q-title h1 {
  font-size: 26px;
  color: #1d2746;
  line-height: 1.4;
}
.q-title .badge {
  margin-left: 10px;
  line-height: 1.4;
  margin-top: 5px;
}

.forum-post-content {
  padding-left: 62px;
}
.forum-post-content .forum-post-btm {
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #e8ecee;
  padding-bottom: 20px;
  padding-top: 30px;
}
.forum-post-content .forum-post-btm .taxonomy {
  font-size: 14px;
}
.forum-post-content .forum-post-btm .taxonomy i, .forum-post-content .forum-post-btm .taxonomy img {
  margin-right: 10px;
}
.forum-post-content .forum-post-btm .taxonomy a {
  color: #838793;
}
.forum-post-content .forum-post-btm .taxonomy a:hover {
  color: #10b3d6;
}
.forum-post-content .forum-post-btm .taxonomy a + a {
  padding-right: 2px;
}

.question-icon {
  font-size: 50px;
  color: #1d2746;
  margin-right: 15px;
  font-weight: 600;
}

/** === Best Answer === **/
.best-answer {
  background: #f1fdf3;
  padding: 30px 40px 30px 30px;
  margin-top: 60px;
  border-radius: 6px;
}
.best-answer .accepted-ans-mark {
  color: #0abe76;
  font-size: 14px;
  text-align: right;
}
.best-answer .accepted-ans-mark i {
  padding-right: 5px;
}
.best-answer .best-ans-content {
  margin-top: 30px;
}
.best-answer .best-ans-content .question-icon {
  margin-top: 10px;
  margin-right: 20px;
}
.best-answer .best-ans-content p {
  font-size: 15px;
}
.best-answer .best-ans-content p:last-child {
  margin-bottom: 0;
}

/** === All answer === **/
.all-answers {
  margin-top: 60px;
}
.all-answers .title {
  font-size: 20px;
  font-weight: 500;
  margin-bottom: 20px;
}
.all-answers .filter-bar {
  background: #f7f8f9;
  border: 1px solid #eaeeef;
  padding: 10px 30px;
  justify-content: space-between;
  border-radius: 4px;
}
.all-answers .filter-bar .custom-select {
  box-shadow: none;
}
.all-answers .filter-bar .custom-select:focus {
  outline: none;
  box-shadow: 0 0 0 0.2rem transparent;
  border: none;
}
.all-answers .filter-bar .custom-select::after {
  border-bottom: 1px solid #6b707f;
  border-right: 1px solid #6b707f;
  height: 6px;
  width: 6px;
  right: 5px;
}
.all-answers .filter-bar p {
  margin-top: 8px;
  margin-bottom: 0;
  font-size: 14px;
  color: #6b707f;
}
.all-answers .filter-bar .nice-select {
  background: #f7f8f9;
  height: 100%;
  border: 0;
  padding: 0 25px 0 0;
}
.all-answers .filter-bar .nice-select .list {
  width: 100px;
}
.all-answers .filter-bar .nice-select::-ms-expand {
  display: none;
}
.all-answers .forum-comment {
  margin-top: 30px;
}
.all-answers .forum-comment .comment-content {
  margin-left: 60px;
  margin-top: 10px;
  border-bottom: 1px solid #e8ecee;
  padding-bottom: 28px;
}
.all-answers .forum-comment .comment-content p {
  font-size: 14px;
  line-height: 1.6;
}
.all-answers .forum-comment .comment-content p:last-child {
  margin-bottom: 0;
}
.all-answers .forum-comment .comment-content .action-btns {
  margin-top: 20px;
  transition: all 1s ease-in;
}
.all-answers .pagination-wrapper {
  background: transparent;
  box-shadow: none;
  margin-top: 15px;
}

/** === Forum Sidebar === **/
.forum_sidebar {
  padding-left: 28px;
  font-size: 14px;
  padding-right: 5px;
}

/*---------------------------------------------------- */
/*----------------------------------------------------*/
/*=========== blog_top_post_area css ==========*/
.blog_top_post {
  box-shadow: 0 3px 6px 0 rgba(4, 73, 89, 0.06);
  border-radius: 6px;
  margin: 0;
}
.blog_top_post .p_top_img {
  position: relative;
  padding-right: 0;
}
.blog_top_post .p_top_img .p_img {
  position: absolute;
  padding-right: 0;
  height: 100%;
  top: 0;
  right: 0;
  width: 100%;
  background: url("../img/blog-grid/top_post.jpg");
  background-size: cover;
}
.blog_top_post .b_top_post_content {
  padding: 50px 30px 50px 50px;
}
.blog_top_post .b_top_post_content .post_tag {
  padding-bottom: 15px;
}
.blog_top_post .b_top_post_content h3 {
  font-size: 26px;
  font-weight: 500;
  line-height: 36px;
  margin-bottom: 17px;
  transition: color 0.2s linear;
}
.blog_top_post .b_top_post_content h3:hover {
  color: #10b3d6;
}
.blog_top_post .b_top_post_content .learn_btn {
  color: #6b707f;
}
.blog_top_post .b_top_post_content .learn_btn:hover {
  color: #10b3d6;
}
.blog_top_post .b_top_post_content p {
  margin-bottom: 22px;
}
.blog_top_post .b_top_post_content .post_author {
  padding-top: 45px;
}

.post_tag a {
  font-size: 14px;
  color: #6b707f;
  display: inline-block;
}
.post_tag a.cat-KbDoc {
  color: #fd8d2a;
}
.post_tag a.cat-megento {
  color: #f26322;
}
.post_tag a.cat-woocommerce {
  color: #96588a;
}
.post_tag a.c_blue {
  color: #10b3d6;
}
.post_tag a.cat-laravel {
  color: #ff2d20;
}
.post_tag a + a {
  padding-left: 10px;
}
.post_tag a + a:before {
  content: "";
  width: 1px;
  height: 13px;
  background: #B7B9BE;
  display: inline-block;
  margin-right: 13px;
}

.post_author .round_img {
  border-radius: 50%;
  overflow: hidden;
  margin-right: 15px;
}
.post_author .round_img img {
  height: 40px;
  width: auto;
}
.post_author .author_text {
  margin-top: 5px;
}
.post_author .author_text h4 {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 0;
  color: #1d2746;
}
.post_author .author_text .date {
  font-size: 13px;
  font-weight: 400;
  color: #6b707f;
}

/*=========== blog_top_post_area css ==========*/
/*=========== Start blog_grid_area css ==========*/
.doc_blog_grid_area {
  padding-bottom: 100px;
}

.blog_grid_inner {
  border-top: 1px solid #e7edf0;
}

.blog_tab .nav-item {
  margin-bottom: 0;
}
.blog_tab .nav-item .nav-link {
  padding: 17px 21px;
  border: 0;
  border-radius: 0;
  font-weight: 500;
  color: #858997;
  font-size: 14px;
  position: relative;
  background: transparent;
  transition: all 0.2s ease-in;
}
.blog_tab .nav-item .nav-link:before {
  content: "";
  width: 100%;
  position: absolute;
  top: 0;
  height: 2px;
  left: 0;
  background: #10b3d6;
  transform: scale(0);
  transform-origin: 50% 50%;
  transition: all 0.3s ease-in;
}
.blog_tab .nav-item .nav-link.active, .blog_tab .nav-item .nav-link:hover {
  background: #e9f6f9;
  color: #10b3d6;
}
.blog_tab .nav-item .nav-link.active:before, .blog_tab .nav-item .nav-link:hover:before {
  transform: scale(1);
}
.blog_tab .nav-item.cat-laravel .nav-link:hover {
  background: rgba(255, 45, 32, 0.2);
  color: #ff2d20;
}
.blog_tab .nav-item.cat-laravel .nav-link:before {
  background: #ff2d20;
}
.blog_tab .nav-item.cat-KbDoc .nav-link:hover {
  background: rgba(253, 141, 42, 0.2);
  color: #fd8d2a;
}
.blog_tab .nav-item.cat-KbDoc .nav-link:before {
  background: #fd8d2a;
}
.blog_tab .nav-item.cat-megento .nav-link:hover {
  background: rgba(242, 99, 34, 0.2);
  color: #f26322;
}
.blog_tab .nav-item.cat-megento .nav-link:before {
  background: #f26322;
}
.blog_tab .nav-item.cat-woocommerce .nav-link:hover {
  background: rgba(150, 88, 138, 0.2);
  color: #96588a;
}
.blog_tab .nav-item.cat-woocommerce .nav-link:before {
  background: #96588a;
}

.blog_grid_tab {
  padding-top: 60px;
  margin-bottom: -40px;
}

.b_title {
  font-size: 22px;
  font-weight: 500;
  line-height: 30px;
  transition: all 0.2s linear;
}
.b_title:hover {
  color: #10b3d6;
}

.blog_grid_post {
  border-radius: 4px;
  background-color: white;
  box-shadow: 0 3px 6px 0 rgba(4, 73, 89, 0.06);
  margin-bottom: 40px;
  transition: all 0.3s linear;
  overflow: hidden;
  cursor: pointer;
}
.blog_grid_post .grid_post_content {
  padding: 20px 30px 25px;
}
.blog_grid_post .grid_post_content .post_tag {
  padding-bottom: 10px;
}
.blog_grid_post .grid_post_content .b_title {
  margin-bottom: 18px;
}
.blog_grid_post .grid_post_content .post_author {
  padding-top: 18px;
}
.blog_grid_post:hover {
  box-shadow: 0 20px 24px 0 rgba(4, 73, 89, 0.1);
}

.doc_blog_grid_area_two {
  padding: 100px 0 120px;
}

/*=========== End blog_grid_area css ==========*/
/*=========== Start blog_area css ==========*/
.blog_area {
  padding: 100px 0 120px;
}

.blog_single_item .blog_single_img {
  display: block;
  margin-bottom: 25px;
}
.blog_single_item .blog_single_img img {
  max-width: 100%;
}
.blog_single_item p {
  font-size: 16px;
  line-height: 30px;
  margin-bottom: 26px;
}
.blog_single_item p a {
  color: #10b3d6;
  text-decoration: underline;
}

blockquote {
  background: #f3f8f9;
  border-left: 4px solid #10b3d6;
  font-size: 20px;
  padding: 50px 40px;
  position: relative;
  z-index: 0;
  margin: 46px 0;
}
blockquote:before, blockquote:after {
  content: "";
  position: absolute;
  z-index: -1;
}
blockquote:before {
  background: url("../img/blog-classic/quote_top.png") no-repeat scroll left top;
  width: 132px;
  height: 81px;
  top: 0;
}
blockquote:after {
  background: url("../img/blog-classic/quote_bottom.png") no-repeat scroll left bottom;
  width: 77px;
  height: 75px;
  right: 40px;
  bottom: 30px;
}
blockquote .c_head {
  color: #6b707f;
  font-weight: 400;
  line-height: 34px;
  margin-bottom: 45px;
}
blockquote .author {
  font-weight: 500;
  font-size: 16px;
  color: #1d2746;
}

.single_post_two {
  padding-top: 10px;
}
.single_post_two h2 {
  font-size: 30px;
  font-weight: 500;
  margin-bottom: 15px;
}
.single_post_two img {
  margin-bottom: 25px;
  max-width: 100%;
}
.single_post_two p {
  margin-bottom: 28px;
}

.blog_social {
  padding-top: 20px;
}
.blog_social h5 {
  font-size: 16px;
  margin-bottom: 0;
}
.blog_social .f_social_icon {
  display: inline-block;
  margin-top: 28px;
  position: relative;
  z-index: 0;
  padding-top: 0;
  padding-left: 24px;
  padding-right: 24px;
}
.blog_social .f_social_icon:before, .blog_social .f_social_icon:after {
  content: "";
  width: 105%;
  position: absolute;
  top: 50%;
  height: 1px;
  background: #dde5e7;
}
.blog_social .f_social_icon:before {
  left: -103%;
}
.blog_social .f_social_icon:after {
  right: -103%;
}
.blog_social .f_social_icon li a {
  margin-right: 0;
  border: 1px solid #e5edef;
  color: #989da9;
  width: 44px;
  height: 44px;
  line-height: 44px;
}
.blog_social .f_social_icon li + li {
  margin-left: 5px;
}

.blog_post_author {
  background: #f3f8f9;
  padding: 40px;
  padding-right: 70px;
  margin-top: 35px;
  border-radius: 5px;
}
.blog_post_author .author_img {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  margin-right: 30px;
  overflow: hidden;
}
.blog_post_author .media-body h5 {
  font-weight: 500;
  font-size: 18px;
  margin-bottom: 20px;
}
.blog_post_author .media-body p {
  margin-bottom: 0;
}

.comment_inner {
  padding-top: 80px;
}

.comment_box {
  margin-bottom: 0;
}
.comment_box .post_comment .comment_author {
  padding-top: 40px;
}
.comment_box .post_comment .comment_author .img_rounded {
  border-radius: 50%;
  margin-right: 30px;
}
.comment_box .post_comment .comment_author .media-body {
  border-bottom: 1px solid #dde5e7;
  padding-bottom: 24px;
}
.comment_box .post_comment .comment_author .media-body .comment_info h3 {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 5px;
}
.comment_box .post_comment .comment_author .media-body .comment_info .comment_date {
  font-size: 14px;
  font-weight: 400;
  color: #6b707f;
  margin-bottom: 15px;
}
.comment_box .post_comment .comment_author .media-body .comment_reply {
  font-size: 14px;
  font-weight: 500;
  color: #1d2746;
  transition: all 0.2s linear;
}
.comment_box .post_comment .comment_author .media-body .comment_reply i {
  vertical-align: middle;
  font-size: 18px;
  position: relative;
  top: -2px;
  padding-right: 5px;
}
.comment_box .post_comment .comment_author .media-body .comment_reply:hover {
  color: #10b3d6;
}
.comment_box .post_comment .reply_comment {
  padding-left: 80px;
}

.blog_comment_box {
  padding-top: 80px;
}
.blog_comment_box p {
  margin-bottom: 0;
}
.blog_comment_box .get_quote_form {
  padding-top: 40px;
}
.blog_comment_box .get_quote_form .form-group {
  margin-bottom: 30px;
}
.blog_comment_box .get_quote_form .form-group .form-control {
  height: 60px;
  border-radius: 4px;
  border: 1px solid #e1e8ea;
  font-size: 14px;
  line-height: 1.4;
  padding-left: 30px;
}
.blog_comment_box .get_quote_form .form-group .form-control.placeholder {
  color: #6b707f;
}
.blog_comment_box .get_quote_form .form-group .form-control:-moz-placeholder {
  color: #6b707f;
}
.blog_comment_box .get_quote_form .form-group .form-control::-moz-placeholder {
  color: #6b707f;
}
.blog_comment_box .get_quote_form .form-group .form-control::-webkit-input-placeholder {
  color: #6b707f;
}
.blog_comment_box .get_quote_form .form-group .form-control:focus {
  box-shadow: 0 20px 30px 0 rgba(4, 73, 89, 0.1);
  border-color: #f4f8f8;
}
.blog_comment_box .get_quote_form .form-group .form-control.message {
  height: 160px;
  padding-top: 22px;
}
.blog_comment_box .get_quote_form .thm_btn {
  padding: 12px 35px;
  box-shadow: none;
}
.blog_comment_box .get_quote_form .thm_btn:hover {
  box-shadow: 0 20px 30px 0 rgba(12, 118, 142, 0.24);
}

/*============= doc_blog_classic_area css ==============*/
.doc_blog_classic_area blockquote {
  margin-top: 0;
  margin-bottom: 50px;
}

.blog_classic_item {
  overflow: hidden;
  margin-bottom: 50px;
  transition: all 0.2s linear;
  cursor: pointer;
}
.blog_classic_item .b_top_post_content {
  padding: 26px 40px 30px;
}
.blog_classic_item .b_top_post_content .post_tag {
  padding-bottom: 10px;
}
.blog_classic_item .b_top_post_content .p_bottom {
  align-items: center;
}
.blog_classic_item .b_top_post_content .p_bottom .learn_btn {
  color: #1d2746;
}
.blog_classic_item .b_top_post_content .p_bottom .learn_btn:hover {
  color: #10b3d6;
}
.blog_classic_item .b_top_post_content .post_author {
  padding-top: 0;
  align-items: center;
}
.blog_classic_item:hover {
  box-shadow: 0 20px 24px 0 rgba(4, 73, 89, 0.1);
}

.video_post {
  position: relative;
  z-index: 1;
}
.video_post:before {
  content: "";
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: rgba(9, 16, 50, 0.2);
  position: absolute;
  z-index: 0;
}

.video_icon {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  background-color: white;
  box-shadow: 0 20px 40px 0 rgba(1, 16, 58, 0.14);
  text-align: center;
  line-height: 75px;
  font-size: 30px;
  position: absolute;
  transition: all 0.3s linear;
  display: inline-block;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.video_icon:before {
  content: "";
  width: 100%;
  height: 100%;
  border: 0.2px solid #fff;
  border-radius: 50%;
  opacity: 0.6;
  position: absolute;
  left: 0;
  transform: scale(1.5);
}
.video_icon i {
  color: #10b3d6;
  text-shadow: 0 6px 13px rgba(12, 118, 142, 0.6);
  position: relative;
}
.video_icon i:after {
  width: 300%;
  height: 300%;
  position: absolute;
  left: 50%;
  top: 50%;
  border-radius: 50%;
  box-shadow: 0 1px 15px 1px rgba(255, 255, 255, 0.5);
  content: "";
  -webkit-animation: pulse 2s infinite;
  -webkit-transform: scale(8);
  -ms-transform: scale(8);
  transform: scale(8);
  animation: pulse 2s infinite;
}

.blog_link_post {
  background: #f3f8f9;
  border-left: 4px solid #10b3d6;
  padding: 45px 40px;
  line-height: 34px;
  position: relative;
  overflow: hidden;
  margin-bottom: 50px;
}
.blog_link_post:before, .blog_link_post:after {
  content: "";
  position: absolute;
}
.blog_link_post:before {
  background: url("../img/blog-classic/icon_01.png") no-repeat;
  top: 5px;
  left: 40px;
  width: 42px;
  height: 41px;
}
.blog_link_post:after {
  background: url("../img/blog-classic/icon_02.png") no-repeat;
  bottom: -35px;
  right: 30px;
  width: 100px;
  height: 100px;
}
.blog_link_post p {
  font-size: 20px;
  color: #6b707f;
  margin-bottom: 0;
  transition: color 0.2s linear;
}
.blog_link_post p:hover {
  color: #10b3d6;
}

.blog_related_post {
  padding-top: 80px;
}
.blog_related_post .c_head {
  margin-bottom: 45px;
}
.blog_related_post .row {
  margin-bottom: -40px;
}
.blog_related_post .blog_grid_post {
  box-shadow: none;
}
.blog_related_post .blog_grid_post img {
  border-radius: 4px;
  max-width: 100%;
}
.blog_related_post .grid_post_content {
  padding-left: 0;
  padding-right: 0;
  padding-bottom: 0;
}
.blog_related_post .grid_post_content .post_tag {
  padding-bottom: 4px;
}
.blog_related_post .grid_post_content .b_title {
  font-size: 18px;
  line-height: 24px;
}
.blog_related_post .grid_post_content p {
  margin-bottom: 0;
}

/*============ keyframes animation  =============*/
@-webkit-keyframes pulse {
  0% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(2);
  }
}
@keyframes pulse {
  0% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(2);
  }
}
/*============= doc_blog_classic_area css ==============*/
.pagination .page-numbers {
  width: 36px;
  height: 36px;
  display: inline-flex;
  border-radius: 4px;
  border: 1px solid #a7e1ed;
  text-align: center;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  vertical-align: middle;
  color: #10b3d6;
  transition: all 0.2s linear;
}
.pagination .page-numbers i {
  font-size: 24px;
}
.pagination .page-numbers + .page-numbers {
  margin-left: 10px;
}
.pagination .page-numbers:hover, .pagination .page-numbers.current {
  background-color: #10b3d6;
  border-color: #10b3d6;
  box-shadow: 0 20px 30px 0 rgba(12, 118, 142, 0.24);
  color: #fff;
}
.pagination .page-numbers.current {
  box-shadow: none;
}

/*=========== End blog_area css ==========*/
/*=========== Start blog_sidebar css ==========*/
.widget .c_head {
  margin-bottom: 35px;
}
.widget + .widget {
  margin-top: 53px;
}
.widget > ul.list-unstyled {
  margin-top: -10px;
}

.about_widget .img {
  border: 1px solid #e5edef;
  padding: 8px;
  border-radius: 4px;
}
.about_widget .img img {
  max-width: 100%;
}
.about_widget .text {
  padding-top: 28px;
}
.about_widget .text h3 {
  font-family: "Great Vibes", cursive;
  font-size: 40px;
  font-weight: 400;
  margin-bottom: 12px;
}
.about_widget .text p {
  margin-bottom: 0;
}

.categorie_list {
  margin-bottom: 0;
}
.categorie_list li a {
  font-size: 16px;
  font-weight: 400;
  color: #1d2746;
  position: relative;
  padding-left: 20px;
}
.categorie_list li a:before {
  content: "";
  width: 6px;
  height: 6px;
  background: #aab0c0;
  position: absolute;
  left: 0;
  top: 6.5px;
}
.categorie_list li a span {
  padding-left: 10px;
}
.categorie_list li a:hover {
  color: #10b3d6;
}
.categorie_list li a:hover:before {
  background: #10b3d6;
}
.categorie_list li + li {
  margin-top: 8px;
}

.recent_post_item {
  align-items: center;
}
.recent_post_item img {
  border-radius: 4px;
  margin-right: 20px;
}
.recent_post_item .media-body h5 {
  font-size: 16px;
  line-height: 20px;
  font-weight: 500;
  transition: color 0.2s linear;
}
.recent_post_item .media-body h5:hover {
  color: #10b3d6;
}
.recent_post_item .media-body .entry_post_date {
  font-size: 14px;
  color: #6b707f;
}
.recent_post_item + .recent_post_item {
  margin-top: 20px;
}

.w_tag_list {
  display: flex;
  flex-wrap: wrap;
  margin: -5px;
}
.w_tag_list li {
  margin: 4px;
}
.w_tag_list li a {
  color: #6b707f;
  padding: 2px 18px;
  display: inline-block;
  background: #f3f8f9;
  font-size: 14px;
  border-radius: 4px;
  transition: all 0.2s linear;
}
.w_tag_list li a:hover {
  box-shadow: 0 10px 20px 0 rgba(12, 118, 142, 0.24);
  background: #10b3d6;
  color: #fff;
}

.comments_widget {
  margin-bottom: 0;
}
.comments_widget li h6 {
  font-size: 13px;
  font-weight: 500;
  color: #1d2746;
  margin-bottom: 12px;
}
.comments_widget li h6 i {
  color: #6b707f;
  padding-right: 8px;
}
.comments_widget li .text {
  display: block;
  background-color: #f3f8f9;
  border: 1px solid #e7edee;
  border-radius: 4px;
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
  color: #686d7d;
  padding: 8px 24px;
}
.comments_widget li + li {
  margin-top: 23px;
}

.instragram_info {
  margin: -5px;
  display: flex;
  flex-wrap: wrap;
}
.instragram_info .instragram_item {
  display: block;
  margin: 5px;
  position: relative;
  z-index: 0;
  border-radius: 4px;
  overflow: hidden;
  transition: all 0.1s linear;
  width: calc(90% / 3);
}
.instragram_info .instragram_item img {
  max-width: 100%;
}
.instragram_info .instragram_item:before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(29, 39, 70, 0.5);
  z-index: 0;
  opacity: 0;
  transition: all 0.2s linear;
}
.instragram_info .instragram_item:hover:before {
  opacity: 1;
}

/*=========== End blog_sidebar css ==========*/
/*---------------------------------------------------- */
/*----------------------------------------------------*/
/*================ signup_area css ==============*/
.signup_area {
  height: 100vh;
  min-height: 580px;
  background: #fbfcfd;
}
.signup_area .row {
  height: 100%;
}

.sign_left {
  width: calc(50% - 360px);
  position: relative;
  z-index: 1;
  padding: 100px 70px 0;
}
.sign_left .top {
  left: 0;
  top: 0;
  z-index: -1;
}
.sign_left .bottom {
  bottom: 0;
  right: 0;
  z-index: -1;
}
.sign_left .round {
  position: absolute;
  width: 650px;
  height: 650px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.06);
  left: -150px;
  bottom: -60px;
  z-index: -1;
}
.sign_left h2 {
  color: #fff;
  font-size: 36px;
  line-height: 44px;
}

.signup_left {
  background: #795fee;
}
.signup_left .middle {
  top: 50%;
  margin-top: -150px;
}

.sign_right {
  width: calc(50% + 360px);
  flex: 1;
  display: flex;
  align-items: center;
}

.sign_inner {
  max-width: 520px;
  margin: 0 auto;
}
.sign_inner h3 {
  font-size: 26px;
  line-height: 34px;
}
.sign_inner p {
  font-size: 14px;
}
.sign_inner p a {
  color: #1d2746;
  font-weight: 500;
}
.sign_inner .btn-google {
  box-shadow: 0 4px 10px 0 rgba(4, 73, 89, 0.06);
  border-radius: 6px;
  border: 1px solid #e6ecee;
  font-size: 14px;
  color: #6b707f;
  padding: 8px 27px;
  display: inline-block;
  margin-top: 20px;
}
.sign_inner .btn-google img {
  vertical-align: inherit;
  padding-right: 8px;
}
.sign_inner .btn-google:hover {
  box-shadow: none;
}
.sign_inner .divider {
  border-bottom: 1px solid #e3e8ea;
  position: relative;
  margin-bottom: 30px;
  margin-top: 36px;
}
.sign_inner .divider .or-text {
  font-size: 16px;
  color: #1d2746;
  position: absolute;
  top: -14px;
  padding: 0 10px;
  background: #fbfcfd;
  display: inline-block;
  left: 50%;
  margin-left: -21px;
}

.login_form .form-group {
  margin-bottom: 30px;
  position: relative;
}
.login_form .form-group .small_text {
  color: #1d2746;
  margin-bottom: 5px;
}
.login_form .form-group .form-control {
  border-radius: 6px;
  background-color: #fbfcfd;
  border: 1px solid #e3e8ea;
  font-size: 14px;
  color: #1d2746;
  font-weight: 400;
  font-family: "Roboto", sans-serif;
  height: 60px;
  line-height: 60px;
  padding: 2px 30px 0;
  transition: all 0.2s linear;
}
.login_form .form-group .form-control.placeholder {
  color: #878b99;
  font-family: "Roboto", sans-serif;
}
.login_form .form-group .form-control:-moz-placeholder {
  color: #878b99;
  font-family: "Roboto", sans-serif;
}
.login_form .form-group .form-control::-moz-placeholder {
  color: #878b99;
  font-family: "Roboto", sans-serif;
}
.login_form .form-group .form-control::-webkit-input-placeholder {
  color: #878b99;
  font-family: "Roboto", sans-serif;
}
.login_form .form-group .form-control:focus {
  box-shadow: 0 20px 30px 0 rgba(4, 73, 89, 0.1);
}
.login_form .form-group .check_box {
  margin-top: -10px;
}
.login_form .form-group .check_box input[type=checkbox] {
  position: relative;
  top: 1px;
  width: 0;
  -webkit-box-shadow: none;
  box-shadow: none;
  background: transparent;
}
.login_form .form-group .check_box input[type=checkbox]:before {
  content: "";
  display: block;
  position: absolute;
  width: 12px;
  height: 12px;
  background: #fbfcfd;
  top: 0;
  left: 0;
  border-radius: 2px;
  border: 1px solid #a6abb7;
}
.login_form .form-group .check_box input[type=checkbox]:checked:after {
  content: "N";
  font-family: "ElegantIcons";
  display: block;
  font-size: 10px;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  color: #10b3d6;
  text-align: center;
  line-height: 12px;
  left: 0;
}
.login_form .form-group .l_text {
  font-size: 16px;
  color: #6b707f;
  padding-left: 20px;
  margin-bottom: 0;
}
.login_form .form-group .l_text span {
  color: #10b3d6;
}
.login_form .form-group .confirm_password {
  position: relative;
}
.login_form .form-group .forget_btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  right: 30px;
  font-size: 14px;
  color: #a5a9b4;
}
.login_form .thm_btn {
  padding: 9px 24px;
  box-shadow: none;
}
.login_form .thm_btn:hover {
  box-shadow: 0 20px 30px 0 rgba(12, 118, 142, 0.24);
}

/*================ signup_area css ==============*/
/*================ signin_left css ==============*/
.signin_left {
  background: #10b3d6;
}
.signin_left:before {
  content: "";
  left: 0;
  width: 100%;
  height: 100px;
  background-image: linear-gradient(180deg, #10b3d6 0%, #1d2746 100%);
  position: absolute;
  bottom: 0;
  z-index: 0;
}
.signin_left .round {
  bottom: 100px;
  width: 600px;
  height: 600px;
  left: -50px;
}
.signin_left .middle {
  bottom: 50px;
}
.signin_left .bottom {
  bottom: 100px;
}

/*================ signin_left css ==============*/
/*---------------------------------------------------- */
/*----------------------------------------------------*/
/*=========== contact_area css =========*/
.get_info_area {
  position: relative;
  z-index: 1;
}

.get_info_item {
  border: 1px solid #e3e8ea;
  border-radius: 6px;
  padding: 31px 35px;
  margin-left: 0;
  margin-right: 0;
}
.get_info_item .media img {
  margin-right: 60px;
}
.get_info_item .media .media-body h5 {
  margin-bottom: 4px;
  font-size: 22px;
}
.get_info_item .media .media-body p {
  margin-bottom: 0;
  color: #6b707f;
}
.get_info_item .time {
  font-size: 16px;
  color: #6b707f;
}
.get_info_item .time span {
  color: #1d2746;
}
.get_info_item .doc_border_btn {
  max-width: 190px;
  width: 100%;
  display: block;
  text-align: center;
  padding-left: 10px;
  padding-right: 10px;
}
.get_info_item + .get_info_item {
  margin-top: 30px;
}

.contact_info {
  padding-top: 80px;
}
.contact_info .contact_form .form-group h6 {
  font-size: 16px;
}
.contact_info .contact_form .form-group .box_info {
  margin-left: -20px;
  margin-right: -20px;
}
.contact_info .contact_form .form-group .form-check {
  display: inline-block;
  font-size: 16px;
  line-height: 16px;
  font-weight: 400;
  color: #6b707f;
  padding-left: 22px;
  margin: 20px 20px 15px;
}
.contact_info .contact_form .form-group .form-check input[type=checkbox] {
  width: 12px;
  position: absolute;
  outline: none;
  height: 12px;
  top: 1px;
  left: 0;
  margin: 0;
}
.contact_info .contact_form .form-group .form-check input[type=checkbox]:before {
  content: "";
  display: block;
  position: absolute;
  width: 12px;
  height: 12px;
  border: 1px solid #6b707f;
  top: 0;
  left: 0;
  border-radius: 2px;
  background-color: #fff;
}
.contact_info .contact_form .form-group .form-check input[type=checkbox]:after {
  content: "";
  display: block;
  width: 5px;
  height: 11px;
  border: solid #10b3d6;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
  position: absolute;
  top: -2px;
  left: 5px;
  opacity: 0;
  transition: all 0.2s linear;
}
.contact_info .contact_form .form-group .form-check input[type=checkbox]:checked:before {
  border-color: #10b3d6;
}
.contact_info .contact_form .form-group .form-check input[type=checkbox]:checked:after {
  opacity: 1;
}
.contact_info .contact_form .form-group .form-check input[type=checkbox]:checked + label {
  color: #10b3d6;
}
.contact_info .contact_form .form-group .form-check input[type=radio] {
  width: 12px;
  position: absolute;
  outline: none;
  height: 12px;
  margin: 0;
  left: 0;
  top: 1px;
}
.contact_info .contact_form .form-group .form-check input[type=radio]:before {
  content: "";
  display: block;
  position: absolute;
  width: 12px;
  height: 12px;
  border: 1px solid #6b707f;
  border-radius: 50%;
  top: 0;
  left: 0;
  background-color: #fff;
  transition: all 0.2s linear;
}
.contact_info .contact_form .form-group .form-check input[type=radio]:after {
  content: "";
  display: block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #10b3d6;
  position: absolute;
  top: 50%;
  left: 50%;
  margin-left: -3px;
  margin-top: -3px;
  transform: scale(0);
  transition: all 0.2s linear;
}
.contact_info .contact_form .form-group .form-check input[type=radio]:checked:before {
  border-color: #10b3d6;
}
.contact_info .contact_form .form-group .form-check input[type=radio]:checked:after {
  transform: scale(1);
}
.contact_info .contact_form .form-group .form-check input[type=radio]:checked + label {
  color: #10b3d6;
}
.contact_info .contact_form .form-group .form-control {
  height: 60px;
  line-height: 55px;
  background: #f9fbfc;
  box-shadow: none;
  border-color: #d9e1e3;
  padding-left: 30px;
  font-weight: 400;
  transition: all 0.2s linear;
  border-radius: 6px;
}
.contact_info .contact_form .form-group .form-control.placeholder {
  color: #878b99;
}
.contact_info .contact_form .form-group .form-control:-moz-placeholder {
  color: #878b99;
}
.contact_info .contact_form .form-group .form-control::-moz-placeholder {
  color: #878b99;
}
.contact_info .contact_form .form-group .form-control::-webkit-input-placeholder {
  color: #878b99;
}
.contact_info .contact_form .form-group .form-control.message {
  height: 200px;
  padding-top: 0;
}
.contact_info .contact_form .form-group .form-control:focus {
  background: #fbfcfd;
  box-shadow: 0 20px 30px 0 rgba(4, 73, 89, 0.1);
}
.contact_info .contact_form .form-group:last-child {
  margin-bottom: 0;
}
.contact_info .contact_fill {
  padding-top: 20px;
}

/*=========== contact_area css =========*/
/*---------------------------------------------------- */
/*----------------------------------------------------*/
/*============ error_area css ========*/
.error_area {
  display: flex;
  position: relative;
  z-index: 1;
  height: 100vh;
  min-height: 620px;
}

.error_dot {
  position: absolute;
  border-radius: 50%;
}
.error_dot.one {
  width: 8px;
  height: 8px;
  background: #10b3d6;
  left: 325px;
  top: 120px;
  animation: spin2 2s infinite alternate;
}
.error_dot.two {
  width: 16px;
  height: 16px;
  background: #f99e43;
  left: 170px;
  bottom: 260px;
  animation: spin1 1s infinite alternate;
}
.error_dot.three {
  width: 30px;
  height: 30px;
  background: #beebf5;
  animation: spin1 2s infinite alternate;
  right: 225px;
  top: 370px;
}
.error_dot.four {
  width: 6px;
  height: 6px;
  background: #ffa3be;
  bottom: 148px;
  right: 185px;
  animation: spin2 2s infinite alternate;
}

.error_content_two {
  max-width: 970px;
  margin: 0 auto;
}
.error_content_two h2 {
  font-size: 36px;
  font-weight: 700;
  margin-bottom: 15px;
}
.error_content_two p {
  font-size: 18px;
  color: #82879c;
}

.error_img {
  padding-bottom: 55px;
  position: relative;
  z-index: 1;
}
.error_img .error_shap {
  left: 50%;
  transform: translateX(-50%);
  z-index: -1;
}
.error_img .clipInDown {
  display: inline-block;
  position: relative;
}
.error_img .one {
  top: -50px;
}
.error_img .two {
  margin: 0 12px;
  top: -12px;
}
.error_img .three {
  top: -58px;
}
.error_img .img_one {
  animation: customUp 1.5s infinite alternate;
}
.error_img .img_two {
  animation: customUp 1.8s infinite alternate;
}
.error_img .img_three {
  animation: customUp 2s infinite alternate;
}

.error_search {
  margin-bottom: 40px;
  padding-top: 25px;
}
.error_search .form-control {
  height: 60px;
  border-radius: 4px;
  background: #fff;
  box-shadow: 0 16px 50px 0 rgba(3, 62, 75, 0.1);
  font-size: 16px;
  font-weight: 400;
  color: #1d2746;
  border: 0;
  padding-left: 30px;
}
.error_search .form-control.placeholder {
  color: #82879c;
}
.error_search .form-control:-moz-placeholder {
  color: #82879c;
}
.error_search .form-control::-moz-placeholder {
  color: #82879c;
}
.error_search .form-control::-webkit-input-placeholder {
  color: #82879c;
}

/*============ error_area css ========*/
/*---------------------------------------------------- */
/*----------------------------------------------------*/
.body_dark {
  color: #d3d4d4;
  /** === Sticky Menu Navbar === **/
  /** === Focus, active color === **/
  /** !-- End Focus, active color **/
  /** === Border color === **/
  /** !-- End Border color **/
}
.body_dark .body_wrapper {
  background: #131417;
}
.body_dark .sticky_menu .navbar-brand img {
  display: none;
}
.body_dark .sticky_menu .navbar-brand img + img {
  display: block;
}
.body_dark .sticky_menu .menu_one, .body_dark .sticky_menu .menu > .nav-item.submenu .dropdown-menu, .body_dark .sticky_menu .menu > .nav-item.submenu .dropdown-menu:before {
  background: #252830;
}
.body_dark .sticky_menu .menu > .nav-item.submenu .dropdown-menu, .body_dark .sticky_menu .menu > .nav-item.submenu .dropdown-menu:before {
  border-color: #5a5f73;
}
.body_dark .sticky_menu .menu_one .menu > .nav-item .nav-link, .body_dark .sticky_menu .menu > .nav-item.submenu .dropdown-menu .nav-item .nav-link h5 {
  color: #fff;
}
.body_dark .search_form .form-control {
  background: #5a5f73;
  border-color: #2c303a;
}
.body_dark .search_form button {
  color: #8f94a6;
}
.body_dark.onepage-doc .nav-sidebar .nav-item .docs-progress-bar, .body_dark .fontsize-controllers .btn-group .btn:focus,
.body_dark .doc_rightsidebar .bootstrap-select > .dropdown-menu .inner .dropdown-menu li a.active, .body_dark .doc_rightsidebar .bootstrap-select > .dropdown-menu .inner .dropdown-menu li a:hover {
  background: #444857;
}
.body_dark.onepage-doc .nav-sidebar .nav-item, .body_dark .doc_rightsidebar .bootstrap-select > .dropdown-menu, .body_dark .doc_rightsidebar .dropdown-toggle, .body_dark .fontsize-controllers .btn-group, .body_dark.onepage-doc .nav-sidebar .nav-item .dropdown_nav, .body_dark .doc_rightsidebar .doc_switch input[type=checkbox],
.body_dark .doc_rightsidebar .bootstrap-select > .dropdown-menu .inner .dropdown-menu {
  background: #2c303a;
}
.body_dark .doc_left_sidebarlist:before {
  background: #1e1f26;
}
.body_dark .doc_rightsidebar .doc_switch input[type=checkbox],
.body_dark .doc_rightsidebar .dropdown-toggle, .body_dark .fontsize-controllers .btn-group .btn.rvfs-reset, .body_dark .fontsize-controllers .btn-group,
.body_dark .doc_left_sidebarlist:before, .body_dark .doc_left_sidebarlist, .body_dark .nav-sidebar + .nav-sidebar, .body_dark .doc_rightsidebar, .body_dark .v_menu, .body_dark .toggle_body, .body_dark .basic_table_info, .body_dark .basic_table_info tbody tr th, .body_dark .basic_table_info tbody tr td, .body_dark .typography_content .code-preview, .body_dark .doc_left_sidebarlist:before {
  border-color: #272830;
}
.body_dark.onepage-doc .nav-sidebar .nav-item .dropdown_nav:before {
  background: #272830;
}
.body_dark .nav-sidebar .nav-item .nav-link {
  color: #c7c9d3;
}
.body_dark .nav-sidebar .nav-item .nav-link.active, .body_dark .nav-sidebar .nav-item .nav-link:hover {
  color: #10b3d6;
}
.body_dark .nav-sidebar .nav-item .dropdown_nav li a {
  color: #bbb;
}
.body_dark .doc_menu .nav-link,
.body_dark .doc_rightsidebar .doc_right_link li a {
  color: #bbb;
}
.body_dark .doc_menu .nav-link.active, .body_dark .doc_menu .nav-link:hover,
.body_dark h1, .body_dark h2, .body_dark h3, .body_dark h4, .body_dark h5, .body_dark h6 {
  color: #fff;
}
.body_dark .documentation_item .icon {
  background: rgba(255, 255, 255, 0.2);
}
.body_dark .fontsize-controllers .btn-group .btn,
.body_dark .doc_rightsidebar .bootstrap-select .dropdown-toggle .filter-option-inner-inner {
  color: #c7c9d3;
}
.body_dark .border_bottom {
  background: #384c59;
}
.body_dark .link, .body_dark .version, .body_dark .question_box .signup_form .input-group, .body_dark .tab_shortcode .tab-content {
  border: 1px solid #384c59;
  background: #1f3341;
}
.body_dark .tab_shortcode .nav-tabs .nav-item .nav-link {
  background: #1f3341;
  border-color: #384c59;
}
.body_dark .question_box .signup_form .input-group .form-control {
  color: #6b707f;
}
.body_dark .question_box .signup_form .input-group button:before {
  background: #384c59;
}
.body_dark .get_started p {
  color: #d3d4d4;
}
.body_dark .f_bg_color, .body_dark .page_breadcrumb, .body_dark .toggle_btn, .body_dark .doc_accordion .card-header button {
  background: #1f3341;
}
.body_dark .shortcode_text ul li a,
.body_dark .tab_shortcode .nav-tabs .nav-item .nav-link.active, .body_dark .page_breadcrumb .breadcrumb .breadcrumb-item.active, .body_dark .breadcrumb-item + .breadcrumb-item::before, .body_dark .toggle_btn.collapsed:before, .body_dark .toggle_btn.collapsed, .body_dark .doc_accordion .card-header button, .body_dark .table {
  color: #fff;
}
.body_dark .toggle_body, .body_dark .doc_accordion, .body_dark .message_alert {
  background: #061f2f;
}
.body_dark .toggle_btn {
  color: #10b3d6;
}
.body_dark .menu > .nav-item.submenu .dropdown-menu .nav-item .nav-link h5 {
  color: #1d2746;
}

/*---------------------------------------------------- */
/*----------------------------------------------------*/
/*========== Start footer_area css ==========*/
.footer_area {
  padding-top: 100px;
  position: relative;
  z-index: 1;
}
.footer_area .leaf {
  right: 60px;
  bottom: 0;
  width: 180px;
  z-index: -1;
}
.footer_area .f_man {
  right: 280px;
  bottom: 40px;
}
.footer_area .f_man_two {
  bottom: 60px;
  left: 125px;
}
.footer_area .f_cloud {
  bottom: 410px;
  left: 150px;
}
.footer_area .f_email {
  left: 75px;
  bottom: 290px;
}
.footer_area .f_email_two {
  bottom: 200px;
  left: 250px;
}

.footer_p_top {
  padding-top: 225px;
}

.footer_top .border_bottom {
  margin-top: 100px;
}

.border_bottom {
  width: 100%;
  height: 1px;
  background: #e8f0f1;
}

.f_widget .f_title {
  font-size: 18px;
  margin-bottom: 30px;
}
.f_widget.subscribe_widget {
  padding-right: 40px;
}
.f_widget.subscribe_widget .f_logo {
  display: inline-block;
  margin-bottom: 32px;
}
.f_widget.subscribe_widget h4 {
  margin-bottom: 34px;
}
.f_widget.link_widget .link_list {
  margin-bottom: 0;
}
.f_widget.link_widget .link_list li a {
  color: #6b707f;
  font-size: 16px;
}
.f_widget.link_widget .link_list li a:hover {
  color: #10b3d6;
}
.f_widget.link_widget .link_list li + li {
  margin-top: 15px;
}

.footer_subscribe_form {
  position: relative;
}
.footer_subscribe_form .form-control {
  border-radius: 4px;
  background: #fff;
  box-shadow: 0 20px 30px 0 rgba(4, 73, 89, 0.08);
  height: 56px;
  border: 1px solid transparent;
  color: #1d2746;
  padding-left: 25px;
  position: relative;
  transition: all 0.2s linear;
}
.footer_subscribe_form .form-control.placeholder {
  color: #878b99;
}
.footer_subscribe_form .form-control:-moz-placeholder {
  color: #878b99;
}
.footer_subscribe_form .form-control::-moz-placeholder {
  color: #878b99;
}
.footer_subscribe_form .form-control::-webkit-input-placeholder {
  color: #878b99;
}
.footer_subscribe_form .form-control:focus {
  outline: none;
}
.footer_subscribe_form .s_btn {
  border: 0;
  font-size: 16px;
  font-weight: 400;
  background: #10b3d6;
  border-radius: 4px;
  color: #fff;
  position: absolute;
  top: 8px;
  bottom: 8px;
  right: 8px;
  padding: 7px 22px;
}

.f_social_icon {
  padding-top: 40px;
}
.f_social_icon li {
  display: inline-block;
}
.f_social_icon li a {
  width: 46px;
  height: 46px;
  border-radius: 50%;
  border: 2px solid #79ccde;
  font-size: 14px;
  line-height: 45px;
  text-align: center;
  display: block;
  color: #10b3d6;
  margin-right: 6px;
}
.f_social_icon li a:hover {
  background: #10b3d6;
  border-color: #10b3d6;
  box-shadow: 0 10px 20px 0 rgba(12, 118, 142, 0.2);
  color: #fff;
}

.footer_bottom {
  padding: 38px 0 66px;
  position: relative;
  z-index: 1;
}
.footer_bottom p {
  margin-bottom: 0;
  font-size: 16px;
  color: #6b707f;
}
.footer_bottom p a {
  color: #10b3d6;
}

.pl_30 {
  padding-left: 30px;
}

.pl_70 {
  padding-left: 70px;
}

.simple_footer {
  padding: 30px 0;
  position: relative;
  z-index: 1;
  background: #1d2746;
}
.simple_footer .row {
  align-items: center;
}
.simple_footer .leaf_right {
  position: absolute;
  right: 170px;
  bottom: 0;
  z-index: -1;
}
.simple_footer p {
  margin-bottom: 0;
  font-size: 16px;
  color: #a4a8b4;
}
.simple_footer p a {
  color: #10b3d6;
}
.simple_footer .f_social_icon {
  margin-bottom: 0;
  padding-top: 0;
}
.simple_footer .f_social_icon li a {
  width: 40px;
  height: 40px;
  line-height: 40px;
  color: #8d93a4;
  border-color: #8d93a4;
}
.simple_footer .f_social_icon li a:hover {
  background: #10b3d6;
  border-color: #10b3d6;
  color: #fff;
}

/*========== End footer_area css ==========*/
/*---------------------------------------------------- */

